#Docker file used to build chrome117 image
FROM docker-remote.registry.kroger.com/library/node:20.11.0-slim

COPY ./docker/libs/chromedriver_linux64_117.zip ./
COPY ./docker/scripts/bootstrap_chrome.sh ./

RUN echo 'Acquire::https::artifactory.kroger.com::Verify-Peer "false";' > /etc/apt/apt.conf.d/kroger-cert

RUN echo "deb http://artifactory.kroger.com/artifactory/debian stretch main" > /etc/apt/sources.list \
  && apt-get update \
  && apt-get -y install git curl xvfb gconf-service libasound2 libatk1.0-0 libc6 libcairo2 libcups2 \
  libdbus-1-3 libexpat1 libfontconfig1 libgcc1 libgconf-2-4 libgdk-pixbuf2.0-0 libglib2.0-0 \
  libgtk-3-0 libnspr4 libpango-1.0-0 libpangocairo-1.0-0 libstdc++6 libx11-6 libx11-xcb1 libxcb1 \
  libxcomposite1 libxcursor1 libxdamage1 libxext6 libxfixes3 libxi6 libxrandr2 libxrender1 libxss1 \
  libxtst6 apt-transport-https ca-certificates fonts-liberation libappindicator1 libnss3 lsb-release xdg-utils wget vim \
  g++ build-essential x11vnc fluxbox wmctrl fonts-roboto openjdk-8-jdk \
  && rm -rf /var/lib/apt/lists/*

RUN apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys 4EB27DB2A3B88B8B && \
    echo "deb [arch=amd64] https://artifactory.kroger.com/artifactory/google-debian/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list && \
    apt-get update && apt-get -y install sudo && \
    wget -q -O - https://artifactory.kroger.com/artifactory/google-debian/linux_signing_key.pub | sudo apt-key add - && \
    apt-get update \
    && apt-get install -y google-chrome-stable 

ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV CHROMEDRIVER_FILEPATH=/chromedriver_linux64_117.zip
ENV JAVA_HOME="/usr/lib/jvm/java-8-openjdk-amd64/"
ENV PATH=$PATH:$JAVA_HOME/bin
CMD '/bootstrap_chrome.sh'
