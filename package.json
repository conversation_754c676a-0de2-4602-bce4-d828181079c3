{"name": "sfp-apps-automation", "version": "1.0.0", "description": "UI Automated scripts for SFP Apps", "main": "index.js", "scripts": {"pretest": "rimraf TestReports/allure-results && rimraf allure-report && rimraf test-screenshots && rimraf wdio-logs && rimraf mocha-reports-json && rimraf mochawesome-report", "presmoke": "rimraf TestReports/allure-results && rimraf allure-report && rimraf test-screenshots && rimraf wdio-logs && rimraf mocha-reports-json && rimraf mochawesome-report", "preci-smoke": "rimraf TestReports/allure-results && rimraf allure-report && rimraf test-screenshots && rimraf wdio-logs && rimraf mocha-reports-json && rimraf mochawesome-report", "test": "wdio ConfigFiles/wdio.conf.js", "regression_navigator": "wdio ConfigFiles/wdio.conf.js --suite regression_navigator", "regression_EM": "wdio ConfigFiles/wdio.conf.js --suite regression_EM", "dev": "wdio ConfigFiles/wdio.conf.js --suite dev", "regression-headless": "wdio ConfigFiles/wdio.conf.js MODE=headless --suite regression_headless", "regression-UI": "wdio ConfigFiles/wdio.conf.js --suite regression_UI", "regression_oss": "wdio ConfigFiles/wdio.conf.js --suite regression_oss", "smoke": "wdio ConfigFiles/wdio.conf.js --suite smoke", "ci-backcheck": "wdio ConfigFiles/wdio.ci.conf.js --suite backcheck", "ci-nav-dev-backcheck": "wdio ConfigFiles/wdio.ci.conf.js --suite nav_dev_backcheck", "ci-healthcheck": "wdio ConfigFiles/wdio.ci.conf.js --suite healthcheck", "ci-test": "wdio ConfigFiles/wdio.ci.conf.js", "ci_regression_navigator_main_1": "wdio ConfigFiles/wdio.ci.conf.js --suite regression_navigator_main_1", "ci_regression_navigator_main_2": "wdio ConfigFiles/wdio.ci.conf.js --suite regression_navigator_main_2", "ci_regression_navigator_minor_1": "wdio ConfigFiles/wdio.ci.conf.js --suite regression_navigator_minor_1", "ci_regression_navigator_minor_2": "wdio ConfigFiles/wdio.ci.conf.js --suite regression_navigator_minor_2", "ci_regression_navigator_role_based": "wdio ConfigFiles/wdio.ci.conf.js --suite regression_navigator_role_based", "ci_regression_EM_1": "wdio ConfigFiles/wdio.ci.conf.js --suite regression_EM_1", "ci_regression_EM_2": "wdio ConfigFiles/wdio.ci.conf.js --suite regression_EM_2", "ci_regression_EM_3": "wdio ConfigFiles/wdio.ci.conf.js --suite regression_EM_3", "ci_regression_oss": "wdio ConfigFiles/wdio.ci.conf.js --suite regression_oss", "ci-smoke": "wdio ConfigFiles/wdio.ci.conf.js --suite smoke", "ci_prod_smoke": "wdio ConfigFiles/wdio.ci.conf.js --suite prod_smoke", "ci_nav_smoke": "wdio ConfigFiles/wdio.ci.conf.js --suite nav_smoke", "ci_em_smoke": "wdio ConfigFiles/wdio.ci.conf.js --suite em_smoke", "ci_cleanup_db": "wdio ConfigFiles/wdio.ci.conf.js --suite cleanup_db", "ci_cleanup_db_oss": "wdio ConfigFiles/wdio.ci.conf.js --suite oss cleanup_db", "ci_storeGroup": "wdio ConfigFiles/wdio.ci.conf.js --suite store_group_management", "test-rp": "RP_ENABLE=true wdio ConfigFiles/wdio.conf.js"}, "java": {"repositories": [{"id": "maven", "url": "http://artifactory.kroger.com/artifactory/libs-release/", "localRepository": "."}], "dependencies": [{"groupId": "com.krogerqa.selenium", "artifactId": "qmetry-import-utility", "version": "1.4", "classifier": "jar-with-dependencies"}]}, "author": "Varun|Shekur", "license": "ISC", "devDependencies": {"@babel/cli": "^7.15.7", "@babel/core": "^7.15.5", "@babel/preset-env": "^7.15.6", "@babel/register": "^7.15.3", "@reportportal/agent-js-webdriverio": "^5.1.4", "@wdio/allure-reporter": "^7.19.7", "@wdio/concise-reporter": "^7.14.1", "@wdio/devtools-service": "^7.12.5", "@wdio/local-runner": "^7.12.4", "@wdio/mocha-framework": "^7.12.4", "@wdio/selenium-standalone-service": "^7.16.11", "@wdio/spec-reporter": "^7.10.1", "axios": "^0.27.2", "chai": "^4.3.4", "chromedriver": "^135.0.0", "eslint": "^7.32.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.24.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-wdio": "^7.19.4", "local-runner": "^0.1.1-alpha", "pdfjs-dist": "^2.13.216", "rimraf": "^3.0.2", "underscore": "^1.13.7", "wdio-chromedriver-service": "^7.2.0", "webdriverio": "^7.16.12"}, "dependencies": {"@wdio/cli": "^7.0.0", "allure-commandline": "^2.13.8", "babel-preset-latest": "^6.24.1", "downloadjs": "^1.4.7", "downloads-folder": "^3.0.1", "i": "^0.3.7", "mssql": "^10.0.3", "node-downloader-helper": "^1.0.19", "node-fetch-npm": "^2.0.4", "node-java-maven": "^0.1.2", "node-stream-zip": "^1.15.0", "os": "^0.1.2", "pdf-parse": "^1.1.1", "read-excel-file": "^5.2.15", "update": "^0.7.4", "xlsx": "^0.18.5", "xmlhttprequest": "^1.8.0"}}