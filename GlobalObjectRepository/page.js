/**
 * main page object containing all methods, selectors and functionality
 * that is shared across all page objects
 */
import allureReporter from '@wdio/allure-reporter';
import * as chai from "chai";


const assert = chai.assert;


let childIndex, columnIndex;
module.exports = class Page {
    /**
     * Opens a sub page of the page
     * @param path path of the sub page (e.g. /path/to/page.html)
     */
    async moduleLink() {
        return await $('#page > app-breadcrumb > div > ol > li:nth-child(3) >a')
    }

    async open(path) {
        await browser.pause(2000)
        return await browser.url(await browser.config.baseUrl + path);

    }

    async clickOn(element, timeout = 15000) {
        try {
            await element.scrollIntoView();
            await element.waitForClickable({ timeout });
            await element.click();
        } catch (error) {
            console.error(`❌ Failed to click on element: ${await element.selector || '[Unknown Selector]'}`);
            console.error(error.message);
            throw new Error(`${element.selector} was not clickable within ${timeout}.`);
        }
    }

    async clickOn2(element) {
        if (await element.isDisplayed()) {
            await element.waitForDisplayed({timeout: 2000})
            await element.click();
        } else {
            console.error('Element is not clickable here');
        }
    }

    async elementIsClickable(element){
        let flag = false;
        if(await element.waitForDisplayed({timeout: 6000,timeoutMsg: "Element is not displayed in given time"}) && await element.waitForClickable({timeout: 6000, timeoutMsg: "Element is not clickable in given time"})) {
            flag = true;
        }
        return flag;
    }

   async setValue(element, value) {
       await element.waitForDisplayed({timeout: 3000})
       await element.setValue(value)
    }

    async GetText(element) {
        let elExist = await element.waitForExist({timeout: 5900,timeoutMsg: "Error: element did not exist in given time"})
        if(elExist){
            return (await element.getText())
        }
        await console.error('Error: Element did not exist or appear');
    }

    async doIsDisplay(element) {
       await element.waitForDisplayed()
        return element.isDisplayed()
    }

    async goTo(tab) {
        let expectedLinkText = await tab;
        switch (tab) {
            case 'POGs':
                expectedLinkText = 'Planograms';
                break;
            case 'Item by POG':
                expectedLinkText = 'Item By POG';
                break;
        }
        let module = await $('=' + tab);
        await module.scrollIntoView()
        await module.waitForClickable({timeout: 15000})
        await module.click();
        let link =  this.moduleLink;
        if(await link.waitForExist({timeout: 10000,timeoutMsg: "Error: Link did not appear before timeout"})){
            let actualLinkText = await this.GetText(link);
            expect(await actualLinkText).toEqual(await expectedLinkText);
        }
    }

    async validateTab(tab) {
        let module = await $('=' + tab);
        return module;
    }

    async filterUIData(uiData, arrayValues) {
        let values = arrayValues;
        let filteredUiData = await uiData.filter(item => !values.includes(item))
        return filteredUiData
    }


    async validateNewTab(parentGUID) {
        await browser.waitUntil(async () => await (await browser.getWindowHandles()).length >= 2,
            {timeout:15000,timeoutErrorMessage: 'New window did not open within 9 second',})
        let allGUIDs = await browser.getWindowHandles();
        for (let index = allGUIDs.length - 1; index >= 0; index--) {
            let windowHandle = await allGUIDs[index]
            if (await windowHandle !== await parentGUID) {
                await browser.switchToWindow(await allGUIDs[index])
                break;
            }
        }
    }


    async stringGenerator(prefix) {
        return await prefix + Math.random().toString(15).replace(/[^a-zA-Z0-9]+/g, '').substring(0, 10)

    }

    async getUniqueArrayValue(array) {
        let unique = await array.filter((v, i, a) => a.indexOf(v) === i).filter
        ((val) => {
            if (val)
                return (val !== undefined && val !== null && val !== ",")
        })
        return await unique.sort()
    }

    async getUniqueUnsortedArrayValue(array) {
        return  await array.filter((v, i, a) => a.indexOf(v) === i).filter
        ((val) => {
            if (val)
                return (val !== undefined && val !== null && val !== ",")
        })

    }

    async checkDuplicate(arr) {
        let map = {};
        let result = false;
        let position = []
        for (let index = 0; index < arr.length; index++) {
            if (await map[arr[index]]) {
                result = true
                position.push(index)
                allureReporter.addArgument("Result set has duplicate elements at row " + index, arr[index].filter(function (val) {
                    if (val) return val
                }).join(", "))
            }
            map[arr[index]] = true;
        }
        if (result) {
            console.log(`Result set has duplicate elements at rows: `, position);
        } else {
            console.log('Result set does not have duplicate elements');
        }
        return result
    }

    async waitForPageLoad(element, value) {
        let el = await element;
        await el.waitForDisplayed({ timeout: 69000 });
        const actualText = await el.getText();
        if (await actualText !== value) {
            throw new Error(`Expected text to be "${value}", but got "${actualText}"`);
        }
    }

    /*every item (x) is greater than or equal to the item before it (arr[i - 1]) or has no item before it (i === 0)*/
    async isAscending(arr) {
        return await arr.every(function (x, i) {
            return i === 0 || x >= arr[i - 1];
        });
    }

    async isDescending(arr) {
        return (await Promise.all(arr.map((x, i) => {
            if (i === 0) return true;
            return x <= arr[i - 1];
        }))).every(Boolean);
    }

    // looping substring(0,3) from an array
    async extractingDigitFromArray(array, x, y) {
        let newArray = [];
        for (let index = 0; index <= await array.length - 1; index++) {
            newArray.push( (array[index].substring(x, y)).trim())
        }
        return newArray;
    }

    // if array has any empty value, then returns false; no empty value = true
    async arrayHasNoEmptyValues(array) {
        let flag = false;
        for (let index = 0; index < array.length; index++) {
            if (await array[index] !== '') {
                flag = true;
            } else {
                await console.error('Error: This array has empty value')
                flag = false;
            }
        }
        return flag
    }

    async getArrayElementsText(array) {
        let arrayList = [];
        for (let rowNumber = 0; rowNumber < await array.length; rowNumber++) {
            let text = await array[rowNumber].getText();
            arrayList.push(await text)
        }
        return arrayList
    }

    async replaceArrayElement(array, toBeReplaceValue, toReplaceValue) {
        const newArr = await array.map(element => {
            if (element === toBeReplaceValue) {
                return toReplaceValue;
            }
            return element;
        });
        return newArr
    }

    // specially for empty string
    async removeValueFromArray(array, value) {
        let result = await array.filter(function (el) {
            return el !== value;
        });
        return result;
    }

    // return true when array has expected element
    async arrayHasExpectedValue(arrayList, value) {
        for (let index = 0; index < await arrayList.length; index++) {
            if (arrayList[index] === value) {
                return value
            }
        }
    }


    async extractArrayFromArrays(array, index) {
        // Check if the input is not an array, return an empty array
        if (!Array.isArray(array)) return [];
        // Map through the array, preserving null and undefined elements
        return array.map(element => {
            if (element === null || element === undefined) {
                // Return null or undefined elements as-is
                return element;
            }
            // Return the value at the specified index, or undefined if index doesn't exist
            return element[index];
        });
    }

    async removeElementFromArray(array, value) {
        const index = await array.indexOf(value);
        if (index > -1) {
            array.splice(index, 1)
        }
        return array
    }

    async removeEmptyStringFromArray(array) {
        const result = [];
        await array.forEach(el => {
            if ( el !== '') {
                result.push(el)
            }
        })
        return result;
    }

    async validateDescendingOrder(array) {
        return await array.every(function (x, i) {
            return i === 0 || x <= array[i - 1];
        });
    }

    async validateAscendingOrder(array) {
        return await array.every(function (x, i) {
            return i === 0 || x >= array[i - 1];
        });
    }

    async removeElementsFromArrayOfArrays(array, firstValue, secondValue) {
        let newArray = [];
        for (let index = 0; index < await array.length; index++) {
            let result = array[index].filter(function (item) {
                return item !== firstValue && item !== secondValue;
            });
            newArray.push(result)
        }
        return newArray;
    }

    async concatArrayElements(array) {
        let firstSubString = array[1].substring(0, 1);
        return (await firstSubString + ' - ').concat(array[0]);
    }

    async waitBrowserToLoadPage(duration) {
        await browser.pause(duration)
    }

    async inputBoxIsNotEmpty(inputLocator) {
        if (await inputLocator.value().length !== 0) {
            return true
        }
        return true;
    }

    async inputBoxIsEmpty(inputLocator, attribute) {
        if (await inputLocator.getAttribute(attribute) === null) {
            return true
        }
        return true;
    }

    async iterateDropdownListAndClickValue(array, value) {
        let dropdown = await array;
        for (let rowNumber = 0; rowNumber < await dropdown.length; rowNumber++) {
            let dropdownList = await dropdown[rowNumber];
            if (await dropdown[rowNumber].getText() === value) {
                await dropdown[rowNumber].scrollIntoView();
                await dropdownList.click();
                break;
            }
        }
    }

    async randomStringGenerator(length) {
        return await Math.random().toString(36).substr(2, length);
    }

// returns true if value is not null, including 'null'; otherwise returns false;
    async isNotNull(value) {
        return await value !== null;
    }

    async extractDigits(string) {
        let digit = await string.match(/\d/g).join("");
        return digit;
    }

    async extractDigitsByIndex(string, index) {
        let digit = await string.match(/(\d[\d]*)/g)[index];
        return digit;
    }

    async splittingDigits(array, num) {
        let newArray = [];
        for (let index = 0; index < await array.length - 1; index++) {
            newArray.push(array[index].split("-")[num])
        }
        return newArray;
    }

    async dateDescendingOrder(array) {
        let descendingArray = await array.sort((a, b) => new Date(b).getTime() - new Date(a).getTime());
        return descendingArray;
    }

    async getColumnArrayByIndex(array, columnIndex) {
        let columnList = [];
        for (let index = 0; index < array.length; index++) {
            columnList.push(await array[index][columnIndex])
        }
        return columnList;
    }

    async getTextOfElement(element) {
        let elem = await (element);
        await elem.waitForExist({timeout: 15000, timeoutMsg:`Error: ${element.selector} did not appear before time out`});
        return (await elem.getText());
    }

    // removes extra space in front of each array element
    async removeExtraSpaceFromArrayElement(array) {
        let expectedResult = [];
        for (let index = 0; index <= array.length; index++) {
            if (await typeof array[index] === 'string' && await array[index] !== '') {
                let final = await array[index].trimStart()
                expectedResult.push(final)
            }
        }
        return expectedResult;
    }

    async waitForTableExist(tableLocatorName) {
        let table = await tableLocatorName;
        await table.waitForExist({timeout: 30000, timeoutMsg:'Error: did not appear before time out'})
        await expect(table).toExist();
    }

    async findValueFromArray(array, value) {
        return await array.find(element => element === value);
    }

    async sortByArrayElementLength(array) {
        return await array.sort((a, b) => (a.length - b.length))
    }

    async isExist(elementName, selector){
        try{
            //eslint-disable-next-line quote-props
            const elm = await $(selector);
            const element = await elm.isExisting();
            if(element === true){
                return true;
            }else{
                console.log(elementName + ": " + selector);
                await assert.fail(0, 1, `${elementName}:: element not present :: ${selector}`)
            }
        } catch (e){
            browser.takeScreenshot();
            assert.fail(0,1,`${elementName}:: element not present :: ${selector}`)
        }
    }

    async isDisplayed(elementName, selector){
        try{
            //eslint-disable-next-line quote-props
            const elm = await $(selector);
            await elm.waitForExist({timeout: 2000, timeoutMsg:'Error: value did not appear before timeout'})
            const element = await elm.isDisplayed();
            if(element === true){
                return true;
            }else{
                console.log(elementName + ": " + selector);
                await assert.fail(0, 1, `${elementName}:: element not present :: ${selector}`)
            }
        } catch (e){
            browser.takeScreenshot();
            assert.fail(0,1,`${elementName}:: element not present :: ${selector}`)
        }
    }

    async removeStartsWithElements(array, removedItem){
        let final;
        final = await array.filter(item => !item.startsWith(removedItem));
        return final;
    }

    async sortArrayOfArrays(arr){
        return  await arr.sort(([a, b], [c, d]) => c - a || b - d);
    }

    async isElementExist(element, array){
        return await array.includes(element);
    }

    async findIndexNumFromArray(tableHeaders, header){
        let arrayList = await this.getArrayElementsText(tableHeaders);
        let headerIndex =  arrayList.indexOf(header);
        return headerIndex;
    }

    async returnNewArrayBySplitArray(arrayOne, arrayTwo, indexNum, splitBy, num){
        let newArray = [];
        for (let index = 0; index <  arrayOne.length; index++) {
            newArray.push(await arrayTwo[indexNum].split(splitBy)[num].trim());
        }
        return newArray;
    }

    async getNumberArrayElements(array, arrayIndexNumber) {
        let newArray = [];
        for (let index = 0; index < await arrayIndexNumber; index++) {
            let extractedElement = await array[index]
            await browser.pause(2000)
            newArray.push(await extractedElement);
        }
        return newArray;
    }

    async getCurrentPageRowLength(tableName, rowList){
        await this.waitForTableExist(tableName)
        return await rowList.length;
    }

    async extractTextValue(textString, pattern) {
        const regex = new RegExp(pattern);
        const match = await textString.match(await regex);
        if (match) {
            const extractedText = match[0];
            return extractedText;
        } else {
            return "No match found.";
        }
    }

    async sumArrayElements(arr, endIndex){
        let total = 0;
        for(let index = 0; index<arr.length - (arr.length - endIndex); index++){
            if(await typeof parseInt(arr[index]) === 'number'){
                total += parseInt(arr[index]);
            }
        }
        return total;
    }

    async removeWhiteSpaceFromArrayOfArrays(twoDArray){
        return await twoDArray.map(subArray => {
            return subArray.map(el => {
                if (el !== null) {
                    return el.replace(/\s+$/, '');
                }
                return el
            })
        })
    }

    async strToIntArrayOfArrays(arrayOfArrayData){
        for(let tableIndex = 0; tableIndex < arrayOfArrayData.length; tableIndex++) {
            for (let tableInnerIndex = 0; tableInnerIndex < arrayOfArrayData[tableIndex].length; tableInnerIndex++) {
                if(/^(0|[1-9][0-9]*)$/.test(arrayOfArrayData[tableIndex][tableInnerIndex])) {
                    arrayOfArrayData[tableIndex][tableInnerIndex] = Number(arrayOfArrayData[tableIndex][tableInnerIndex]);
                }
            }
        }
        return await arrayOfArrayData;
    }

    async blankToNull(arrayOfArrayData){
        return await arrayOfArrayData.map(arr =>arr.map(val => val === null ? 'null': val));
    }

    async blankToNullStrToIntArrayOfArrays(arrayOfArrayData){
        return await this.blankToNull(await this.strToIntArrayOfArrays(arrayOfArrayData));
    }

    async processJSAlert(alertMessageCheck){
        let result = false;
        let alertStatusOpen = await browser.isAlertOpen();
        let alertText = await browser.getAlertText();
        await browser.acceptAlert();
        let alertStatusClose = await browser.isAlertOpen();
        //need to keep only one of those: alertStatusOpen && !alertStatusClose
        //why alertMessageCheck should be equal to alertStatusOpen
        if( alertMessageCheck === alertText && alertStatusOpen && !alertStatusClose){
            result = true;
        }
        return result;
    }

    userNamePasswordGenerator(stringLength) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        result += chars.charAt(Math.floor(Math.random() * chars.length));
        result += chars.charAt(Math.floor(Math.random() * chars.length));
        result += chars.charAt(Math.floor(Math.random() * chars.length));
        for (let index = 0; index < stringLength; index++) {
            result += chars[Math.floor(Math.random() * chars.length)];
        }
        return result.substring(0, stringLength);
    }

    // Function to remove suffix from a string
    removeSuffix(str, suffix) {
       const regex = new RegExp(suffix.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '$');
       return str.replace(regex, '').trim();
}

    // will be used in upcoming test modification towards non-data dependant tests
    async getHeaderArrayListText(headerList){
        await super.waitBrowserToLoadPage(2000)
        return await this.getArrayElementsText(headerList);
    }

    async addArrayElementsByIndex(arr,startIndex, endIndex){
        let firstNum = parseInt(await arr[startIndex]);
        let secondNum = parseInt(await arr[endIndex]);
        return (firstNum + secondNum);
    }

    async removeExtraSpaces(array) {
        let cleanedArray = [];
        await array.forEach(function(subArray) {
            let cleanedSubArray = [];
            subArray.forEach(function (string){
                if(typeof string !== 'string'){
                    cleanedSubArray.push(string);
                    return;
                }
                if(string.trim() !== ''){
                    cleanedSubArray.push( string.replace(/\s+/g, ' ').trim() )
                }
            });
            cleanedArray.push(cleanedSubArray)
        })
        return cleanedArray
    }

    async getCellValueWithColumnIndex(tableHeaders, columnName, columnLocator){
        columnIndex = await this.findIndexNumFromArray(tableHeaders, columnName)
        let cellText;
        childIndex = columnIndex + 1;
        cellText = await (await columnLocator.getText());
        return cellText;
    }

    async validateButtonsExist(buttonListLocator){
        let buttonList = await buttonListLocator;
        for (let index = 0; index < buttonList.length; index++) {
            const button = buttonList[index].getText();
            if (button === null) {
                return `Button with index ${index} does not exist`;
            }
        }
        return true;
    }

    async tableIsNotEmpty(tableLocator) {
        let flag;
         let table = await tableLocator;
         let tableDisplayed = await table.isDisplayed()
        if(await tableDisplayed){
            flag = true
        }else{
            console.error("Table does not have data")
            flag = false
        }
        return flag;
    }


    async allArrayElementsMatchValue(array, value){
        if(array.length === 0){
            return false;
        }
        let newArray = await array.filter(arrayElement => arrayElement === value);
        return array.length === newArray.length;
    }
  
    async extractArrayValuesAsString(arr, index) {
        return await arr
            .filter(item => item !== "Select All")
            .map(item => item.match(/\d+/g)[index])
            .join("','");
    }

    async extractArrayValuesAsArray(arr, index) {
        const filteredArr = await arr.filter(item => item !== "Select All");
        const values = await filteredArr.map(item => item.split(' - ')[index]);
        return values;
    }

    async removeEmptyStringsFromNestedArray(nestedArray) {
        return await Promise.all(nestedArray.map(async (arr) => {
            return (await Promise.all(arr.map((el) => {
                return el !== '' ? el : undefined;
            }))).filter((el) => el !== undefined);
        }));
    }

    async validateSingleButtonExist(buttonListLocator, buttonName) {
        let buttonList = await buttonListLocator;
        for (let index = 0; index < await buttonList.length; index++) {
            const button = await buttonList[index].getText();
            if (await button === buttonName) {
                return true
            }
        }
        return false;
    }

    async sortNestedArrayByIndex(arr, index) {
        return await arr.sort((a, b) => {
            const strA = a[index].toString();
            const strB = b[index].toString();
            if (strA < strB) {
                return -1;
            } else if (strA > strB) {
                return 1;
            } else {
                return 0;
            }
        });
    }

    async extractSubArraysByIndex(nestedArray,index) {
        return await nestedArray.map(subArray => subArray.slice(0, index));
    }

    async extractDigitsFromArrayAndConcatenateWithSeparator(array, separator) {
        const extractedDigits = await array.map(item => {
            const match = item.match(/(\d+)/);
            return match ? match[0] : '';
        }).filter(digit => digit !== '');
        return await extractedDigits.join(separator);
    }

    async concatDigitsArray(digitsArray) {
        return await digitsArray.join('').replace(/\D/g, '');
    }

    async elementExists(selector) {
        try {
            const element = await selector;
            return await element.isExisting();
        } catch (error) {
            return false;
        }
    }

    async deleteArrayLastElement(arr) {
        if (await arr.length > 0) {
            const lastElement = await arr.pop();
            return lastElement;
        } else {
            return 'The array is empty. No element to delete.';
        }
    }

    async locatorDisabled(locator){
        let flag;
        const element = await locator;
        const elDisabled = await element.getAttribute('class');
        const attributeDisabled = await element.getAttribute('disabled');
        if(await elDisabled.includes('disabled') || await attributeDisabled || await elDisabled.includes('p-disabled') || await elDisabled.includes('p-checkbox-disabled') ){
            flag = true
        }else{
            console.error("Element is not disabled !")
            flag = false
        }
        return flag;
    }

    async arrayTo2DArraySkipFirstColumn(tableData, numOfColumns, numOfRows){

        let outputArray = [];
        for (let colIdx = 1; colIdx < tableData.length - numOfRows;   ) {
            let uiRowData = []
            for (let rowIdx = 1; rowIdx < numOfColumns; rowIdx++, colIdx++) {
                    let data = tableData[colIdx];
                    uiRowData.push(await this.GetText(data));
            }
            colIdx++;
            outputArray.push(uiRowData)
        }
        return outputArray
    }


    async waitForLongSpinner(spinnerElement) {
        await spinnerElement.waitForExist({timeout: 6000, timeoutMsg: 'Error: element did not appear before timeout'});
        let counter = 0;
        let elementExist = await spinnerElement.isExisting();
        while (elementExist && counter <= 60) {
            await this.waitBrowserToLoadPage(1000);
            elementExist = await spinnerElement.isExisting();
            counter++;
        }
        await console.log("seconds waited for element: " + counter);
        await this.waitBrowserToLoadPage(5000);
    }

    async arrayHasNoDuplicates(arr){
        return await arr.length === new Set(arr).size;
    }

    async getEnabledHeaders(locator){
        let enabledHeaders = [];
        const elementList = await locator;
        for(let element of elementList){
            if(!(await element.getAttribute('class')).includes('disabled')){
                 enabledHeaders.push(await element.getText())
            }
        }
        return enabledHeaders;
    }

    async extractArrayValuesByIndex(arr, indexStart, indexEnd){
        if(await indexStart < 0 || indexEnd >= await arr.length || await indexStart > indexEnd){
            throw new Error('Invalid indices provided');
        }
        return await arr.slice(indexStart, indexEnd+1)
    }

    async arrayIncludesArray(arr1, arr2){
        return await arr2.every(value =>  arr1.includes(value));

    }

    async clickCheckBox(tableLocatorName, checkBoxListLocator, checkBoxIndex) {
        await this.waitForTableExist(tableLocatorName);
        let checkBox;
        let checkBoxList = await checkBoxListLocator;
        for (let index = 0; index < checkBoxList.length - checkBoxIndex; index++) {
            checkBox = await checkBoxList[index];
            await checkBox.click();
        }
    }

    async returnNumOfSelectedCheckBox(tableLocatorName, checkBoxListLocator) {
        await this.waitForTableExist(tableLocatorName);
        let checkList = await checkBoxListLocator;
        await this.waitBrowserToLoadPage(1000);
        let count = 0;
        for (let index = 0; index< await checkList.length; index++) {
            if (await ((await checkList[index].getAttribute("class")).includes('p-highlight')) === true)
                count++;
        }
        return count;
    }

    async clickButtonByLabel(locatorList, text) {
        const buttonList = await locatorList;
        for (let index = 0; index < await buttonList.length; index++) {
            const buttonText = await (await buttonList[index]).getText();
            if (await buttonText.trim() === await text) {
                await buttonList[index].click();
                break;
            }
        }
    }

    async extractNumbersAfterPattern(inputString, patternString = ':'){
        const parts = await inputString.split(patternString);
        if(await parts.length>1){
            const afterPattern = await parts[0];
            const numbers = await afterPattern.split('\n').map(line =>line.trim()).filter(line => /^\d+$/.test(line));
            return numbers
        }
        return 'Error: String does not have Reset numbers !'
    }

    async extractValueAfterPattern(inputString, partIndex, patternString = ':'){
        const parts = await inputString.split(patternString);
        if(await parts.length>1){
            return await parts[partIndex]
        }
        return 'Error: inputString does not have values !'
    }

    async clickElementWithScroll(elementLocator){
        const elem = await elementLocator;
        await elem.waitForDisplayed({ timeout: 3000 });
        await elem.scrollIntoView({ inline: "end", behavior: 'smooth' });
        await elem.click();
    }

    async getSelectedOptionText(options){
        for(let option of options){
            if(await option.isSelected()){
                return await option.getText();
            }
        }
        return "";
    }

    async removeElementByText(array, text){
        return await array.filter(element => element !== text);
    }

    async filterArrayByMultipleTexts(array, texts){
        return await array.filter(element => texts.some(text => element.includes(text)))
    }

    async isOrdered(array, order){
        if(order === 'ascending') {
            return await array.every((value, index, array) => {
                return index === 0 || parseInt(array[index - 1]) < parseInt(value);
            });
        }else if(order === 'descending') {
            return await array.every((value, index, array) => {
                return index === 0 || parseInt(array[index - 1]) > parseInt(value);
            });
        }else{
            throw new Error('Invalid order specified !')
        }
    }

    async extractMatchingValuesFromArray(arrayA, arrayB){
        return await arrayA.filter( value => arrayB.includes(value));
    }

    async getRandomArrayValue(array){
        if(await array.length === 0){
            return null;
        }
        const randomIndex = Math.floor(Math.random() * await array.length);
        return await array[randomIndex];
    }

    async returnDifferentElementInFirstArray(array1, array2){
        const set2 = await new Set(array2);
        for(const element of await array1){
            if(!set2.has(element)){
                return element;
            }
        }
        return false;
    }

    async replaceEmptyStringWithNull(array){
        await array.forEach((row) => {
            row.forEach((value, index) =>{
                if(value === ''){
                    row[index] = null;
                }
            })
        })
        return await array;
    }

    async removeDoubleSpaces(array){
        return await array.map(innerArray =>{
            return innerArray.map(element =>{
                if(typeof element === 'string'){
                    return element.replace(/\s{2,}/g, ' ');
                }
                return element;
            })
        })
    }

    async convertToStrings(array){
        return await array.map(innerArray =>
             innerArray.map(element =>
                String(element))
            );
    }

    async convertNullToZero(array){
        return await array.map(innerArray =>
            innerArray.map(element =>
               element === null || element === 'null' ? '0':  String(element))
        );
    }

    async formatArrayForQuery(arr){
        return await arr.map(num => `'${num}'`).join("','");
    }

    async convertNullZeroToEmptyString(array){
        return await array.map(innerArray =>
            innerArray.map(element => {
                if(element === '0.0' || element === 'null' || element === '0') {
                    return '';
                }
                return element;
                })
        )
    }

    async removeSpaces(array) {
        return await array.map(innerArray =>
            innerArray.map(element => {
                if (typeof element === 'string') {
                    return element.replace(/\s+/g, ' ').trim();
                }
                return element;
            })
        )
    }

    async arrayIncludesStringValue(array, stringValue, useFlag = true){
        for(let index=0;index< await array.length;index++){
            if(!array[index].includes(stringValue)){
                if(!useFlag){
                    return false;
                }
            }
        }return true
    }

    async stringIncludesArrayValues(str, arr){
        const stringLines = await str.split('\n').map(line => line.trim());
        return arr.every(value => stringLines.includes(String(value)));
    }

    async sortNumbersInString(numbers) {
        // Step 1: Split the string into an array of numbers
        let numArray = await numbers.split(',').map(num => Number(num));
        // Step 2: Sort the numbers in ascending order
        numArray.sort((a, b) => a - b);
        // Step 3: Join the sorted numbers back into a string
        return numArray.join(',');
    }

    async isValueInArray(array, value) {
        return await array.includes(value);
    }

    async removeZerosFromArrays(array) {
        return array.map(innerArray =>
            innerArray
                .map(value => (value === '0' ? "" : value))
                .filter(value => value !== "")
        );
    }

    async deleteFirstElement(arrayOfArray){
        if(await arrayOfArray.length>0){
            await arrayOfArray.shift();
        }
        return arrayOfArray;
    }
  
    async convertArrayToNewLines(arrayList) {
        return await arrayList.join('\n');
    }

    async getDigitsAfterSecondColon(str) {
        // Regular expression to match digits after each colon
        const regex = /.*?:\s*(\d+)/g;
        // Find all matches using the regex
        const matches = [...str.matchAll(regex)];
        // Check if there are at least two matches
        if (await (matches.length) >= 2) {
            // Return the digits after the second colon (second match group)
            return matches[1][1];
        }
        throw new Error("Not enough colons found in the string.");
    }
    
    async generateRandomNumber(numDigits) {
        let randomNumber = '';
        // Generate random number with the specified number of digits
        for (let index = 0; index < numDigits; index++) {
            randomNumber += Math.floor(Math.random() * 10); 
        }
        return parseInt(randomNumber); 

    }

}
