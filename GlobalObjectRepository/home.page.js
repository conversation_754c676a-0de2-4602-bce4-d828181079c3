const Page = require('./page.js');
let data = require('../TestData/data.json');
class HomePage extends Page {

    /**
     * sub page containing specific selectors and methods for a specific page
     */

    get applicationMenu()           { return $('#menu') }
    get analyticsMenu()             { return $('#headerWrapper > app-header-nav > div > div > ul > li:nth-child(2) > a') }
    get adminMenu()                 { return $('#headerWrapper > app-header-nav > div > div > ul > li:nth-child(3) > a') }
    get applicationMenuList()       { return $$('li.nav-item.dropdown.multi-level-dropdown.open > ul >li>a') }
    get headerDropDownNames()       { return $$('div > .nav.navbar-nav>li>a') }
    get sfpLogo()                   { return $('div>a>img.header-logo-img') }
    get usernameDisplayed()         { return $('span.user-name-display.bg-neutral-600.mt-8') }
    get Admin()                     { return $('li:nth-child(1)+li.dropdown-item.dropdown-submenu.p-0>a +ul>li:nth-child(1)>a')}
    get adminSubmenu()              { return $$('li:nth-child(1)+li.dropdown-item.dropdown-submenu.p-0>a +ul>li>a')}
    get adminSubmenuList()          { return $$("li:nth-child(1)+li.dropdown-item.dropdown-submenu.p-0>a +ul>li:nth-child(1)>a +ul >li>a")}
    get eventManagerSubmenuList()   { return $$('li:nth-child(1)+li.dropdown-item.dropdown-submenu.p-0>a +ul>li>a')}
    get pageLabel()                 { return $("#page > content > div > app-auto-activation-report > div > p")}
    get eventPageLabel()            { return $("#page content app-reset-completion>div>p")}

    async clickDropdown(Locator) {
        let dropdown;
        switch (Locator) {
            case 'Applications':
                dropdown = await this.applicationMenu;
                break;
            case 'Analytics':
                dropdown = await this.analyticsMenu;
                break;
            case 'Administration':
                dropdown = await this.adminMenu;
                break;
        }
         super.clickOn(dropdown);
    }

    async pageText() {
        let text = await this.getTextOfElement(await this.applicationMenu);
        // Assert the selector "Applications"
        await super.waitBrowserToLoadPage(2000)
        await expect(this.applicationMenu).toBeDisplayedInViewport()
        return console.log("Menu Text visible:", await text);
    }

    async applicationMenuListText(Locator,selectValue) {
        await this.clickDropdown(Locator)
        await super.waitBrowserToLoadPage(2000)
        let list = await this.applicationMenuList
        for (let i = 0; i < list.length; i++) {
           if( await list[i].getText() === selectValue){
               await list[i].moveTo();
           }
         }
    }



    async selectAdminSubMenu(option){
        let submenuList = await this.adminSubmenuList
        await expect(submenuList).toBeElementsArrayOfSize(3)
        let flag  = false
        await browser.pause(6000)
        for(let i=0;i<submenuList.length;i++){
            if(await submenuList[i].getText() === option ){
                await submenuList[i].click()
                flag = true    
                break;
            }      
        }
        return flag
    }

    async getTextHeaderNames() {
        const headerNames = await this.headerDropDownNames
        headerNames.filter(async (result) => {
            console.log(await super.GetText(result))
        })
        await expect(headerNames).toHaveText(data.headerdata)
        await expect(headerNames).toBeElementsArrayOfSize(3)
    }

    async getSfpLogo() {
        const SFPLogo = await this.sfpLogo
        await expect(SFPLogo).toBeDisplayedInViewport()
    }

    async dropdownSingleItem(Locator) {
        await this.clickDropdown(Locator)
        await super.waitBrowserToLoadPage(2000)
        let list = await this.applicationMenuList
        return list.length === 1;
    }
}

module.exports = new HomePage();
