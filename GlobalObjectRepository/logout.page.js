const Page = require('./page.js');

/**
 * sub page containing specific selectors and methods for a specific page
 */
class LogOutPage extends Page {
    /**
     * define selectors using getter methods
     */
     
    get btnSignOutArrow2()              { return $('#headerWrapper > app-header-user > div > div > div > span.downArrow > kds-icon-chevron-down > kds-icon > svg') }
    get btnSignOutArrow()              { return $('div[class="logBox"]>div>div>span>kds-icon-chevron-down> kds-icon>svg') }
    get btnSignOut()                   {return $('#headerWrapper > app-header-user > div > div.ers-context-container > ul > li > a')}
    get logOutConfirmationMessage()    {return $('body > div > form > h2')}
    get logOutButton()                 {return $('body > div > form > button')}
    get signedOffMessage()             {return $('#page-ui-container > div > div > div > div.branding-template-form-container>h1')}
    get logOutCancelledConfirmation()  {return $('div[id="info-container"]>p.text-primary')}

    /**
     * a method to encapsule automation code to interact with the page
     * e.g. to SignOut from Application
     */
    async logout() {
        let logOutWarningMessage = 'Are you sure you want to log out?';
            await (await this.btnSignOutArrow).waitForDisplayed({ timeout: 90000 });
            await super.clickOn(await this.btnSignOutArrow)
            await super.waitBrowserToLoadPage(2000)
            await (await this.btnSignOut).waitForDisplayed({ timeout: 90000 });
            await super.waitBrowserToLoadPage(2000)
            await super.clickOn(await this.btnSignOut)
            let logOutText = await this.getTextOfElement(await this.logOutConfirmationMessage);
            if(logOutText ===logOutWarningMessage){
                await super.clickOn(await this.logOutButton);
                await (await this.signedOffMessage).waitForDisplayed({ timeout: 9000 });
                await expect(await this.getTextOfElement(await this.signedOffMessage)).toEqual('Signed Off');
            }else{
                console.error('LogOut button did not appear within given time !')
            }
            await browser.deleteSession();
    }

}

module.exports = new LogOutPage();
