const Page = require('./page.js');
const envVariables = require("../envConfig");

class LoginPage extends Page {

    get passwordValue()     { return $('#password') }
    get btnSubmit()         { return $('#btnSignIn') }
    get btnContinue()        { return $('button[id="btnSignIn"] > div') }
    get agreeBtn()          { return $('#skbutton_form-submit') }
    get signInError()       { return $('#errorMessage > p') }
    get authKrogerClick()   { return $('body > div > div > table > tbody > tr > td > a') }
    get userNameInput()     { return $('#submittedIdentifier') }
    get logInText()         { return $('div > div > div > div > div > h1') }

    async login(userName, userPassword) {
        const userNameInput = await this.userNameInput;
        await super.waitBrowserToLoadPage(3000)
        try {
            const [usernameValue, passwordValue] = await Promise.all([await userName, await userPassword]);
            if (await usernameValue && await passwordValue) {
                await (await this.userNameInput).waitForDisplayed({ timeout: 90000 });
                await userNameInput.setValue(await usernameValue);
                await this.btnSubmit.click();
                await (await this.passwordValue).waitForDisplayed({ timeout: 9000 });
                await this.passwordValue.setValue(await passwordValue);
                await this.btnSubmit.click();
            } else {
                console.error('Username or password are missing');
            }
        } catch (error) {
            console.error('An error occurred during login:', error);
        }
    }

    async loginBasic(username, password) {

        try {
            let loginMessage = await this.logInText;
            await (await loginMessage).waitForExist({ timeout: 9000 });
            await (await this.userNameInput).waitForDisplayed({ timeout: 9000 });
            await this.userNameInput.setValue(username);
            await this.btnSubmit.click();
            await (await this.passwordValue).waitForDisplayed({ timeout: 9000 });
            await this.passwordValue.setValue(password);
            await (await this.btnSubmit).waitForDisplayed({ timeout: 9000 });
            await this.btnSubmit.click();
            console.log('Password submitted and Submit clicked')
            await browser.saveScreenshot('./TestReports/allure-results/TakingScreenShot.png');
            // Check if the agree button is displayed and click it if it is
            const isAgreeBtnDisplayed = await this.agreeBtn.isDisplayed();
            if (isAgreeBtnDisplayed) {
                await this.agreeBtn.click();
            } else {
                console.log("Agree button is not displayed. Proceeding without clicking it.");
            }
        } catch (e){
            await browser.refresh();
            let loginMessage = await this.logInText;
            await (await loginMessage).waitForExist({ timeout: 9000 });
            await (await this.userNameInput).waitForDisplayed({ timeout: 9000 });
            await this.userNameInput.setValue(username);
            await this.btnSubmit.click();
            await (await this.passwordValue).waitForDisplayed({ timeout: 9000 });
            await this.passwordValue.setValue(password);
            await (await this.btnSubmit).waitForDisplayed({ timeout: 9000 });
            await this.btnSubmit.click();
            console.log('Password submitted and Submit clicked - in Catch')
            await browser.saveScreenshot('./TestReports/allure-results/TakingScreenShot.png');
            // Check if the agree button is displayed and click it if it is
            const isAgreeBtnDisplayed = await this.agreeBtn.isDisplayed();
            if (isAgreeBtnDisplayed) {
                await this.agreeBtn.click();
            } else {
                console.log("Agree button is not displayed. Proceeding without clicking it.");
            }
            
        }
    }

    async open() {
        await browser.pause(2000)
        return await super.open('');
    }


}

module.exports = new LoginPage();
