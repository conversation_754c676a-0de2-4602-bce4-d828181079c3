name: Navigator Main

on:
  schedule:
    - cron: "5 3 * * *"
  workflow_dispatch:
    inputs:
      environment:
        description: 'Navigator Main Run - Stage by Default'
        required: true
        default: 'stage'
        type: choice
        options:
          - stage
          - qa1
          - qa2
  push:
    branches: [develop]

env:
  JFROG_URL: https://krogertechnology.jfrog.io/artifactory/api/npm/npm-repo/
  NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

jobs:

  healthcheck-run:
    continue-on-error: false
    runs-on: [self-hosted, kubernetes]
    container:
      image: docker://krogertechnology-docker.jfrog.io/item-location/sfp-apps-test-automation:v117
      credentials:
        username: ${{ secrets.ARTIFACTORY_USER }}
        password: ${{ secrets.ARTIFACTORY_PASSWORD }}

    name: healthcheck-run
    strategy:
      fail-fast: false
      matrix:
        environment: [ "${{ github.event.inputs.environment || 'stage' }}" ]
        script: [ci-healthcheck]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup local env
        id: setup
        uses: actions/setup-node@v4
        with:
          node-version: 18.x

      - name: Install dependency
        run: npm install

      - name: Run HealthCheck
        id: test
        run: |
          env secretUser=${{ secrets.SFP_STAGE_USER }} secretPWD=${{ secrets.SFP_STAGE_PASSWORD }} ENV=${{ matrix.environment }} npm run ci-healthcheck

  navigator-main:
    needs: healthcheck-run
    continue-on-error: true
    runs-on: [self-hosted, kubernetes]
    container:
      image: docker://krogertechnology-docker.jfrog.io/item-location/sfp-apps-test-automation:v117
      credentials:
        username: ${{ secrets.ARTIFACTORY_USER }}
        password: ${{ secrets.ARTIFACTORY_PASSWORD }}

    name: navigator-main
    strategy:
      fail-fast: false
      matrix:
        environment: [ "${{ github.event.inputs.environment || 'stage' }}" ]
        suite: [ci_regression_navigator_main_1, ci_regression_navigator_main_2]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup local env
        id: setup
        uses: actions/setup-node@v4
        with:
          node-version: 18.x

      - name: Install dependency
        run: npm install

      - name: Create required directories
        run: mkdir -p ./screenshots

      - name: Daily regression run
        id: test
        continue-on-error: true
        run: |
          env secretUser=${{ secrets.SFP_STAGE_USER }} secretPWD=${{ secrets.SFP_STAGE_PASSWORD }} ENV=${{ matrix.environment }} npm run ${{ matrix.suite }}
      
      - name: Remove symbolic links from allure-results
        run: find TestReports/allure-results -type l -delete

      - name: Upload Allure Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: allure-results-${{ matrix.suite }}
          path: TestReports/allure-results

  merge-reports:
    needs: navigator-main
    continue-on-error: true
    runs-on: [self-hosted, kubernetes]
    container:
      image: docker://krogertechnology-docker.jfrog.io/item-location/sfp-apps-test-automation:v117
      credentials:
        username: ${{ secrets.ARTIFACTORY_USER }}
        password: ${{ secrets.ARTIFACTORY_PASSWORD }}

    name: Merge Allure Reports
    steps:
      - name: Download Allure result artifacts
        uses: actions/download-artifact@v4
        with:
          path: downloaded-results

      - name: Combine all results into merged directory
        run: |
          mkdir -p merged-results
          for dir in downloaded-results/*; do
            find "$dir" -type f -exec cp {} merged-results/ \;
          done

      - name: Checkout GitHub Pages
        uses: actions/checkout@v4
        with:
          ref: gh-pages
          path: gh-pages

      - name: Generate Allure Report
        uses: simple-elf/allure-report-action@v1.5
        with:
          allure_results: merged-results
          gh_pages: gh-pages
          allure_report: navigatorMain
          allure_history: navigatorMain
          keep_reports: 3

      - name: Remove 'attachments' folders to reduce file count
        run: |
          find navigatorMain -type d -name "attachments" -print -exec rm -rf {} +

      - name: Push merged report to GitHub Pages 🚀
        uses: peaceiris/actions-gh-pages@v3
        with:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          PUBLISH_BRANCH: gh-pages
          PUBLISH_DIR: navigatorMain
          destination_dir: navigatorMain
