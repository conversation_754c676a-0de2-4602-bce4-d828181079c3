name: OneStopShop Regression
on:
  schedule:
    - cron: "5 8 * * *"
  workflow_dispatch:
    inputs:
      kickoff:
        description: 'Automatic kick off - Daily Regression Testing SFPApps OSS'
        default:  'develop'
  push:
    branches: [develop, feature/ERSD-13172]

env:
  JFROG_URL: https://krogertechnology.jfrog.io/artifactory/api/npm/npm-repo/
  NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

jobs:
  healthcheck-run:
    continue-on-error: false
    runs-on: [ self-hosted, linux, kubernetes ]
    container:
      image: docker://krogertechnology-docker.jfrog.io/item-location/sfp-apps-test-automation:v117
      credentials:
        username: ${{ secrets.ARTIFACTORY_USER }}
        password: ${{ secrets.ARTIFACTORY_PASSWORD }}


    name: healthcheck-run
    strategy:
      fail-fast: false
      matrix:
        script: [ ci-healthcheck ]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup local env
        id: setup
        uses: actions/setup-node@v4
        with:
          node-version: 18.x

      - name: Install dependency
        run: |
          npm install

      - name: Run HealthCheck
        id: test
        continue-on-error: false
        run: |
          env secretUser=${{ secrets.SFP_STAGE_USER }} secretPWD=${{ secrets.SFP_STAGE_PASSWORD }} ENV=stage npm run ci-healthcheck

  oss-daily-regression:
    needs:  healthcheck-run
    continue-on-error: true
    runs-on: [ kubernetes, self-hosted, linux ]
    container:
      image: docker://krogertechnology-docker.jfrog.io/item-location/sfp-apps-test-automation:v117
      credentials:
        username: ${{ secrets.ARTIFACTORY_USER }}
        password: ${{ secrets.ARTIFACTORY_PASSWORD }}

    name: oss-daily-regression
    strategy:
      fail-fast: false
      matrix:
        script: [ ci_regression_oss ]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup local env
        id: setup
        uses: actions/setup-node@v4
        with:
          node-version: 18.x

      - name: Install dependency
        run: |
          npm install

      - name: Daily regression run
        id: test
        continue-on-error: false
        run: |
          env secretUser=${{ secrets.SFP_STAGE_USER }} secretPWD=${{ secrets.SFP_STAGE_PASSWORD }} ENV=stage npm run ci_regression_oss

      # - name: Cleanup DB
      #   id: dbcleanup
      #   continue-on-error: true
      #   if: always()
      #   run: |
      #     env secretUser=${{ secrets.SFP_STAGE_USER }} secretPWD=${{ secrets.SFP_STAGE_PASSWORD }} ENV=stage npm run ci_cleanup_db_oss

      - name: Get Allure history
        uses: actions/checkout@v4
        if: always()
        continue-on-error: true
        with:
          ref: gh-pages
          path: gh-pages

      - name: Set Allure Reports
        uses: simple-elf/allure-report-action@master
        if: always()
        id: allure-report
        with:
          allure_results: TestReports/allure-results
          gh_pages: gh-pages
          allure_report:  oss                   #generate report at new destonation folder
          allure_history: oss                  #gather allure history 
          #subfolder: navigator
          keep_reports: 5

      - name: Deploy reports to Github Pages  🚀
        if: always()                                 #${{ steps.allure-report.outcome == 'success' }}
        id: deploy-reports-ghpages
        uses: peaceiris/actions-gh-pages@v3
        with:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          PUBLISH_BRANCH: gh-pages
          PUBLISH_DIR:  oss
          destination_dir: oss
         # keep_files: true

      - name: Get Job URL
        if: always()
        id: get_job_url
        run: echo ::set-output name=url_output::"https://legendary-broccoli-nn719gm.pages.github.io/oss"

      
