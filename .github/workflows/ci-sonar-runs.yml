# This workflow will do a clean install of node dependencies, cache/restore them, build the source code and run tests on headless chrome in a docker container, publish reports on Git pages

name: Run SFPApps Backcheck tests and SonarScan
on:
  workflow_dispatch:
    inputs:
      kickoff:
        description: 'Periodic testing for code stability using SonarScan'
        default:  'develop'
  pull_request:
    branches: [develop]
  push:
    branches:
      - develop
      - master

env:
  JFROG_URL: https://krogertechnology.jfrog.io/artifactory/api/npm/npm-repo/
  NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

jobs:
  sonar-scan:
    runs-on: [kubernetes, self-hosted]
    continue-on-error: true
    steps:
      - uses: actions/setup-java@v4
        with:
          distribution: 'temurin' # See 'Supported distributions' for available options
          java-version: '21'
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: SonarQube Scan
        uses: sonarsource/sonarqube-scan-action@v3.0.0
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

  backcheck-run:
    needs:  sonar-scan
    continue-on-error: false
    runs-on: [kubernetes, self-hosted]
    container:
      image: docker://krogertechnology-docker.jfrog.io/item-location/sfp-apps-test-automation:v117
      credentials:
        username: ${{ secrets.ARTIFACTORY_USER }}
        password: ${{ secrets.ARTIFACTORY_PASSWORD }}

    name: backcheck-run
    strategy:
      fail-fast: false
      matrix:
        script: [ ci-backcheck ]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup local env
        id: setup
        uses: actions/setup-node@v4
        with:
          node-version: 18.x

      - name: Install dependency
        run: |
          npm install

      - name: Run BackCheck
        id: test
        continue-on-error: false
        env:
          secretUser: ${{ secrets.SFP_STAGE_USER }}
          secretPWD: ${{ secrets.SFP_STAGE_PASSWORD }}
          ENV: stage
        run: npm run ci-backcheck
