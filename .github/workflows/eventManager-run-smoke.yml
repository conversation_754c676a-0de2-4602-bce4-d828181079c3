name: Event Manager Stage Smoke
on:
  schedule:
    - cron: "0 11 * * *"
  workflow_dispatch:
    inputs:
      kickoff:
        description: 'Daily Event Manager Smoke Run'
        default:  'develop'
  push:
    branches: [develop]

env:
  JFROG_URL: https://krogertechnology.jfrog.io/artifactory/api/npm/npm-repo/
  NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

jobs:
  healthcheck-run:
    continue-on-error: false
    runs-on: [  self-hosted, kubernetes ]
    container:
      image: docker://krogertechnology-docker.jfrog.io/item-location/sfp-apps-test-automation:v117
      credentials:
        username: ${{ secrets.ARTIFACTORY_USER }}
        password: ${{ secrets.ARTIFACTORY_PASSWORD }}

    name: healthcheck-run
    strategy:
      fail-fast: false
      matrix:
        script: [ ci-healthcheck ]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup local env
        id: setup
        uses: actions/setup-node@v4
        with:
          node-version: 20.x

      - name: Install dependency
        run: |
          npm install
      - name: Run HealthCheck
        id: test
        continue-on-error: false
        run: |
          env secretUser=${{ secrets.SFP_STAGE_USER }} secretPWD=${{ secrets.SFP_STAGE_PASSWORD }} ENV=stage npm run ci-healthcheck

  eventmanager-daily-smoke-suite:
    needs:  healthcheck-run
    continue-on-error: true
    runs-on: [ kubernetes, self-hosted ]
    container:
      image: docker://krogertechnology-docker.jfrog.io/item-location/sfp-apps-test-automation:v117
      credentials:
        username: ${{ secrets.ARTIFACTORY_USER }}
        password: ${{ secrets.ARTIFACTORY_PASSWORD }}

    name: eventmanager-daily-smoke-suite
    strategy:
      fail-fast: false
      matrix:
        script: [ ci_em_smoke ]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup local env
        id: setup
        uses: actions/setup-node@v4
        with:
          node-version: 20.x

      - name: Install dependency
        run: |
          npm install

      - name: create required directories
        run: | 
           mkdir -p ./screenshots

      - name: EM Daily Smoke Run
        id: test
        continue-on-error: false
        run: |
          env secretUser=${{ secrets.SFP_STAGE_USER }} secretPWD=${{ secrets.SFP_STAGE_PASSWORD }} ENV=stage npm run ci_em_smoke

      - name: Get Allure history
        uses: actions/checkout@v4
        if: always()
        continue-on-error: true
        with:
          ref: gh-pages
          path: gh-pages

      - name: Set Allure Reports
        uses: simple-elf/allure-report-action@master
        if: always()
        id: allure-report
        with:
          allure_results: TestReports/allure-results
          gh_pages: gh-pages
          allure_report:  eventmanagerSmokeResult             #generate report at new destonation folder
          allure_history: eventmanagerSmokeResult                      #gather allure history 
          keep_reports: 4

      - name: Remove unnecessary 'attachments' folders to reduce file count
        run: |
          echo "Searching for and deleting 'attachments' folders in report output..."
          find eventmanagerSmokeResult -type d -name "attachments" -print -exec rm -rf {} +
        
      - name: Deploy reports to Github Pages 🚀
        if: always()                                 #${{ steps.allure-report.outcome == 'success' }}
        id: deploy-reports-ghpages
        uses: peaceiris/actions-gh-pages@v4
        with:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          PUBLISH_BRANCH: gh-pages
          PUBLISH_DIR:  eventmanagerSmokeResult
          destination_dir: eventmanagerSmokeResult
          
      - name: Get Job URL
        if: always()
        id: get_job_url
        run: echo ::set-output name=url_output::"https://legendary-broccoli-nn719gm.pages.github.io/eventmanagerSmokeResult"
