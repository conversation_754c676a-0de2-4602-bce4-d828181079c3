name: Navigator Prod Smoke
on:
  schedule:
    - cron: "5 3 * * *"
  workflow_dispatch:
    inputs:
      kickoff:
        description: 'Daily prod Smoke Testing SFPApps Navigator'
        default:  'develop'
        options:
          - stage
          - prod
  push:
    branches: [develop]
  workflow_call:
    secrets:
      ARTIFACTORY_USER:
        required: true
      ARTIFACTORY_PASSWORD:
        required: true
#      SFP_APPS_TEST_AUTOMATION_TOKEN:
#        required: true
      NPM_TOKEN:
        required: true
      SFP_NAV_PROD_USER:
        required: true
      SFP_NAV_PROD_PASSWORD:
        required: true
env:
  JFROG_URL: https://krogertechnology.jfrog.io/artifactory/api/npm/npm-repo/
  NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

jobs:
  healthcheck-run:
    continue-on-error: false
    runs-on: [ kubernetes, self-hosted ]
    container:
      image: docker://krogertechnology-docker.jfrog.io/item-location/sfp-apps-test-automation:v117
      credentials:
        username: ${{ secrets.ARTIFACTORY_USER }}
        password: ${{ secrets.ARTIFACTORY_PASSWORD }}
    name: healthcheck-run
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          repository: krogertechnology/sfp-apps-test-automation
          ref: 'develop'
          #token: ${{secrets.SFP_APPS_TEST_AUTOMATION_TOKEN}}

      - name: Setup local env
        id: setup
        uses: actions/setup-node@v4
        with:
          node-version: 20.x

      - name: Install dependency
        run: |
          npm install
      - name: Run HealthCheck
        id: test
        continue-on-error: false
        run: |
          env secretUser=${{ secrets.SFP_NAV_PROD_USER }} secretPWD=${{ secrets.SFP_NAV_PROD_PASSWORD }} ENV=prod npm run ci-healthcheck

  navigator-daily-prod-smoke:
    needs:  healthcheck-run
    continue-on-error: true
    runs-on: [ self-hosted, kubernetes ]
    container:
      image: docker://krogertechnology-docker.jfrog.io/item-location/sfp-apps-test-automation:v117
      credentials:
        username: ${{ secrets.ARTIFACTORY_USER }}
        password: ${{ secrets.ARTIFACTORY_PASSWORD }}
    name: navigator-daily-prod-smoke
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          repository: krogertechnology/sfp-apps-test-automation
          ref: 'develop'
          #token: ${{secrets.SFP_APPS_TEST_AUTOMATION_TOKEN}}

      - name: Setup local env
        id: setup
        uses: actions/setup-node@v4
        with:
          node-version: 20.x

      - name: Install dependency
        run: |
          npm install

      - name: Daily prod smoke run
        id: test
        continue-on-error: true
        run: |
          env secretUser=${{ secrets.SFP_NAV_PROD_USER }} secretPWD=${{ secrets.SFP_NAV_PROD_PASSWORD }} ENV=prod npm run ci_prod_smoke

      - name: Get Allure history
        uses: actions/checkout@v4
        if: always()
        continue-on-error: true
        with:
          ref: gh-pages
          path: gh-pages

      - name: Set Allure Reports
        uses: simple-elf/allure-report-action@master
        if: always()
        id: allure-report
        with:
          allure_results: TestReports/allure-results
          gh_pages: gh-pages
          allure_report:  navigatorProdSmoke             #generate report at new destination folder
          allure_history: navigatorProdSmoke                      #gather allure history
          keep_reports: 4
          
      - name: Remove unnecessary 'attachments' folders to reduce file count
        run: |
          echo "Searching for and deleting 'attachments' folders in report output..."
          find navigatorProdSmoke -type d -name "attachments" -print -exec rm -rf {} +

      - name: Deploy reports to Github Pages 🚀
        if: always()                                 #${{ steps.allure-report.outcome == 'success' }}
        id: deploy-reports-ghpages
        uses: peaceiris/actions-gh-pages@v3.9.3
        with:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          PUBLISH_BRANCH: gh-pages
          PUBLISH_DIR:  navigatorProdSmoke
          destination_dir: navigatorProdSmoke


      - name: Get Job URL
        if: always()
        id: get_job_url
        run: echo ::set-output name=url_output::"https://legendary-broccoli-nn719gm.pages.github.io/navigatorProdSmoke"
