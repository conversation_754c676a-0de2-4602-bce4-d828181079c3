module.exports = {
    "url": {
        "dev": 'https://merappdev.cfcdcinternaltest.kroger.com/',
        "qa": 'https://sfp-apps-qa2-stage.itmloc.rch-cdc-cxnonprod.kroger.com/home',
        "stage": 'https://sfpappsstage.kroger.com/',
        "test": 'https://sfpappstest.kroger.com/',
        "prod": 'https://sfpapps.kroger.com/',
        "nav-stage": 'https://sfp-apps-ui-stage.itmloc.rch-cdc-cxnonprod.kroger.com/',
        "oss-stage": 'https://sfp-apps-ui-stage.itmloc.rch-cdc-cxnonprod.kroger.com/',
        "nav-stage-hdc": 'https://sfp-apps-ui-stage.itmloc.rch-hdc-cxnonprod.kroger.com/',
        "oss-stage-hdc": 'https://sfp-apps-ui-stage.itmloc.rch-hdc-cxnonprod.kroger.com/',
        "qa1": 'https://sfpapps-ui-qa1.itmloc.rch-cdc-cxnonprod.kroger.com/',
        "qa2": 'https://sfpapps-ui-qa2.itmloc.rch-cdc-cxnonprod.kroger.com/',
    },

    "healthURL":"/manage/health",
    "user": {
        "dev": 'svc5940WebAppD',
        "qa": 'svc5940WebAppS',
        "stage": 'svc5940WebAppS',
        "test": 'svc5940WebAppS',
        "prod": 'SFPN060P',
        "nav-stage": 'svc5940WebAppS',
        "oss-stage": 'svc5940WebAppS',
        "nav-stage-hdc": 'svc5940WebAppS',
        "oss-stage-hdc": 'svc5940WebAppS',
        "qa1": 'svc5940WebAppS',
        "qa2": 'svc5940WebAppS',
    },

    "password": {
        "dev": '',
        "qa": '',
        "stage": '',
        "test": '',
        "prod": '',
        "nav-stage": '',
        "oss-stage": '',
        "nav-stage-hdc": '',
        "oss-stage-hdc": '',
        "qa1": '',
        "qa2": '',
    },

    "divisionalUser": {
        "dev": '',
        "qa": '',
        "stage": 'SFPN021',
        "test":  'SFPN021',
        "prod": ''
    },

    "divisionalPassword": {
        "dev": '',
        "qa": '',
        "stage": 'tJI7DpsAGlhWkF8J55JE',
        "test":  '',
        "prod": ''
    },
    "multiDivisionalUser": {
        "dev": '',
        "qa": '',
        "stage": 'SFPN022',
        "test":  '',
        "prod": ''
    },

    "multiDivisionalPassword": {
        "dev": '',
        "qa": '',
        "stage": 'vSXzcjPfqPztwnRCXF',
        "test":  '',
        "prod": ''
    },

    "storeUser": {
        "dev": '',
        "qa": '',
        "stage": 'SFPN031',
        "test":  '',
        "prod": ''
    },

    "storePassword": {
        "dev": '',
        "qa": '',
        "stage": 'e1QqdzQ21RDo3iUOtoxhAFqtT',
        "test": '',
        "prod": ''
    },
    "corpUser": {
        "dev": '',
        "qa": '',
        "stage": '',
        "test":  '',
        "prod": ''
    },

    "corpPassword": {
        "dev": '',
        "qa": '',
        "stage": 'nu7K7rDs3N7znWY3FY32Q60',
        "test": '',
        "prod": ''
    },

    "ILSAPIUser": {
        "dev" : "",
        "qa" : "",
        "stage": "svc5940ItemLocS",
        "test": "",
        "prod": ""
    },

    "ILSAPIPassword" : {
        "dev" : "",
        "qa" : "",
        "stage": "Rh20iB5xwfxs1JJsbfEVeVgQ0wZ",
        "test": "",
        "prod": ""
    },

    "resetCompletionUser": {
        "dev" : "",
        "qa" : "",
        "stage": "svc8378areapis",
        "prod": ""
    },

    "resetCompletionPassword" : {
        "dev" : "",
        "qa" : "",
        "stage": "hj0RqG8vo3LHD1AKMavChI",
        "prod": ""
    },

    "DBNames":{
        "SFP_STAGE": "SFP-Stage",
        "SFP_TEST" : "SFP-Test",
        "CKB_STAGE": "CKB-Stage",
        "ILP_STAGE": "ILP-Stage",
        "NAV_STAGE": "NAV-Stage",
        "OSS_STAGE": "OSS-Stage",
        "NAV_STAGE-HDC": "NAV-Stage-HDC",
        "OSS_STAGE-HDC": "OSS-Stage-HDC",
    },

};
