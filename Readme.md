**Pre-requisites:**
1. Node
2. Java
3. NPM Installer

Using brew installer, install above base dependencies to begin setup of this project.

**Installation:**

1. Clone the project on your local machine by running this command on your local 

        ` git clone https://github.com/krogertechnology/sfp-apps-test-automation.git`

2. Once cloning is complete, go to projects root directory through terminal. To install all npm packages and to update your node_modules , run this command

          `npm install`

**Run Tests:**

Run All Tests:
To run all tests on the project, go to root directory from terminal.
Open data.json and ensure the values for 'username' & 'password' are filled in to access the SFP apps from UI
On terminal at root of the project run command 
        `ENV=stage npm run test`

To run Navigator tests in headless mode
        `ENV=nav-stage MODE=headless npm run test`

To run OSS tests in headless mode
       `ENV=oss-stage MODE=headless npm run test`

To run tests in headless mode 
       ``ENV=stage MODE=headless npm run test``

This command will run the test scripts on https://merappstage.cfcdcinternaltest.kroger.com/login

**Run Regression Test Suite in STAGE environment:**
To execute tests under regression suite use command
        `ENV=stage npm run regression`
To execute regression tests in headless mode
        `ENV=stage MODE=headless npm run regression`
To execute Navigator tests under regression suite use command
         `ENV=nav-stage npm run regression`
To execute OSS tests under regression suite use command
         `ENV=oss-stage npm run regression`

**Run Regression Test Suite in TEST environment:**
To execute tests under regression suite use command
         `ENV=test npm run regression`
To execute regression tests in headless mode
          `ENV=test MODE=headless npm run regression`

**Run One Test:**

To run a single test, you can add a 'only' filter on testcase level in your testScript file such as   
       
       `it.only('testcase to be run')` 
        
and then run the regular command 
     
     `ENV=stage npm run test`

**Skip One Test:**
To skip a single test, you can add a 'skip' filter on testcase level in your testscript file such as
      
      `it.skip('testcase to be skipped')` 
      
and then run the regular command 
     
     `ENV=stage npm run test`

**ENV variable:**
ENV variable would allow you to pass your test environment from command line. Options are `'qa','dev', 'stage','prod'`,
which will pick the corresponding URL to run the tests

**Test Report:**
The project runs Allure test reporter. To view test results for last test run, run this command on terminal 
        
        npx allure open
**Run In HeadLess Mode:**

        ENV=stage MODE=headless npm run test

**Run Tests in Dev:**

To execute your tests that are under development, add your tests under "Dev" suite on wdio.conf.js, then use command
        `ENV=stage npm run dev`

**Coding Standards Setup**

.eslintrc.json contains current linter rules for JS scripting on this project
```
   npm install eslint
   npm install eslint-plugin-wdio@latest --save-dev   
   npx eslint /path/to/file 
``` 
Activate Eslint checker on your IntelliJ and provide custom path to .eslintrc file

**CI TEST REPORTS**

   Home Page: https://legendary-broccoli-nn719gm.pages.github.io/homepage.html 

   Stage-Navigator:
1. Navigator Stage Main Regression: https://legendary-broccoli-nn719gm.pages.github.io/navigatorMainRegression/
2. Navigator Stage Minor Regression: https://legendary-broccoli-nn719gm.pages.github.io/navigatorMinorRegression/
3. Store and Divisional (role-based) User regression: https://legendary-broccoli-nn719gm.pages.github.io/navigatorStageRoleBased/

   Stage-One Stop Shop Regression:
4. OneStopShop: https://legendary-broccoli-nn719gm.pages.github.io/oss/

   Prod-Navigator:
5. Navigator Corp User Prod Smoke: https://legendary-broccoli-nn719gm.pages.github.io/navigatorProdSmoke

   Event Manager-stage:
6. Event Manager: https://legendary-broccoli-nn719gm.pages.github.io/eventmanager-results/
7. Event Manager - Split Regression (replacing 6.):
#1: https://legendary-broccoli-nn719gm.pages.github.io/eventmanager-results-ci_regression_EM_1/
#2: https://legendary-broccoli-nn719gm.pages.github.io/eventmanager-results-ci_regression_EM_2/
#3: https://legendary-broccoli-nn719gm.pages.github.io/eventmanager-results-ci_regression_EM_3/

Daily Smoke Run:

8. Navigator Stage Smoke Run Result: https://legendary-broccoli-nn719gm.pages.github.io/navigator_smokeResult/

9. EventManager Stage Smoke Run Result: https://legendary-broccoli-nn719gm.pages.github.io/eventmanagerSmokeResult/

**Running Test in Windows**

The file wdio.config.js gets the fields from System.Properties to set the variables ENV and MODE.  The System.Properties fields and the  variables in wdio.config.js have the same names: ENV, MODE.

The ReadMe.md file shows how to run the tests locally with the command (ENV=stage npm run test).  This command is actually two statements run in succession (“ENV=stage”  and then  “npm run test”).    The “npm run test” is a npm command and works the same in MAC OS and Windows.   “ENV=Stage” sets the System.Properties in MAC OS, but it does not work in Windows.   There are three different ways to get this working for Windows.

    Fix 1 – change commands as you run the test

      MAC:  ENV=Stage npm run test
      Windows:  $env:ENV='stage'; npm run test; $env:ENV=""

    Fix 2 – temporily change variable(s) in local – don’t push these temp changes

      Changing some lines in wdio.config.js
      FROM: const ENV = process.env.ENV
      TO:   const ENV='stage'
      FROM:  const MODE=process.nev.MODE
      TO:  const MODE='some value’      (might not be needed)
      *change back before PR or Merge
      *Once you change the ENV and MODE you can run with the command: npm run test

    Fix 3 – change framework -

      This framework sets up the System.Properites with items that the tests later use to set it’s run configuration.   The framework could be changed to use npm parameters.  This change would make the npm syntax the same for MAC OS and Windows.

      Example:  npm run test -- stage headless
      Example:  npm run test -- stage
      Example:  npm run test -- foo  foofoo  moreFoo

