import { openAllure, generateReport } from '../util/report'
import { testSetup, testTeardown } from '../helpers/testSetupTeardown'
const envVariables = require('../envConfig')
const ENV = process.env.ENV
const MODE = process.env.MODE
const path = require('path')
let chromeArgs = []
const rmdir = require('../util/rmdir')
import { suitesConfig } from './suites'
const os = require('os');
const fs = require('fs')
global.downloadDir = path.join(os.homedir(), 'TempDownloads');


if (!ENV || !['qa', 'dev', 'stage', 'test', 'prod', 'nav-stage', 'oss-stage', 'nav-stage-hdc', 'oss-stage-hdc', 'qa1', 'qa2'].includes(ENV)) {
    console.log('please pass the correct ENV value : ENV=qa|dev|stage|test|prod|nav-stage|oss-stage|nav-stage-hdc|oss-stage-hdc|qa1')
    process.exit()
}
if (MODE === 'headless') {
    chromeArgs.push('headless')
    chromeArgs.push('--disable-gpu')
    chromeArgs.push('--window-size=1920,1080')
}

exports.config = {

    specs: [
        'Tests/**Specs/**.Test.js'                         //specs, path to spec folders

    ],
    //capturing suites config from suits.js file
    suites: suitesConfig,

    // ============
    // Capabilities
    // ============
    maxInstances: 1,
    capabilities: [{
        maxInstances: 1,                                //number of simultaneous browser sessions to be created on test run
        browserName: 'chrome',
        'goog:chromeOptions': {
            args: chromeArgs,
            prefs: {
                'download.default_directory': downloadDir
            }
        }
    }],
    host: 'localhost',
    // ===================
    // Test Configurations                                  // Define all options that are relevant for the WebdriverIO instance here
    // ===================
    logLevel: 'error',                                  // Level of logging verbosity: trace | debug | info | warn | error | silent
    // logLevels: {
    //     //webdriver: 'info',
    //     '@wdio/wdio-rerun-service': 'trace'
    // },
    bail: 0,                                            // If you only want to run your tests until a specific amount of tests have failed use bail (default is 0 - don't bail, run all tests).
    baseUrl: envVariables.url[ENV] || 'https://sfpappsstage.kroger.com/',                    // Set a base URL in order to shorten url command calls.
    waitForTimeout: 20000,                              // Default timeout for all waitFor* commands.
    connectionRetryTimeout: 120000,                     // Default timeout in milliseconds for request, if browser driver or grid doesn't send response
    connectionRetryCount: 3,                            // Default request retries count
    services: [['chromedriver', {                        // Test runner services. Ex: services: ['selenium-standalone']
        path: '/wd/hub',
        args: ['--whitelisted-ips=']
    }]],
    framework: 'mocha',                                 // The following are supported: Mocha, Jasmine, and Cucumber: https://webdriver.io/docs/frameworks
    specFileRetries: 1,                                 // The number of times to retry the entire specfile when it fails as a whole
    specFileRetriesDelay: 0,                          // Delay in seconds between the spec file retry attempts
    specFileRetriesDeferred: true,                      // Whether or not retried specfiles should be retried immediately or deferred to the end of the queue

    reporters: ['spec',                                // Test reporter for stdout.The only one supported by default is 'dot':https://webdriver.io/docs/dot-reporter
        ['allure', {
            outputDir: 'TestReports/allure-results/',
            disableWebdriverStepsReporting: false,
            disableWebdriverScreenshotsReporting: false,
        }]],


    mochaOpts: {                                        // Options to be passed to Mocha.http://mochajs.org/
        ui: 'bdd',
        timeout: 600000,
        require: ['@babel/register'],
    },

    // =====
    // Hooks
    // =====
    /**
     * Gets executed once before all workers get launched.
     * @param {Object} config wdio configuration object
     * @param {Array.<Object>} capabilities list of capabilities details
     */
    onPrepare: function (config, capabilities) {


        // if (!fs.existsSync(downloadDir)){           // make sure download directory exists
        //     fs.mkdirSync(downloadDir);             // if it doesn't exist, create it
        // }
    },
    /**
     * Gets executed before a worker process is spawned and can be used to initialise specific service
     * for that worker as well as modify runtime environments in an async fashion.
     * @param  {String} cid      capability id (e.g 0-0)
     * @param  {[type]} caps     object containing capabilities for session that will be spawn in the worker
     * @param  {[type]} specs    specs to be run in the worker process
     * @param  {[type]} args     object that will be merged with the main configuration once worker is initialised
     * @param  {[type]} execArgv list of string arguments passed to the worker process
     */
    // onWorkerStart: function (cid, caps, specs, args, execArgv) {
    // },
    /**
     * Gets executed just before initialising the webdriver session and test framework. It allows you
     * to manipulate configurations depending on the capability or spec.
     * @param {Object} config wdio configuration object
     * @param {Array.<Object>} capabilities list of capabilities details
     * @param {Array.<String>} specs List of spec file paths that are to be run
     */
    // beforeSession: function (config, capabilities, specs) {
    // },
    /**
     * Gets executed before test execution begins. At this point you can access to all global
     * variables like `browser`. It is the perfect place to define custom commands.
     * @param {Array.<Object>} capabilities list of capabilities details
     * @param {Array.<String>} specs        List of spec file paths that are to be run
     * @param {Object}         browser      instance of created browser/device session
     */
    //all steps in before function will run before every test file's Describe block
    before: async function () {
        if (MODE !== "headless") {
            await browser.maximizeWindow();
        }
    },
    /**
     * Runs before a WebdriverIO command gets executed.
     * @param {String} commandName hook command name
     * @param {Array} args arguments that command would receive
     */
    // beforeCommand: function (commandName, args) {
    // },
    /**
     * Hook that gets executed before the suite starts
     * @param {Object} suite suite details
     */

    beforeSuite: async function () {

        // Login with main test account. Can be overwritten on Spec level
        await testSetup(await envVariables.user[ENV], await envVariables.password[ENV]);
    },
    /**
     * Function to be executed before a test (in Mocha/Jasmine) starts.
     */
    // beforeAll: async function (){
    //     await testSetup(envVariables.user[ENV],envVariables.password[ENV]);

    // },

    beforeTest: async function () {
    },
    /**
     * Hook that gets executed _before_ a hook within the suite starts (e.g. runs before calling
     * beforeEach in Mocha)
     */
    // beforeHook: async function (test, context) {

    // },
    /**
     * Hook that gets executed _after_ a hook within the suite starts (e.g. runs after calling
     * afterEach in Mocha)
     */
    // afterHook: function (test, context, { error, result, duration, passed, retries }) {
    // },
    /**
     * Function to be executed after a test (in Mocha/Jasmine).
     */
    afterTest: async (test, context, { error, result, duration, passed }) => {
        if (error) {
            // Save to both locations for compatibility
            const screenshotDir = path.resolve(__dirname, '../TestReports/screenshots');
            const allureDir = path.resolve(__dirname, '../TestReports/allure-results/attachments');

            try {
                // Ensure directories exist
                if (!fs.existsSync(screenshotDir)) {
                    fs.mkdirSync(screenshotDir, { recursive: true });
                }
                if (!fs.existsSync(allureDir)) {
                    fs.mkdirSync(allureDir, { recursive: true });
                }

                // Create sanitized filename
                const sanitizedTitle = test.title.replace(/[^a-zA-Z0-9-_]/g, '_');
                const timestamp = Date.now();
                const filename = `${sanitizedTitle}_${timestamp}.png`;

                // Save screenshot to both locations
                const screenshotPath = path.join(screenshotDir, filename);
                const allurePath = path.join(allureDir, filename);

                await browser.saveScreenshot(screenshotPath);
                await browser.saveScreenshot(allurePath);

                console.log(`Screenshot saved to: ${screenshotPath} and ${allurePath}`);
            } catch (screenshotError) {
                console.error(`Failed to save screenshot: ${screenshotError.message}`);
            }
        }
    },


/**
 * Hook that gets executed after the suite has ended
 * @param {Object} suite suite details
 */
//each describe block is treated as suite and this code will be executed end of each test file
afterSuite: async function () {
    //  run delete events query after suite mentioned in param object

},
/**
 * Runs after a WebdriverIO command gets executed
 * @param {String} commandName hook command name
 * @param {Array} args arguments that command would receive
 * @param {Number} result 0 - command success, 1 - command error
 * @param {Object} error error object if any
 */
// afterCommand: function (commandName, args, result, error) {
// },
/**
 * Gets executed after all tests are done. You still have access to all global variables from
 * the test.
 * @param {Number} result 0 - test pass, 1 - test fail
 * @param {Array.<Object>} capabilities list of capabilities details
 * @param {Array.<String>} specs List of spec file paths that ran
 */
after: async () => {
    await testTeardown();
},
    /**
     * Gets executed right after terminating the webdriver session.
     * @param {Object} config wdio configuration object
     * @param {Array.<Object>} capabilities list of capabilities details
     * @param {Array.<String>} specs List of spec file paths that ran
     */
    // afterSession: function (config, capabilities, specs,sc {
    // },
    /**
     * Gets executed after all workers got shut down and the process is about to exit. An error
     * thrown in the onComplete hook will result in the test run failing.
     * @param {Object} exitCode 0 - success, 1 - fail
     * @param {Object} config wdio configuration object
     * @param {Array.<Object>} capabilities list of capabilities details
     * @param {<Object>} results object containing test results
     */
    onComplete: async function () {
        await rmdir(downloadDir)
        await generateReport()
        await openAllure()
    },
    /**
     * Gets executed when a refresh happens.
     * @param {String} oldSessionId session ID of the old session
     * @param {String} newSessionId session ID of the new session
     */
    //onReload: function(oldSessionId, newSessionId) {
    //}
}
