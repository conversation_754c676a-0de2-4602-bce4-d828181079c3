const path = require('path');
const fs = require('fs');
const os = require('os');
const _ = require('lodash');
const loginPage = require('../GlobalObjectRepository/login.page.js');
const { generateReport } = require('../util/report');

const secretPWD = process.env.secretPWD;
const secretUser = process.env.secretUser;
//const environment = process.env.ENV || 'stage';
global.downloadDir = path.join(os.homedir(), 'TempDownloads');

const ciChromeArgs =
    ['no-sandbox',
        'headless',
        'disable-gpu',
        'disable-dev-shm-usage',
        'disable-setuid-sandbox',
        'disable-seccomp-filter-sandbox',
        'window-size=1920,1080',
        'disable-web-security',
        'ignore-certificate-errors',
        'ignore-ssl-errors ',
        'ignore-certificate-errors-spki-list',
        'applicationCacheEnabled=false',
        'disable-ipv6',
        'enable-precise-memory-info',
        'disable-extensions',
        'disable-gpu', 'no-first-run',
        'enable-javascript',
        'proxy-bypass-list=egress-proxy,127.0.0.1,localhost,*stage.*,pickrunsutility*',
        'connection_timeout=90000',
        'connectionRetryTimeout: 90000',
        'remote-debugging-port=9222', 'remote-debugging-address=0.0.0.0',
        'disable-popup-blocking', 'safebrowsing-disable-auto-update', 'disable-hang-monitor', 'disable-prompt-on-repost', 'disable-sync', 'disable-translate', 'enable-automation', 'disable-client-side-phishing-detection']

// const environmentUrls = {
//     //stage: 'https://stage.example.com',
//     stage: 'https://sfpappsstage.kroger.com',
//     prod: 'https://prod.example.com',
// };

let overrides = {
   // baseUrl: environmentUrls[environment] || environmentUrls['stage'],
    capabilities: [{
        maxInstances: 10,
        browserName: 'chrome',
        'goog:chromeOptions': {
            args: ciChromeArgs,
            prefs: {
                //'download.default_directory': global.downloadDir,
                'download.default_directory': downloadDir
            }
        }
    }],
    logLevel: 'debug',
    outputDir: './logs',
    onComplete: async function () {
        await generateReport();
    },
    before: async function () {
        if (!secretUser || !secretPWD) {
            throw new Error("Missing environment variables: secretUser or secretPWD");
        }
        try {
            await loginPage.open();
            await loginPage.loginBasic(secretUser, secretPWD);
            const logBox = $('//div[@class="logBox"]');
            await logBox.waitForExist({timeout: 30000});
            await expect(browser).toHaveUrlContaining(this.baseUrl);
        } catch (err) {
            console.error("Login failed:", err.message);
            throw err;
        }
    },
    reporters: [
        ['allure', {
            outputDir: 'TestReports/allure-results/',
            disableWebdriverStepsReporting: false,
            disableWebdriverScreenshotsReporting: false,
        }],
    ],
    afterTest: async function (test, context, { error }) {
        if (error) {
            // Save to both locations for compatibility
            const screenshotDir = './TestReports/screenshots';
            const allureDir = './TestReports/allure-results/attachments';

            try {
                // Ensure directories exist
                if (!fs.existsSync(screenshotDir)) {
                    fs.mkdirSync(screenshotDir, { recursive: true });
                }
                if (!fs.existsSync(allureDir)) {
                    fs.mkdirSync(allureDir, { recursive: true });
                }

                // Create sanitized filename
                const sanitizedTitle = test.title.replace(/[^a-zA-Z0-9-_]/g, '_');
                const timestamp = Date.now();
                const filename = `${sanitizedTitle}_${timestamp}.png`;

                // Save screenshot to both locations
                await browser.saveScreenshot(`${screenshotDir}/${filename}`);
                await browser.saveScreenshot(`${allureDir}/${filename}`);

                console.log(`Screenshot saved to: ${screenshotDir}/${filename} and ${allureDir}/${filename}`);
            } catch (screenshotError) {
                console.error(`Failed to save screenshot: ${screenshotError.message}`);
            }
        }
    },
};

exports.config = _.defaultsDeep(overrides, require('./wdio.conf.js').config);
