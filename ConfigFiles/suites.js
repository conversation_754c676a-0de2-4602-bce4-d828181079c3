//Lists various test suites that are used to run tests on this framework
export let suitesConfig= {

    healthcheck: [
        'Tests/SFPAppsHealthCheck.Test.js'
    ],
    backcheck: [
        'Tests/homePage.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateNavigatorDropDowns.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateCreateSFPEvent.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateActivationSfpEvent.Test.js',
    ],
    nav_dev_backcheck: [
        'Tests/homePage.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateNavigatorDropDowns.Test.js',
    ],
    smoke: [
        'Tests/SFPNavigator/NavigatorSpecs/validateNavigatorTabsActivatedWithPogKey.Test.js',
        'Tests/homePage.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateCompositeTabDisplaysDifferentDivNums.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/ValidateStoreTabExcelToDBDivOnly.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateNavigatorDropDowns.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateMappingReport.Test.js',                              
        'Tests/SFPNavigator/NavigatorSpecs/validateMultipleSearchCriteria.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/verifyClearOptions.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateItemByPOGTabExcelToDB.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateFP.Test.js',
    ],

    //include scripts under development
    dev: [
        '   Tests/SFPEventManager/EventManagerSpecs/validateCommodityRecapExcelDbData.Test.js',
        '   Tests/SFPEventManager/EventManagerSpecs/validateCreateSFPEvent.Test.js',
        '   Tests/SFPEventManager/EventManagerSpecs/validateDefaultSetValueInTagOption.Test.js',
        '   Tests/SFPEventManager/EventManagerSpecs/validateDeleteCancelBtnOnResetCompletion.Test.js',
        '   Tests/SFPEventManager/EventManagerSpecs/validateDeleteSchedulingEvents.Test.js',
        '   Tests/SFPEventManager/EventManagerSpecs/validateEMFiltersDropdowns.Test.js',
        //'   Tests/SFPEventManager/EventManagerSpecs/validateErrorMessageInUPLOrder.Test.js',
        '   Tests/SFPEventManager/EventManagerSpecs/validateFiltersClearDropdowns.Test.js',
        '   Tests/SFPEventManager/EventManagerSpecs/validateFiltersOnEventMaintenanceByEventType.Test.js',
        '   Tests/SFPEventManager/EventManagerSpecs/validateOfficeAndStoreTagInEvent.Test.js',
        '   Tests/SFPEventManager/EventManagerSpecs/validateREFInAppRefresh.Test.js',
        '   Tests/SFPEventManager/EventManagerSpecs/validateSFPActivationEventAlertMessage.Test.js',
        '   Tests/SFPEventManager/EventManagerSpecs/validateSFPEventAlertMessage.Test.js',
        '   Tests/SFPEventManager/EventManagerSpecs/validateSFPEventEnableAutoActivation.Test.js',
        '   Tests/SFPEventManager/EventManagerSpecs/validateSFPEventRefreshFlow.Test.js',
        '   Tests/SFPEventManager/EventManagerSpecs/validateSFPEventShelfStripTypes.Test.js',
        '   Tests/SFPEventManager/EventManagerSpecs/validateSFPRefreshEventAlertMessage.Test.js',
        '   Tests/SFPEventManager/EventManagerSpecs/validateStoreReviewAndCommodityTab.Test.js'

    ],
    //navigator main regression suite; 23 tests
    // eslint-disable-next-line camelcase
    regression_navigator_main_1:[
        'Tests/homePage.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateNavigatorDropDowns.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateAisleDropdownMappingReport.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateComparisonReportExcelData.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateComparisonReportILPData.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateComparisonReportSFPUIData.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateDefaultSortOnPogItemByStore.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateDuplicateRecords.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateFP.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateDuplicateRecordsOnFloorPlansTab.Test.js',
    ],
    regression_navigator_main_2:[ 
        'Tests/SFPNavigator/NavigatorSpecs/validateFPDepartment.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateItemByPOGTabFiltersExcelToDB.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateItemByPOGTabFiltersUI.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateMappingReport.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateMappingReportBySSN.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateMappingReportFilters.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateMappingReportStoreType.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateMergePOGAssortmentTab.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateCompositeTabPDFBatchDownload.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/ValidateStoreTabExcelToDBDivOnly.Test.js',

    ],
    //navigator minor regression suite; 28 tests
    // eslint-disable-next-line camelcase
    regression_navigator_minor_1: [
        'Tests/SFPNavigator/NavigatorSpecs/validatePogItemByStoreExcelData.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validatePOGBySSN.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validatePOGHeaders.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/ValidatePOGTabExcelToDB.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateNavigatorTabsActivatedWithPogKey.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateSingleSearchCriteria.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateSSNLinkFromMappingReportOnItemByPog.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateTabPagination.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validatePogItemByStoreUiDataToDb.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateMerchandisedAssortmentButton.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateStoreCountLinkOnPogTab.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateItemByPogFiltersByComparingUiToDb.Test.js',
    ],
    regression_navigator_minor_2:[
        'Tests/SFPNavigator/NavigatorSpecs/validatePogItemByStoreTableDataWithUpc.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/ValidateMultiplePogsOnPOGsTab.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/ValidatePOGTabStoreCountCheckBox.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateNegativeLoginFlow.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/ValidateStoreTabExcelToDBToUI.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateCompositeTabExcelToDbToUI.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateMappingReportDefaultPageNumber.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateComparisonReportTablePageDropdown.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateFloorPlanEventType.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateFooterStringOnPogItemByStore.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validatePageFooter.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateEditPogsOnPogsTab.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validatePogOpenedOnFloorPlansTab.Test.js',


    ],
    //navigator minor regression suite; 27 tests
    // eslint-disable-next-line camelcase
    regression_navigator_role_based:[

        'Tests/SFPNavigator/NavigatorSpecs/validateMultipleSearchCriteria.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateNavigatorFilterDropdowns.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateNoOfPlanogramsOnFloorPlansTab.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateNoOfStoresMappingReport.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validatePageNumbers.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validatePaginationData.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateMappingReportPdfBatchDownloadAll.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validatePOGMappingReportFlow.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateMappingReportMultiCombinationExcelToDB.Test.js',
        // Role based user validation
        'Tests/SFPNavigator/NavigatorSpecs/UserRoleSpecs/validateClearButtonsForDivisionalUser.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/UserRoleSpecs/validateClearButtonsForStoreUser.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/UserRoleSpecs/validateMappedPogsForDivisionalUser.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/UserRoleSpecs/validateUnmappedPogsForDivisionalUser.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/UserRoleSpecs/validateDisabledPogFilterBtnsDivisionalUser.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/UserRoleSpecs/validateMappedPogse2e2.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/UserRoleSpecs/validateDefaultStoreValueNotClearedForStoreUser.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/UserRoleSpecs/validateCorpUserAccessToNavigator.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/UserRoleSpecs/validateStoreDropdownListForDivisionalUser.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/UserRoleSpecs/validateMultiDivisionalUserDropdown.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/UserRoleSpecs/validateStoreUserInEachTab.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/UserRoleSpecs/validateStoreUserTabHighlight.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/UserRoleSpecs/validateDivisionalUserInEachTab.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/UserRoleSpecs/validateDivisionalUserTabHighlight.Test.js',
        // e2e tests
        'Tests/SFPNavigator/NavigatorSpecs/E2eSpecs/e2eNavigateToComparisonViaPogItemByStore.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/E2eSpecs/e2eValidateNumOfItemsOnCompositeTabClickable.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/E2eSpecs/e2eValidatePOGTabLinkToItemByPogToMapping.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/E2eSpecs/e2eValidateCompositeFlowToMappingExcel.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/E2eSpecs/e2eValidateStoreColumnValuesNotUniqueOnMappingReport.Test.js',


    ],
    //Event manager regression suite; 57 tests
    // eslint-disable-next-line camelcase
    regression_EM_1: [

        'Tests/SFPEventManager/EventManagerSpecs/validateSFPEventRefreshFlow.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateReleaseEventEnabled.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validatePogStatusInSelectingPog.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateSelectingPogCommodities.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateSFPEventAisleRebuild.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateActivationSfpEvent.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateSFPEventAisleDelete.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/ValidateStoreSelection.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateCorporateSfpEvent.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateDeletedEvents.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateREFInAppRefresh.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateEventHeaderTabelForStripAndTag.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateOfficeAndStoreTagInEvent.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateCalenderInEventScheduling.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateSFPEventAlertMessage.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateHeadersINSelectingPogCommodities.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateDefaultSetValueInTagOption.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateSFPCAOEventIDColumn.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateEventRemodel.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validatePaginationOnEmPogPopUP.Test.js',
        
    ],
    regression_EM_2: [
        'Tests/SFPEventManager/EventManagerSpecs/validatePogCountEmPageToDb.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateFiltersOnEventMaintenanceByEventType.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateEditScheduleForActivationEvents.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateEditSchedulesForDivisionalEvents.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateEditStoreForActivationEvents.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateEditStoreForDivisionalEvents.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateCreatedDateAndRevisedDate.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateFloorDBkeyPlanogramTable.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateCorporateSplitDivisionEvent.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateDeleteSchedulingEvents.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateSlideOutPannelActivaionEvent.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateSlideOutPannelNpeEvent.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateSlideOutPannelRefreshEvent.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateAddEventDetailNII.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateAddEventDetailNPE.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateAddEventDetailnpeRemodel.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateSlideOutPannelCorpEvent.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateScheduleEditWeek.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateAdminTabInAutoActivation.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateSlideoutPanelEventManagerHomePageTest.js',
    ],
    regression_EM_3: [
        'Tests/SFPEventManager/EventManagerSpecs/UserRoleSpecs/validateResetCompletionStoreUser.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/UserRoleSpecs/validateEventManagerStoreUser.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/UserRoleSpecs/validateEventManagerDivisionUser.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/UserRoleSpecs/validateResetCompletionDivisionUser.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateEventWithMultipleSchedule.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateAddEventDetailRefreshEvent.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/UserRoleSpecs/validateEventManagerCorporateUser.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/UserRoleSpecs/validateResetCompletionCorporateUser.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/UserRoleSpecs/validateResetCompletionMultiDivisionUser.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateUPLOrderPO.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateStripDatesAndTags.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/UserRoleSpecs/validateCorpUserDivStripsError.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/UserRoleSpecs/validateCorpUserDivStripsErrorToCorpStripError.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/UserRoleSpecs/validateDivisionUserStripsError.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateCreateUPLOrderEvent.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateFiscalWeekAlertInEventScheduling.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateResetCompleteCommodityDropdownCascade.Test.js'
    ],
    

        // CancelReset tests
        // 'Tests/SFPEventManager/EventManagerSpecs/validateEnableResetCompletionCheckBox.Test.js',
        // 'Tests/SFPEventManager/EventManagerSpecs/UserRoleSpecs/validateEditNokActiveDropdownsForDivUserTest.js',
        // 'Tests/SFPEventManager/EventManagerSpecs/UserRoleSpecs/validateUnEditNokCompassScheduleTypeForDivUser.Test.js',
        // 'Tests/SFPEventManager/EventManagerSpecs/UserRoleSpecs/validateEditRemodelScheduleTypeForDivUser.Test.js',
        // 'Tests/SFPEventManager/EventManagerSpecs/UserRoleSpecs/validateEditCompassActiveDropdownsForDivUser.Test.js',
        // 'Tests/SFPEventManager/EventManagerSpecs/UserRoleSpecs/validateCancelResetBtnForDivisionUser.Test.js',
        // 'Tests/SFPEventManager/EventManagerSpecs/UserRoleSpecs/validateCheckboxesInactiveForUnCancellableResetsDivUser.Test.js',

          // 'Tests/SFPEventManager/EventManagerSpecs/validateAutoActivationDetailSummary.Test.js',
        // 'Tests/SFPEventManager/EventManagerSpecs/validateCommodityRecapExcelDbData.Test.js',
        // 'Tests/SFPEventManager/EventManagerSpecs/validateStoreReviewAndCommodityTab.Test.js',
        // 'Tests/SFPEventManager/EventManagerSpecs/validatePDFDownloadOnResetComplete.Test.js',
        // 'Tests/SFPEventManager/EventManagerSpecs/validateUpdateResetCompletion.Test.js',
        // 'Tests/SFPEventManager/EventManagerSpecs/validateResetCompleteDropdown.Test.js',
    store_group_management: [
        //CustomStore Group Management
        'Tests/SFPEventManager/EventManagerSpecs/CustomStoreGroup/applyCustomGroupToEventCreation.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/CustomStoreGroup/duplicatedGroupNameNotAllowedWithinDivisionForESG.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/CustomStoreGroup/columnFilterFeatures.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/CustomStoreGroup/customGroupPaginationAndFooterString.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/CustomStoreGroup/e2eValidateCustomStoresMatchOnStoreSelectionPage.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/CustomStoreGroup/validateWarningMessages.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/CustomStoreGroup/createAndDeletedCSG&SSEAsCorpUser.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/CustomStoreGroup/createdAndEditNewCustomStoreGroup.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/CustomStoreGroup/customStoreGroupDivisionalUser.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/CustomStoreGroup/deleteCustomStore.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/CustomStoreGroup/createShelfStripAndCompassExclusions.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/CustomStoreGroup/kompassStoresMatchOnStoresSelectionPage.Test.js',     
    ],
    //All scenarios that need to run in headless mode only
    // eslint-disable-next-line camelcase
    regression_headless: [
        'Tests/SFPNavigator/NavigatorSpecs/validatePDFContentOnFloorplans.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validatePDFContentOnPlanograms.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validatePDFDownloadOnFloorplans.Test.js',

    ],
    //All scenarios requiring open in new tab
    // eslint-disable-next-line camelcase
    regression_UI: [
        'Tests/SFPNavigator/NavigatorSpecs/validateOpenPDF.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validatePogOpenedOnFloorPlansTab.Test.js'
    ],
    //All scenarios requiring downloading PDF or PSA
    // eslint-disable-next-line camelcase
    regression_navigator_download_UI: [
        'Tests/SFPNavigator/NavigatorSpecs/validateCompositeTabAllPDFBatchDownload.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validatePSAFilesOnPogTab.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateFloorPlanPdfAndPogBatchDownLoad.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validatePDFandPSABatchDownloadOnPogs.test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateActiveInactivePDFBatchDownloadOnPogsTab.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateMappingReportPDFBatchDown.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validatePDFDownloadPlanogramsTab.Test.js'
    ],

    // eslint-disable-next-line camelcase
    regression_oss:[
        //'Tests/SFPOneStopShop/OneStopShopSpecs/*'
        'Tests/SFPOneStopShop/OneStopShopSpecs/validateEditRecordsOnSath.Test.js',
        'Tests/SFPOneStopShop/OneStopShopSpecs/validateSATHFilterDropdowns.Test.js',
        'Tests/SFPOneStopShop/OneStopShopSpecs/validateSathProcessedDateDescendingOrder.Test.js',
        'Tests/SFPOneStopShop/OneStopShopSpecs/validateSathProcessStatus.Test.js'
    ],

    //Include non-db querying tests only
    prod_smoke: [
        'Tests/homePage.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateDivisionDropdownValuesUnique.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateFpDepartmentDropdowns.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateHeaderSortButtonsOnMappingReport.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateCompositeTabDisplaysDifferentDivNums.Test.js'

    ],
    nav_smoke: [
        'Tests/homePage.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateDivisionDropdownValuesUnique.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateFpDepartmentDropdowns.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateHeaderSortButtonsOnMappingReport.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateCompositeTabDisplaysDifferentDivNums.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateNavigatorFilterDropdowns.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateComparisonReportByUpc.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/verifyClearOptions.Test.js',
        'Tests/SFPNavigator/NavigatorSpecs/validateNavigatorMainPageDropdownsMultiSelect.Test.js'
        
    ],
    em_smoke: [
     
        'Tests/SFPEventManager/EventManagerSpecs/validateCreateSFPEvent.Test.js', 
        'Tests/SFPEventManager/EventManagerSpecs/validateAddEventDetailNII.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateAddEventDetailNPE.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateAddEventDetailRefreshEvent.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateCorporateSfpEvent.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateDefaultSetValueInTagOption.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateDeletedEvents.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateREFInAppRefresh.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateSFPEventRefreshFlow.Test.js',
        'Tests/SFPEventManager/EventManagerSpecs/validateSlideOutPannelActivaionEvent.Test.js'
        
    ],


    cleanup_db:[
        'Tests/DbCleanup.Test.js'
    ]

}
