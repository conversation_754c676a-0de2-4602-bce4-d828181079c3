# Report Portal Integration Guide

This document explains how to integrate and use Report Portal with the SFP Apps UI Automation framework.

## Overview

Report Portal is a service for test automation results analysis and reporting. This integration allows you to send test results from your WebDriverIO tests to Report Portal for centralized reporting and analysis.

## Prerequisites

1. Access to Report Portal instance at `http://reportportal.kroger.com:8080`
2. API key from your CoE SME (Center of Excellence Subject Matter Expert)
3. Project created in Report Portal

## Setup

### 1. Install Dependencies

The required dependency is already added to `package.json`:

```json
"@reportportal/agent-js-webdriverio": "^5.1.4"
```

Install it by running:
```bash
npm install
```

### 2. Configuration

#### Properties File Configuration

Update the `src/test/resources/reportportal.properties` file with your specific values:

```properties
# Report Portal endpoint URL
rp.endpoint=http://reportportal.kroger.com:8080

# API key for authentication - replace with actual key from CoE SME
rp.api.key=[YOUR_ACTUAL_API_KEY]

# Project name in Report Portal
rp.project=[YOUR_PROJECT_NAME]

# Launch name for test execution
rp.launch=sfp-apps-ui-automation

# Enable/disable Report Portal reporting
rp.enable=${reportportal:false}
```

#### Environment Variables (Recommended)

You can override configuration using environment variables:

```bash
export RP_ENDPOINT="http://reportportal.kroger.com:8080"
export RP_API_KEY="your-api-key-here"
export RP_PROJECT="your-project-name"
export RP_LAUNCH="SFP-Apps-Custom-Launch-Name"
export RP_ENABLE="true"
```

## Usage

### Running Tests with Report Portal

#### Method 1: Using npm scripts (Recommended)

```bash
# Run all tests with Report Portal
npm run test-rp

# Run regression tests with Report Portal
npm run regression-rp

# Run smoke tests with Report Portal
npm run smoke-rp
```

#### Method 2: Using environment variables

```bash
# Enable Report Portal for any test run
RP_ENABLE=true npm run test

# With custom configuration
RP_ENABLE=true RP_PROJECT="my-project" RP_LAUNCH="my-launch" npm run regression_EM
```

#### Method 3: Direct WebDriverIO command

```bash
# Enable Report Portal via environment variable
RP_ENABLE=true wdio ConfigFiles/wdio.conf.js --suite smoke
```

### Configuration Options

| Environment Variable | Properties File Key | Description | Default |
|---------------------|-------------------|-------------|---------|
| `RP_ENABLE` | `rp.enable` | Enable/disable Report Portal | `false` |
| `RP_ENDPOINT` | `rp.endpoint` | Report Portal URL | `http://reportportal.kroger.com:8080` |
| `RP_API_KEY` | `rp.api.key` | Authentication token | Required |
| `RP_PROJECT` | `rp.project` | Project name | `sfp-apps-project` |
| `RP_LAUNCH` | `rp.launch` | Launch name | `SFP-Apps-{ENV}-{DATE}` |
| `RP_DESCRIPTION` | `rp.description` | Launch description | Auto-generated |

## Features

### Automatic Launch Naming

If no custom launch name is provided, the system automatically generates one:
- Format: `SFP-Apps-{ENVIRONMENT}-{DATE}`
- Example: `SFP-Apps-stage-2024-01-15`

### Test Attributes

Tests are automatically tagged with:
- `framework: webdriverio`
- `environment: {current_env}`
- `application: sfp-apps`
- `automation: ui`

### Error Handling

- If Report Portal is unreachable, tests continue normally
- Configuration errors are logged but don't stop test execution
- Screenshots are still captured and attached to failed tests

## Troubleshooting

### Common Issues

1. **"Report Portal is disabled" message**
   - Ensure `RP_ENABLE=true` is set
   - Check that `rp.enable=true` in properties file

2. **Authentication errors**
   - Verify your API key with CoE SME
   - Ensure the key has proper permissions for the project

3. **Project not found**
   - Verify project name exists in Report Portal
   - Check spelling and case sensitivity

4. **Network connectivity issues**
   - Verify access to `http://reportportal.kroger.com:8080`
   - Check firewall/proxy settings

### Debug Mode

Enable debug logging by setting:
```bash
RP_DEBUG=true RP_ENABLE=true npm run test-rp
```

### Logs

Report Portal configuration is logged at test startup:
```
Report Portal Configuration:
  Endpoint: http://reportportal.kroger.com:8080
  Project: your-project-name
  Launch: SFP-Apps-stage-2024-01-15
  Environment: stage
  Mode: DEFAULT
```

## Best Practices

1. **Use environment variables** for sensitive data like API keys
2. **Create meaningful launch names** for different test runs
3. **Use consistent project naming** across teams
4. **Monitor Report Portal** for test trends and failures
5. **Clean up old launches** periodically to save space

## Support

For Report Portal setup and configuration issues:
1. Contact your CoE SME for API keys and project setup
2. Refer to Report Portal documentation
3. Check with your team's automation lead

For framework-specific issues:
1. Check the console logs during test execution
2. Verify WebDriverIO configuration
3. Test with Report Portal disabled first to isolate issues
