const loginPage = require('../GlobalObjectRepository/login.page');
const envVariables = require('../envConfig')
const envEMVariables = require('../Tests/SFPEventManager/UserRoles/roles')
const logoutPage = require('../GlobalObjectRepository/logout.page');
const data = require('../TestData/data.json')
const ENV = process.env.ENV
const query = require('../SQLConnection/globalQueries')
import  { executeQuery } from '../SQLConnection/server.js.ts'

export async function testSetup(userName, userPassword){
    await loginPage.open();
    await loginPage.loginBasic(await userName, await userPassword)
    console.log('after LoginBasic')
    const logBox = $('//div[@class="logBox"]');
    await logBox.waitForExist({timeout: 30000});
    // await expect(browser).toHaveUrlContaining(await envVariables.url[ENV]);
    // await expect(browser).toHaveTitle(data.homeTitle);
      }

export async function testTeardown(){
    await logoutPage.logout()
}

export async function databaseCleanup(){
   //deleting event data after test run at suite teardown
    await executeQuery(query.deleteAutomationEventDataQuery.replace('@userid', envVariables.user[ENV]));
    await executeQuery(query.deleteAutomationEventDataQuery.replace('@userid', envEMVariables.storeUserEM[ENV]));
    return 'Automation event data from EM have been deleted !'
}

//  Skipping this function until OSS is fully restored; Don't DELETE
// export async function ossDataBaseCleanup(){
//     //deleting version data on OSS after test run at suite teardown
//     await executeQuery(query.deleteAutomationVersionDataQuery.replace('@userid', envVariables.user[ENV]))
//     console.log("Automation version data from OSS have been deleted")
// }


