{"upc_0081000687108": "0081000687108", "upc_0002260099853": "0002260099853", "upc_0085002852404": "0085002852404", "upc_0087911400907": "0087911400907", "upc_0003980011727": "0003980011727", "upc_0001111005273": "0001111005273", "upc_0065230870280": "0065230870280", "upc_0007147500013": "0007147500013", "upc_0000000004061": "0000000004061", "upc_0076537700123": "0076537700123", "upc_0086000678386": "0086000678386", "upc_0001340945132": "0001340945132", "upc_0001376402703": "0001376402703", "upc_0079249300176": "0079249300176", "upc_0006414404702": "0006414404702", "upc_0001111079195": "0001111079195", "upc_0003760004953": "0003760004953", "upc_0081779500410": "0081779500410", "upc_0008105499538": "0008105499538", "upc_0004339639978": "0004339639978", "upc_0008312084017": "0008312084017", "upc_0081809400574": "0081809400574", "upc_0004000000031": "0004000000031", "upc_0078616241144": "0078616241144", "upc_0001111088340": "0001111088340", "upc_0008800403919": "0008800403919", "upc_0008769275134": "0008769275134", "upc_0005410000360": "0005410000360", "upc_0008312084187": "0008312084187", "pogKey_3721056": "3721056", "pogKey_6514998": "6514998", "pogKey_3759271": "3759271", "pogKey_4685841": "4685841", "pogKey_4788078": "4788078", "pogKey_4374326": "4374326", "pogKey_5572967": "5572967", "pogKey_7266886": "7266886", "pogKey_7262414": "7262414", "pogKey_4703266": "4703266", "pogKey_4873368": "4873368", "pogKey_5553298": "5553298", "pogKey_4502789": "4502789", "pogKey_7081199": "7081199", "pogKey_7329765": "7329765", "pogKey_5316570": "5316570", "pogKey_7098932": "7098932", "pogKey_4502779": "4502779", "pogKey_5627694": "5627694", "pogKey_5755490": "5755490", "pogKey_5717130": "5717130", "DBKey_270962": "270962", "floorKey_446352": "446352", "pogKey_4320222": "4320222", "pogKey_5490303": "5490303", "pogKey_4018649": "4018649", "pogKey_5482494": "5482494", "pogKey_7071897": "7071897", "pogKey_5827958": "5827958", "pogKey_7054616": "7054616", "pogKey_6714134": "6714134", "pogKey_7120288": "7120288", "pogKey_6817565": "6817565", "pogKey_7087370": "7087370", "pogKey_6833777": "6833777", "pogKey_6604532": "6604532", "pogKey_6482673": "6482673", "pogKey_6735384": "6735384", "pogKey_7073048": "7073048", "pogKey_7040812": "7040812", "pogKey_7111083": "7111083", "pogKey_7328525": "7328525", "pogKey_6657233": "6657233", "pogKey_7061562": "7061562", "floorKey_1146873": "1146873", "floorKey_1117241": "1117241", "pogKey_196519": "196519", "pogKey_7430073": "7430073", "POGKeyListMapping": "6758611\n7138915\n7053606\n7041572\n7287608\n6057741\n7126088", "pogKey_List_Pog_PSA": "6645752\n6884631\n6985087\n6931559\n6827227\n6556212", "pogKey_List_Pog_PDF": "6645752\n6884631\n6985087\n6591750\n6607287\n6777475\n6990204\n6817819\n7111083", "pogKey_List_Pog_PSA_Empty": "5497425\n5497424\n5881716\n6209936\n5881761\n5881754", "SSN_D014_L00000_D01_C055_VF90_F006_MX": "D014_L00000_D01_C055_VF90_F006_MX", "SSN_D014_L00000_D03_C162_V305_I018_MX": "D014_L00000_D03_C162_V305_I018_MX", "SSN_D014_L00000_D01_C013_V011_F003_MX": "D014_L00000_D01_C013_V011_F003_MX", "SSN_D014_L00000_D03_C162_V004_I014_MX": "D014_L00000_D03_C162_V004_I014_MX", "SSN_D014_L00000_D01_C113_V100_F016_MX": "D014_L00000_D01_C113_V100_F016_MX", "SSN_D014_L00000_D01_C204_VN51_F004_MX": "D014_L00000_D01_C204_VN51_F004_MX", "space_Station_Name_MR": "Space Station Name", "colCount_spog": 34, "colCount_fp": 15, "colCount_POGs": 18, "colCount_Composite": 9, "colCount_MappingReport": 26, "dropdwnCount_floorplans": 5, "div_011_Atlanta_Division": "011 - Atlanta Operating Division", "div_014_Cincinnati_Division": "014 - Cincinnati Operating Division", "div_024_Louisville_Division": "024 - Louisville Operating Division", "div_706_Smiths_Division": "706 - Smiths Operating Division", "div_704_Food_4_Less_Division": "704 - Food 4 Less Operating Division", "div_018_Michigan_Division": "018 - Michigan Operating Division", "div_021_Central_Division": "021 - Central Operating Division", "div_090_Ruler_Stores_Division": "090 - Ruler Stores Operating Divisio", "div_016_Columbus_Division": "016 - Columbus Operating Division", "div_534_Roundys_Wisconsin_Division": "534 - Roundys Wisconsin Division", "div_701_Fred_Meyer_Stores": "701 - Fred <PERSON> Operating Division", "divisional_user_division_014": "014 - Cincinnati Operating Division", "divisional_user_division_016": "016 - Columbus Operating Division", "div_708_Food_4_Less_Midwest_Division": "708 - Food 4 Less - Midwest Operatin", "store_user_014_00359": "014 - 00359", "store_014_00359": "014 - 00359", "store_014_00344": "014 - 00344", "store_704_00351": "704 - 00351", "store_706_00028": "706 - 00028", "store_014_00361": "014 - 00361", "store_014_00353": "014 - 00353", "store_014_00367": "014 - 00367", "store_011_00020": "011 - 00020", "store_014_00317": "014 - 00317", "store_018_00038": "018 - 00038", "store_018_00074": "018 - 00074", "store_014_00351": "014 - 00351", "store_016_00251": "016 - 00251", "store_534_00406": "534 - 00406", "store_016_00268": "016 - 00268", "store_016_00128": "016 - 00128", "store_021_00016": "021 - 00016", "store_016_00385": "016 - 00385", "store_014_00429": "014 - 00429", "store_014_00335": "014 - 00335", "store_014_00053": "014 - 00053", "store_014_00426": "014 - 00426", "store_014_00411": "014 - 00411", "store_014_00336": "014 - 00336", "store_011_00002": "011 - 00002", "store_708_00552": "708 - 00552", "store_011_00025": "011 - 00025", "store_016_00506": "016 - 00506", "set_Store_024_00729": ["024 - 00729", "Select All"], "set_store_014_00429": ["014 - 00429", "Select All"], "set_store_014_00426": ["014 - 00426", "Select All"], "set_store_014_00411": ["014 - 00411", "Select All"], "set_store_014_00353": ["014 - 00353", "Select All"], "set_store_014_00053": ["014 - 00053", "Select All"], "set_store_016_00506": ["016 - 00506", "Select All"], "dept_07_PACKAGE_PRODUCE": "07-PACKAGE PRODUCE", "dept_06_PHARMACY": "06-PHARMACY", "dept_58_Fuel": "58-<PERSON>", "dept_87_Gm": "87-Gm", "dept_01_GROCERY": "01-GROCERY", "dept_03_HBC": "03-<PERSON><PERSON>", "PHARMACY_65_RX_OTC": "06-PHARMACY - 65-RX-O<PERSON>", "subDept_07_PACKAGE_PRODUCE_07_PACKAGE_PRODUCE": "07-PACKAGE PRODUCE - 07-PACKAGE PRODUCE", "subDept_01_GROCERY_12_SOFT_DRINKS": "01-GROCERY - 12-<PERSON>O<PERSON> DRINKS", "comm_07_PACKAGE_PRODUCE_642_PROCESSED": "07-PACKAGE PRODUCE - 642-PROCESSED", "RX_OTC_250_DIABETIC_TESTING_SUPPLIES": "65-RX-OTC - 250-DIABETIC TESTING SUPPLIES", "DIABETIC_TESTING_SUPPLIES": "250-DIABETIC TESTING SUPPLIES - 250-DIABETIC TESTING SUPPLIES", "commGrp_642_PROCESSED_642_PROCESSED": "642-PROCESSED - 642-PROCESSED", "commGrp_65_RX_OTC_251_APOTHECARY": "65-RX-OTC - 251-APOTHECARY", "commGrp_032_SPECIALTY_SOFT_DRINKS": "12-SOFT DRINKS - 032-SPECIALTY SOFT DRINKS", "commGrp_251_APOTHECARY_251_APOTHECARY": "251-APOTHECARY - 251-APOTHECARY", "comm_01_GROC_ALL_OTHER_005_CAN_SEAFOOD_SHELF_STABLE": "01-GROC-<PERSON><PERSON> OTHER - 005-CAN SEAFOOD - SHELF STABLE", "comm_01_GROC_ALL_OTHER_001_CANNED_FRUIT_FRUIT_BOWLS": "01-GROC-<PERSON><PERSON> OTHER - 001-CANNED FRUIT/FRUIT BOWLS", "comm_01_GROC_ALL_OTHER_001_CAN_VEGETABLES_SHELF_STABLE": "01-GROC-<PERSON><PERSON> OTHER - 002-CAN VEGETABLES - SHELF STABLE", "planogramStatusLive": ["Live"], "planogramStatusPending": "Pending", "emptySelectAisleValue": "", "extractDigitFrom_index_0": "0", "extractDigitEndWith_index_3": "3", "Floorplan_Dept_Auth": "Authorization Only", "Floorplan_Dept_TotalScore": "Total Store", "floorPlanStatusLive": "Live", "floorPlanStatusPending": "Pending", "SelectAll": "Select All", "SelectFPDepartment": "Select FP Department", "eventType_KOM": "KOM", "eventName_SPE": "SPE", "systemData_ILP": "ILP", "systemData_SFP": "SFP", "compositeData": ["", "014", "00344", "Live", "2004-07-29", "P07 W02 Y2004", "PDF", "185", "23869"], "fpDropdwn": ["Select All", "Authorization Only", "Seasonal", "Seasonal Authorization Only", "Total Store"], "divisionlist": ["011 - Atlanta Operating Division", "014 - Cincinnati Operating Division", "016 - Columbus Operating Division", "018 - Michigan Operating Division"], "defaultSortDivisionList": ["011 - Atlanta Operating Division", "016 - Columbus Operating Division", "018 - Michigan Operating Division"], "divisionListFP": ["014 - Cincinnati Operating Division", "016 - Columbus Operating Division", "018 - Michigan Operating Division"], "divisionListPogPSA": ["014 - Cincinnati Operating Division", "016 - Columbus Operating Division", "Select All"], "divisionListPogPSA1": ["011 - Atlanta Operating Division", "014 - Cincinnati Operating Division", "Select All"], "divisionList_SortByAisle": ["014 - Cincinnati Operating Division", "016 - Columbus Operating Division", "018 - Michigan Operating Division"], "divisionList2": ["018 - Michigan Operating Division", "021 - Central Operating Division", "024 - Louisville Operating Division"], "divisionListStoreTab": ["011 - Atlanta Operating Division", "014 - Cincinnati Operating Division", "016 - Columbus Operating Division", "Select All"], "filterDivisionlist": ["011 - Atlanta Operating Division", "014 - Cincinnati Operating Division", "018 - Michigan Operating Division", "706 - Smiths Operating Division", "708 - Food 4 Less - Midwest Operatin"], "compositeDivisionList_011_014": ["011 - Atlanta Operating Division", "014 - Cincinnati Operating Division", "Select All"], "storelist": ["011 - 00025", "011 - 00020", "011 - 00346", "014 - 00344", "Select All"], "storeListMultiSearch": ["011 - 00025", "014 - 00335", "016 - 00621", "018 - 00515", "Select All"], "defaultSortStoreList": ["011 - 00025", "016 - 00506", "018 - 00515"], "storeList_SelectAisleMappingReport": ["014 - 00351", "014 - 00395", "014 - 00429"], "pendingStoreList_SelectAisleMappingReport": ["Select All", "Select All"], "storeList_selectAisleListLive": ["002 - AISLE 2", "004 - AISLE 4", "006 - AISLE 6"], "storeListFP": ["014 - 00355", "016 - 00957", "018 - 00436", "Select All"], "storeList2": ["018 - 00277", "021 - 00071", "024 - 00015"], "storeListPogItemStore": ["011 - 00012", "014 - 00351", "016 - 00558", "018 - 00615", "Select All"], "storeList_MappingReport": ["014 - 00335", "014 - 00353", "014 - 00355", "014 - 00364", "014 - 00390", "Select All"], "storeList_StoreType": ["014 - 00335", "014 - 00353"], "storeList_StoreType_POG": ["016 - 00251", "016 - 00264", "016 - 00268", "016 - 00273", "016 - 00312", "Select All"], "storeList_StoreTab": ["011 - 00002", "011 - 00012", "011 - 00016", "014 - 00053", "014 - 00335", "016 - 00036", "016 - 00128", "016 - 00202", "Select All"], "departmentList": ["01-GROCERY", "03-<PERSON><PERSON>", "06-PHARMACY", "07-PACKAGE PRODUCE"], "departmentList_MultipleSearch": ["03-<PERSON><PERSON>", "06-PHARMACY", "Select All"], "filterDepartment": "01-GROCERY", "filterSubDeptList": ["01-GROCERY - 02-REFRIG GROCERY", "01-GROCERY - 05-DAIRY", "01-GROCERY - 12-<PERSON>O<PERSON> DRINKS", "01-GROCERY - 52-<PERSON><PERSON>"], "filterSubDept": "01-GROCERY - 05-DAIRY", "subDepartmentList": ["01-GROCERY - 05-DAIRY", "01-GROCERY - 52-<PERSON><PERSON>"], "subDepartmentList_MultiSearch": ["03-HBC - 16-<PERSON><PERSON>UT<PERSON>", "03-HBC - 67-BAB<PERSON>", "Select All"], "commodityList": ["05-DAIRY - 061-MILK BY-PRODUCTS", "05-DAIRY - 062-YOGURT", "Select All"], "commodityList_MultiSearch": ["16-BEAUTY - 165-HAIR CARE ACCESSORIES", "16-BEAUTY - 185-SUNTAN", "Select All"], "filterCommodityList": ["05-DAIRY - 061-MILK BY-PRODUCTS", "05-DAIRY - 062-YOGURT", "05-DAIRY - 064-EG<PERSON>"], "commodityGroupList": ["062-YOGURT - 062-YOGURT 011", "062-YOGURT - 062-YOGURT 016"], "commodityGroupList_MultiSearch": ["165-HAIR CARE ACCESSORIES - 165-HAIR CARE ACCESSORIES", "185-SUNTAN - 185-SUNTAN", "Select All"], "planogramStatusList": ["Live", "Pending", "Select All"], "floorPlanStatus": ["Live", "Pending", "Select All"], "eventType": ["KOM", "SPE", "NOK", "REM", "SEA", "Select Event Type"], "eventName": ["Kompass/ Calendar Reset", "Select All"], "POGHeaders": ["Status", "POG Type", "Pending Date", "Date Live", "Period Week Year", "PDF", "PSA", "Store Count", "Number (#) of Items", "DB<PERSON>ey", "Space Station Name", "Space Station Version Description", "POG Name"], "storeListBatchDownload": ["014 - 00335", "014 - 00336", "014 - 00344", "Select All"], "UPCList": "0001340951537\n0001340935231\n0001340934341\n0001340951741", "UPC_List_ComparisonReport2": "0083972800644\n0008981900171\n0008600346192", "UPC_List_ComparisonReport": "0008600378036\n0083972800040\n0008308530058", "UPC_List_ComparisonReport3": "0001111012482\n0007203002404\n0007203002111", "UPC_ComparisonReport_0008312084207": "0008312084207", "Store_Planogram_Item": "Store Planogram Item", "PDF_PSA_values": ["PDF", "PSA", "No file found"], "PDF_POG_values": ["PDF", "Planograms", "No file found"], "col_noOfStores": "# of Stores", "col_noOfItems": "# of Items", "col_POGDBKey": "POG <PERSON>", "col_UPC": "UPC", "mappingReport_AisleColumn": "Aisle #", "POG_Item_by_Store": "POG & Item by Store", "pogItemByStore_SearchCriteria": "Search Criteria", "mappingReport_Page_1": "1", "mappingReport_Page_2": "2", "mappingReport_Div_Column": "Div", "SearchCriteria": "Search Criteria", "subString_0": 0, "subString_1": 1, "subString_2": 2, "subString_3": 3, "subString_4": 4, "subString_6": 6, "subString_8": 8, "mappingReport_Filters": "Filters", "comparisonReport_UPC": "UPC:", "space_station_name": "Space Station Name", "Item_By_POG": "Item By POG", "ItemByPOG_SearchCriteria": "Search Criteria", "comparisonReport_ils": "ils", "mappingReport_StoreType": "StoreType", "mappingReport_Store_Type": "Store Type", "mappingReport_Select_All": "Select All", "mappingReport_Food_Store": "Food Store", "mappingReport_Clear_Store_Type": "Clear Store Type", "mappingReport_Select_Store_Type": "Select Store Type", "mappingReport_PogType": "PogType", "mappingReport_Display_Space": "Display Space", "mappingReport_Basic": "Basic", "mappingReport_Clear_POG_Type": "Clear POG Type", "floorDBKey_453117": "453117", "floorDBKey_766365": "766365", "integer_1": 1, "mappingReport_Sort_By_Aisle": "Sort By <PERSON>sle", "Export_To_Excel_text": "Export To Excel", "Merchandised_Assortment_text": "Merchandised Assortment", "Full_Assortment": "Full Assortment", "Delete_Flag": "Delete Flag", "store_Count": "Store Count", "mapping_Report": "Mapping Report", "pog_DBKey_text": "POG <PERSON>", "number_of_Items": "Number (#) of Items", "store": "Store", "dBKey": "DB<PERSON>ey", "Planograms": "Planograms", "pogs": "POGs", "item_By_Pog_Text": "Item by POG", "Pog_Key_Text": "POG Key", "mappingpdf_text": "mappingpdf", "floorplancheck_text": "<PERSON><PERSON><PERSON><PERSON>", "planogramcheck_text": "planogramcheck", "FP_Layout_text": "FP Layout", "planogram_list_text": "Planogram List", "div_error_message": "Division Cannot be null", "comparisonReport_TableNames": ["ilp", "sfp"], "navigator_POG_StoreCount": "storeCountCheck", "selectList_FilterCriteria_Text": "Select List Filter C<PERSON>ria:", "select_Division": "Select Division", "env_nav_stage_hdc": "nav-stage-hdc", "items_Selected_116": "116 Items Selected", "POGKeyList": "5869823\n5869824\n5869828\n5869832\n5387005\n5870602\n5870745", "storeListStoreTabComposite": ["011 - 00002", "011 - 00012", "011 - 00016", "014 - 00335", "014 - 00351", "014 - 00353", "014 - 00355", "014 - 00359", "014 - 00361", "016 - 00128", "016 - 00202", "016 - 00232", "016 - 00251", "016 - 00264", "016 - 00268", "016 - 00273", "016 - 00312"], "searchCriteriaLocatorNames": "1487,1488,1506,3633,3635,953,3636,3637,3638,3575,3707,5617,3711,3623,3713,3714,3716", "div011_Index_0": 0, "div014_Index_1": 1, "div016_Index_2": 2, "div018_Index_3": 3, "div090_Index_11": 11, "div706_Index_22": 22, "store014_00335_Index_1": 1, "pogDbKey_5901406_8": 8, "num_Of_Planograms": "# of Planograms", "Only_Mapped_POGs": "Only Mapped POGs", "Only_Unmapped_POGs": "Only Unmapped POGs", "storeListStoreTabCompositeMapping": ["011 - 00002", "014 - 00335", "Select All"], "compositeTabTheSameStoreList": ["011 - 00335", "014 - 00335", "Select All"], "comparisonReportStoreList1": ["014 - 00381", "014 - 00722", "Select All"], "comparisonReportStoreList": ["016 - 00128", "016 - 00202", "016 - 00506", "Select All"], "divisionListCompositeMapping": ["011 - Atlanta Operating Division", "014 - Cincinnati Operating Division", "Select All"], "dbkey_stores_011_00002_014_00335": "1487T3633", "divisionList_011T014": "011T014", "dbkey_store_018_00074": "5632", "db_POG_Status_Default": "default", "db_POG_Status_Pending": "2", "no_records_found": "No records found", "page_row_size_120": 120, "filterDivisionListPOG": ["011 - Atlanta Operating Division", "018 - Michigan Operating Division", "021 - Central Operating Division", "025 - Delta Operating Division", "701 - Fred <PERSON> Operating Division", "703 - Ralphs Operating Division"], "commodityPogsTab": ["01-GROC-<PERSON><PERSON> OTHER - 001-CANNED FRUIT/FRUIT BOWLS", "01-GROC-<PERSON><PERSON> OTHER - 002-CAN VEGETABLES - SHELF STABLE", "Select All"], "select_Store": "Select Store", "select_Department": "Select Department", "select_Sub_Department": "Select Sub Department", "select_Planogram_Status": "Select Planogram Status", "select_Commodity": "Select Commodity", "select_Commodity_Group": "Select Commodity Group", "select_Floorplan_Status": "Select Floorplan Status", "select_Event_Type": "Select Event Type", "select_Event_Type_KOM": "KOM", "select_Event_Name": "Select Event Name", "one_Item_Selected": "1 Item Selected", "sfp_Navigator_Text": "SFP Navigator", "please_select_record_message": "Please select at least one record to batch download", "navBarList_first_3": ["Store", "Floorplans", "Mapping Report"], "navBarList_first_4": ["Store", "Composite", "Floorplans", "Mapping Report"], "navBarList_first_6": ["Store", "Composite", "Floorplans", "Mapping Report", "POGs", "Item by POG"], "navBarList_first_7": ["Store", "Composite", "Floorplans", "Mapping Report", "POGs", "Item by POG", "POG & Item by Store"], "navBarList_first_8": ["Store", "Composite", "Floorplans", "Mapping Report", "POGs", "Item by POG", "POG & Item by Store", "Comparison Report"], "button_List": ["Search Criteria", "Export To Excel", "Merchandised Assortment", "Full Assortment", "Adds", "Deletes", "NII Info", "Move - Adjustments", "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON>"], "navBarList_3_DivUser": ["Store", "Floorplans", "Mapping Report"], "commodityDropdown": "commodity-select", "commGroupDropdown": "comm-group-select", "divisionDropdown": "divisionMultiSelect", "departmentDropdown": "dept-select", "eventTypeDropdown": "event-type-select", "eventNameDropdown": "event-name-select", "floorplanDropdown": "floorplan-select", "planogramDropdown": "pog-status-select", "storeDropdown": "store-select", "subDeptDropdown": "sub-dept-select", "aisleDropDown": "<PERSON><PERSON><PERSON><PERSON>", "fpFilterDropDown": "selectedFloorplanDepartment", "pogFilterDropDown": "selectedPogType", "storeFilterDropDown": "selectedStoreType", "store_016_multiple": ["016 - 00506", "016 - 00510"], "storeList_Multiple_MappingReport": ["014 - 00335", "014 - 00353", "014 - 00355", "014 - 00364", "014 - 00390"], "storeList_Dual_MappingReport": ["014 - 00335", "014 - 00351"], "divisionList_011_014": ["011 - Atlanta Operating Division", "014 - Cincinnati Operating Division"], "sameStoreDifferentDivision": ["011 - 00335", "014 - 00335"], "division_014_multiple": ["014 - 00053", "014 - 00082", "014 - 00335", "014 - 00336", "014 - 00344"], "select_3_divisions": ["011 - Atlanta Operating Division", "014 - Cincinnati Operating Division", "016 - Columbus Operating Division"], "select_3_stores": ["011 - 00335", "014 - 00335", "016 - 00506"]}