{"Add_Version_text": "Add Version", "department_01_GROCERY": "01-GROCERY", "department_03_Drug_GM": " 03-DRUG / GM ", "department_06_PHARMACY": "06-PHARMACY", "department_09_MEAT": "09-MEAT", "commodity_000_SHELF_EXTENDERS": "000-SHELF EXTENDERS/LOCAL", "commodity_224_INSECTICIDES": "224-INSECTICIDES", "commodity_248_MOBILITY_AIDS": "248-MOBILITY AIDS", "commodity_250_DIABETIC_TESTING": "250-DIABETIC TESTING SUPPLIES", "commodity_427_SEAFOOD_TROUT": "427-SEAFOOD - TROUT", "commodity_041_FROZEN_POTATOES": "041-<PERSON><PERSON><PERSON><PERSON> POTATOES", "set_SATH_division_011": ["011-Atlanta Operating Division"], "set_SATH_division_014": ["014-Cincinnati Operating Division"], "set_SATH_division_018": ["018-Michigan Operating Division"], "set_SATH_division_021": ["021-Central Operating Division"], "set_SATH_division_026": ["026-Nashville Operating Division"], "set_SATH_division_701": ["701-<PERSON>"], "set_SATH_store_011_00618": ["011-00618"], "set_SATH_store_011_00625": ["011-00625"], "set_SATH_store_011_00979": ["011-00979"], "set_SATH_store_014_00335": ["014-00335"], "set_SATH_store_014_00364": ["014-00364"], "set_SATH_store_018_00038": ["018-00038"], "set_SATH_store_018_00074": ["018-00074"], "set_SATH_store_018_00115": ["018-00115"], "set_SATH_store_021_00001": ["021-00001"], "set_SATH_store_021_00973": ["021-00973"], "set_SATH_store_026_00093": ["026-00093"], "set_SATH_store_701_00011": ["701-00011"], "set_SATH_process_status_fail": ["Fail"], "set_SATH_process_status_pass": ["Pass"], "set_department_01": ["01"], "set_commodity_000": ["000"], "set_selectAll": ["Select All"], "ui_state_disabled_text": "ui-state-disabled", "delete_confirmation_message": "Are you sure you want to request DELETE?", "Delete_Requested_Date_text": "Delete\nRequested Date", "ossUrl": "onestopshop/spaceStation", "sathUrl": "onestopshop/sathThreshold", "Select_Department": "Select Department", "Select_Commodity": "Select Commodity", "Select_Version": "Select Version", "department_07_PRODUCE": "07-PROD<PERSON><PERSON>", "department_16_COSMETICS": "16-COSMETICS", "commodity_408_FRESH_KITCHENS_FRUIT": "408-FRE<PERSON><PERSON> KITCHENS FRUIT", "commodity_193_FRAGRANCES": "193-FRAGRANCES", "filterDepartmentList": ["01-GROCERY", "03-DRUG / GM", "06-PHARMACY", "07-PROD<PERSON><PERSON>", "09-MEAT", "10-DELI / BAKERY", "16-COSMETICS", "17-NATURAL FOODS", "58-FUEL"], "threshold_Number_15": 15, "Number_15": 15, "three_Items_Selected": "3 Items Selected", "two_Items_Selected": "2 Items Selected", "select_All": "Select All", "select_Division": "Select Division", "select_Store": "Select Store", "select_Process_Status": "Select Process Status", "get_query_top_45_data": "45", "get_query_top_60_data": "60", "paginate_till_page_4": "4", "paginate_till_page_5": "5", "processed_Date_string": "Processed Date", "sath_exel_download_msg": "Your download has started...", "SATHDivisionList": ["011-Atlanta Operating Division", "016-Columbus Operating Division", "018-Michigan Operating Division"], "SATHStoreList": ["011-00012", "016-00621", "018-00443"], "SATHProcessStatusList": ["Fail", "Pass"], "pageDropdownList": ["15", "30", "60"], "num_Of_Column_Name_SS": "# of POGs", "pog_Column_Name_SS": "POGs Mapped", "num_Of_Column_empty_message": "There are no POGs to be displayed.", "pog_Column_empty_message": "There are no Mapped POGs to be displayed.", "automation_User_Name": "svc5940WebAppS", "version_id_609": "609", "version_id_100": "100", "no_records_found": "No records found."}