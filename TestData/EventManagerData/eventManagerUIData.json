{"div_Corporate_060": "Corporate - 060", "div_Cincinnati_Operating_Division_014": "Cincinnati Operating Division - 014", "div_Atlanta_Operating_Division_011": "Atlanta Operating Division - 011", "division_Cincinnati_Operating_Division_014": "014 - Cincinnati Operating Division", "Division_011_Atlanta_Division": "011 - Atlanta Operating Division", "division_Fred_Meyer_Division_701": "701 - Fred <PERSON> Operating Division", "div_Columbus_Operating_Division_016": "Columbus Operating Division - 016", "div_016_Columbus_Operating_Division": "016 - Columbus Operating Division", "division_Cincinnati_Operating_Division_014_Txt": "Division\n014 - Cincinnati Operating Division", "Division_011_Atlanta_Division_011_Txt": "Division\n011 - Atlanta Operating Division", "division_Fred_Meyer_Division_701_Txt": "Division\n701 - Fred <PERSON> Operating Division", "selectedLocation_014_00336": "014 - 00336", "selectedLocation_014_00351": "014 - 00351", "selectedLocation_014_00466": "014 - 00466", "selectedLocation_701_00013": "701 - 00013", "selectedLocation_014_00920": "014 - 00920", "selectedLocation_014_00336_Txt": "Location\n014 - 00336", "selectedLocation_014_00466_Txt": "Location\n014 - 00466", "selectedLocation_014_00351_Text": "Location\n014 - 00351", "selectedLocation_014_00920_Text": "Location\n014 - 00920", "selectedLocation_701_00013_Text": "Location\n701 - 00013", "selectedDepartment_00_OCADO": "00-MFC", "selectedDepartment_00_MFC_Txt": "Department\n00-MFC", "selectedResetType_Non_KOMPASS": "Non- KOMPASS", "selectedResetType_Non_KOMPASS_Txt": "Reset Type\nNon- KOMPASS", "selectedCommodity_00_MFC_45_FROZEN_GROCERY": "00-MFC - 45-<PERSON><PERSON><PERSON><PERSON> GROCERY", "selectedCommodity_00_MFC_45_FROZEN_GROCERY_Txt": "Commodity\n00-MFC - 45-F<PERSON><PERSON><PERSON> GROCERY", "selectedCompletionType_Yes": "Yes", "selectedCompletionType_Yes_Txt": "Reset Complete ?\nYes", "selectedDivision_UnSelected": "Division\nSelect Division", "selectedLocation_UnSelected": "Location\nSelect Location", "selectedDepartment_UnSelected": "Department\nSelect Department", "selectedResetType_UnSelected": "Reset Type\nSelect Reset Type", "selectedCommodity_UnSelected": "Commodity\nSelect Commodity", "selectedCompletionType_UnSelected": "Select Reset Complete", "store_Selection_351": "351", "store_Selection_00426": "426", "store_Selection_395": "395", "store_Selection_429": "429", "store_Selection_350": "350", "store_Selection_531": "531", "store_Selection_581": "581", "store_Selection_335": "335", "selecting_POG_Sub_Department": "15-DELI - 15-DE<PERSON><PERSON> BULK", "selecting_POG_Commodity": "451-DELI MEAT: BULK - 15-DELI BULK - 15-<PERSON><PERSON><PERSON>", "selecting_POG_CommodityGroup": "451-DELI MEAT: BULK - 451-DELI MEAT: BULK", "selecting_POG_Department_15_DELI": "15-DELI", "selecting_POG_SubDepartment_15_DELI_49_SPECIALTY_CHSE": "15-DELI - 49-SPECIALTY CHSE", "selecting_POG_Commodity_462_SPECIALTY_CHEESE_PRE_PACK_49_SPECIALTY_CHSE_15_DELI": "462-SPECIALTY CHEESE PRE PACK - 49-SPECIALTY CHSE - 15-DE<PERSON>I", "selecting_POG_CommodityGroup_462_SPECIALTY_CHEESE_PRE_PACK_462_SPECIALTY_CHEESE_PRE_PACK": "462-SPECIALTY CHEESE PRE PACK - 462-SPECIALTY CHEESE PRE PACK", "filters_Commodity_15_DELI_462_SPECIALTY_CHEESE_PRE_PACK": "15-DELI - 462-SPECIALTY CHEESE PRE PACK", "selecting_POG_PogDBKey_Table": "PogDBKey Table", "selecting_POG_PogDBKey_List": "PogDBKey List", "event_Schedule_PopUp_DBKey_Table": "PopUp DBKey Table", "event_Schedule_PopUp_DBKey_List": "PopUp DBKey List", "event_Schedule_Popup_Schedule_Table": "Popup Schedule Table", "event_upc_Popup_Table": "Popup Upc Table", "event_Schedule_Popup_Schedule_List": "Popup Schedule List", "event_Schedule_Popup_Schedule_Div_List": "Popup div List", "eventType_KOMPASS": "KOMPASS", "eventType_Remodel": "Remodel", "eventType_Ad_Hoc": "Ad-Hoc", "type_Update": "Update", "type_APPRefresh": "APP Refresh", "type_Activation": "Activation", "type_NII": "New Item Introduction (NII)", "SlideOuttype_NII": "New Item Introduction", "vendor_AZB": "AZB", "heading_Prefix_RES": "RES", "heading_Prefix_OFC": "OFC", "heading_Prefix_OFN": "OFN", "eventType_SFP_Event": "SFP Event", "eventDesc": "test", "processWeekDay_Tuesday": "Tuesday", "fiscalWeek_5": "5 - (02-27)", "Create_SFP_Event": "Create SFP Event", "SFP_Event": "SFP Event", "Create_SFP_Activation_Event": "Create SFP Activation Event", "Event_Schedule": "Event Schedule", "Event_Refresh": "Create SFP Refresh Event", "Event_Refresh_Alert": "Refresh Event has been created successfully", "Store_Selection": "Store Selection", "Select_Pogs": "Select Pogs", "Upload_UPC": "Upload UPC(s)", "UPC_Value_61076426171": "61076426171", "dept_01_GROCERY": "01-GROCERY", "subdept_01_GROCERY_01_GROC_ALL_OTHER": "01-GROCERY - 01-GROC-<PERSON><PERSON>", "subdept_03_HBC_03_HEALTH": "03-HBC - 03-HEALTH", "subdept_03_HEALTH": "03-HEALTH", "commodity_003_CAN_BEANS_01_GROC_ALL_OTHER_01_GROCERY": "003-<PERSON><PERSON> BEANS - 01-GROC-ALL OTHER - 01-GROCERY", "commodity_01_GROCERY_01_GROC_ALL_OTHER_013_SPICES_EXTRACTS": "013-SPICES & EXTRACTS - 01-GROC-ALL OTHER - 01-GROCERY", "commodity_01_GROCERY_01_GROC_ALL_OTHER_003_CAN_BEANS": "003-<PERSON><PERSON> BEANS - 01-GROC-ALL OTHER - 01-GROCERY", "commodity_172_ANALGESICS_03_HEALTH_03_HBC": "172-ANALGESICS - 03-HEALTH - 03-HBC", "CommodityGroup_013_SPICES_EXTRACTS_013_SPICES_EXTRACTS": "013-SPICES & EXTRACTS - 013-SPICES & EXTRACTS", "commodityGroup_172_ANALGESICS_172_ANALGESICS": "172-ANALGESICS - 172-ANALGESICS", "Admin_Auto_Activation_Report": "Admin -> Auto Activation Report", "Admin_reset_completion": "Admin -> Reset Completion", "location_014_00305": "014 - 00305", "location_014_00466": "014 - 00466", "department_03_HBC": "03-<PERSON><PERSON>", "Year_2019": "2019", "Year_2020": "2020", "Year_2021": "2021", "Year_2022": "2022", "Year_2023": "2023", "Year_2024": "2024", "Year_2025": "2025", "Year_Input": "Year", "Period_Input": "Period", "Week_Input": "Week", "Period_3": "3", "Period_1": "1", "Period_8": "8", "Period_Empty": "--", "Week_2_10": "2 (10)", "Week": "--", "Week_4_12": "4 (12)", "Week_4_32": "4 (32)", "Week_3_3": "3 (3)", "ErrorCode_5": "5 - UPC is not a base UPC", "ErrorCode_4": "4 - <PERSON><PERSON> not found", "ErrorCode_3": "3 - <PERSON><PERSON> has invalid status code", "UPC_0001230022083": "0001230022083", "DBkey_3718937": "5662780", "DBkey_3670699": "5814821", "DBkey_4860101": "4860101", "DBkey_4771044": "7041357", "DBkey_7239932": "7239932", "PogCommodities_Array_Size": 13, "AdminSubmenu_AutoActivationReport": "Auto Activation Report", "AdminSubmenu_ResetCompletion": "Reset Completion", "Event_Reset_Schedule_Maintenance": "Event/Reset Schedule Maintenance", "ApplicationsSubmenuSFPEventManager": "SFP Event Manager", "AdminTab": "Admin", "Store_351": "351", "Store_353": "353", "Store_128": "128", "Store_423": "423", "selectedDivision_ResetCompletion": "selectedDivision", "selectedLocation_ResetCompletion": "selectedLocation", "selectedDepartment_ResetCompletion": "selectedDepartment", "selectedResetType_ResetCompletion": "selectedResetType", "selectedCommodity_ResetCompletion": "selectedCommodity", "selectedCompletionType_ResetCompletion": "selectedCompletionType", "Location_ResetCompletion": "location", "Locations_ResetCompletion": "locations", "VendorName_ResetCompletion": "vendorName", "VendorNames_ResetCompletion": "vendorNames", "ResetType_ResetCompletion": "resetType", "ResetTypes_ResetCompletion": "resetTypes", "ResetComplete_ResetCompletion": "selectedCompletionType", "ResetComplete_NoResponse": "No Response", "ResetComplete_Deleted": "Deleted", "ResetComplete_SpaceStationName": "Space Station Name", "ResetComplete_StoreReview_Loc": "Loc", "ResetComplete_StoreReview_Div": "Div", "Year_ResetCompletion": "year", "Years_ResetCompletion": "years", "Week_ResetCompletion": "week", "Weeks_ResetCompletion": "weeks", "ResetComplete_Yes": "Yes", "ResetComplete_No": "No", "ResetComplete_All": "Select All", "PaginationPageNumber_100": "100", "PaginationPageNumber_20": "20", "PaginationPageNumber_10": "10", "PaginationPageNumber_5": "5", "Location": "Location", "Locations": "Locations", "Department": "Department", "Departments": "Departments", "Category_Name": "CategoryName", "Product_Group": "ProductGroup", "UPL_Orders": "UPL Orders", "Create_UPL_Orders": "Create UPL Orders", "Loc00005_Oab_Albany_Fred_Meyer_Stores": "00005 - Oab Albany - Fred <PERSON> Operating Division", "Loc00007_Fred_Meyer_Corvallis_Fred_Meyer_Stores": "00007 - <PERSON> - <PERSON>", "Depart_96_GM_MISC": "96 GM MISC", "Depart_Fuel": "FUEL", "Charcoal": "Charcoal and Firelogs", "FLG": "~FLG", "Date_12_28_2022": "12/28/2022", "deleteText": "Are you sure you want to DELETE? Click 'OK' to delete or 'Cancel' to abort?", "pogDeleteMsg": "Planogram(s) removed successfully", "Division_014": "Division 014", "Header_Pog": "Pog", "Header_scheduleType": "Schedule Type", "Header_Type": "Type", "Header_Activation": "Act", "Header_2_Weeks": "2wk", "Header_ScheduledStr": "S-Str", "Header_Scheduled": "Sched", "Header_Action": "Action", "Pog_Header": "Pog ID", "Pog_Name": "Name", "Pog_DBKey": "DB<PERSON>ey", "Pog_DBStatus": "DbStatus", "Header_Refresh": "Refresh", "Header_CAOEventID": "CAO Event ID", "Event_Activation_Yes": "Yes", "Event_2Weeks_Yes": "Yes", "Header_Strips": "Strips", "Header_Tags": "Tags", "Header_UPL": "UPL", "UPL_ATTN_No": "ATTN No", "Event_refreshType": "DAB", "Event_refreshType_DAD": "DAD", "Event_refreshType_D": "D", "Event_refreshType_B_D_APP": "B-D-APP", "Event_ID": "Event Id", "Event_Strips_S_R": "S - R", "Event_Tags_O_RES": "O - RES", "Event_Tags_O_OFC": "O - OFC", "Event_Tags_S_RES": "S - RES", "Pog_Key_Header": "POG <PERSON>", "Space_Station_Name_Header": "Space Station Name", "Space_Station_Version_Description": "Space Station Version Description", "POG_Status_Header": "POG Status", "Digital_Refresh_ClickList": "digitalRefresh", "Vestcom_Refresh_Tag_Sequence": "vestcomRefresh", "Allocation": "allocationRefresh", "APP_Refresh": "appRefresh", "ReleaseEvent_Edit": "Edit", "ReleaseEvent_Delete": "Delete", "ReleaseEvent_Corporate": "Corporate", "ReleaseEventMsg": "Event has been RELEASED", "CheckBox_RequestShelfStrips": "Request Shelf Strips", "CheckBox_SystemHeading": "System Heading", "EventShelf_Strip_type_R": "R", "Event_strip_type_Store": "Store", "EventMaintanceUrl": "EventMaintenance", "EMHeaderlength": 33, "resetCompletion_Search": "Search", "resetCompletion_SelectDivision": "Select Division", "resetComplete_Yes": "Yes", "resetComplete_No": "No", "resetComplete_NoResponse": "No Response", "resetComplete_SelectAll": "Select All", "resetComplete_Deleted": "Deleted", "dropdown_2_Items_Selected": "2 Items Selected", "dropdown_4_Items_Selected": "4 Items Selected", "resetCompleteLoc_014_00359": "014 - 00359", "resetCompleteCommodity_01_GROCERY027_BEVERAGE_ENHANCERS": "01-GROCERY - 027-<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>ERS", "index_0": "0", "index_11": "11", "index_12": "12", "Number_2": 2, "Number_0": 0, "Number_1": 1, "Number_3": 3, "Number_4": 4, "Number_7": 7, "Number_8": 8, "Index_1": 1, "Index_2": 2, "Period_Number_9": "9", "checkbox_attribute_status": "ui-chkbox-box ui-widget ui-state-default", "StoreReview_Delete": "Delete", "ResetComplete_Search": "Search", "ResetComplete_Clear_Filters": "Clear Filters", "Display_Space": "Display Space", "White_Space": "White Space", "SelectAll": "Select All", "corpEvent_Released": "RELEASED", "addEventDropDown": ["SFP Event", "SFP Activation", "SFP Refresh", "SFP Tags", "UPL Orders"], "divisionlist": ["Cincinnati Operating Division - 014", "Atlanta Operating Division - 011", "Columbus Operating Division - 016", "Michigan Operating Division - 018", "Select All"], "divisionlist1": ["Cincinnati Operating Division - 014", "Columbus Operating Division - 016", "Select All"], "locationlist": ["014 - 00305", "016 - 00128", "Select All"], "departmentlist": ["03-<PERSON><PERSON>", "01-GROCERY", "Select All"], "ErrorCodeList": ["5 - UPC is not a base UPC", "3 - <PERSON><PERSON> has invalid status code", "4 - <PERSON><PERSON> not found", "Select All"], "Commoditylist": ["01-GROCERY - 000-SHELF EXTENDERS", "01-GROCERY - 001-CANNED FRUIT/FRUIT BOWLS", "Select All"], "Commodity_SHELF_EXTENDERS": "01-GROCERY - 000-SHELF EXTENDERS", "Commodity_FRUIT_BOWLS": "01-GROCERY - 001-CANNED FRUIT/FRUIT BOWLS", "Commodity_CAN_VEGETABLES": "01-GROCERY - 002-<PERSON>N VEGETABLES - SHELF STABLE", "Commodity_CAN_BEANS": "01-GROCERY - 003-CAN BEANS", "Commodity_MEAT_SHELF_STABLE": "MEAT - SHELF STABLE", "Event_Manger_storeList": ["351", "395", "429"], "PogCommoditylist": ["013-SPICES & EXTRACTS - 01-GROC-ALL OTHER - 01-GROCERY", "014-MOLASSES/SYRUPS/PANCAKE MIXES - 01-GROC-ALL OTHER - 01-GROCERY "], "ResetComplete_No_Yes": ["No", "Yes", "Select All"], "ResetComplete_No_Deleted": ["No", "Deleted", "Select All"], "ResetCompleteCommodity_01_GROCERY_027_BEVERAGE_ENHANCERS": ["01-GROCERY - 027-<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>ERS", "Select All"], "PogCommodity_PogStatus": ["Live"], "Department_01_GROCERY": "01-GROCERY", "Commodity_01_GROCERY_000_SHELF_EXTENDERS": "01-GROCERY - 000-SHELF EXTENDERS", "Week_3_35": "3 (35)", "Completed_columnHeader": "Completed", "NotCompleted_columnHeader": "Not Completed", "Deleted_ColumnHeader": "Deleted", "Not_Completed": "Not Completed", "storeReviewRows": "storeReviewRows", "ng_star_inserted": "ng-star-inserted", "deleted_ng_star_inserted": "deleted ng-star-inserted", "Fiscal_Week": "Fiscal Week", "Save_Schedule": "Save Schedule", "checkBox_RequestShelfStrips": "ne_shelfstrips", "checkBox_RequestTags": "ne_FullShelfTags", "checkBox_NewItemsOnly": "ne_NewItemsOnly", "checkBox_ActivateCAO2weeksbeforeEventSchedule": "twoWeekBox", "checkBox_SystemHeading": "ne_system_heading", "checkBox_Digital_Refresh_ClickList": "digitalRefresh", "checkBox_Enable_Auto_Activation": "autoActivationBox", "checkBox_CAO_2week_EventSchedule": "twoWeekBox", "radioBtn_Office": "Office", "radioBtn_Store": "Store", "radioBtn_MaintenanceStripsChangesOnly": "C", "radioBtn_aisleDelete": "aisledelete", "radioBtn_aisleRebuild": "aislerebuild", "radioBtn_pog": "pog", "field_userHeading": "user_heading", "field_sets": "sets", "SelectedPOGSheaderlist": ["Space Station Name", "Space Station Version Description", "POG Status", "POG Pending Date", "POG Live Date", "POG <PERSON>", "Enterprise Project Identifier", "Dept", "Sub-Dept", "Commodity", "Commodity Group", "PDF"], "REF_Type_B_V_D_APP": "B-V-D-APP", "REF_Type_B_DAD_APP": "B-DAD-APP", "REF_Type_B_V_APP": "B-V-APP", "Filters_text": "Filters", "DIV": "DIV", "selectedDivision_eventMaintenance": "selectedDivision", "selectedDepartment_eventMaintenance": "selectedDepartment", "selectedSubDept_eventMaintenance": "selectedSubDept", "selectedCommodity_eventMaintenance": "selectedCommodity", "selectedWeek_eventMaintenance": "selectedWeek", "eventID": "eventID", "Filters": "Filters", "Sched_Store": "SchedStore", "Schedul_Div": "Schedule Div", "Index_0": 0, "Event_Creation": "Event Creation", "Event_Creation_Successful": "Event Creation Successful", "Corporate_Event_Split": "Corporate Event Split", "Sched": "Sched", "plus_Stores_header": "# Store", "plus_Stores_header_CAOID": "CAO Event ID", "plus_Stores_header_RevBy": "Rev By", "plus_Stores_header_RevisedDate": "Rev Date", "plus_Stores_header_CreatedDate": "Created Date", "PogString": "PogString", "Filters_Depart_15_DELI": "15-DELI", "Filters_selectedSubDept": "selectedSubDept", "Filters_Event_Desc": "Event Desc", "Commodity_15_DELI_451_DELI_MEAT_BULK": "15-DELI - 451-DELI MEAT: BULK", "Filters_selectedCommodity": "selectedCommodity", "select_EventType_selectedEventType": "selectedEventType", "header_Event_Id": "Event Id", "SFP_Activation": "SFP Activation", "SFP_Refresh": "SFP Refresh", "Event_Strips_S_V": "S - V", "Event_Strips_S_I": "S - I", "Event_Strips_S_C": "S - C", "Event_Strips_S_F": "S - F", "Event_Strips_O_V": "O - V", "Event_Strips_O_I": "O - I", "Event_Strips_O_C": "O - C", "Event_Strips_O_F": "O - F", "Event_Strip_V": "ne_shelfstrips", "Event_Strip_I": "I", "Event_Strip_C": "C", "Event_Strip_F": "F", "Checkbox_Office_ne_event_strip_typ": "ne_event_strip_typ", "NO_SCHEDULES_ASSIGNED": "NO SCHEDULES ASSIGNED FOR THIS EVENT", "NO_STORES_SELECTED": "NO STORES SELECTED FOR SCHEDULED WEEK", "Please_assign_a_schedule": "Please assign a schedule for this event to proceed with store selection", "Please_Select_Stores": "Please Select Stores For Every Scheduled Week To Proceed with Pog Selection.", "Please_Select_Stores_UPC": "Please Select Stores For Every Scheduled Week To Proceed with Upload UPC(s).", "Please_Select_Stores_NPE": "Please Select Stores For Every Scheduled Week To Proceed with Pog Selection.", "Please_select_at_least_one_store": "Please select at least one store to proceed further", "OK_Text": "Ok", "Save_Schedule_Failed_message": "Save Schedule Failed", "Event_Type": "Event Type", "clearEventType": "clearEventType", "Select_Event_Type": "Select Event Type", "Division_text": "Division", "selectedDivision_text": "selectedDivision", "clearDivision_text": "clearDivision", "clearDepartment_text": "clearDepartment", "clearSubDepartment_text": "clearSubDepartment", "clearCommodity_text": "clearCommodity", "clearPeriodWeek_text": "clearPeriodWeek", "Select_Division_text": "Select Division", "Select_Department_text": "Select Department", "Select_Sub_Department_text": "Select Sub Department", "Select_Commodity_text": "Select Commodity", "Select_Period_Week_text": "Select Period Week", "Department_15_DELI": "15-DELI", "Sub_Depart_15_DELI_49_SPECIALTY_CHSE": "15-DELI - 49-SPECIALTY CHSE", "Sub_Department_text": "Sub Department", "Commodity_15_DELI_462_SPECIALTY_CHEESE_PRE_PACK": "15-DELI - 462-SPECIALTY CHEESE PRE PACK", "Commodity_text": "Commodity", "PeriodWeek_2022_Week_4": "2022 - Week 4", "Period_Week_text": "Period Week", "attribute_ng_reflect_model": "ng-reflect-model", "inputString_sfpEvent1234560be0b01506": "sfpEvent1234560be0b01506", "noRecordFound_alert": "No Records found for specified Criteria", "enterAllFields_alert": "Please enter the values for Location, Department, ATTNTO and fpStatus (fpstatus value should be either 1, 2, 3 or 7 only)", "reset_completion": "EventMaintanence/resetCompletion", "div_Columbus_Division_016": "016 - Columbus Division", "releaseEventSpinnerText": "Releasing Event...", "sildePanel_NPE_KOM_Yes": "KOMPASS\nupdate\nYes", "sildePanel_UPC_KOM_Yes": "KOMPASS\nactivation\nYes", "sildePanel_REF_KOM_Yes": "KOMPASS\nnull\n-", "sildePanel_014_1_1_103": "014\n1\n1\n103", "sildePanelNpe_014_2_2_111": "014\n2\n2\n106", "sildePanel_014_2_2_100": "014\nYes(1)\n2\n2\n100", "sildePanel_014_2_2_103": "014\nYes(1)\n2\n2\n106", "sildePanelUpc_014_YES_2_2_102": "014\nYes(1)\n2\n2\n103", "sildePanelRef_014__1_1_104": "014\n-\n1\n1\n107", "slidePanelSchedules": "scheduleTab", "slidePanelPOGs": "planogramTab", "slidePanelStores": "storeTab", "slidePanelColumnStores": "Stores", "slidePanelColumnStoresDiv": "Division", "slidePanelStoreHeader": "# of Stores", "testUserName": "Svc5940webapps", "testUserId": "svc5940WebAppS", "lastModifiedBy": "Last Modified By", "status": "Status", "createdBy": "Created By", "testUserNameFullName": "Stage, Automation", "testUserNameLastName": "Stage", "pogSearchOption": "035-WATER P02", "storesSearchOption": "351 353 370", "fiscalWeekAlert": "This week and year already exist in the Schedule List. If you decide to update it, this week will be merged with the existing schedule.", "Division_016": "Division 016", "sildePanel_Corp_KOM_Yes": "KOMPASS\nnew item introduction\nYes", "slidePanel_Corp_Div014_Schedule": "014\n-\n1\n1\n107", "slidePanel_Corp_Div016_Schedule": "016\n-\n1\n1\n115", "SFP_EVENT": "SFP Event", "EventMaintenanceResetCompletion": "EventMaintenance/resetCompletion", "attributeDisabled": "disabled", "homeTitle": "SFP Apps", "storeReviewTableName": "storeReviewTable", "commodityRecapTableName": "commodityRecapTable", "one_item_selected": "1 Item Selected", "select_store": "Select Store", "Reset_Id": "Reset Id", "div_021_Central_Operating_Division": "021 - Central Operating Division", "div_025_Delta_Operating_Division": "025 - Delta Operating Division", "select_location_text": "Select Location", "reset_completion_update_button_text": "Are you sure you want to UPDATE? Click 'Proceed' to update or 'Cancel' to abort?", "reset_complete": "resetComplete", "reset_Complete_Yes_No": ["Yes", "No", "Select All"], "request_table_popup_type_success": "success", "request_table_popup_type_warning_event_exists": "warning_event", "request_table_popup_type_warning_fpstatus": "warning_fpstatus", "select_date": "Select Date", "select_category_name": "Select Category Name", "select_product_group": "Select Product Group", "processing_complete": "processing complete", "stripErrorToolTipText": "Strips will not be delivered for one or more scheduled weeks. Remove strip request or adjust the schedule(s).", "releaseEventWarnning": "This event has items that may need your attention. Ensure all issues are resolved before releasing the event.", "modify_success_message": "Success!\nReset Completion Information Updated.", "editModeMessage": "You are in the edit mode. Save or clear changes before changing the page.", "changedSavedMessage": "Changes Saved", "storeManagerPostponedOption": "Store Manager Postponed", "selectOneMessage": "Select One", "zero_records_found": "Zero Records Found", "available_for_reset": "Available for reset", "yes_cancel_resets": "Yes, cancel resets(s)", "no_keep_resets": "No, keep reset(s)", "resetCancelledSuccessMessage": "Selected reset(s) were successfully canceled.", "no_keep_it": "No, keep it", "yes_delete_it": "Yes, delete it", "storeGroup_successfully_created_message": "Store Group was successfully created.", "groupName_exists_message": "This group name already exists. Change the name of your group.", "store_group_updated_message": "Store Group Updated", "store_unmatched_message": "There are no other groups matching your current store selection in this division. You can proceed with creating a new store group without any duplication.", "store_matched_message": "Selected stores match the stores of other group(s). You can use the existing group or proceed to create a duplicate group version.", "storeSelection_Override_Warning_message": "Store Selection Override Warning", "groupCategory_must_be_selected_message": "Group category must be selected.", "store_not_selected_message": "At least one store has to be selected to create a new group."}