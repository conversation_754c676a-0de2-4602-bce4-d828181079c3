const Page = require("../../../GlobalObjectRepository/page");
let rowNumber, columnIndex,option, rowIndex, headerIndex;

class SpaceStationVersionDescriptionPage extends Page {
    get btnAddVersion()                             { return $('button[aria-label="Add Version"]')}
    get headingAddVersion()                         { return $('div.heading-m')}
    get addVersionSelectDropdown()                  { return ('div>[name="?DropDown"] > div > div > kds-icon-chevron-down > kds-icon > svg')}
    get filterSelectDropdown()                      { return ('div>[id="?Dropdown"] > div > div > kds-icon-chevron-down > kds-icon > svg')}
    get filterSelectDropdownValue()                 { return $(`div>[id="${option}Dropdown"] > div >div`)}
    get addVersionDropdownList()                    { return $$('li[class*="ers-list-item items overlay-nc"]') }
    get filterDropdownList()                        { return $$('li[class*="menu-item ers-tree-list-parent"]') }
    get inputVersionDesc()                          { return $('#formVersionDescInput')}
    get inputVersion()                              { return $('#formVersionInput')}
    get btnAdd()                                    { return $('button[aria-label="Add"]')}
    get btnFilters()                                { return $('button[aria-label="Filters"]')}
    get inputFilterName()                           { return ('#?Dropdown > div > div:nth-child(2) > div > mx-search-input input')}
    get spaceStationHeaders()                       { return $$('#versionDescTable>div>div>table>thead>tr>th')}
    get ossTableHeaderList()                        { return $$('th[class="ui-sortable-column"]')}
    get nextButtonPagination()                      { return $('#versionDescTable>div>p-paginator>div>a.ui-paginator-next') }
    get columnValueList()                           { return $$(`#versionDescTable>div>div>table>tbody>tr>td:nth-child(${columnIndex})`)}
    get deleteRowButton()                           { return $(`#versionDescTable>div>div>table>tbody>tr:nth-child(${rowNumber})>td>div>#deleteRowButton>button>kds-icon-trash>kds-icon>svg`)}
    get editRowButton()                             { return $(`#editRowButton > button > kds-icon-edit > kds-icon > svg`)}
    get yesDeleteBtn()                              { return $('button[aria-label="Yes, Delete"] >span') }
    get yesEditBtn()                                { return $('button[aria-label="Yes, Edit"] >span') }
    get rowColumnValueList()                        { return $(`#versionDescTable>div>div>table>tbody>tr:nth-child(${rowNumber})>td:nth-child(${columnIndex})`)}
    get rowColumnValueListInput()                   { return $(`#versionDescTable > div > div > table > tbody > tr:nth-child(1) > td:nth-child(${columnIndex}) > p-celleditor > input`)}
    get saveEditBtn()                               { return $('#saveEditButton > button > kds-icon-success > kds-icon > svg') }
    get tableValue()                                { return $('div#page tbody') }
    get nthColumnList()                             { return $$(`#versionDescTable > div > div > table > tbody > tr > td:nth-child(${columnIndex}`)}
    get linkClearFilter()                           { return (`mx-multi-select[id="?Dropdown"]> div > div.mx-dropdown-input-wrapper.compact > kds-icon-close > kds-icon > svg`)}
    get clearAllFilterBtn()                         { return $('kds-button[id="clearAllFiltersButton"]>button')}
    get tableFirstData()                            { return $('#versionDescTable>div>div>table>tbody>tr:nth-child(1)>td:nth-child(1)')}
    get addVersionUniqueErrorMessage()              { return $('app-add-version-modal-form>div>form>div>kds-message>kds-text>span') }
    get entryAlreadyExistMessage()                  { return $('kds-message>kds-text>span')}
    get singleColumnValue()                         { return $(`#versionDescTable>div>div>table>tbody>tr:nth-child(1)>td:nth-child(${columnIndex})`)}
    get ssvTableSecondPageBtn()                     { return $('#versionDescTable>div>p-paginator>div>span>a:nth-child(2)')}
    get staticDepartmentColumnValue()               { return $('tbody>tr>td:nth-child(2)')}
    get staticCommodityColumnValue()                { return $('tbody>tr>td:nth-child(3)')}
    get staticVersionIdColumnValue()                { return $('#versionDescTable > div > div > table > tbody > tr:nth-child(1) > td:nth-child(4)')}
    get staticVersionDescColumnValue()              { return $('tbody>tr>td:nth-child(6)')}
    get addVersionUniqueMessage()                   { return $('app-add-version-modal-form>div>form>div>kds-message>kds-text>span') }
    get firstRow()                                  { return $(`#versionDescTable>div>div>table>tbody>tr:nth-child(1)>td:nth-child(${headerIndex})`)}
    get singleDepartColumnValue()                   { return $('#versionDescTable>div>div>table>tbody>tr>td:nth-child(2)')}
    get enterValue()                                { return $('#formVersionDescInput')}
    get pageBreadCrumb()                            { return $('#page>app-breadcrumb>div>ol>li.ng-star-inserted>a')}
    get sathFilterDropdown()                        { return ('#?Dropdown>div>div')}
    get sathFilterDropdownList()                    { return ('#?Dropdown>div>div>div> mx-multi-select-list>div>ul>div>mx-menu-item>li>div>div.w-full>kds-label>label>div')}
    get btnSathFilters()                            { return $('#filterButton>button')}
    get btnClearAllFilters()                        { return $('#clearAllFiltersButton>button')}
    get btnSearch()                                 { return $('#searchButton>button')}
    get getFirstRowVersionIdValue()                 {return $('#versionDescTable>div>div>table>tbody>tr:nth-child(1)>td:nth-child(4)')}
    get pogInfoTableHeaders()                       { return $$('#pogInfoTable>div>div>div>div>div>table>thead>tr>th')}
    get columnText()                                { return $(`div#page tbody tr:nth-child(1)>td:nth-child(${columnIndex})`) }
    get columnLink()                                { return $(`div#page tbody tr:nth-child(1)>td:nth-child(${columnIndex})>a`) }
    get pogInfoTable()                              { return $('#pogInfoTable>div>div>div>div>table>tbody') }
    get pogInfoTableNthColumnList()                 { return $$(`#pogInfoTable>div>div>div>div>table>tbody>tr>td:nth-child(${columnIndex}`)}
    get deptDropdown()                              { return $('#formDeptDropdown>div>div>ers-single-select-list>ul>cdk-virtual-scroll-viewport>div>ers-single-select-list-item>li>label') }
    get inputVersionName()                          { return ('#form?Dropdown>div>div>div>ers-filter-search>div>div>input')}
    get versionSelectDropdown()                     { return ('#form?Dropdown>div>div>kds-icon-chevron-down>kds-icon>svg')}
    get closeBtnInfoTable()                         { return $('#closeModalIcon>button') }
    get noPogMessage()                              { return $('#pogInfoTable>div>div>div>div>table>tbody>tr>td>span') }
    get getFirstRowDepartmentValue()                {return $('#versionDescTable>div>div>table>tbody>tr:nth-child(1)>td:nth-child(2)')}
    get ossTable()                                  { return $('#versionDescTable>div>div>table>tbody')}
    get ossFirstRowList()                           { return $$('#versionDescTable>div>div>table>tbody>tr:nth-child(1)>td')}
    get ossNextButtonPagination()                   { return $('#versionDescTable>div>p-paginator>div>a.ui-paginator-next')}
    get ossTableFirstRow()                          { return $('#versionDescTable>div>div>table>tbody>tr:nth-child(1)')}
    get ossRowList()                                { return $$(`#versionDescTable>div>div>table>tbody>tr`)}
    get ossTableNthColumns()                        { return $$('#versionDescTable>div>div>table>tbody>tr:nth-child('+rowIndex+')>td')}
    get ossPageOneBtnPagination()                   { return $('#versionDescTable>div>p-paginator>div>span>a:nth-child(1)')}
    get activePaginationNum()                       { return $('#versionDescTable>div>p-paginator>div>span>a.ui-paginator-page.ui-paginator-element.ui-state-default.ui-corner-all.ng-star-inserted.ui-state-active') }
    get versionErrorMessage()                       { return $('kds-message>kds-text>span') }
    get cancelBtn()                                 { return $('#cancelButton>button') }
    get sathUiDropdownList()                        {return ('#?Dropdown>div>div>div>div>mx-multi-select-list>ul>div>mx-menu-item>li>div>div.w-full')}

    async addVersion(dept,commodity,versionID,versionDesc) {
        await super.waitBrowserToLoadPage(2000);
        await super.clickOn(this.btnAddVersion)
        await this.headingAddVersion.waitForDisplayed({ timeout: 20000 }, 'Error: Add Version window did not appear before timeout')
        await this.selectDropdownValue("department", dept)
        await super.waitBrowserToLoadPage(2000);
        await this.selectDropdownValue("commodity", commodity)
        await this.inputVersion.setValue(versionID)
        await this.inputVersionDesc.setValue(versionDesc)
        await super.clickOn(await this.btnAdd)
    }

    async filterRecord(dept,commodity,versionID) {
        await super.clickOn(await this.btnFilters)
        if(dept!=='') {
            await this.setValues("department", dept)
        }
        if(commodity!=='') {
            await this.setValues("commodity", commodity)
        }
        await super.waitBrowserToLoadPage(2000);
        if(versionID!=='') {
            await this.setValues("version", versionID)
        }
    }

    async selectDropdownValue(locator,value) {
        let dropdown = $(this.addVersionSelectDropdown.replace('?DropDown', locator))
        await dropdown.waitForExist({timeout: 59000})
        await super.clickOn(await dropdown)
        await browser.pause(2000)
        let text = await this.addVersionDropdownList
        for (let index = 0; index < text.length; index++) {
            let appList = text[index]
            if (await text[index].getText() === value) {
                await super.waitBrowserToLoadPage(2000);
                await text[index].scrollIntoView()
                await super.waitBrowserToLoadPage(2000);
                await appList.click();
                break;
            }
        }
        !expect(super.doIsDisplay(this.addVersionDropdownList))
    }

    async setValues(LocatorName, selectValue) {
        await super.waitBrowserToLoadPage(2000)
        let dropdown = await $(this.filterSelectDropdown.replace('?', await LocatorName));
        await dropdown.waitForExist({timeout: 59000})
        await super.clickOn(await dropdown);
        await super.waitBrowserToLoadPage(1000)
        for (let len = 0; len < await selectValue.length; len++) {
            let setText = await $((this.inputFilterName).replace('?', await LocatorName));
            await super.waitBrowserToLoadPage(1000)
            await setText.setValue(await selectValue[len]);
            await super.waitBrowserToLoadPage(2000);
            let text;
            text = await $$((this.sathUiDropdownList).replace('?', await LocatorName));
            for (let valueLength = 0; valueLength < await selectValue.length; valueLength++) {
                for (let index = 0; index < text.length; index++) {
                    let appList = await text[index]
                    if (await (await text[index]).getText() === await selectValue[valueLength]) {
                        await super.waitBrowserToLoadPage(2000)
                        await appList.click();
                        break;
                    }
                }
            }
        }
        await browser.keys('Escape')
        //!expect(super.doIsDisplay(this.filterDropdownList))
    }

    async getHeaderIndex(header) {
        let headerData = await this.ossTableHeaderList;
        let headerList = await this.getArrayElementsText(headerData);
        let headerIndex=headerList.indexOf(header)+1
        return headerIndex;
    }

    /* edit or delete record based on the table header and value */
    async performActions(action, header, description) {
        const getActionButton = async (action) => {
            if (action === 'delete') return await this.deleteRowButton;
            if (action === 'edit') return await this.editRowButton;
            throw new Error(`Unsupported action: ${action}`);
        };
        const processRow = async (columnListText, description, action) => {
            for (let len = 0; len < columnListText.length; len++) {
                if (await columnListText[len] === description) {
                    const actionButton = await getActionButton(action);
                    await actionButton.waitForExist({ timeout: 9000 }, 'Element does not exist in given time');
                    await actionButton.click();
                    return true;
                }
            }
            return false;
        };
        let nextButton = await this.nextButtonPagination;
        columnIndex = await this.getHeaderIndex(header) + 1;
        while (await nextButton.isEnabled) {
            const nextButtonClassName = await this.nextButtonPagination.getAttribute("class");
            const columnList = await this.columnValueList;
            const columnListText = await this.getArrayElementsText(columnList);
            if (await processRow(columnListText, description, action)) break;
            await super.clickOn(nextButton);
            await super.waitBrowserToLoadPage(10000);
            if (nextButtonClassName.includes('ui-state-disabled')) break;
        }
    }

    async confirmDelete() {
        await super.clickOn(await this.yesDeleteBtn)
        await browser.keys('Enter');
    }

    async saveEdit() {
        await super.clickOn(await this.saveEditBtn)
        await super.clickOn(await this.yesEditBtn)
        await browser.keys('Enter');
    }

    async getColumnValue(key, KeyValue,returnValue ) {
        let nextButton = await this.nextButtonPagination;
        let result;
        while (nextButton.isEnabled) {
            columnIndex = await (await this.getHeaderIndex(key)) + 1
            let columnList = await this.columnValueList;
            let nextButtonClassName = await this.nextButtonPagination.getAttribute("class");
            let columnListText = await this.getArrayElementsText(await columnList);
            columnIndex = await this.getHeaderIndex(returnValue) + 1
            for (let len = 0; len < columnListText.length; len++) {
                if (await columnListText[len] === KeyValue) {
                    rowNumber = len + 1;
                    let deleteRequestedDate = await this.rowColumnValueList;
                    await deleteRequestedDate.waitForExist({timeout: 4000}, 'Element is not clickable in given time')
                    result = deleteRequestedDate.getText();
                }
            }
            await super.clickOn(await nextButton);
            await super.waitBrowserToLoadPage(10000);
            if (nextButtonClassName.includes('ui-state-disabled')) {
                break;
            }
        }
        return result
    }

    async updateRecord(column,setValue) {
        columnIndex = await this.getHeaderIndex(column)
        columnIndex = columnIndex + 1;
        let inputField=await this.rowColumnValueListInput;
        await inputField.waitForExist({timeout:30000});
        await inputField.setValue(setValue);
    }

    /*dynamically finds column tile and loops the column values*/
    async getColumnList(header, tableLocator){
        await this.waitForTableExist(tableLocator);
        await super.waitBrowserToLoadPage(2000)
        columnIndex = await this.getHeaderIndex(header) +1;
        let columnList = await this.nthColumnList;
        let columnTextList = await this.getArrayElementsText(columnList);
        return columnTextList;
    }

    async clearFilter(dropdownName){
        let dropdownLocator = await $(this.linkClearFilter.replace('?', dropdownName))
        await super.clickOn(dropdownLocator)
    }

    async getDropDownValue(dropdown){
        option=dropdown
        return super.GetText(await this.filterSelectDropdownValue)
    }

    // Extracting selected substring based on array index
    async extractingSubstringsFromString(string, arrayIndex, start,end){
        let subStrings = await string.split(':');
        return  subStrings[arrayIndex].substring(start, end);
    }

    async getSingleColumnValue(header){
        let headerIndex = await this.getHeaderIndex(await header);
        columnIndex = headerIndex + 1;
        let columnValue = await this.singleColumnValue;
        let resultText = await columnValue.getText();
        return resultText;
    }

    async isEmpty(value){
        return ( !value || await value.length === 0);
    }

    async clickFilterBtn() {
        await super.clickOn(await this.btnFilters)
        await browser.keys('Enter');
    }

    async getCorrespondingColumnValue(referencerHeader, returnHeader, value) {
        let finalValue = [];
        let referenceNum = await this.getHeaderIndex(referencerHeader);
        headerIndex =  referenceNum + 1;
        let row = await this.firstRow;
        if(await row.getText() === value) {
            let returnNum = await this.getHeaderIndex(returnHeader);
            headerIndex = returnNum + 1;
            let text = await this.firstRow;
            finalValue.push(await text.getText())
        }
        return finalValue;
    }

    async clickAddVersion() {
        await super.waitBrowserToLoadPage(2000);
        await super.clickOn(this.btnAddVersion)
    }

    async getDropDownList(locator) {
        let dropdown = await $(this.addVersionSelectDropdown.replace('?DropDown', locator));
        await super.clickOn(await dropdown);
        await super.waitBrowserToLoadPage(3000)
        let text = await this.addVersionDropdownList;
        let appList = [];
        for (let index = 0; index < text.length; index++) {
            appList.push(await text[index].getText())
        }
        return appList
    }

    async returnNumOfArrayElements(arrayList, num, thresholdNum){
        let finalArray = [];
        for(let index=0; index<arrayList.length; index++){
            if( arrayList[index] >= thresholdNum){
                let longList =  await arrayList[num];
                finalArray.push(await longList)
            } else {
                let shortList =  await arrayList[index];
                finalArray.push(await shortList)
            }
        }
        return finalArray ;
    }

    async readDropdownInputBox(dropdownName){
        let dropdown = await $(this.sathFilterDropdown.replace('?', dropdownName));
        await dropdown.waitForExist({timeout: 6000}, 'Element did not display on time !')
        return await dropdown.getText();
    }

    async clearAllFilter(){
        await super.clickOn(await this.btnClearAllFilters)
    }

    async clickSathFilter(){
        await super.clickOn(await this.btnSathFilters)
    }

    async getFilterDropDownList(locator) {
        let dropdown = await $(this.sathFilterDropdown.replace('?', locator));

        await super.clickOn(await dropdown);
        await super.waitBrowserToLoadPage(2000)
        let text = await $$(this.sathUiDropdownList.replace('?', locator));
        let appList = [];
        for (let index = 0; index < text.length; index++) {
            appList.push(await text[index].getText())
        }
        return appList
    }

    async selectFilterDropdownValue(locator,value) {

        await super.clickOn(await this.clearAllFilterBtn);
        let dropdown = $(this.filterSelectDropdown.replace('?', locator))
        await super.clickOn(await dropdown)
        await browser.pause(2000)
        let text = $((this.sathFilterDropdownList).replace('?', locator));
        for (let index = 0; index < text.length; index++) {
            let appList = text[index]
            if (await text[index].getText() === value) {
                await super.waitBrowserToLoadPage(2000);
                await text[index].scrollIntoView()
                await super.waitBrowserToLoadPage(2000);
                await appList.click();
                await super.clickOn(await this.clearAllFilterBtn);
                break;
            }

        }
    }

    async getOssAllUiData(pageNum) {
        await this.waitForTableExist(await this.ossTable);
        let columns = await this.ossFirstRowList;
        let uiData=[]
        let nextButton = await this.ossNextButtonPagination;
        let startValue
        if(await this.ossTableFirstRow.isExisting()){
            startValue=1
        }else{
            startValue=0
        }
        while (nextButton.isEnabled) {
            // between switching pages, nextButton class attribute becomes "disabled" which stops pagination on the first page. Thus must have this wait.
            await super.waitBrowserToLoadPage(4000)
            let nextButtonClassName = await this.ossNextButtonPagination.getAttribute("class")
            for (rowIndex = 1; rowIndex <= await this.ossRowList.length; rowIndex++) {
                let uiRowData = []
                for (columnIndex = startValue; columnIndex < columns.length; columnIndex++) {
                    let data = await this.ossTableNthColumns[columnIndex]
                    uiRowData.push(await data.getText())
                }
                uiData.push(uiRowData)
            }
            await super.clickOn(nextButton)
            await super.waitBrowserToLoadPage(2000)
            await this.ossPageOneBtnPagination.waitForClickable({ timeout: 59000 }, 'Error: Page one did not appear before timeout')
            if (nextButtonClassName.includes("ui-state-disabled") || await this.activePaginationNum.getText() === pageNum) {
                break;
            }
        await browser.keys('Escape');
        }
        return uiData
    }

    async getLinkTextGeneric(tableLocator, key) {
        await this.waitForTableExist(tableLocator);
        let headerData = await this.spaceStationHeaders;
        await super.waitBrowserToLoadPage(4000)
        let linkText
        for (let index = 0; index < headerData.length; index++) {
            if ( (await headerData[index].getText() ) === (key) ) {
                columnIndex = index + 1;
                linkText = await this.columnText.getText()
                if (await this.columnText.isClickable()) {
                    await super.clickOn(await this.columnLink)
                }
                break
            }
        }
        return linkText
    }

    async getHeaderIndexGeneric(specificHeaderName, tableHeaders) {
        let headerData = await tableHeaders;
        let headerList = await this.getArrayElementsText(headerData);
        let headerIndex = headerList.indexOf(specificHeaderName)+1
        return headerIndex;
    }

    async getColumnListPogInfoTable( specificHeaderName, tableLocator){
        await this.waitForTableExist( tableLocator);
        await super.waitBrowserToLoadPage(6000)
        columnIndex = await this.getHeaderIndexGeneric(specificHeaderName, await this.pogInfoTableHeaders) ;
        let columnList = await this.pogInfoTableNthColumnList;
        let columnTextList = await this.getArrayElementsText(columnList);
        await super.clickOn(await this.closeBtnInfoTable);
        return columnTextList;
    }

    async setValuesDigitsOnly(Locator, selectValue) {
        let dropdown = $(this.versionSelectDropdown.replace('?', Locator))
        await dropdown.waitForExist({timeout: 59000})
        await super.clickOn(await dropdown)
        for (let len = 0; len < selectValue.length; len++) {
            let setText = $((this.inputVersionName).replace('?', Locator))
            await setText.waitForExist({timeout: 59000})
            await setText.setValue(selectValue[len])
            let text = await this.addVersionDropdownList
            await super.waitBrowserToLoadPage(2000)
            for (let valueLength = 0; valueLength < selectValue.length; valueLength++) {
                for (let index = 0; index < text.length; index++) {
                    let appList = await text[index]
                        if ( (await text[index].getText()).match(/\d/g).join("") === selectValue[valueLength]) {
                        await super.waitBrowserToLoadPage(3000)
                        await appList.click();
                        break;
                    }
                }
            }
        }
    }

    async addVersionDigitsOnly(dept, commodity, versionID, versionDesc) {
        await super.waitBrowserToLoadPage(6000);
        await super.clickOn(this.btnAddVersion)
        await this.headingAddVersion.waitForDisplayed({ timeout: 20000 }, 'Error: Add Version window did not appear before timeout')
        await this.setValuesDigitsOnly("Dept", dept)
        await super.waitBrowserToLoadPage(2000);
        await this.setValuesDigitsOnly("Comm", commodity)
        await this.inputVersion.setValue(versionID)
        await this.inputVersionDesc.setValue(versionDesc)
        await super.clickOn(await this.btnAdd)
    }

    async returnArrayElementsByOrder(array){
        let arr = [];
        for(let index=0; index< await array.length; index++){
            if(await array[index] === 'Live'){
                arr.push(array[index])
                break;
            } else if(await array[index] === 'Pending'){
                arr.push(array[index])
                break;
            } else if(await array[index] === 'Analysis'){
                arr.push(array[index])
                break;
            } else if(await array[index] === 'Merch Complete'){
                arr.push(array[index])
                break;
            } else{
                arr.push('Work in progress')
                break;
            }
        }
        return arr;
    }

}

module.exports = new SpaceStationVersionDescriptionPage();

