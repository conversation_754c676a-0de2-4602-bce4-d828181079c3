const Page = require("../../../GlobalObjectRepository/page");
const spaceStationPage  = require("../OneStopShopPO/spaceStationVersionDescription.page")

let rowIndex,columnIndex;
class SathThresholdMaintenancePage extends Page {

    get btnSearch()                               { return $('kds-button[id="searchButton"]>button')}
    get sathTable()                               { return $('p-table[id="sathThresholdTable"]>div>div>table>tbody')}
    get sathTableFirstRow()                       { return $('p-table[id="sathThresholdTable"]>div>div>table>tbody>tr:nth-child(1)')}
    get ossTableHeaderList()                      { return $$('table[class="p-datatable-table ng-star-inserted"]>thead>tr>th')}
    get sathColumnList()                           { return $$(`tbody[class="p-element p-datatable-tbody"]>tr>td:nth-child(${columnIndex})`)}
    get sathRowList()                              { return $$(`tbody[class="p-element p-datatable-tbody"]>tr`)}
    get sathEditBtnList()                          { return $$('kds-button[id="editRowButton"]>button')}
    get sathFirstRowList()                         { return $$('p-table[id="sathThresholdTable"]>div>div>table>tbody>tr:nth-child(1)>td')}
    get sathTableNthColumns()                      { return $$('p-table[id="sathThresholdTable"]>div>div>table>tbody>tr:nth-child('+rowIndex+')>td')}
    get sathPageOneBtnPagination()                 { return $('p-paginator[class="p-element ng-star-inserted"]>div>span>button:nth-child(1)')}
    get sathPageNumberBtn()                        { return $('p-dropdown[styleclass="p-paginator-rpp-options"]>div')}
    get activePaginationNum()                      { return $('p-paginator>div>span>button.p-ripple.p-element.p-paginator-page.p-paginator-element.p-link.ng-star-inserted.p-highlight') }
    get paginationDropdownBtn()                    { return $('div[class="p-paginator-rpp-options p-dropdown p-component"]>div+span')}
    get paginationDropdownList()                   { return $$('p-dropdownitem[class="p-element ng-star-inserted"]')}
    get sathEditCancelBtnList()                    { return ('kds-button[id="?Button"]')}
    get sathCancelEditBtnList()                    { return $$('kds-button[id="cancelEditButton"]>button>kds-icon-failure>kds-icon>svg')}
    get sathStatusToggleBtnList()                  { return $$('kds-toggle[id="statusToggleButton"]>input')}
    get sathStatusToggleTextList()                 { return $$('tbody>tr>td:nth-child(7)>p-celleditor>kds-label>label>span')}
    get sathSaveBtnList()                          { return $$('kds-button[id="saveEditButton"]>button')}
    get sathTopRowSingleColumnValue()              { return $(`p-table[id="sathThresholdTable"]>div>div>table>tbody>tr:nth-child(1)>td:nth-child(${columnIndex})`)}
    get saveAlertTextMainText()                    { return $('#updateSuccessMsg>h3')}
    get saveAlertText()                            { return $('#updateSuccessMsg>p')}
    get sathSingleHeaderValue()                    { return $(`thead[class="p-datatable-thead"]>tr>th:nth-child(${columnIndex})`)}
    get sathExcelDownloadList()                    { return $$('kds-button[id="downloadRowButton"]>button>kds-icon-download')}
    get sathExcelDownloadMsg()                     { return $('#downloadStatusMsg>kds-text>span')}
    get backButtonPagination()                     {return $('p-paginator[class="p-element ng-star-inserted"]>div>button:nth-child(1)')}
    get sathPageDropdownList()                     {return $$('li[role="option"]>span')}
    get sathPageDropdown()                         {return $('div[class="p-paginator-rpp-options p-dropdown p-component"]>div+span')}
    get sathNextButtonPagination()                 { return $('p-paginator>div>button.p-ripple.p-element.p-paginator-next') }
    get sathDivColumns()                           {return $$('tbody> tr > td:nth-child(2)')}
    get sathTableRowList()                         {return $$('p-table[id="sathThresholdTable"]>div>div>table>tbody>tr')}
    get zeroRecordsMessage()                       {return $('p-table[id="sathThresholdTable"] table>tbody>tr>td')}

    async clickSearch(){
        await super.clickOn(await this.btnSearch)
    }

    async getAllUIData(pageNum) {
        await this.waitForTableExist(await this.sathTable)
        let columns = await this.sathFirstRowList;
        let uiData=[]
        let nextButton = await this.sathNextButtonPagination;
        let startValue
        if(await this.sathTableFirstRow.isExisting()){
            startValue=1
        }else{
            startValue=0
        }
        while (nextButton.isEnabled) {
            let nextButtonClassName = await this.sathNextButtonPagination.getAttribute("class")
            for (rowIndex = 1; rowIndex <= await this.sathRowList.length; rowIndex++) {
                let uiRowData = []
                for (columnIndex = startValue; columnIndex < columns.length; columnIndex++) {
                    let data = await this.sathTableNthColumns[columnIndex]
                    uiRowData.push(await data.getText())
                }
                uiData.push(uiRowData)
            }
            await super.clickOn(nextButton)
            await super.waitBrowserToLoadPage(1000)
            await this.sathPageOneBtnPagination.waitForClickable({ timeout: 59000 }, 'Error: Page one did not appear before timeout')
            if (nextButtonClassName.includes("p-disabled") || await this.activePaginationNum.getText() === pageNum) {
                break
            }
        }
        return uiData
    }

    async getPaginationPageNumber(selectValue) {
        let pageNumDropdownBtn = await this.paginationDropdownBtn;
        await super.clickOn(pageNumDropdownBtn)
        await super.waitBrowserToLoadPage(3000)
        let paginationNumber = await this.paginationDropdownList;
        for (let index = 0; index < paginationNumber.length; index++) {
            let pageList = paginationNumber[index];
            if (await paginationNumber[index].getText() === selectValue) {
                await paginationNumber[index].scrollIntoView()
                await super.waitBrowserToLoadPage(2000)
                await pageList.click()
                break;
            }
        }
        await browser.keys('Escape')
        !expect(super.doIsDisplay(this.paginationDropdownList))
    }

    async sortByColumnHeader(columnHeader, dynamicIndex){
        await super.waitBrowserToLoadPage(3000);
        columnIndex = await this.getHeaderIndex(columnHeader)
        columnIndex = columnIndex + dynamicIndex;
        let singleColumnValue = await this.sathSingleHeaderValue
        await super.clickOn(await singleColumnValue)
    }

    async getHeaderIndex(header) {
        let headerData = await this.ossTableHeaderList;
        let headerList = await this.getArrayElementsText(headerData);
        let headerIndex=headerList.indexOf(header)+1;
        return headerIndex;
    }

    async sathEditRecord(editIndex, columnHeader) {
        await this.waitForTableExist(await this.sathTable);
        await super.waitBrowserToLoadPage(5000);
        let columnList = await $$(await this.sathEditCancelBtnList.replace('?', 'editRow'));
        await super.waitBrowserToLoadPage(3000);
        for (let index = 0; index < await (columnList.length - editIndex); index++) {
            let elements = await columnList[index];
            await super.waitBrowserToLoadPage(3000);
            await elements.click();
            await super.waitBrowserToLoadPage(3000);
            let statusColumnList = await this.sathStatusToggleBtnList;
            await this.clickArrayElement(statusColumnList)
            await super.waitBrowserToLoadPage(3000);
            let saveColumnList = await this.sathSaveBtnList;
            await this.clickArrayElement(saveColumnList)
        }
        await super.waitBrowserToLoadPage(5000)
        let text = await this.getTopColumnValue(columnHeader)
        return text;
    }

    async clickArrayElement(arrayList) {
        await super.waitBrowserToLoadPage(3000);
        for (let index = 0; index < arrayList.length; index++) {
            let elements = await arrayList[index];
            await super.waitBrowserToLoadPage(3000);
            await elements.click();
        }
    }

    async getTopColumnValue(columnHeader) {
        await super.waitBrowserToLoadPage(3000);
        columnIndex = await this.getHeaderIndex(columnHeader)
        let singleColumnValue = await this.sathTopRowSingleColumnValue;
        await super.waitBrowserToLoadPage(3000);
        return (await singleColumnValue.getText());
    }

    async getCorrespondingColumnData(referenceColumnHeader, returningColumnHeader) {
        await super.waitBrowserToLoadPage(3000);
        let result = [];
        let referenceColumnIndex = await this.getHeaderIndex(referenceColumnHeader)
        columnIndex = referenceColumnIndex + 1;
        let columnList = await this.sathColumnList;
        for(let index =0; index< columnList.length; index++){
            if(await columnList[index].getText() === 'Pass') {
                let returningColumnIndex = await this.getHeaderIndex(returningColumnHeader)
                columnIndex = returningColumnIndex + 1;
                let returningColumnList = await this.sathColumnList;
                let finalText = await returningColumnList[index].getText();
                result.push(finalText)
            }
        }
        return result;
    }

    async columnLength(actionName){
        let columnLengthNum = [];
        await this.waitForTableExist(await this.sathTable);
        await super.waitBrowserToLoadPage(2000);
        if(actionName === 'edit' ){
            let editColumnLength =  await this.sathEditBtnList.length;
            columnLengthNum.push(editColumnLength)
        } else if( actionName === 'cancel'){
            let cancelColumnLength =  await this.sathCancelEditBtnList.length;
            columnLengthNum.push(cancelColumnLength)
        }
        return columnLengthNum;
    }

    async clickSathEditButtons(actionName, num) {
        await this.waitForTableExist(await this.sathTable);
        if(actionName === 'editAction') {
            let columnList = await $$(this.sathEditCancelBtnList.replace('?', 'editRow') );
            for (let index = 0; index <= columnList.length - num; index++) {
                let checkBox = await columnList[index];
                await super.waitBrowserToLoadPage(3000);
                await checkBox.click();
            }
        } else if(actionName === 'cancelAction' ){
            let cancelColumnList = await $$(this.sathEditCancelBtnList.replace('?', 'cancelEdit') );
            for (let cancelIndex = 0; cancelIndex < cancelColumnList.length - num; cancelIndex++) {
                let cancelCheckBox = await cancelColumnList[cancelIndex];
                await super.waitBrowserToLoadPage(5000);
                await cancelCheckBox.click();
            }
        }
    }

    async sathFilterRecord(division, store, processStatus){
        await super.waitBrowserToLoadPage(2000);
        if(await division !=='') {
            await spaceStationPage.setValues('division', await division);
        }
        if(await store !=='') {
            await super.waitBrowserToLoadPage(2000)
            await spaceStationPage.setValues('store', await store);
        }
        await super.waitBrowserToLoadPage(3000);
        if(await processStatus !=='') {
            await spaceStationPage.setValues('processStatus', await processStatus);
        }
    }

    async selectSathFilterDropdown(dropdownName) {
        await super.waitBrowserToLoadPage(2000);
        await spaceStationPage.setValues(dropdownName, dropdownName);
    }

    async clickSathExcelDownloadBtb(editIndex) {
        await this.waitForTableExist(await this.sathTable);
        await super.waitBrowserToLoadPage(3000);
        let columnList = await this.sathExcelDownloadList;
        await super.waitBrowserToLoadPage(3000);
        for (let index = 0; index < (columnList.length - (columnList.length - editIndex)); index++) {
            let elements = await columnList[index];
            await super.waitBrowserToLoadPage(3000);
            await elements.click();
        }
    }

    async selectPageDropdownNumber(columnCount) {
        await super.waitBrowserToLoadPage(2000)
        let pageNumbersLocator = await this.sathPageDropdown;
        await pageNumbersLocator.waitForExist({timeout: 79000}, 'Error: Locator did not appear on time ')
        await pageNumbersLocator.click();
        await super.waitBrowserToLoadPage(3000)
        let pageDropdownTxt = await this.sathPageDropdownList
        for (let index = 0; index < pageDropdownTxt.length; index++) {
            let pageList = pageDropdownTxt[index];
            if (await pageDropdownTxt[index].getText() === await columnCount) {
                await pageDropdownTxt[index].scrollIntoView();
                await super.waitBrowserToLoadPage(3000)
                await pageList.click()
                break;
            }
        }
        await browser.keys('Escape')
    }

    async getRowCount(pageNum) {
        let columnValues = [];
        let nextButton = await this.sathNextButtonPagination
        while (nextButton.isEnabled) {
            let nextBtnClassName = await this.sathNextButtonPagination.getAttribute("class")
            let column = await this.sathDivColumns;
            columnValues.push(column.length)
            await nextButton.scrollIntoView()
            await super.clickOn(nextButton)
            await super.waitBrowserToLoadPage(3000)
            if (nextBtnClassName.includes("p-disabled") || await this.activePaginationNum.getText() === pageNum) {
                break;
            }
        }
        return columnValues;
    }

}

module.exports = new SathThresholdMaintenancePage()
