const qrString = require('../../../SQLConnection/queryOSS');
const result = require('../../../SQLConnection/server.js.ts');
const ossData = require('../../../TestData/OneStopShopData/oneStopShopUIData.json')
const spaceStationPage = require("../OneStopShopPO/spaceStationVersionDescription.page");
const {assert} = require('chai');
const {DBNames} = require("../../../envConfig.js");

let versionId, versionDescription, newVersionID, newVersionDescription, lastVersionID, lastVersionDescription;

describe('validateEditRecordsOnOSS: validate EDIT function by changing values of Department, Commodity, Version Desc and Version ID columns; Then validate changes both on UI and DB ', () => {

    before(async () => {
        await spaceStationPage.open('onestopshop/spaceStation');
        versionId =  (await spaceStationPage.randomStringGenerator(3)).toUpperCase();
        newVersionID =  (await spaceStationPage.randomStringGenerator(3)).toUpperCase();
        versionDescription =  await spaceStationPage.stringGenerator("Automation");
        newVersionDescription =  await spaceStationPage.stringGenerator("ChangingVersionDesc");
        lastVersionDescription =  await spaceStationPage.stringGenerator("LastVersionDesc");
        await spaceStationPage.addVersion(ossData.department_01_GROCERY,ossData.commodity_000_SHELF_EXTENDERS,versionId,versionDescription);
        await spaceStationPage.filterRecord('', '',[versionId]);
        await spaceStationPage.waitForPageLoad(spaceStationPage.staticVersionIdColumnValue, versionId );
    })

    it('validate EDIT function by changing value of Version Desc column ', async () => {

        await spaceStationPage.waitBrowserToLoadPage(3000);
        let versionDescFromUi = await spaceStationPage.getSingleColumnValue('Version Desc');
        await spaceStationPage.performActions('edit','Version Desc', versionDescFromUi);
        await spaceStationPage.waitBrowserToLoadPage(3000);
        await spaceStationPage.updateRecord('Version Desc', await newVersionDescription);
        await spaceStationPage.waitBrowserToLoadPage(3000);
        await spaceStationPage.saveEdit();
        await spaceStationPage.waitForPageLoad(spaceStationPage.staticVersionDescColumnValue, newVersionDescription );
        let finalVersionDescFromUi = await spaceStationPage.getSingleColumnValue('Version Desc');
        await expect(versionDescription).toEqual(versionDescFromUi);
        await assert.notEqual(versionDescFromUi, finalVersionDescFromUi, 'Version Desc column value has not been updated !');
        await spaceStationPage.waitBrowserToLoadPage(3000);
        let dbResult =  await (await result.getResult(qrString.spaceStationVersionFiltersQuery.replace('@Description', await newVersionDescription), DBNames.SFP_STAGE)).flat();
        let dbResultNotNull = await spaceStationPage.isNotNull(await dbResult);
        await expect(dbResultNotNull.toString()).toEqual('true');
        await spaceStationPage.waitBrowserToLoadPage(3000);
        await expect(finalVersionDescFromUi).toEqual(dbResult[3].toString());
    })

    it('validate EDIT function by changing value of Version ID column ', async () => {
        await spaceStationPage.waitBrowserToLoadPage(3000);
        let versionIDFromUi = await spaceStationPage.getSingleColumnValue('Version ID');
        await spaceStationPage.performActions('edit','Version ID', versionIDFromUi);
        await spaceStationPage.waitBrowserToLoadPage(3000);
        await spaceStationPage.updateRecord('Version ID', await newVersionID);
        await spaceStationPage.waitBrowserToLoadPage(3000);
        await spaceStationPage.saveEdit();
        await expect(versionId).toEqual(versionIDFromUi);
        await browser.refresh();
        await spaceStationPage.filterRecord('', '',[newVersionID]);
        await spaceStationPage.waitForPageLoad(spaceStationPage.staticVersionIdColumnValue, newVersionID );
        let finalVersionIDFromUi = await spaceStationPage.getSingleColumnValue('Version ID');
        await assert.notEqual(versionIDFromUi, finalVersionIDFromUi, 'Version ID column value has not been updated !');
        await spaceStationPage.waitBrowserToLoadPage(3000);
        let dbResult =  await (await result.getResult(qrString.spaceStationVersionFiltersQuery.replace('@Description', await newVersionDescription), DBNames.SFP_STAGE)).flat();
        let dbResultNotNull = await spaceStationPage.isNotNull(await dbResult);
        await expect(dbResultNotNull.toString()).toEqual('true');
        await spaceStationPage.waitBrowserToLoadPage(3000);
        await expect(finalVersionIDFromUi).toEqual(dbResult[2].toString());
    })

    it('validate EDIT function by changing values of versionID and Version Desc ', async () => {
        lastVersionID =   (await spaceStationPage.randomStringGenerator(3)).toUpperCase();
        let versionIDNumFromUi = await spaceStationPage.getSingleColumnValue('Version ID');
        await spaceStationPage.waitBrowserToLoadPage(3000);
        let versionDescNumFromUi = await spaceStationPage.getSingleColumnValue('Version Desc');
        await spaceStationPage.waitBrowserToLoadPage(3000);
        await spaceStationPage.performActions('edit','Version ID', versionIDNumFromUi);
        await spaceStationPage.performActions('edit','Version Desc', versionDescNumFromUi);
        await spaceStationPage.waitBrowserToLoadPage(5000);
        await spaceStationPage.updateRecord('Version ID', lastVersionID);
        await spaceStationPage.updateRecord('Version Desc', await lastVersionDescription);
        await spaceStationPage.waitBrowserToLoadPage(5000);
        await spaceStationPage.saveEdit();
        await browser.refresh();
        await spaceStationPage.filterRecord('', '',[lastVersionID]);
        await spaceStationPage.waitForPageLoad(spaceStationPage.staticVersionIdColumnValue, lastVersionID );
        let finalVersionIDFromUi = await spaceStationPage.getSingleColumnValue('Version ID');
        await spaceStationPage.waitBrowserToLoadPage(3000);
        let finalVersionDescFromUi = await spaceStationPage.getSingleColumnValue('Version Desc');
        await spaceStationPage.waitBrowserToLoadPage(3000);
        await assert.notEqual(versionDescNumFromUi, finalVersionDescFromUi, 'Version Description column value has not been updated !');
        await expect(finalVersionDescFromUi).toEqual(await lastVersionDescription);
        await assert.notEqual(versionIDNumFromUi, finalVersionIDFromUi, 'Version ID column value has not been updated !');
        await expect(finalVersionIDFromUi).toEqual( lastVersionID);
        await spaceStationPage.waitBrowserToLoadPage(5000);
        let dbResult =  await (await result.getResult(qrString.spaceStationVersionFiltersQuery.replace('@Description', await lastVersionDescription), DBNames.SFP_STAGE)).flat();
        let dbResultNotNull = await spaceStationPage.isNotNull(await dbResult);
        await expect(dbResultNotNull.toString()).toEqual('true');
        await expect(dbResult[2]).toEqual(finalVersionIDFromUi);
        await expect(dbResult[3]).toEqual(finalVersionDescFromUi);
    })

})




