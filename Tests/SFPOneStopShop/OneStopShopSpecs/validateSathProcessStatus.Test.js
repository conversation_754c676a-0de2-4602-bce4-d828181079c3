const sathThresholdPage = require("../OneStopShopPO/sathThresholdMaintenace.page.js");
const ossData = require("../../../TestData/OneStopShopData/oneStopShopUIData.json");
const result = require("../../../SQLConnection/server.js.ts");
const qrString = require("../../../SQLConnection/queryOSS");
const {DBNames} = require("../../../envConfig.js");
const {assert} = require("chai");

describe('validateSathProcessStatus: Validate SATH table by filter dropdowns and compare to db', () => {

    beforeEach(async () => {
        await sathThresholdPage.waitBrowserToLoadPage(3000);
        await sathThresholdPage.open(ossData.sathUrl);
        await sathThresholdPage.waitBrowserToLoadPage(3000);
    })

    it('Should validate SATH table data when FAIL is selected: compare UI to db', async () => {

        await sathThresholdPage.waitBrowserToLoadPage(2000);
        await sathThresholdPage.sathFilterRecord(await ossData.set_SATH_division_026, await ossData.set_SATH_store_026_00093, await ossData.set_SATH_process_status_fail);
        let divNum = await sathThresholdPage.extractDigits(ossData.set_SATH_division_026.toString());
        let storeNum = await (ossData.set_SATH_store_026_00093.toString().match(/(\d[\d]*)/g)[1]);
        let failSubString = await (ossData.set_SATH_process_status_fail.toString().slice(0,1) );
        await sathThresholdPage.clickSearch();
        await sathThresholdPage.waitBrowserToLoadPage(3000);
        let tableRowCount = await sathThresholdPage.sathTableRowList;
        if(await (tableRowCount.length) >= 1){
            await sathThresholdPage.sortByColumnHeader(ossData.processed_Date_string, 0);
            await sathThresholdPage.waitBrowserToLoadPage(3000);
            let uiData = await sathThresholdPage.getAllUIData(ossData.paginate_till_page_4);
            await sathThresholdPage.waitBrowserToLoadPage(3000);
            let tableDbList = await (await result.getResult(qrString.sathTableQuery.replace('@num', ossData.get_query_top_45_data).replace('@Div', divNum).replace('@Store', storeNum).replace("@Status", failSubString),DBNames.SFP_STAGE));
            await expect(uiData.flat()).toEqual(tableDbList.flat());
        }else{
            await expect(await sathThresholdPage.zeroRecordsMessage.getText()).toEqual(ossData.no_records_found);
            await assert.fail(0, 1, "Error: Table does not have data. Update the input data from dropdowns !")
        }
    })

    it('Should validate SATH table data when PASS is selected: compare UI to db ', async () => {

        await sathThresholdPage.sathFilterRecord(await ossData.set_SATH_division_011, await ossData.set_SATH_store_011_00618, await ossData.set_SATH_process_status_pass);
        let divNum = await sathThresholdPage.extractDigits(ossData.set_SATH_division_011.toString());
        let storeNum = await (ossData.set_SATH_store_011_00618.toString().match(/(\d[\d]*)/g)[1]);
        let passSubString = await (ossData.set_SATH_process_status_pass.toString().slice(0,1) );
        await sathThresholdPage.clickSearch();
        await sathThresholdPage.sortByColumnHeader(ossData.processed_Date_string, 0);
        await sathThresholdPage.waitBrowserToLoadPage(3000);
        let uiData = await sathThresholdPage.getAllUIData(ossData.paginate_till_page_4);
        let tableDbList = await (await result.getResult(qrString.sathTableQuery.replace('@num', ossData.get_query_top_45_data).replace('@Div', divNum).replace('@Store', storeNum).replace("@Status", passSubString),DBNames.SFP_STAGE));
        await expect(await (uiData.flat())).toEqual(await tableDbList.flat());
    })

    it('Should validate SATH table page number dropdown list: compare pageDropDownList to number of rows on table ', async () => {

        await sathThresholdPage.sathFilterRecord(await ossData.set_SATH_division_011, '', await ossData.set_SATH_process_status_pass);
        await sathThresholdPage.clickSearch();
        let dropdownListData = ossData.pageDropdownList;
         for (let index = 0; index < await dropdownListData.length; index++) {
             await sathThresholdPage.selectPageDropdownNumber(await dropdownListData[index])
             await sathThresholdPage.waitBrowserToLoadPage(2000)
             await sathThresholdPage.clickOn(await sathThresholdPage.backButtonPagination)
             await sathThresholdPage.waitBrowserToLoadPage(2000)
             let rowCount = await sathThresholdPage.getRowCount('3');
             await sathThresholdPage.waitBrowserToLoadPage(2000)
             for(let rowNum = 0; rowNum < await (rowCount.length - 1); rowNum ++){
                await sathThresholdPage.waitBrowserToLoadPage(2000)
                 expect(await (await rowCount[rowNum]).toString()).toEqual(await (dropdownListData[index]));
             }
         }
    })

})
