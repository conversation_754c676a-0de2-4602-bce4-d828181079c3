const ossData = require('../../../TestData/OneStopShopData/oneStopShopUIData.json')
const spaceStationPage = require("../OneStopShopPO/spaceStationVersionDescription.page");
const qrString = require('../../../SQLConnection/queryOSS');
const result = require('../../../SQLConnection/server.js.ts');
const {DBNames} = require("../../../envConfig.js");
const chaiAssert = require('chai').expect;

let departmentNum = ossData.department_01_GROCERY.split('-')[0];
let commodityNum =   ossData.commodity_000_SHELF_EXTENDERS.split('-')[0];
let versionId, versionDescription;

describe('validateUniqueEntryErrorMessage: validate unique error messages when creating new version and editing version Id.', () => {

    before(async () => {
        await spaceStationPage.open('onestopshop/spaceStation');
        versionId = (await spaceStationPage.randomStringGenerator(3)).toUpperCase();
        versionDescription =  await spaceStationPage.stringGenerator("Automation");
        await spaceStationPage.addVersion(ossData.department_01_GROCERY,ossData.commodity_000_SHELF_EXTENDERS,versionId,versionDescription);
        await spaceStationPage.waitBrowserToLoadPage(6000);
    })

  it('validate unique version ID error message on Add Version when using duplicated version Id', async () => {

        await spaceStationPage.addVersion(ossData.department_01_GROCERY,ossData.commodity_000_SHELF_EXTENDERS,versionId,versionDescription);
        let alreadyExistErrorMessage = await spaceStationPage.addVersionUniqueMessage.getText();
        let departmentSubStr = await spaceStationPage.extractingSubstringsFromString(alreadyExistErrorMessage, 1,2, 4);
        await expect(departmentSubStr).toEqual(departmentNum);
        let commoditySubStr = await spaceStationPage.extractingSubstringsFromString(alreadyExistErrorMessage, 2,2, 5);
        await expect(commoditySubStr).toEqual(commodityNum);
        let versionIDSubStr = await spaceStationPage.extractingSubstringsFromString(alreadyExistErrorMessage, 3,2, 5);
        await expect(versionIDSubStr).toEqual(versionId);
        // Error message contains values from dropdowns
        await chaiAssert(alreadyExistErrorMessage).to.contain.oneOf([departmentNum,commodityNum,versionId]);
        let errorMessageWithEntryData = `A record with 'Department': '${departmentNum}', 'Commodity': '${commodityNum}' & 'Version': '${versionId}' already exists.`;
        await expect(alreadyExistErrorMessage).toEqual(errorMessageWithEntryData);
        await spaceStationPage.waitBrowserToLoadPage(3000);
            let dbResult =  await (await result.getResult(qrString.spaceStationVersionIdQuery.replace('@Description', await versionDescription), DBNames.SFP_STAGE)).flat();
            let dbResultNotNull = await spaceStationPage.isNotNull(await dbResult);
            await expect(dbResultNotNull.toString()).toEqual('true');
            await expect(versionId).toEqual(dbResult.toString());
        spaceStationPage.clickOn(await spaceStationPage.cancelBtn);
    })

    it('validate unique version ID error message on Filters when using editing and changing Version Id to existing version Id', async () => {

        await spaceStationPage.filterRecord(ossData.set_department_01,ossData.set_commodity_000,"");
        await (await spaceStationPage.ssvTableSecondPageBtn).waitForClickable({ timeout: 120000 }, 'Error: Table contents did not load before timeout')
        let existingVersionID = await spaceStationPage.getSingleColumnValue('Version ID');
        await spaceStationPage.clickFilterBtn();
        await spaceStationPage.filterRecord("","",[versionId]);
        await spaceStationPage.waitBrowserToLoadPage(15000);
        let initialVersionID = await spaceStationPage.getSingleColumnValue('Version ID');
        await spaceStationPage.waitBrowserToLoadPage(12000);
        await spaceStationPage.performActions('edit','Version Desc', versionDescription);
        await spaceStationPage.waitBrowserToLoadPage(3000);
        await spaceStationPage.updateRecord('Version ID', existingVersionID);
        await spaceStationPage.waitBrowserToLoadPage(3000);
        await spaceStationPage.saveEdit();
        let alreadyExistErrorMessage = await spaceStationPage.entryAlreadyExistMessage.getText();
        let departmentSubStr = await spaceStationPage.extractingSubstringsFromString(alreadyExistErrorMessage, 1,2, 4);
        await expect(departmentSubStr).toEqual(departmentNum);
        let commoditySubStr = await spaceStationPage.extractingSubstringsFromString(alreadyExistErrorMessage, 2,2, 5);
        await expect(commoditySubStr).toEqual(commodityNum);
        let versionIDSubStr = await spaceStationPage.extractingSubstringsFromString(alreadyExistErrorMessage, 3,2, 5);
        await expect(versionIDSubStr).toEqual(existingVersionID);
        let errorMessageWithEntryData = `A record with 'Department': '${departmentNum}', 'Commodity': '${commodityNum}' & 'Version': '${existingVersionID}' already exists.`
        await expect(alreadyExistErrorMessage).toEqual(errorMessageWithEntryData);
        await spaceStationPage.waitBrowserToLoadPage(3000);
        let dbResult =  await (await result.getResult(qrString.spaceStationVersionIdQuery.replace('@Description', await versionDescription), DBNames.SFP_STAGE)).flat();
        let dbResultNotNull = await spaceStationPage.isNotNull(await dbResult);
        await expect(dbResultNotNull.toString()).toEqual('true');
        await expect(initialVersionID).toEqual(dbResult.toString());
    })
})
