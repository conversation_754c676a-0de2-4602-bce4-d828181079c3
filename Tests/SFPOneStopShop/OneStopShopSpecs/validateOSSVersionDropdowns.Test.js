const spaceStationPage = require("../OneStopShopPO/spaceStationVersionDescription.page");
const qrString = require('../../../SQLConnection/queryOSS');
let ossData = require('../../../TestData/OneStopShopData/oneStopShopUIData.json')
const result = require('../../../SQLConnection/server.js.ts');
const {assert} = require('chai');
const eventMaintanencePage = require("../../SFPEventManager/EventManagerPO/eventMaintanence.page");
const {DBNames} = require("../../../envConfig.js");

describe('validateOSSVersionDropdowns: Validate dropdowns on OSS Version', () => {

    it('Should validate the Short-List Commodity dropdown values when Department drop values are selected', async () => {
        spaceStationPage.open(ossData.ossUrl);
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 70000 }, 'Error: Table contents for event maintenance page didn`t load before timeout');
        await spaceStationPage.clickAddVersion();
            for (let i = 0; i < ossData.filterDepartmentList.length; i++) {
            let Depart = ossData.filterDepartmentList[i].match(/\d/g).join("");
            await spaceStationPage.enterValue.waitForExist({timeout: 59000}, 'Error: Version Description  did not appear before timeout')
            await spaceStationPage.selectDropdownValue('department', ossData.filterDepartmentList[i]);
            await spaceStationPage.waitBrowserToLoadPage(3000);
            let commodityListUI =  (await spaceStationPage.getDropDownList('commodity'))
            let selectedCommodityListUI =  await spaceStationPage.returnNumOfArrayElements(commodityListUI, ossData.Number_15, ossData.threshold_Number_15);
            let commodityListDB = await (await result.getResult(qrString.addVersionCommodityDropdownQuery.replace('@DeptNum', Depart), DBNames.SFP_STAGE)).flat();
            for(let j= 0; j< selectedCommodityListUI.length; j++){
                await assert.equal(selectedCommodityListUI[j], commodityListDB[j], 'Error: failed at Department ' + ossData.filterDepartmentList[i]);
            }
        }
    })
})
