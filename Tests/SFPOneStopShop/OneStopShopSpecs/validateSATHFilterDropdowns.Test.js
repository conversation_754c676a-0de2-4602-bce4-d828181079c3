const spaceStationPage = require("../OneStopShopPO/spaceStationVersionDescription.page");
const sathThresholdPage = require("../OneStopShopPO/sathThresholdMaintenace.page.js");
const qrString = require('../../../SQLConnection/queryOSS');
let ossData = require('../../../TestData/OneStopShopData/oneStopShopUIData.json')
const result = require('../../../SQLConnection/server.js.ts');
const {assert} = require('chai');
const {DBNames} = require("../../../envConfig.js");

describe('validateSATHFilterDropdowns: Validate SATH Filter dropdown values and clear buttons', () => {

    beforeEach(async () => {
        await sathThresholdPage.waitBrowserToLoadPage(5000);
        await  spaceStationPage.open(ossData.sathUrl);
        await spaceStationPage.waitBrowserToLoadPage(8000);
    })

    it('Should validate single value selection from SATH dropdowns, clear buttons and compare Division dropdown to db ', async () => {

        await spaceStationPage.waitBrowserToLoadPage(3000);
        await sathThresholdPage.sathFilterRecord(await ossData.set_SATH_division_014, await ossData.set_SATH_store_014_00335, await ossData.set_SATH_process_status_fail);
        await spaceStationPage.waitBrowserToLoadPage(3000);
        let divDropdownText = await spaceStationPage.readDropdownInputBox("division");
        await spaceStationPage.waitBrowserToLoadPage(1000);
        await assert.notEqual(await divDropdownText,  'Select Division', 'Error: Two values should not be the same !');
        let storeDropdownText = await spaceStationPage.readDropdownInputBox("store");
        await assert.notEqual(await storeDropdownText,  'Select Store', 'Error: Two values should not be the same !');
        let processStatusDropdownText = await spaceStationPage.readDropdownInputBox("processStatus");
        await assert.notEqual(await processStatusDropdownText,  'Select Process Status', 'Error: Two values should not be the same !');
        await spaceStationPage.clearFilter("division");
        await sathThresholdPage.waitBrowserToLoadPage(3000);
        let divTextCleared = await spaceStationPage.readDropdownInputBox("division");
        await sathThresholdPage.waitBrowserToLoadPage(5000);
        await expect(await divTextCleared).toEqual(ossData.select_Division);
        await spaceStationPage.clearFilter("store");
        await sathThresholdPage.waitBrowserToLoadPage(3000);
        let storeTextCleared = await spaceStationPage.readDropdownInputBox("store");
        await expect(storeTextCleared).toEqual(ossData.select_Store);
        await spaceStationPage.clearFilter("processStatus");
        await sathThresholdPage.waitBrowserToLoadPage(3000);
        let statusTextCleared = await spaceStationPage.readDropdownInputBox("processStatus");
        await expect(statusTextCleared).toEqual(ossData.select_Process_Status);
        let divisionListUI =  await spaceStationPage.getFilterDropDownList('division');
        let divisionListUICleaned = await spaceStationPage.removeElementFromArray(divisionListUI, ossData.select_All);
        let divListDb = await (await result.getResult(qrString.sathFilterDivisionDropdownQuery, DBNames.SFP_STAGE)).flat();
        await expect(await divListDb).toEqual(await divisionListUICleaned);
    })

    it('Should validate multi-value selection from SATH dropdowns, clearAllFilters button and comparing ProcessStatus dropdown UI to db ', async () => {

        await spaceStationPage.waitForPageLoad(await spaceStationPage.btnSearch,"Search");
        await spaceStationPage.waitBrowserToLoadPage(2000);
        await spaceStationPage.setValues("division", await ossData.SATHDivisionList);
        await spaceStationPage.waitBrowserToLoadPage(3000);
        let divDropdownText = await spaceStationPage.readDropdownInputBox("division");
        await spaceStationPage.waitBrowserToLoadPage(3000);
        await assert.notEqual(await divDropdownText,  'Select Division', 'Error: Two values should not be the same !');
        await spaceStationPage.setValues("store", await ossData.SATHStoreList);
        let storeDropdownText = await spaceStationPage.readDropdownInputBox("store");
        await assert.notEqual(await storeDropdownText,  'Select Store', 'Error: Two values should not be the same !');
        await spaceStationPage.setValues("processStatus", await ossData.SATHProcessStatusList);
        let processStatusDropdownText = await spaceStationPage.readDropdownInputBox("processStatus");
        await assert.notEqual(await processStatusDropdownText,  'Select Process Status', 'Error: Two values should not be the same !');
        await spaceStationPage.clearAllFilter();
        await sathThresholdPage.waitBrowserToLoadPage(3000);
        let divListCleared = await spaceStationPage.readDropdownInputBox("division");
        await sathThresholdPage.waitBrowserToLoadPage(5000);
        await expect(divListCleared.toString()).toEqual(ossData.select_Division);
        let statusListUI =  await spaceStationPage.getFilterDropDownList('processStatus');
        let statusListCleaned = await spaceStationPage.removeElementFromArray(statusListUI, ossData.select_All);
        let storeListDb = await (await result.getResult(qrString.sathFilterStatusQuery, DBNames.SFP_STAGE)).flat();
        await sathThresholdPage.waitBrowserToLoadPage(5000);
        if( await storeListDb !== null){
            await expect(await storeListDb[0]).toEqual((await statusListCleaned[1]).substr(0,1));
            await expect(await storeListDb[1]).toEqual( (await statusListCleaned[0]).substr(0,1));
        }
    })

    it('Should validate SATH dropdown values and compare UI to db ', async () => {

        await spaceStationPage.waitBrowserToLoadPage(2000);
        let divListUI =  await spaceStationPage.getFilterDropDownList('division');
        await spaceStationPage.waitBrowserToLoadPage(2000);
        let divListUICleaned = await spaceStationPage.removeElementFromArray(await divListUI, ossData.select_All);
        for(let divIndex=0; divIndex <= 3;divIndex++){
            let divNum = await divListUICleaned[divIndex].match(/\d/g).join("");
            await sathThresholdPage.sathFilterRecord([await (await divListUICleaned[divIndex])], '', '');
            await spaceStationPage.waitBrowserToLoadPage(3000);
            let storeListUI =  await spaceStationPage.getFilterDropDownList('store');
            await spaceStationPage.waitBrowserToLoadPage(2000);
            let storeListUICleaned = await spaceStationPage.removeElementFromArray(await storeListUI, ossData.select_All);
            let storeNumUI = await spaceStationPage.splittingDigits(await storeListUICleaned, 1);
            await spaceStationPage.waitBrowserToLoadPage(2000);
            let storeListDb = await (await (await result.getResult(qrString.sathFilterDropdownQuery.replace('@Div', divNum), DBNames.SFP_STAGE)).flat()).sort();
            await spaceStationPage.waitBrowserToLoadPage(2000);
            for(let storeIndex = 0; storeIndex<30; storeIndex++){
                await assert.equal(await storeNumUI[storeIndex], await storeListDb[storeIndex], 'Error: failed at Division ' + await divListUICleaned[divIndex]);
                await spaceStationPage.clickOn(await spaceStationPage.clearAllFilterBtn);
            }
        }
    })
})
