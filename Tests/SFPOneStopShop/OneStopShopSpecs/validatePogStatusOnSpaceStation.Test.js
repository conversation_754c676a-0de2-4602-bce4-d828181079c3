const spaceStationPage = require("../OneStopShopPO/spaceStationVersionDescription.page");
const ossData = require('../../../TestData/OneStopShopData/oneStopShopUIData.json')
const eventMaintenancePage = require("../../SFPEventManager/EventManagerPO/eventMaintanence.page");
const assert = require('chai').assert

describe('validatePogStatusOnSpaceStation: validate pog status on space station table determines the status on planogram table ', () => {

    beforeEach(async () =>{
        await spaceStationPage.open(ossData.ossUrl)
        await (await eventMaintenancePage.tableSecondPageBtn).waitForClickable({ timeout: 70000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await spaceStationPage.waitBrowserToLoadPage(3000);
        await spaceStationPage.clickFilterBtn();
        await spaceStationPage.btnFilters.click();
    })

    it('should validate pog status on space station when infoPogTable status is Live and Work In Progress ', async () => {

        let departNum =  await spaceStationPage.extractDigits(ossData.department_01_GROCERY);
        let commodityNum =  await spaceStationPage.extractDigits(ossData.commodity_041_FROZEN_POTATOES);
        let versionId =  await spaceStationPage.extractDigits(ossData.version_id_609);
        await spaceStationPage.filterRecord([departNum], [commodityNum], [versionId]);
        await spaceStationPage.waitForPageLoad(await spaceStationPage.getFirstRowVersionIdValue, versionId);
    })

    it('should validate pog status on space station when infoPogTable status is Live, Analysis and Pending ', async () => {

        let departNum =  await spaceStationPage.extractDigits(ossData.department_03_Drug_GM);
        let commodityNum =  await spaceStationPage.extractDigits(ossData.commodity_224_INSECTICIDES);
        let versionId =  await spaceStationPage.extractDigits(ossData.version_id_100);
        await spaceStationPage.filterRecord([departNum], [commodityNum], [versionId]);
        await spaceStationPage.waitForPageLoad(await spaceStationPage.getFirstRowVersionIdValue, versionId);
    })
    beforeEach(async () =>{

        let pogStatus = await spaceStationPage.getColumnList('POG Status', await spaceStationPage.tableValue);
        await spaceStationPage.getLinkTextGeneric( await spaceStationPage.tableValue, ossData.num_Of_Column_Name_SS);
        await spaceStationPage.waitForTableExist(await spaceStationPage.pogInfoTable);
        let UiStatusList =  await spaceStationPage.getColumnListPogInfoTable('Status', await spaceStationPage.pogInfoTable);
        let statusListUnique = await spaceStationPage.getUniqueArrayValue(UiStatusList);
        await assert.isAbove(statusListUnique.length, 1);
        let sortByLength = await spaceStationPage.sortByArrayElementLength(statusListUnique);
        let screenedStatusValue = await spaceStationPage.returnArrayElementsByOrder(sortByLength);
        await expect(pogStatus.toString()).toEqual(screenedStatusValue.toString());
    })

})

