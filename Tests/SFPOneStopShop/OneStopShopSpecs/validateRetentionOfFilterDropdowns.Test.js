const qrString = require('../../../SQLConnection/queryOSS');
const result = require('../../../SQLConnection/server.js.ts');
const ossData = require('../../../TestData/OneStopShopData/oneStopShopUIData.json')
const spaceStationPage = require("../OneStopShopPO/spaceStationVersionDescription.page");
const eventMaintanencePage = require('../../SFPEventManager/EventManagerPO/eventMaintanence.page');
const {clickOn} = require('../../../GlobalObjectRepository/login.page');
const {DBNames} = require("../../../envConfig.js");
const chaiExpect = require("chai").expect;

let departmentNum = ossData.department_06_PHARMACY.match(/(\d[\d]*)/g)[0];
let commodityNum = ossData.commodity_250_DIABETIC_TESTING.match(/(\d[\d]*)/g)[0];

beforeEach(async () => {

    await spaceStationPage.open('onestopshop/spaceStation');
    await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 70000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
    await spaceStationPage.filterRecord([departmentNum], [commodityNum], '');
})

describe('validateRetentionOfFilterDropdowns: validate retention of table data screened by Department and Commodity when newVersion is created', () => {

    it('Should validate Department and Commodity columns retain table data when newVersion is created with "SAME" department and commodity ', async () => {
        let versionId =  (await spaceStationPage.randomStringGenerator(3)).toUpperCase();
        console.log('$$$ versionId', versionId)
        let versionDescription = await  spaceStationPage.stringGenerator("Automation");
        await spaceStationPage.waitForPageLoad(spaceStationPage.staticDepartmentColumnValue, departmentNum );
        await spaceStationPage.addVersion(ossData.department_06_PHARMACY, ossData.commodity_250_DIABETIC_TESTING, versionId, versionDescription);
        await spaceStationPage.waitForPageLoad(spaceStationPage.staticVersionIdColumnValue, versionId );
        let departmentList = await spaceStationPage.getColumnList('Department', await spaceStationPage.tableValue);
        let  departmentListUniqueValue = await spaceStationPage.getUniqueArrayValue(departmentList)
        await expect(departmentNum).toEqual(departmentListUniqueValue.toString());
        let commodityList = await spaceStationPage.getColumnList('Commodity', await spaceStationPage.tableValue);
        let  commodityListUniqueValue = await spaceStationPage.getUniqueArrayValue(commodityList)
        await expect(commodityNum).toEqual(commodityListUniqueValue.toString());
        let versionDescList = await spaceStationPage.getColumnList('Version Desc', await spaceStationPage.tableValue);
        let versionDescListFirstElement = versionDescList.shift();
        // validate newVersion is on the top
        await expect(versionDescription).toEqual(versionDescListFirstElement);
        let dbResult = await (await result.getResult(qrString.spaceStationVersionIdQuery.replace('@Description', versionDescListFirstElement), DBNames.SFP_STAGE)).flat().toString();
        await expect(versionId).toEqual(dbResult);
    })


    it('Should validate Department and Commodity columns retain table data when newVersion is created with "DIFFERENT" department and commodity', async () => {
        let versionId =  ( await spaceStationPage.randomStringGenerator(3)).toUpperCase();
        let versionDescription = await  spaceStationPage.stringGenerator("Automation");
        await spaceStationPage.waitForPageLoad(spaceStationPage.staticDepartmentColumnValue, departmentNum );
        await spaceStationPage.addVersion(ossData.department_09_MEAT, ossData.commodity_427_SEAFOOD_TROUT, versionId, versionDescription);
        await spaceStationPage.waitBrowserToLoadPage(6000);
        let versionDescList = await spaceStationPage.getColumnList('Version Desc', await spaceStationPage.tableValue);
        await chaiExpect(versionDescList).to.not.include(versionDescription);
        await spaceStationPage.clearFilter('Dept');
        await spaceStationPage.clearFilter('Comm');
        await spaceStationPage.clickFilterBtn();
        await spaceStationPage.waitBrowserToLoadPage(2000);
        clickOn(await spaceStationPage.pageBreadCrumb);
        await spaceStationPage.filterRecord([await spaceStationPage.extractDigits(ossData.department_09_MEAT)], [await spaceStationPage.extractDigits(ossData.commodity_427_SEAFOOD_TROUT)], [versionId]);
        await spaceStationPage.waitForPageLoad(spaceStationPage.staticVersionIdColumnValue, versionId );
        let finalVersionDescList = await spaceStationPage.getSingleColumnValue('Version Desc')
        let dbResult = await (await result.getResult(qrString.spaceStationVersionIdQuery.replace('@Description', finalVersionDescList)) ).flat().toString();
        await expect(versionId).toEqual(dbResult);

               })
           })


