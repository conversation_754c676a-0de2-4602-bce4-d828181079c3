const spaceStationPage = require("../OneStopShopPO/spaceStationVersionDescription.page");
let ossData = require('../../../TestData/OneStopShopData/oneStopShopUIData.json')
const { getTodayDate } = require('../../../util/date');
const { assert } = require('chai');

describe('validateEditRequestDeleteEntry: Verify editing a request delete entry creates a new entry', () => {
    it('should Verify editing a request delete entry creates a new entry ', async () => {

        await spaceStationPage.open(ossData.ossUrl)
        let versionId = (await spaceStationPage.randomStringGenerator(3)).toUpperCase();
        let versionDescription = await spaceStationPage.stringGenerator("Automation");
        await spaceStationPage.addVersion(ossData.department_01_GROCERY,ossData.commodity_000_SHELF_EXTENDERS,versionId,versionDescription)
        await spaceStationPage.filterRecord(ossData.set_department_01,ossData.set_commodity_000,[versionId])
        await spaceStationPage.waitBrowserToLoadPage(12000);
        await spaceStationPage.performActions('delete','Version Desc',versionDescription)
        await spaceStationPage.confirmDelete()
        await spaceStationPage.waitBrowserToLoadPage(12000);
        let deletedDate = await spaceStationPage.getColumnValue('Version Desc', versionDescription,'Delete\nRequested Date');
        let currentDate=getTodayDate().replace(/\//g,'-')
        await assert.include(deletedDate,currentDate)
        await spaceStationPage.performActions('edit','Version Desc',versionDescription)
        let updatedVersionDescription = versionDescription.replace('Automation','Updated')
        await spaceStationPage.updateRecord('Version Desc', updatedVersionDescription);
        await spaceStationPage.saveEdit()
        await spaceStationPage.waitBrowserToLoadPage(12000);
        let updatedVersionDescriptionUI = await spaceStationPage.getColumnValue('Version Desc', updatedVersionDescription,'Version Desc');
        await assert.include(updatedVersionDescription,updatedVersionDescriptionUI)
        let deletedDateAfterUpdate = await spaceStationPage.getColumnValue('Version Desc', updatedVersionDescription,'Delete\nRequested Date');
        await assert.equal(deletedDateAfterUpdate,'')
        let deletedByAfterUpdate = await spaceStationPage.getColumnValue('Version Desc', updatedVersionDescription,'Delete\nRequested By');
        await assert.equal(deletedByAfterUpdate,'')
    })
})
