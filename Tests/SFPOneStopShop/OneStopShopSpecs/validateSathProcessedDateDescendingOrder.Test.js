const sathThresholdPage = require("../OneStopShopPO/sathThresholdMaintenace.page.js");
const ossData = require("../../../TestData/OneStopShopData/oneStopShopUIData.json");

describe('validateSathProcessedDateDescendingOrder: validate ProcessedDate column is sorted in descending order - latest date lists on the top', () => {

    it('Should validate Sath ProcessedDate column in in descending order in default', async () => {

        await sathThresholdPage.waitBrowserToLoadPage(3000);
        await sathThresholdPage.open(ossData.sathUrl);
        await sathThresholdPage.waitBrowserToLoadPage(3000);
        await sathThresholdPage.waitForPageLoad(await sathThresholdPage.btnSearch, 'Search');
        await sathThresholdPage.sathFilterRecord('', '', ossData.set_SATH_process_status_pass);
        await sathThresholdPage.waitBrowserToLoadPage(3000);
        await sathThresholdPage.clickSearch();
        await sathThresholdPage.waitForTableExist(await sathThresholdPage.sathTable);
        await sathThresholdPage.waitBrowserToLoadPage(9000);
        let uiData = await (await sathThresholdPage.getAllUIData("3"));
        let uiDataNotEmpty = await sathThresholdPage.arrayHasNoEmptyValues(await uiData);
        await sathThresholdPage.waitBrowserToLoadPage(3000);
        await expect(uiDataNotEmpty).toEqual(true);
        let processedDataColumnArray = await sathThresholdPage.getColumnArrayByIndex(uiData, "6");
        let dateDescendingOrder = await sathThresholdPage.dateDescendingOrder(processedDataColumnArray);
        await expect(processedDataColumnArray).toEqual(dateDescendingOrder);
    })
})
