const qrString = require('../../../SQLConnection/queryOSS');
const result = require('../../../SQLConnection/server.js.ts');
const ossData = require('../../../TestData/OneStopShopData/oneStopShopUIData.json')
const spaceStationPage = require("../OneStopShopPO/spaceStationVersionDescription.page");
const {getTodayDate} = require("../../../util/date");
const {assert} = require("chai");
const {DBNames} = require("../../../envConfig.js");

describe('validateDeleteRecordOnOSS: validate DELETE dropdown function both on UI and DB ', () => {
    it('validate DELETE dropdown function on UI when new Version is created ', async () => {
        await spaceStationPage.open(ossData.ossUrl)
        let versionId = (await spaceStationPage.randomStringGenerator(3)).toUpperCase();
        let versionDescription = await spaceStationPage.stringGenerator("Automation");
        await spaceStationPage.addVersion(ossData.department_01_GROCERY,ossData.commodity_000_SHELF_EXTENDERS,versionId,versionDescription)
        await spaceStationPage.filterRecord(ossData.set_department_01,ossData.set_commodity_000,[versionId])
        await spaceStationPage.waitBrowserToLoadPage(12000);
        await spaceStationPage.performActions('delete','Version Desc',versionDescription)
        await spaceStationPage.confirmDelete()
        await spaceStationPage.waitBrowserToLoadPage(12000);
        let deletedDate = await spaceStationPage.getColumnValue('Version Desc', versionDescription,'Delete\nRequested Date');
        let currentDate=getTodayDate().replace(/\//g,'-')
        await assert.include(deletedDate,currentDate)
        let dbResult =  await (await result.getResult(qrString.spaceStationModifiedDateQuery.replace('@Description', await versionDescription), DBNames.SFP_STAGE)).flat();
        let dbResultNotNull = await spaceStationPage.isNotNull(await dbResult);
        // validates db result is not empty; otherwise test still passes by comparing empty UI to empty db result
        await expect(dbResultNotNull.toString()).toEqual('true');
        await expect(deletedDate).toEqual(dbResult.toString());
    })

})
