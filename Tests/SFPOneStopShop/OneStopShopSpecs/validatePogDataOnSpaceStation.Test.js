const qrString = require('../../../SQLConnection/queryOSS');
const result = require('../../../SQLConnection/server.js.ts');
const spaceStationPage = require("../OneStopShopPO/spaceStationVersionDescription.page");
const eventMaintenancePage = require("../../SFPEventManager/EventManagerPO/eventMaintanence.page");
const ossData = require('../../../TestData/OneStopShopData/oneStopShopUIData.json')
const {DBNames} = require("../../../envConfig.js");

let deptNum, commodityNum, versionID, versionDescription;

describe('validatePogDataOnSpaceStation: validate Pog status on Space Station ', () => {

     before(async () =>{
    await spaceStationPage.waitBrowserToLoadPage(4000);
    let pogMapped = await (await result.getResult(qrString.ssFindRecordsForPogMapping))[0];
    await spaceStationPage.waitBrowserToLoadPage(4000);
    deptNum = pogMapped[0];
    commodityNum = pogMapped[1];
    versionID = pogMapped[2];
    versionDescription =  await spaceStationPage.stringGenerator("Automation");
    await spaceStationPage.open('onestopshop/spaceStation')
    await (await eventMaintenancePage.tableSecondPageBtn).waitForClickable({ timeout: 70000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
    await spaceStationPage.waitBrowserToLoadPage(4000);
    await spaceStationPage.addVersionDigitsOnly( [deptNum],  [commodityNum], await versionID, await versionDescription);
    await (await result.executeQuery(qrString.updateMappedPogsQuery) );
    await (await result.executeQuery(qrString.updateNumOfPogsQuery) );
    await (await result.executeQuery(qrString.calculatePogStatusQuery) );
    await browser.refresh();
    await (await eventMaintenancePage.tableSecondPageBtn).waitForClickable({ timeout: 70000 }, 'Error: Table contents for event maintenance page didn`t load before timeout');
    await spaceStationPage.filterRecord([deptNum], [commodityNum],[versionID]);
    await spaceStationPage.waitForPageLoad(await spaceStationPage.getFirstRowVersionIdValue, versionID );
    await spaceStationPage.waitBrowserToLoadPage(6000);
     })

    it('validate Pog status on Space Station  ', async () => {

        let numOfPogsNum =  await spaceStationPage.getLinkTextGeneric(await spaceStationPage.tableValue, ossData.num_Of_Column_Name_SS );
        await spaceStationPage.waitForTableExist(await spaceStationPage.pogInfoTable);
        await spaceStationPage.waitBrowserToLoadPage(4000);
        let UISsnList =  await spaceStationPage.getColumnListPogInfoTable('SSN', await spaceStationPage.pogInfoTable);
        let ssnListUnique = await spaceStationPage.getUniqueArrayValue(UISsnList);
        await expect(numOfPogsNum).toEqual(await (ssnListUnique.length.toString()) );
        let dbResult = await (await result.getResult(qrString.ssPogInfoQuery.replace('@SSN', ssnListUnique.join("', '") ), DBNames.SFP_STAGE));
        let dbSsnColumnList = await spaceStationPage.getColumnArrayByIndex(dbResult, 3);
        await expect(UISsnList).toEqual(dbSsnColumnList);
    })

    it('validate pogs mapped by SNN: comparing UI to db  ', async () => {

        let pogMappedNum =  await spaceStationPage.getLinkTextGeneric(await spaceStationPage.tableValue, ossData.pog_Column_Name_SS);
        await spaceStationPage.waitForTableExist(await spaceStationPage.pogInfoTable);
        await spaceStationPage.waitBrowserToLoadPage(4000);
        let UISsnList =  await spaceStationPage.getColumnListPogInfoTable('SSN', await spaceStationPage.pogInfoTable);
        let ssnListUnique = await spaceStationPage.getUniqueArrayValue(UISsnList);
        await expect(pogMappedNum).toEqual(await (ssnListUnique.length.toString() ));
        let dbResultPogMapped = await (await result.getResult(qrString.pogMappedInfoQuery.replace('@SSN', ssnListUnique.join("', '") ), DBNames.SFP_STAGE));
        let dbPogMappedColumnList = await spaceStationPage.getColumnArrayByIndex(dbResultPogMapped, 3);
        await expect(UISsnList).toEqual(dbPogMappedColumnList);
    })

})

