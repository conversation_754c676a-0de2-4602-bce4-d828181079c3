const qrString = require('../../../SQLConnection/queryOSS');
const result = require('../../../SQLConnection/server.js.ts');
const ossData = require('../../../TestData/OneStopShopData/oneStopShopUIData.json')
const spaceStationPage = require("../OneStopShopPO/spaceStationVersionDescription.page");
const eventMaintanencePage = require('../../SFPEventManager/EventManagerPO/eventMaintanence.page');
const {clickOn} = require("../../../GlobalObjectRepository/login.page");
const {DBNames} = require("../../../envConfig.js");

let versionDescriptionName, errorLocator;
describe('validateAddVersionButton: validate ADD Version record both on UI and DB ', () => {
    it('validate ADD function on UI when new Version is created ', async () => {
        await spaceStationPage.open('onestopshop/spaceStation')
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 70000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        let versionId = (await spaceStationPage.randomStringGenerator(3)).toUpperCase();
        let versionDescription = await spaceStationPage.stringGenerator("AutomationCreateVersion");
        await spaceStationPage.addVersion(ossData.department_01_GROCERY,ossData.commodity_000_SHELF_EXTENDERS,versionId,versionDescription);
        errorLocator = await spaceStationPage.versionErrorMessage;
        errorLocator = await errorLocator.isExisting() ? await errorLocator.getText(): "null";
        if( errorLocator === 'null') {
            await spaceStationPage.waitBrowserToLoadPage(3000);
            await spaceStationPage.filterRecord(ossData.set_department_01, ossData.set_commodity_000, [versionId]);
        } else {
            await spaceStationPage.waitBrowserToLoadPage(2000);
            clickOn(await spaceStationPage.cancelBtn);
            let newVersionId = (await spaceStationPage.randomStringGenerator(3)).toUpperCase();
            await spaceStationPage.waitBrowserToLoadPage(2000);
            await spaceStationPage.addVersion(ossData.department_01_GROCERY,ossData.commodity_000_SHELF_EXTENDERS, newVersionId,versionDescription);
            await spaceStationPage.filterRecord(ossData.set_department_01, ossData.set_commodity_000, [newVersionId]);
        }
        await spaceStationPage.waitBrowserToLoadPage(8000);
        versionDescriptionName = await spaceStationPage.getSingleColumnValue('Version ID');
        let dbResult = await (await result.getResult(qrString.spaceStationVersionIdQuery.replace('@Description', versionDescription), DBNames.SFP_STAGE)).flat().toString();
        await expect(versionDescriptionName).toEqual(dbResult);
    })

})



