const spaceStationPage = require('../OneStopShopPO/spaceStationVersionDescription.page');
const qrString = require('../../../SQLConnection/queryOSS');
let ossData = require('../../../TestData/OneStopShopData/oneStopShopUIData.json')
const result = require('../../../SQLConnection/server.js.ts');
const {assert} = require('chai');
const eventMaintenancePage = require('../../SFPEventManager/EventManagerPO/eventMaintanence.page');
const {DBNames} = require("../../../envConfig.js");

let departmentNum = ossData.department_06_PHARMACY.match(/(\d[\d]*)/g)[0];
let commodityNum = ossData.commodity_248_MOBILITY_AIDS.match(/(\d[\d]*)/g)[0];

describe('validateOSSTableData: Validate spaceStation table data: compare UI to DB', () => {

    it('Should validate the Short-List Commodity dropdown values when Department drop values are selected', async () => {
        spaceStationPage.open(ossData.ossUrl);
        await (await eventMaintenancePage.tableSecondPageBtn).waitForClickable({ timeout: 70000 }, 'Error: Table contents for event maintenance page didn`t load before timeout');
        await spaceStationPage.filterRecord([departmentNum], [commodityNum], '');
        await spaceStationPage.waitForPageLoad(spaceStationPage.getFirstRowDepartmentValue, departmentNum );
        let uiTableData = await spaceStationPage.getOssAllUiData('5')
        let uiDataFlat = uiTableData.flat();
        let uiDataFlatFilter = await spaceStationPage.removeExtraSpaceFromArrayElement(uiDataFlat);
        let tableDbList = await (await result.getResult(qrString.ossTableQuery.replace('@num', ossData.get_query_top_60_data).replace('@Depart', departmentNum).replace('@Comm', commodityNum), DBNames.SFP_STAGE));
        let dbDataFlat = tableDbList.flat();
        let dbDataFlatFilter = await spaceStationPage.removeExtraSpaceFromArrayElement(dbDataFlat);
        for(let index =0; index<uiDataFlatFilter.length; index++){
            await assert.equal(uiDataFlatFilter[index], dbDataFlatFilter[index], 'Error: UI data does not match with DB data');
        }
    })

})

