const spaceStationPage = require("../OneStopShopPO/spaceStationVersionDescription.page");
let ossData = require('../../../TestData/OneStopShopData/oneStopShopUIData.json')
let versionId, department, commodity, versionDescription

describe('validateFilters: Verify filter dropdown functionalities on Space Station Version Description page', () => {

    before(async () => {
        await spaceStationPage.open('onestopshop/spaceStation');
        versionId = (await spaceStationPage.randomStringGenerator(3)).toUpperCase();
        department = ossData.department_01_GROCERY;
        commodity = ossData.commodity_000_SHELF_EXTENDERS;
        versionDescription = await spaceStationPage.stringGenerator("Automation");
        await spaceStationPage.addVersion(department, commodity, versionId, versionDescription);
    })

    afterEach(async () => {
        await spaceStationPage.btnFilters.click();
    })

    it('should Verify filtering records based on department', async () => {
        await spaceStationPage.filterRecord(ossData.set_department_01, '', '');
        await spaceStationPage.waitBrowserToLoadPage(18000);
        let departmentUi = await spaceStationPage.getColumnList('Department', await spaceStationPage.tableValue);
        let uniqueDept = await spaceStationPage.getUniqueArrayValue(departmentUi);
        await expect(uniqueDept).toEqual(ossData.set_department_01);
        await spaceStationPage.clearFilter('Dept');
        let dropDownValue = await spaceStationPage.getDropDownValue('department');
        await expect(dropDownValue).toEqual(ossData.Select_Department);
    })

    it('should Verify filtering records based on department and commodity', async () => {
        await spaceStationPage.filterRecord(ossData.set_department_01, ossData.set_commodity_000, '');
        await spaceStationPage.waitBrowserToLoadPage(18000);
        let departmentUi = await spaceStationPage.getColumnList('Department', await spaceStationPage.tableValue);
        let uniqueDept = await spaceStationPage.getUniqueArrayValue(departmentUi)
        await expect(uniqueDept).toEqual(ossData.set_department_01);
        let commodityUi = await spaceStationPage.getColumnList('Commodity', await spaceStationPage.tableValue);
        let uniqueComm = await spaceStationPage.getUniqueArrayValue(commodityUi);
        await expect(uniqueComm).toEqual(ossData.set_commodity_000);
        await spaceStationPage.clearFilter('Comm');
        let commDropDownValue = await spaceStationPage.getDropDownValue('commodity');
        await expect(commDropDownValue).toEqual(ossData.Select_Commodity);
        await spaceStationPage.clearFilter('Dept');
        let deptDropDownValue = await spaceStationPage.getDropDownValue('department');
        await expect(deptDropDownValue).toEqual(ossData.Select_Department);
    })

    it('should Verify filtering records based on department,commodity and versionID', async () => {
        await spaceStationPage.filterRecord(ossData.set_department_01, ossData.set_commodity_000, [versionId]);
        await spaceStationPage.waitBrowserToLoadPage(18000);
        let departmentUi = await spaceStationPage.getColumnList('Department', await spaceStationPage.tableValue);
        let uniqueDept = await spaceStationPage.getUniqueArrayValue(departmentUi);
        await expect(uniqueDept).toEqual(ossData.set_department_01);
        let commodityUi = await spaceStationPage.getColumnList('Commodity', await spaceStationPage.tableValue);
        let uniqueComm = await spaceStationPage.getUniqueArrayValue(commodityUi)
        await expect(uniqueComm).toEqual(ossData.set_commodity_000);
        let versionIdUi = await spaceStationPage.getColumnList('Version ID', await spaceStationPage.tableValue);
        let uniqueVersionIdUi = await spaceStationPage.getUniqueArrayValue(versionIdUi)
        await expect(uniqueVersionIdUi.toString()).toEqual(versionId.toString());
        await spaceStationPage.clearFilter('Version');
        let versionDropDownValue = await spaceStationPage.getDropDownValue('version');
        await expect(versionDropDownValue).toEqual(ossData.Select_Version);
        await spaceStationPage.clearFilter('Comm');
        let commDropDownValue = await spaceStationPage.getDropDownValue('commodity');
        await expect(commDropDownValue).toEqual(ossData.Select_Commodity);
        await spaceStationPage.clearFilter('Dept');
        let deptDropDownValue = await spaceStationPage.getDropDownValue('department');
        await expect(deptDropDownValue).toEqual(ossData.Select_Department);

    })

})
