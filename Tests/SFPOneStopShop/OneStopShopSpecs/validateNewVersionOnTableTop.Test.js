const spaceStationPage = require("../OneStopShopPO/spaceStationVersionDescription.page");
let ossData = require('../../../TestData/OneStopShopData/oneStopShopUIData.json')
const result = require("../../../SQLConnection/server.js.ts");
const qrString = require("../../../SQLConnection/queryOSS");
const {DBNames} = require("../../../envConfig.js");

let versionId, versionDescription;
describe('validateNewVersionOnTableTop: Verify newly created version is listed on the top of the table and compare to db', () => {

    before(async () => {
        await spaceStationPage.open(ossData.ossUrl)
    })

    it('validateNewVersionOnTableTop: Verify newly created version displays on the top of table and compare to db', async () => {
        versionId = (await spaceStationPage.randomStringGenerator(3)).toUpperCase();
        console.log('$$$ versionId', versionId)
        versionDescription = await spaceStationPage.stringGenerator("Automation");
        await spaceStationPage.addVersion(ossData.department_07_PRODUCE, ossData.commodity_408_FRESH_KITCHENS_FRUIT, versionId, versionDescription);
        await spaceStationPage.waitBrowserToLoadPage(20000);
        let versionIDFromTopTable = await spaceStationPage.getCorrespondingColumnValue("Version Desc", "Version ID", versionDescription);
        console.log('%%% versionIDFromTopTable', versionIDFromTopTable)

        await expect(versionId).toEqual(versionIDFromTopTable.toString());
        let commodityFromTopTable = await spaceStationPage.getCorrespondingColumnValue("Version Desc", "Commodity", versionDescription);
        let commodityDigits = ossData.commodity_408_FRESH_KITCHENS_FRUIT.match(/(\d[\d]*)/g)[0];
        await expect(commodityDigits).toEqual(commodityFromTopTable.toString());
        let departIDFromTopTable = await spaceStationPage.getCorrespondingColumnValue("Version Desc", "Department", versionDescription);
        let departDigits = ossData.department_07_PRODUCE.match(/(\d[\d]*)/g)[0];
        await expect(departDigits).toEqual(departIDFromTopTable.toString());
    })

    it('Verify newly created version using Filter function and compare to db', async () => {
        await spaceStationPage.filterRecord('', '', [versionId]);
        await spaceStationPage.waitForPageLoad(spaceStationPage.singleDepartColumnValue, '07');
        let versionIDFromFilter = await spaceStationPage.getCorrespondingColumnValue("Version Desc","Version ID", versionDescription );
        let dbResult = await (await result.getResult(qrString.spaceStationVersionIdQuery.replace('@Description', versionDescription), DBNames.SFP_STAGE)).flat().toString();
        await expect(versionIDFromFilter.toString()).toEqual(dbResult.toString());
    })

})
