const sathThresholdPage = require("../OneStopShopPO/sathThresholdMaintenace.page.js");
const ossData = require("../../../TestData/OneStopShopData/oneStopShopUIData.json");
const result = require("../../../SQLConnection/server.js.ts");
const qrString = require("../../../SQLConnection/queryOSS");
const {DBNames} = require("../../../envConfig.js");

let initialColumnLength, finalColumnLength, initialCancelColumnLength;
describe('validateEditRecordsOnSath: validate Edit and Cancel buttons on SATH table', () => {

    beforeEach(async () => {

        await sathThresholdPage.waitBrowserToLoadPage(3000);
        await sathThresholdPage.open(ossData.sathUrl);
        await sathThresholdPage.waitBrowserToLoadPage(3000);
        await sathThresholdPage.sathFilterRecord('', '', ossData.set_SATH_process_status_fail);
        await sathThresholdPage.waitBrowserToLoadPage(3000);
        await sathThresholdPage.clickSearch();
        await sathThresholdPage.waitForTableExist(await sathThresholdPage.sathTable);
        await sathThresholdPage.waitBrowserToLoadPage(5000);
    })

    it('Should validate Edit and Cancel buttons when Fail is selected from ProcessStatus', async () => {

        initialColumnLength = (await sathThresholdPage.columnLength('edit'));
        await sathThresholdPage.waitBrowserToLoadPage(3000);
        await sathThresholdPage.clickSathEditButtons('editAction', 13);
        await sathThresholdPage.waitBrowserToLoadPage(3000);
        finalColumnLength = (await sathThresholdPage.columnLength('edit'));
        let totalEditColumnLength = (parseInt(finalColumnLength) + (parseInt(initialColumnLength) - parseInt(finalColumnLength))).toString();
        await expect(initialColumnLength.toString()).toEqual(totalEditColumnLength);
        initialCancelColumnLength = (await sathThresholdPage.columnLength('cancel'));
        await sathThresholdPage.waitBrowserToLoadPage(3000);
        await sathThresholdPage.clickSathEditButtons('cancelAction', 0);
        await sathThresholdPage.waitBrowserToLoadPage(3000);
        let totalCancelColumnLength = ((parseInt(finalColumnLength) - parseInt(finalColumnLength)) + parseInt(initialCancelColumnLength)).toString();
        await expect(initialCancelColumnLength.toString()).toEqual(totalCancelColumnLength);
    })

    it('Should validate single Edit and Confirmation buttons when Fail is selected from ProcessStatus', async () => {

        await sathThresholdPage.waitBrowserToLoadPage(3000);
        let pageRowLength = await sathThresholdPage.getCurrentPageRowLength(await sathThresholdPage.sathTable, await sathThresholdPage.sathRowList);
        let processedDate = await sathThresholdPage.sathEditRecord(await (await pageRowLength -1), 'Processed Date');
        await sathThresholdPage.waitBrowserToLoadPage(3000);
        let statusText = await sathThresholdPage.getTopColumnValue('Status');
        await expect(statusText).toEqual((await ossData.set_SATH_process_status_pass).toString());
        await sathThresholdPage.waitBrowserToLoadPage(3000);
        let storeListDb = await (await result.getResult(qrString.sathStatusQuery.replace('@PDate', processedDate), DBNames.SFP_STAGE));
        let statusListDbUnique = await sathThresholdPage.getUniqueArrayValue(await storeListDb[0].flat());
        await expect(statusListDbUnique.toString()).toEqual(statusText);
    })

    it.skip('Should validate multiple Edit and Confirmation buttons when Fail is selected from ProcessStatus', async () => {
        //Note: skipping this test to avoid from not having enough data on the table
        await sathThresholdPage.sathEditRecord('13', 'Processed Date');
        await sathThresholdPage.waitBrowserToLoadPage(5000);
        let statusText = await sathThresholdPage.getTopColumnValue('Status', -1);
        await expect(statusText).toEqual('Pass');
        let processDateList = (await sathThresholdPage.getCorrespondingColumnData('Status', 'Processed Date'));
        let processDataListFormatted = await (processDateList.join("', '"));
        let divNumList = (await sathThresholdPage.getCorrespondingColumnData('Status', 'Division'));
        let divNumListFormatted = await (divNumList.join("', '"));
        let storeNumList = (await sathThresholdPage.getCorrespondingColumnData('Status', 'Store'));
        let storeNumListFormatted = await (storeNumList.join("', '"));
        await sathThresholdPage.clickSearch();
        let statusListDb = await (await result.getResult(qrString.sathStatusQuery.replace('@Div', divNumListFormatted).replace('@Store', storeNumListFormatted).replace('@PDate', processDataListFormatted), DBNames.SFP_STAGE)).flat();
        let statusListDbUnique = await sathThresholdPage.getUniqueArrayValue(statusListDb)
        await expect(statusText).toEqual(statusListDbUnique.toString());
    })

})
