const qrString = require('../../../SQLConnection/queryOSS');
const result = require('../../../SQLConnection/server.js.ts');
const ossData = require('../../../TestData/OneStopShopData/oneStopShopUIData.json')
const spaceStationPage = require("../OneStopShopPO/spaceStationVersionDescription.page");
const eventMaintenancePage = require("../../SFPEventManager/EventManagerPO/eventMaintanence.page");
const {DBNames} = require("../../../envConfig.js");

let versionId, versionDescription;
let departNum = ossData.department_16_COSMETICS.match(/(\d[\d]*)/g)[0];
let commodityNum = ossData.commodity_193_FRAGRANCES.match(/(\d[\d]*)/g)[0];

describe('validatePogZeroConditionOnSpaceStation: validate pog messages when NumOfPogs and pogsMapped are zero ', () => {

    before(async () =>{

        await spaceStationPage.open(ossData.ossUrl)
        versionId = (await spaceStationPage.randomStringGenerator(3)).toUpperCase();
        versionDescription = await spaceStationPage.stringGenerator("Automation");
    })

    it('validate  numberOgPogs column when column value is zero ', async () => {
        await spaceStationPage.addVersion(ossData.department_16_COSMETICS, ossData.commodity_193_FRAGRANCES, versionId, versionDescription);
        await spaceStationPage.waitBrowserToLoadPage(20000);
        await (await result.executeQuery(qrString.updateNewVersionWithZeroPogQuery.replace('@Depart', departNum ).replace('@Comm', commodityNum ).replace('@VersionId', versionId) )) ;
        await (await eventMaintenancePage.tableSecondPageBtn).waitForClickable({ timeout: 70000 }, 'Error: Table contents for event maintenance page didn`t load before timeout');
        await spaceStationPage.filterRecord([departNum], [commodityNum],[versionId]);
        await spaceStationPage.waitForPageLoad(await spaceStationPage.getFirstRowVersionIdValue, versionId );
        await spaceStationPage.waitBrowserToLoadPage(6000);
        let numOfPogsNum =  await spaceStationPage.getLinkTextGeneric(await spaceStationPage.tableValue, ossData.num_Of_Column_Name_SS);
        await spaceStationPage.waitForPageLoad(await spaceStationPage.noPogMessage, ossData.num_Of_Column_empty_message);
        let textMessage = await spaceStationPage.getTextOfElement(await spaceStationPage.noPogMessage);
        await expect(textMessage).toEqual(ossData.num_Of_Column_empty_message);
        spaceStationPage.clickOn(await spaceStationPage.closeBtnInfoTable);
        let dbResult = await (await result.getResult(qrString.readNewVersionWhenPogIsZeroQuery.replace('@Depart', departNum ).replace('@Comm', commodityNum ).replace('@VersionId', versionId), DBNames.SFP_STAGE)) ;
        let dbNumOfPogs = await spaceStationPage.getColumnArrayByIndex(dbResult, 14);
        await expect(numOfPogsNum.toString()).toEqual(dbNumOfPogs.toString().trim());
    })

    it('validate pogsMapped column when column value is zero ', async () => {
        let pogMappedNum =  await spaceStationPage.getLinkTextGeneric(await spaceStationPage.tableValue, ossData.pog_Column_Name_SS);
        await spaceStationPage.waitForPageLoad(await spaceStationPage.noPogMessage, ossData.pog_Column_empty_message);
        let textMessage = await spaceStationPage.getTextOfElement(await spaceStationPage.noPogMessage);
        await expect(textMessage).toEqual(ossData.pog_Column_empty_message);
        spaceStationPage.clickOn(await spaceStationPage.closeBtnInfoTable);
        let dbResult = await (await result.getResult(qrString.readNewVersionWhenPogIsZeroQuery.replace('@Depart', departNum ).replace('@Comm', commodityNum ).replace('@VersionId', versionId), DBNames.SFP_STAGE)) ;
        let dbPogMappedNum = await spaceStationPage.getColumnArrayByIndex(dbResult, 15);
        await expect(pogMappedNum.toString()).toEqual(dbPogMappedNum.toString().trim());
    })

})

