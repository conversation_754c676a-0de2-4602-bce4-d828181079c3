/**
 * Example test demonstrating Report Portal integration
 * This test shows how to use Report Portal features with WebDriverIO
 */

const { testSetup } = require('../../helpers/testSetupTeardown.js');
const envVariables = require("../../envConfig");
const ENV = process.env.ENV;

describe('Report Portal Integration Example', () => {
    
    before(async () => {
        // Setup test with appropriate user credentials
        await testSetup(
            await envVariables.user[ENV], 
            await envVariables.password[ENV]
        );
    });

    beforeEach(async () => {
        // Add a small wait to ensure page is ready
        await browser.pause(1000);
    });

    it('should demonstrate successful test reporting to Report Portal', async () => {
        // Navigate to the application
        await browser.url('/');
        
        // Verify page title
        const title = await browser.getTitle();
        expect(title).toContain('SFP');
        
        // Add some test steps that will be reported
        console.log('Test step 1: Navigated to home page');
        console.log('Test step 2: Verified page title');
        
        // Take a screenshot for Report Portal
        await browser.saveScreenshot('./TestReports/screenshots/rp_example_success.png');
        
        console.log('Test completed successfully - this will appear in Report Portal');
    });

    it('should demonstrate test with custom attributes', async () => {
        // This test demonstrates how test results appear in Report Portal
        await browser.url('/');
        
        // Simulate some test actions
        const currentUrl = await browser.getUrl();
        expect(currentUrl).toContain('kroger.com');
        
        console.log('Custom test with additional logging for Report Portal');
        console.log(`Current URL: ${currentUrl}`);
        console.log(`Environment: ${ENV || 'stage'}`);
        console.log(`Timestamp: ${new Date().toISOString()}`);
    });

    it('should demonstrate test with warning (skipped test)', async () => {
        // This test will be skipped to show how skipped tests appear in Report Portal
        pending('This test is intentionally skipped to demonstrate Report Portal skip handling');
    });

    it('should demonstrate failed test reporting', async () => {
        // This test intentionally fails to show error reporting in Report Portal
        // Uncomment the line below to see how failures are reported
        // expect(false).toBe(true, 'This is an intentional failure for Report Portal demo');
        
        // For now, we'll just log and pass
        console.log('This test would normally fail to demonstrate error reporting');
        console.log('Uncomment the expect statement above to see actual failure reporting');
    });

    it('should demonstrate test with detailed logging', async () => {
        await browser.url('/');
        
        // Detailed logging that will appear in Report Portal
        console.log('=== Detailed Test Execution Log ===');
        console.log('Step 1: Opening application homepage');
        
        const pageTitle = await browser.getTitle();
        console.log(`Step 2: Retrieved page title: ${pageTitle}`);
        
        const viewport = await browser.getWindowSize();
        console.log(`Step 3: Browser viewport: ${viewport.width}x${viewport.height}`);
        
        const userAgent = await browser.execute(() => navigator.userAgent);
        console.log(`Step 4: User agent: ${userAgent}`);
        
        console.log('Step 5: Test execution completed');
        console.log('=== End of Detailed Log ===');
        
        // Assertions
        expect(pageTitle).toBeDefined();
        expect(viewport.width).toBeGreaterThan(0);
        expect(viewport.height).toBeGreaterThan(0);
    });

    afterEach(async () => {
        // Log completion of each test
        console.log('Test case completed');
    });

    after(async () => {
        console.log('Report Portal Example Test Suite completed');
        console.log('Check Report Portal dashboard for detailed results');
    });
});

/**
 * Additional test suite to demonstrate multiple suites in Report Portal
 */
describe('Report Portal - Multiple Suite Example', () => {
    
    it('should show how multiple test suites appear in Report Portal', async () => {
        console.log('This is from a second test suite');
        console.log('Report Portal will organize tests by suite structure');
        
        // Simple assertion
        expect(true).toBe(true);
    });

    it('should demonstrate suite-level organization', async () => {
        console.log('Tests are organized by describe blocks in Report Portal');
        console.log('This helps with test result analysis and reporting');
        
        // Another simple assertion
        expect('Report Portal').toContain('Portal');
    });
});
