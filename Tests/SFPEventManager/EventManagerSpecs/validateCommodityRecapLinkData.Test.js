const ResetCompletionPage = require('../EventManagerPO/resetCompletion.page.js')
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page.js');
const AutoActivationReport = require('../EventManagerPO/autoActivationReport.page.js')
const { clickOn } = require('../../../GlobalObjectRepository/login.page');
const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');

describe('validateCommodityRecapLinkData : validate Complete,Not complete, ' +
    'No-response and Deleted data columns', () => {
    it('validateCommodityRecapClickableColumnValues: Should validate clickable column values by clicking the value and counting rows of new window ', async () => {
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await ResetCompletionPage.open('EventMaintenance/resetCompletion');
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await expect(await ResetCompletionPage.linkSubModule.getText()).toHaveTextContaining(emData.Admin_reset_completion);
        await ResetCompletionPage.waitForPageLoad(ResetCompletionPage.clearFiltersBtn, emData.ResetComplete_Clear_Filters);
        await clickOn(await ResetCompletionPage.clearFiltersBtn);
        await ResetCompletionPage.selectSingleValue(emData.selectedDivision_ResetCompletion, emData.Division_011_Atlanta_Division);
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await ResetCompletionPage.selectSingleValue('selectedDepartment', emData.Department_01_GROCERY );
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await ResetCompletionPage.selectSingleValue('selectedCommodity', emData.Commodity_01_GROCERY_000_SHELF_EXTENDERS);
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await ResetCompletionPage.selectSingleValue(emData.selectedCompletionType_ResetCompletion, emData.ResetComplete_All);
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
       await AutoActivationReport.selectYPW(emData.Year_Input, emData.Year_2023);
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await AutoActivationReport.selectYPW('Period', emData.Period_Empty);
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await AutoActivationReport.selectYPW('Week', emData.Week);
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await storeSelectionPage.clickOn(ResetCompletionPage.searchBtn);
        await storeSelectionPage.clickOn(ResetCompletionPage.commodityRecapBtn);
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        let completedCountTxt = await ResetCompletionPage.getColumnValue(emData.Completed_columnHeader);
        let completedCountTxtNum =   Number(completedCountTxt);
        let notCompletedCountTxt = await ResetCompletionPage.getColumnValue(emData.Not_Completed);
        let notCompletedCountTxtNum =  Number(notCompletedCountTxt);
        let deletedCountTxt = await ResetCompletionPage.getColumnValue(emData.ResetComplete_Deleted);
        let deletedCountTxtNum =  Number(deletedCountTxt);
        let completed = await ResetCompletionPage.getRowNumber(emData.Completed_columnHeader, "commodityrecapRows");
        await storeSelectionPage.clickOn(ResetCompletionPage.commodityRecapBtn);
        await ResetCompletionPage.waitForPageLoad(await ResetCompletionPage.notCompletedHeaderText, emData.Not_Completed);
        let notCompleted = await ResetCompletionPage.getRowNumber(emData.NotCompleted_columnHeader, "commodityrecapRows");
        await storeSelectionPage.clickOn(ResetCompletionPage.commodityRecapBtn);
        await ResetCompletionPage.waitForPageLoad(await ResetCompletionPage.notCompletedHeaderText, emData.Not_Completed);
        let deleted = await ResetCompletionPage.getDeletedRowNumber(emData.Deleted_ColumnHeader, emData.storeReviewRows);
        await expect(completedCountTxtNum).toEqual(completed);
        await expect(notCompletedCountTxtNum).toEqual(notCompleted);
        await expect(deletedCountTxtNum).toEqual(deleted);
    })
})

