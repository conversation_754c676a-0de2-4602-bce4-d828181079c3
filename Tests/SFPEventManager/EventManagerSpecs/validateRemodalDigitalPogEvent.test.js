const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const Result = require('../../../SQLConnection/server.js.ts');
const { assert } = require('chai');
const storeSelectionPage = require('../EventManagerPO/storeSelection.page.js');
const selectPOGPage = require("../EventManagerPO/selectingPogCommodities.page");
const result = require('../../../SQLConnection/server.js.ts');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');

describe('validateRemodalDigitalPogEvent: validate Sfp event POG  Digital Event', () => {

    it('Should validate Sfp event POG  Digital Event', async () => {   
        await eventMaintenancePage.waitBrowserToLoadPage(6000)                                                                  
        await eventMaintanencePage.open('EventMaintanence'); 
        await eventMaintanencePage.waitBrowserToLoadPage(6000)   
        await eventMaintanencePage.tableSecondPageBtn.waitForExist({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'});
        await eventMaintanencePage.selectEventType('SFP Refresh')
        await expect(await eventMaintanencePage.linkCreateSFPEvent.getText()).toEqual(emData.Event_Refresh);
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await expect(await eventMaintanencePage.linkCreateSFPEvent.getText()).toEqual(emData.Event_Refresh);
        await createSFPEventPage.selectDropdown('division', emData.div_Atlanta_Operating_Division_011);
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_Remodel)
        let description = await selectPOG.stringGenerator("RemodalDigitalPog")
        await createSFPEventPage.enterEventResetDescription(description)
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSFPEventPage.clickCheckBox(emData.checkBox_Digital_Refresh_ClickList)
        await eventMaintanencePage.waitBrowserToLoadPage(4000)
        await createSFPEventPage.selectRadioButton(emData.radioBtn_pog)
        await createSFPEventPage.submitEventRefreshDetails()
        await clickOn(await createSFPEventPage.sfpAppModalButton)
        await eventMaintanencePage.waitBrowserToLoadPage(4000)
        let nextPage = await eventMaintanencePage.linkSubModule.getText();
        await expect(nextPage).toEqual(emData.Event_Schedule);
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Event Schedule");
        await createEventSchedulePage.genericClickDropdown('Fiscal Week');
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout');
        await clickOn(storeSelectionPage.saveSchedule);
        let weekText = await storeSelectionPage.storeWeekSchedule.getText();
        await clickOn(storeSelectionPage.saveGoToStore);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Store Selection");
        let storePage = await storeSelectionPage.linkSubModule.getText();
        await expect(storePage).toEqual(emData.Store_Selection);
        let divNum =  (emData.div_Atlanta_Operating_Division_011.match(/\d/g).join("")).toString();
        let dbRemodelStores = await (await result.getResult(qrString.remodelStores.replace(/@div/g, divNum).replace('@num','3'))).flat();
        await storeSelectionPage.clickCheckBoxText(await dbRemodelStores[1]);
        await storeSelectionPage.clickSecondScheduleStoreCheckbox(await dbRemodelStores[2]);
        await clickOn(await storeSelectionPage.saveStoreSchedule);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, emData.Select_Pogs);
        let pogPage = await storeSelectionPage.linkSubModule.getText();
        await expect(pogPage).toEqual(emData.Select_Pogs)
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText,emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText,emData.Department);
        await selectPOGPage.selectDropdown('Department', emData.selecting_POG_Department_15_DELI);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText,emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.selectDropdown('Sub-Department', emData.selecting_POG_SubDepartment_15_DELI_49_SPECIALTY_CHSE);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText,emData.Department);
        await selectPOGPage.selectDropdown('Commodity *', emData.selecting_POG_Commodity_462_SPECIALTY_CHEESE_PRE_PACK_49_SPECIALTY_CHSE_15_DELI);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText,emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.selectDropdown('Commodity Group', emData.selecting_POG_CommodityGroup_462_SPECIALTY_CHEESE_PRE_PACK_462_SPECIALTY_CHEESE_PRE_PACK);
        await clickOn(selectPOGPage.selectChoosePogByCommodityBtn);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText,emData.Department);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        let spaceStationName = await selectPOG.getHeaderValues(emData.Space_Station_Name_Header)
        await clickOn(selectPOGPage.gotoEventListButton);
        await clickOn(await selectPOGPage.sfpAppModalButton)
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await expect(browser).toHaveUrlContaining("EventMaintanence")
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await expect(await eventMaintanencePage.eventCreationText.getText()).toEqual(emData.Event_Creation);
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await eventMaintanencePage.clickFiltersWithDiscp(description)
        let eventDescription = await selectPOG.getEventDescText(description, weekText.replace(', ','/'))
        await expect(eventDescription).toEqual(true);
        let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, description)
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        let eventId = await eventMaintanencePage.getEventId(initialEventId)
        await expect(eventId).toEqual(initialEventId)
        let pogType = await eventMaintanencePage.getHeaderValue(emData.Header_Pog, description)
        let pogStringStoreNum = await eventMaintanencePage.extractingDigit(emData.PogString, emData.PogString, 1);
        await expect(pogType).toEqual(pogStringStoreNum)
        let pogID = await eventMaintanencePage.getPogHeaderValues(emData.Pog_Header)
        await expect(pogID).toEqual(spaceStationName)
        await clickOn(await eventMaintanencePage.popUpCancelBtn);
        let dataFromDb = await Result.getResult(qrString.SfpEvent_Eventid.replace('eventID', initialEventId))
        await assert.equal(dataFromDb, initialEventId)
        let refreshType = await eventMaintanencePage.getHeaderValue(emData.Header_Refresh, description)
        await expect(refreshType).toEqual(emData.Event_refreshType_D)
        let releaseEventenabled = await eventMaintanencePage.selectReleaseEvents(emData.ReleaseEvent_Corporate, emData.Header_Action, description)
        await expect(releaseEventenabled).toEqual(true)
        let releaseText= await (await (eventMaintanencePage.eventReleaseTextAlert)).getText()
        await expect(releaseText).toEqual(emData.ReleaseEventMsg)
        let corpEventStatus = await Result.getResult(qrString.SfpEvent_corpStatus.replace('eventID', initialEventId))
        await assert.equal(corpEventStatus, emData.corpEvent_Released)
    })
})
