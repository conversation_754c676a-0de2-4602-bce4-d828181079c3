const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const {clickOn} = require('../EventManagerPO/createSFPEvent.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const selectPOGPage = require("../EventManagerPO/selectingPogCommodities.page");
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const {assert} = require('chai')

let newScheduleStoreTblId, neweventScheduleDivTbl, neweventScheduleTbl, scheduleStoreTblId, eventScheduleDivTbl,
    eventScheduleTbl

describe('validateEditSchedulesForDivisionalEvents: validate edit schedules weeks changes the schedule ID DB', () => {

    before(async () => {
        let eventDescription = await createSfpEventPage.addEventDetails('SFP Event', emData.div_Cincinnati_Operating_Division_014, emData.eventType_KOMPASS, "editScheduleWeek", emData.vendor_AZB)
        await createSfpEventPage.submitEventDetails()
        await createEventSchedulePage.scheduling(2)
        await storeSelectionPage.saveGoToStoreSelection()
        let div = (emData.div_Cincinnati_Operating_Division_014.match(/\d/g).join("")).toString();
        let dbScheduledStores = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, div).replace('@num', '30'))).flat();
        await storeSelectionPage.storeSelection(dbScheduledStores, 5, 1)
        await storeSelectionPage.storeSelection(dbScheduledStores, 12, 2)
        await storeSelectionPage.storeSelection(dbScheduledStores, 20, 3)
        await storeSelectionPage.saveStoreScheduleButton()
        await selectPOG.selectingPogCommodities('Department', emData.dept_01_GROCERY, 'Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER, 'Commodity', emData.ResetComplete_All, '')
        await clickOn(selectPOGPage.selectChoosePogByCommodityBtn);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.tableFirstRow.waitForExist({timeout: 90000}, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await (await selectPOG.gotoEventListButton).waitForExist({timeout: 90000}, 'Error: EventList button not visible')
        await clickOn(selectPOGPage.gotoEventListButton);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await expect(await eventMaintanencePage.eventCreationText.getText()).toEqual(emData.Event_Creation);
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters);
        await eventMaintanencePage.tableFirstRow.waitForExist({timeout: 90000}, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({timeout: 90000}, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintanencePage.clickFiltersWithDiscp(eventDescription.toString())
        let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, eventDescription)
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        scheduleStoreTblId = await (await result.getResult(qrString.eventScheduleStoreTbl.replace('@evID', initialEventId))).flat();
        eventScheduleDivTbl = await (await result.getResult(qrString.eventScheduleDivTbl.replace('@evID', initialEventId))).flat();
        eventScheduleTbl = await (await result.getResult(qrString.eventScheduleTbl.replace('@evID', initialEventId))).flat();
        let releaseEventEdit = await eventMaintanencePage.selectReleaseEvents(emData.ReleaseEvent_Edit, emData.Header_Action, eventDescription)
        await expect(releaseEventEdit).toEqual(true)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await createSfpEventPage.submitEventDetails()
        await createEventSchedulePage.scheduling()
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await createEventSchedulePage.clickDeleteEventBtn();
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await clickOn(await createEventSchedulePage.sfpAppModalButton)
        await storeSelectionPage.saveGoToStoreSelection()
        await eventMaintanencePage.waitBrowserToLoadPage(30000);
        await storeSelectionPage.storeSelection(dbScheduledStores, 5, 3)
        await eventMaintanencePage.waitBrowserToLoadPage(10000);
        await clickOn(storeSelectionPage.saveStoreSchedule)
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.tableFirstRow.waitForExist({timeout: 90000}, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await (await selectPOG.gotoEventListButton).waitForExist({timeout: 90000}, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await clickOn(selectPOGPage.gotoEventListButton);
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters)
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({timeout: 90000}, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await eventMaintanencePage.clickFiltersWithDiscp(eventDescription.toString())
        let finalEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, eventDescription)
        newScheduleStoreTblId = await (await result.getResult(qrString.eventScheduleStoreTbl.replace('@evID', finalEventId))).flat()
        neweventScheduleDivTbl = await (await result.getResult(qrString.eventScheduleDivTbl.replace('@evID', finalEventId))).flat()
        neweventScheduleTbl = await (await result.getResult(qrString.eventScheduleTbl.replace('@evID', finalEventId))).flat()
    })

    it('Should validate edit schedules weeks changes the schedule ID in scheduleStoreTbl DB', async () => {
        //validate the deleted scheduleStoreTblId for store table
        for (let index = 0; index < newScheduleStoreTblId.length; index++) {
            assert.notEqual(await newScheduleStoreTblId[index], (scheduleStoreTblId[0].toString()), "Deleted Schedule ID found")
        }
    })

    it('Should validate edit schedules weeks changes the schedule ID in eventScheduleTbl DB', async () => {
        //validate the deleted eventScheduleTbl for store table
        for (let index = 0; index < neweventScheduleTbl.length; index++) {
            assert.notEqual(await neweventScheduleTbl[index], (eventScheduleTbl[0].toString()), "Deleted Schedule ID found")
        }
    })

    it('Should validate edit schedules weeks changes the schedule ID in eventScheduleDivTbl DB', async () => {
        //validate the deleted eventScheduleTbl for store table
        for (let index = 0; index < eventScheduleDivTbl.length; index++) {
            assert.notEqual(await eventScheduleDivTbl[index], (neweventScheduleDivTbl[2]), "Deleted Schedule ID found")
        }

        for (let index = 0; index < neweventScheduleDivTbl.length; index++) {
            assert.notEqual(await neweventScheduleDivTbl[index], (eventScheduleDivTbl[0]), "Added Schedule ID not found")
        }
    })
})
