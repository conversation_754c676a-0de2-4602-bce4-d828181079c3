const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const { assert } = require('chai');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');

describe('validateSFPEventRefreshFlow: validate Sfp event refresh', () => {

    it('Should validate SFPEventRefreshFlow page', async () => {
        await eventMaintanencePage.waitBrowserToLoadPage(8000)
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.waitBrowserToLoadPage(4000)
        await eventMaintanencePage.tableSecondPageBtn.waitForExist({ timeout: 90000, timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await eventMaintanencePage.selectEventType('SFP Refresh')
        await expect(await eventMaintanencePage.linkCreateSFPEvent.getText()).toEqual(emData.Event_Refresh); 
        await createSfpEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014)
        await createSfpEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        let description = await selectPOG.stringGenerator("EventRefresh")
        await createSfpEventPage.enterEventResetDescription(description)
        await createSfpEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSfpEventPage.clickCheckBox(emData.checkBox_Digital_Refresh_ClickList)
        await createSfpEventPage.submitEventRefreshDetails()
        await eventMaintanencePage.waitBrowserToLoadPage(5000)
        await eventMaintanencePage.clickOn(createSfpEventPage.sfpAppModalButton)  
        let nextPage = await createSfpEventPage.linkCreateSfpRefreshEvent.getText()
        await expect(nextPage).toEqual(emData.Event_Refresh)
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 , timeoutMsg:'Error: Save Schedule button did not appear before timeout'})
        await createEventSchedulePage.selectDropdown('Fiscal Week');
        await eventMaintanencePage.clickOn(storeSelectionPage.saveSchedule)
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000, timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await (storeSelectionPage.storeWeekSchedule).waitForExist({timeout: 20000, timeoutMsg:'Error: week did not appear'})
        let weekText = await storeSelectionPage.storeWeekSchedule.getText()
        await storeSelectionPage.saveGoToStoreSelection()
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkCreatSfpRefreshEvent, emData.Event_Refresh)
        let storePage = await storeSelectionPage.linkCreatSfpRefreshEvent.getText()
        await expect(storePage).toEqual(emData.Event_Refresh)
        await storeSelectionPage.clickCheckBoxText(emData.Store_351);
        await eventMaintanencePage.clickOn(await storeSelectionPage.saveStoreSchedule)
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, emData.Select_Pogs)
        await eventMaintanencePage.waitBrowserToLoadPage(7000)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await selectPOG.selectDropdown('Department', emData.dept_01_GROCERY)
        await eventMaintanencePage.waitBrowserToLoadPage(4000)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await selectPOG.selectDropdown('Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER)
        await eventMaintanencePage.waitBrowserToLoadPage(4000)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await selectPOG.selectDropdown('Commodity *', emData.commodity_003_CAN_BEANS_01_GROC_ALL_OTHER_01_GROCERY)
        await eventMaintanencePage.waitBrowserToLoadPage(4000)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await selectPOG.selectDropdown('Commodity Group', emData.CommodityGroup_013_SPICES_EXTRACTS_013_SPICES_EXTRACTS)
        await eventMaintanencePage.clickOn(selectPOG.selectChoosePogByCommodityBtn) 
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000, timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        let dbKey = await selectPOG.getHeaderValue(emData.Pog_Key_Header);
        let spaceStationName = await selectPOG.getHeaderValue(emData.Space_Station_Name_Header)
        let spaceStationDescription = await selectPOG.getHeaderValue(emData.Space_Station_Version_Description)
        let pogStatus = await selectPOG.getHeaderValue(emData.POG_Status_Header)
        await (await selectPOG.gotoEventListButton).waitForExist({ timeout: 90000, timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await eventMaintanencePage.clickOn(selectPOG.gotoEventListButton)
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters)
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000, timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        let eventDescription = await selectPOG.getEventDescText(await description, weekText.replace(', ','/'))
        await expect(eventDescription).toEqual(true)
        let pogType = await eventMaintanencePage.getHeaderValue(emData.Header_Pog, await description)
        await expect(await pogType).toEqual("1");
        let pogID = await eventMaintanencePage.getPogHeaderValue(emData.Pog_Header)
        await expect(pogID).toEqual(spaceStationName)
        let name = await eventMaintanencePage.getPogHeaderValue(emData.Pog_Name)
        await expect(name).toEqual(await spaceStationDescription)
        let dbKeyNumber = await eventMaintanencePage.getPogHeaderValue(emData.Pog_DBKey)
        await expect(dbKeyNumber).toEqual(dbKey)
        let statusPog = await eventMaintanencePage.getPogHeaderValue(emData.Pog_DBStatus)
        await expect(statusPog).toEqual(pogStatus)
        await eventMaintanencePage.clickCloseButton('Close');
        let refreshType = await eventMaintanencePage.getHeaderValue(emData.Header_Refresh, description)
        await expect(refreshType).toEqual(emData.Event_refreshType_D)
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 70000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, description)
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        let eventId = await eventMaintanencePage.getEventId(initialEventId)
        await expect(eventId).toEqual(initialEventId)
        let dataFromDb = await result.getResult(qrString.SfpEvent_Eventid.replace('eventID', initialEventId))
        await assert.equal(dataFromDb.length, 1, 'Error: Those two values should not be the same !')
    })
})
