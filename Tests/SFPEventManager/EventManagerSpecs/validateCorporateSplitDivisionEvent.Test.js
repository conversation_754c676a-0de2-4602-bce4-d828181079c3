const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const eventSchedulingPage = require('../EventManagerPO/eventScheduling.page')
const selectPOGPage = require("../EventManagerPO/selectingPogCommodities.page");

describe('validateCorporateMultiSchedulesAndStores: validate corporate Event created UI and DB', () => {

    it('Should validate corporate event is created for multiple schedule and stores', async () => {
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.selectEventType('SFP Event')
        expect(await eventMaintanencePage.linkCreateSFPEvent.getText()).toHaveTextContaining(emData.Create_SFP_Event)
        await createSfpEventPage.selectDropdown('division', emData.div_Corporate_060)
        await createSfpEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        await createSfpEventPage.selectDropdown('selectType', "Update");
        let description = await selectPOG.stringGenerator("CorporateEventSplit");
        await createSfpEventPage.enterEventResetDescription(description)
        await createSfpEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSfpEventPage.submitEventDetails()
        let nextPage = await eventMaintanencePage.linkSubModule.getText();
        await expect(nextPage).toEqual(emData.Event_Schedule);
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        for (let index = 0; index <= 1; index++) {
            await eventMaintanencePage.waitBrowserToLoadPage(3000);
            await eventMaintanencePage.waitForPageLoad(eventSchedulingPage.saveScheduleBtn, emData.Save_Schedule);
            await clickOn(await storeSelectionPage.saveSchedule);
        }
        let week1Text = await (await storeSelectionPage.storeWeekSchedule.getText())
        let week2Text = await (await storeSelectionPage.storeWeekSchedule2.getText())
        let weekText = await (await storeSelectionPage.storeWeekSchedule2.getText()).replace('Week', '').replace(', ', '/')
        const Week = week1Text.concat(' -', weekText).replace(', ', '/')
        await storeSelectionPage.saveGoToStoreSelection()
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(await storePage).toEqual(emData.Store_Selection);
        let divNum14 =  (emData.div_Cincinnati_Operating_Division_014.match(/\d/g).join("")).toString();
        let corpNum06 = (emData.div_Corporate_060.match(/\d/g).join("")).toString();
        let dbScheduledStores = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, await divNum14).replace('@num', '30'))).flat();
        let div14 = emData.div_Cincinnati_Operating_Division_014.replace('Cincinnati Operating', '').replace('- ', '').toString().trim()
        await eventMaintanencePage.waitBrowserToLoadPage(6000);
        await storeSelectionPage.clickPlusText(week1Text)
        await storeSelectionPage.clickPlusText(div14)
        await storeSelectionPage.clickCheckBoxText(await dbScheduledStores[8]);
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.clickPlusText(week1Text)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.clickPlusText(await week2Text)
        await storeSelectionPage.clickPlusText(div14)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.clickSecondScheduleStoreCheckbox(await dbScheduledStores[12]);
        await storeSelectionPage.clickPlusText(await week2Text)
        await clickOn(storeSelectionPage.saveStoreSchedule)
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, emData.Select_Pogs)
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(pogPage).toEqual(emData.Select_Pogs)
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await selectPOGPage.selectDropdown('Department', emData.dept_01_GROCERY);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.selectDropdown('Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await selectPOGPage.selectDropdown('Commodity *', emData.ResetComplete_All);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await clickOn(selectPOGPage.selectChoosePogByCommodityBtn);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await (await selectPOG.gotoEventListButton).waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.waitBrowserToLoadPage(8000)
        await clickOn(selectPOGPage.gotoEventListButton);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await expect(await eventMaintanencePage.eventCreationText.getText()).toEqual(emData.Event_Creation);
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, description)
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        let corpDivision = await eventMaintanencePage.getHeaderValue("Division", description)
        await expect(corpDivision).toEqual(corpNum06);
        let eventId = await eventMaintanencePage.getEventId(initialEventId)
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await clickOn(await eventMaintanencePage.eventListFirstRowSchedStr);
        let popUpSchedule = await eventMaintanencePage.getColumnValues(emData.event_Schedule_Popup_Schedule_Table, emData.event_Schedule_Popup_Schedule_List);
        let store = await popUpSchedule.sort().flat()
        await eventMaintanencePage.clickCloseButton('Close');
        await expect(store[0]).toEqual(dbScheduledStores[8]);
        await expect(store[1]).toEqual(dbScheduledStores[12]);
        await eventMaintanencePage.expandEvent(eventId);
        let initialEventId1 = await eventMaintanencePage.getHeaderValueExpand(emData.Event_ID, description);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await eventMaintanencePage.slideOutClose.click();
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        let divNO = await eventMaintanencePage.getHeaderValueExpand("Division", await description);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await expect(await divNO).toEqual(await divNum14);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        await eventMaintanencePage.waitBrowserToLoadPage(5000);
        await eventMaintanencePage.expandEventPlus(await initialEventId1);
        let columnList = await eventMaintanencePage.getValuesOfClickedNumExpanded(emData.plus_Stores_header);
        let columnListStore = await columnList.flat();
        await expect(columnListStore[0]).toEqual(dbScheduledStores[8]);
        await expect(columnListStore[1]).toEqual(dbScheduledStores[12]);
    })
})
