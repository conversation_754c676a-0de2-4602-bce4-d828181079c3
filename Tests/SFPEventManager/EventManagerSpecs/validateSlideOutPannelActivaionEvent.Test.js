const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const { assert, expect } = require('chai')


let keysToFind;

describe('validateSlideOutPannelActivaionEvent: validate the Slide pannel in EM page', () => {

    it('Should validate he slide out pannel for Activation events', async () => {
        let eventDescription = await createSfpEventPage.addEventDetails1('','SFP Activation', emData.div_Cincinnati_Operating_Division_014, emData.eventType_KOMPASS, "Activation", "ActivationEvent", emData.vendor_AZB)
        await createSfpEventPage.submitEventDetails()
        await createEventSchedulePage.scheduling(1)
        await storeSelectionPage.saveGoToStoreSelection()
        let div = (emData.div_Cincinnati_Operating_Division_014.match(/\d/g).join("")).toString()
        let dbScheduledStores = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, div).replace('@num', '30'))).flat();
        await storeSelectionPage.storeSelection(dbScheduledStores, 5, 1)
        await storeSelectionPage.storeSelection(dbScheduledStores, 12, 2)
        await storeSelectionPage.saveStoreScheduleButton()
        await eventMaintanencePage.waitBrowserToLoadPage(6000);
        await createSfpEventPage.inputUPC.setValue(emData.UPC_Value_61076426171)
        await eventMaintanencePage.clickOn(createSfpEventPage.addUPC)
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000, timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await eventMaintanencePage.clickOn(createSfpEventPage.saveUPC)
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await expect(await eventMaintanencePage.eventCreationText.getText()).to.equal(emData.Event_Creation);
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.clickFiltersWithDiscp(eventDescription.toString())
        let eventDisp = await eventMaintanencePage.getHeaderValue(emData.Filters_Event_Desc, eventDescription)
        let scheduled = await eventMaintanencePage.getHeaderValue(emData.Header_Scheduled, eventDescription)
        //stores
        let scheduledStrCount = await eventMaintanencePage.getHeaderValue(emData.Header_ScheduledStr, eventDescription)
        let popUpSchedule = (await eventMaintanencePage.getColumnValues(emData.event_Schedule_Popup_Schedule_Table, emData.event_Schedule_Popup_Schedule_List)).sort();
        let popUpScheduleDiv = await eventMaintanencePage.getColumnValues(emData.event_Schedule_Popup_Schedule_Table, emData.event_Schedule_Popup_Schedule_Div_List)
        const modifiedopUpScheduleDiv = popUpScheduleDiv.map((element) => element.replace(new RegExp(`^Div\\s*`), ''));
        await expect(popUpSchedule.length.toString()).to.equal(scheduledStrCount)
        await eventMaintanencePage.clickCloseButton('Close');
        let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, eventDescription)
        await eventMaintanencePage.waitBrowserToLoadPage(6000);
        let eventIdInSlide = await (await eventMaintanencePage.getTextOfElement(await eventMaintanencePage.slidePaneleventId));
        await expect(initialEventId).to.equal(await eventMaintanencePage.extractDigits(eventIdInSlide));
        await assert.strictEqual(eventDisp, await eventMaintanencePage.getTextOfElement(eventMaintanencePage.slidePanelDiscription))
        // assert slideOutPanel outside values 
        keysToFind = ['Schedule Type:', 'Type:', 'Activation'];
        const slidePanelTableValues = await eventMaintanencePage.getValuesByKeysSlidePanel("4", keysToFind)
        assert.strictEqual(slidePanelTableValues, emData.sildePanel_UPC_KOM_Yes);
        // assert slideOutPanel outside values 
        keysToFind = ['Division:', 'UPC List:','Schedules:', 'Stores Scheduled:', 'Stores Unscheduled:'];
        const slidePanelTableValues1 = await eventMaintanencePage.getValuesByKeysSlidePanel("5", keysToFind)
        assert.strictEqual(slidePanelTableValues1, emData.sildePanel_014_2_2_103);
        //assert EM Schedule dropdown with slideOutPanel
        await eventMaintanencePage.slidePanelTabsClick(emData.slidePanelSchedules);
        await browser.pause(3000);
        const columnValues = eventMaintanencePage.getColumnValuesByHeaderNameSlidePanel(emData.slidePanelSchedules, "# of Stores");
        assert.deepEqual(scheduled,(await columnValues).length.toString());
        //assert EM Stores dropdown with slideOutPanel
        await eventMaintanencePage.slidePanelTabsClick(emData.slidePanelStores);
        const stotreColumnValues = await eventMaintanencePage.getColumnValuesByHeaderNameSlidePanel(emData.slidePanelStores, emData.slidePanelColumnStores);
        const divisionColumnValues = await eventMaintanencePage.getColumnValuesByHeaderNameSlidePanel(emData.slidePanelStores, emData.slidePanelColumnStoresDiv);
        await assert.deepEqual(popUpSchedule, stotreColumnValues);
        await assert.deepEqual(modifiedopUpScheduleDiv, divisionColumnValues);
    })

})