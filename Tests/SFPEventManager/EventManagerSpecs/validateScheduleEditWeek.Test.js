const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const eventSchedule = require('../EventManagerPO/eventScheduling.page')


describe('validateScheduleEditWeek: Validate the week in Event Scheduling', () => {

    it('validate the week in Event Scheduling', async () => {

        await createSfpEventPage.addEventDetails1('',await 'SFP Event', await emData.div_Cincinnati_Operating_Division_014, await emData.eventType_KOMPASS, await "New Item Introduction (NII)", await "npeEventNII", await emData.vendor_AZB)
        await createSfpEventPage.submitEventDetails();
        await createEventSchedulePage.clickSaveScheduleBtn()
        let week = await eventSchedule.getScheduledWeek()
        await eventMaintanencePage.clickOn(eventSchedule.editScheduleList)
        await createEventSchedulePage.selectFiscalWeekDropdown('Fiscal Week In Modal','scheduleEdit');
        await eventMaintanencePage.clickCloseButton('Update');
        let editedWeek = await eventSchedule.getScheduledWeek()
        await expect(week).not.toEqual(editedWeek)
        await expect(editedWeek.toString()).toContain('(Edited)');
        
        // Activity log code is disabled with flag 
        // await eventMaintanencePage.clickOn(await eventSchedule.viewActivityLog);
        // await eventSchedule.waitForPageLoad(await eventSchedule.activityVisibleText, "Activity Log");
        // let oldvalue = await eventSchedule.getHeaderValue("Old Value")
        // editedWeek = await eventSchedule.removeSuffix(editedWeek.toString(),"(Edited),")
        // let newValue = await eventSchedule.getHeaderValue("New Value")
        // newValue = await eventSchedule.removeSuffix(newValue.toString(),"(current version)")
        // await expect(week.toString()).toEqual(oldvalue)
        // await expect(newValue).toEqual(editedWeek)
        
    })
})
