const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const eventSchedulingPage = require('../EventManagerPO/eventScheduling.page')
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const { assert } = require('chai')
var chaiexpect = require('chai').expect
const { setValue } = require('../../../GlobalObjectRepository/login.page');

let newScheduleStoreTblId,neweventScheduleDivTbl,neweventScheduleTbl,scheduleStoreTblId,eventScheduleDivTbl,eventScheduleTbl,planogramEventScheduleId,newplanogramEventScheduleId

describe('validateEditSchedulesForDivisionalEvents: validate edit schedules weeks changes the schedule ID DB', () => {

    before( async () => {
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.tableSecondPageBtn.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.selectEventType('SFP Activation')
        expect(await eventMaintanencePage.linkCreateSFPEvent.getText()).toHaveTextContaining(emData.Create_SFP_Event)
        await createSfpEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014);
        await createSfpEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS);
        await createSfpEventPage.selectDropdown('selectType', "Activation");
        let description = await selectPOG.stringGenerator("editStoreAct")
        await createSfpEventPage.enterEventResetDescription(description)
        await createSfpEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await setValue(createSfpEventPage.defaultMin,3)
        await setValue(createSfpEventPage.defaultAllocation,1)
        await createSfpEventPage.submitEventDetails()
        let nextPage = await eventMaintanencePage.linkSubModule.getText();
        await expect(nextPage).toEqual(emData.Event_Schedule);
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.waitForPageLoad(eventSchedulingPage.saveScheduleBtn, emData.Save_Schedule);
        await clickOn(await storeSelectionPage.saveSchedule);
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.saveGoToStoreSelection()
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection)
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(storePage).toEqual(emData.Store_Selection)
        let divNum14 = (emData.div_Cincinnati_Operating_Division_014.match(/\d/g).join("")).toString()
        let dbScheduledStores = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, divNum14).replace('@num', '30'))).flat()
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await storeSelectionPage.clickScheduleStoreCheckbox(await dbScheduledStores[0],1)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.clickScheduleStoreCheckbox(await dbScheduledStores[12],1);
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await clickOn(storeSelectionPage.saveStoreSchedule)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Upload UPC(s)")
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(pogPage).toEqual(emData.Upload_UPC)
        await createSfpEventPage.inputUPC.setValue(emData.UPC_Value_61076426171)
        await clickOn(createSfpEventPage.addUPC)
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await clickOn(createSfpEventPage.saveUPC)
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, description)
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        scheduleStoreTblId = await (await result.getResult(qrString.eventScheduleStoreTblId.replace('@evID', initialEventId))).flat();
        eventScheduleDivTbl = await (await result.getResult(qrString.eventScheduleDivTbl.replace('@evID', initialEventId))).flat()
        eventScheduleTbl = await (await result.getResult(qrString.eventScheduleTbl.replace('@evID', initialEventId))).flat()
        planogramEventScheduleId = await (await result.getResult(qrString.planogramEventScheduleTblId.replace('@evID', initialEventId))).flat()
        let releaseEventEdit = await eventMaintanencePage.selectReleaseEvents(emData.ReleaseEvent_Edit, emData.Header_Action, description)
        await expect(releaseEventEdit).toEqual(true)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await createSfpEventPage.submitEventDetails()
        await createEventSchedulePage.selectDropdown('Fiscal Week')
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.saveGoToStoreSelection()
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await storeSelectionPage.clickScheduleStoreCheckbox(await dbScheduledStores[0], 1);
        await storeSelectionPage.clickScheduleStoreCheckbox(await dbScheduledStores[15], 1)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await clickOn(storeSelectionPage.saveStoreSchedule)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Upload UPC(s)")
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await clickOn(createSfpEventPage.saveUPC)
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters);
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        let finalEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, description)
        newScheduleStoreTblId = await (await result.getResult(qrString.eventScheduleStoreTblId.replace('@evID', finalEventId))).flat()
        neweventScheduleDivTbl = await (await result.getResult(qrString.eventScheduleDivTbl.replace('@evID', finalEventId))).flat();
        neweventScheduleTbl = await (await result.getResult(qrString.eventScheduleTbl.replace('@evID', finalEventId))).flat();
        newplanogramEventScheduleId = await (await result.getResult(qrString.planogramEventScheduleTblId.replace('@evID', initialEventId))).flat()
        })

    it('Should validate edit store changes the schedule ID in scheduleStoreTbl DB', async () => {
        //validate the deleted scheduleStoreTblId for store table
        for (let index = 0; index < newScheduleStoreTblId.length; index++) {
            chaiexpect(await newScheduleStoreTblId).to.not.include(await scheduleStoreTblId[3])
        }
    })

    it('Should validate edit store changes the schedule ID in eventScheduleTbl DB', async () => {
        //validate the deleted eventScheduleTbl for store table
        for (let index = 0; index < neweventScheduleTbl.length; index++) {
            assert.equal(await neweventScheduleTbl[index], (eventScheduleTbl.toString()), "Deleted Schedule ID found")
        }
    })

    it('Should validate edit store changes the schedule ID in eventScheduleDivTbl DB', async () => {
        //validate the deleted eventScheduleTbl for store table
        chaiexpect(await neweventScheduleDivTbl).to.include.members(await eventScheduleDivTbl)
    })

    it('Should validate edit store changes the schedule ID in eventplanogramtbl DB', async () => {
        //validate the deleted eventScheduleTbl for store table
        assert.equal(await newplanogramEventScheduleId.length, (planogramEventScheduleId.length), "array is same new store added")
        chaiexpect(await newplanogramEventScheduleId).to.include.members(await planogramEventScheduleId)
    })
})