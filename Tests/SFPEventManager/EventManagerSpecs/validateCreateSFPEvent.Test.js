const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData=require('../../../TestData/EventManagerData/eventManagerUIData.json')
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const {getOnlyDay} = require("../../../util/date");


describe('validateCreateSFPEvent: Validate Create SFP Event Page', async () => {

    beforeEach( async() => {
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintenance');  
        await eventMaintanencePage.waitBrowserToLoadPage(1000)   
        await expect(await eventMaintanencePage.GetText(await eventMaintanencePage.emHomePageText)).toEqual('SFP Event Manager')
        await eventMaintanencePage.tableSecondPageBtn.waitForExist({timeout: 90000, timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await eventMaintanencePage.selectEventType('SFP Event')
        expect (await eventMaintanencePage.linkCreateSFPEvent.getText()).toHaveTextContaining(' Create SFP Event ')
        await createSfpEventPage.selectDropdown('division',emData.div_Cincinnati_Operating_Division_014)
        await createSfpEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS);
        await createSfpEventPage.selectDropdown('selectType', "Update");
        await createSfpEventPage.enterEventResetDescription('Event or Reset Description')
        await createSfpEventPage.selectDropdown('vendor',emData.vendor_AZB)
    })
    
    it('Should Validate Create SFP Event page', async () => {
        await createSfpEventPage.clickCheckBox(emData.checkBox_RequestShelfStrips)
        await createSfpEventPage.selectRadioButton(emData.radioBtn_Office)
        await createSfpEventPage.selectRadioButton(emData.radioBtn_MaintenanceStripsChangesOnly)
        await createSfpEventPage.clickCheckBox(emData.checkBox_RequestTags)
        await createSfpEventPage.selectRadioButton(emData.radioBtn_Store)
        await createSfpEventPage.enterValue(emData.field_userHeading,
        await createSfpEventPage.stringGenerator("TestAutomationSet"))
        await createSfpEventPage.enterValue(emData.field_sets,5)
        await createSfpEventPage.clickCheckBox(emData.checkBox_NewItemsOnly)
        await createSfpEventPage.enterUserComments(await createSfpEventPage.stringGenerator("TestAutomationComments "))
        await createSfpEventPage.clickCheckBox(emData.checkBox_ActivateCAO2weeksbeforeEventSchedule)
        await createSfpEventPage.submitEventDetails()
        let nextPage=await eventMaintanencePage.linkSubModule.getText()
        await expect (nextPage).toEqual('Event Schedule')
        await createEventSchedulePage.selectFiscalWeekDropdown('Fiscal Week');
        await createEventSchedulePage.selectDropdown('Process Weekday', emData.processWeekDay_Tuesday )
        await createEventSchedulePage.datePicker(getOnlyDay())
        await createEventSchedulePage.clickSaveScheduleBtn()
        await createEventSchedulePage.waitScheduledDay(emData.processWeekDay_Tuesday)
        await createEventSchedulePage.clickDoneBtn();
        let parentGUID = await browser.getWindowHandle()
        await eventMaintanencePage.waitBrowserToLoadPage(1000)
        let storeSelectionPage = await createEventSchedulePage.linkSubModule.getText()
        await createEventSchedulePage.validateNewTab(parentGUID)
        await expect(storeSelectionPage).toEqual('Store Selection');
    })

    it('Should Validate Create SFP Event page system heading', async () => {
        await createSfpEventPage.clickCheckBox(emData.checkBox_RequestTags)
        await createSfpEventPage.selectRadioButton(emData.radioBtn_Office)
        await eventMaintanencePage.waitBrowserToLoadPage(1000)
        let userHeading=await createSfpEventPage.stringGenerator("TestAutomation")
        await createSfpEventPage.enterValue(emData.field_userHeading,userHeading)
        await createSfpEventPage.clickCheckBox(emData.checkBox_SystemHeading)
        await createSfpEventPage.enterValue(emData.field_sets,1)
        await expect(await createSfpEventPage.sampleText.getText()).toEqual(emData.heading_Prefix_OFC+userHeading)
    })

    it('Should Validate Create SFP Event page system heading error message', async () => {
        await createSfpEventPage.clickCheckBox(emData.checkBox_RequestTags)
        await createSfpEventPage.selectRadioButton(emData.radioBtn_Office)
        await eventMaintanencePage.waitBrowserToLoadPage(1000)
        await createSfpEventPage.enterValue(emData.field_sets,1)
        await createSfpEventPage.btnSubmit.click()
        await createSfpEventPage.errorUserHeading.waitForExist({timeout: 20000}, 'Error:Error message page did not appear before timeout')
        await expect (await createSfpEventPage.errorUserHeading.isExisting()).toEqual(true)
        await expect (await createSfpEventPage.errorUserHeading.getText()).toEqual('Please enter user heading')
        await expect (await createSfpEventPage.errorSubmit.isExisting()).toEqual(true)
        await expect (await createSfpEventPage.errorSubmit.getText()).toEqual('All mandatory fields need to be filled')
    })
})

