const eventMaintenancePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require("../EventManagerPO/storeSelection.page");
const {clickOn} = require("../EventManagerPO/createSFPEvent.page");
const eventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const PogCommodityPage = require('../EventManagerPO/selectingPogCommodities.page');
const selectPOG = require("../EventManagerPO/selectingPogCommodities.page");
const {setValue} = require("../../../GlobalObjectRepository/login.page");



describe('validateSFPActivationEventAlertMessage: Verify alert message on StoreSelection and Selecting Pog/Commodities pages when saveSchedules on Step2:Scheduling is not selected', () => {
    
    beforeEach(async () => {
        await eventMaintenancePage.waitBrowserToLoadPage(6000) 
        await eventMaintenancePage.open('EventMaintenance');  
        await eventMaintenancePage.waitBrowserToLoadPage(6000)     
        await eventMaintenancePage.tableSecondPageBtn.waitForExist({timeout: 90000, timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await eventMaintenancePage.selectEventType('SFP Activation')
        expect(await eventMaintenancePage.linkCreateSFPEvent.getText()).toHaveTextContaining(emData.Create_SFP_Activation_Event)
        await createSFPEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014)
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS);
        await createSFPEventPage.selectDropdown('selectType', "Activation");
        let description = await selectPOG.stringGenerator("AutomationEventActivation");
        await createSFPEventPage.enterEventResetDescription(description);
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB);
        await setValue(createSFPEventPage.defaultMin,3);
        await setValue(createSFPEventPage.defaultAllocation,1);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await createSFPEventPage.submitEventDetails();
        let nextPage = await eventMaintenancePage.linkSubModule.getText();
        await expect(nextPage).toEqual(emData.Event_Schedule);
    })
    
    it('Should Validate Create SFP Event page', async () => {
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await eventSchedulePage.waitForPageLoad(eventSchedulePage.linkSubModule, emData.Event_Schedule);
        await clickOn(storeSelectionPage.storeSelectionPage);
        let storeAlertMessageCapital= await eventMaintenancePage.clickElementWithText(await storeSelectionPage.storeSelectionAlertMessageCapital,"NO SCHEDULES ASSIGNED FOR THIS EVENT")
        await expect(storeAlertMessageCapital).toEqual(emData.NO_SCHEDULES_ASSIGNED);
        let storeAlertMessage =await eventMaintenancePage.clickElementWithText(await storeSelectionPage.storeSelectionAlertMessage,"Please Select Stores For Every Scheduled Week To Proceed with Pog Selection.")
        await expect(storeAlertMessage).toEqual(emData.Please_Select_Stores);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await eventMaintenancePage.clickElementWithText(await (eventMaintenancePage.okBtn),"Ok")
        await eventSchedulePage.waitForPageLoad(eventSchedulePage.linkSubModule, emData.Event_Schedule);
        await expect(await eventSchedulePage.linkSubModule.getText()).toEqual(emData.Event_Schedule);
        await clickOn(PogCommodityPage.uploadUPCPage);
        let pogAlertMessageCapital = await eventMaintenancePage.clickElementWithText(PogCommodityPage.uploadUPCAlertMessageCapital,"NO STORES SELECTED FOR SCHEDULED WEEK");
        await expect(pogAlertMessageCapital).toEqual(emData.NO_STORES_SELECTED);
        let pogAlertMessage = await eventMaintenancePage.clickElementWithText(PogCommodityPage.uploadUPCAlertMessageCapital,"Please Select Stores For Every Scheduled Week To Proceed with Upload UPC(s).")
        await expect(pogAlertMessage).toEqual(emData.Please_Select_Stores_UPC);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await eventMaintenancePage.clickElementWithText(await (eventMaintenancePage.okBtn),"Ok")
        await expect(await storeAlertMessageCapital).toEqual('NO SCHEDULES ASSIGNED FOR THIS EVENT');
        await eventMaintenancePage.clickElementWithText(await (eventMaintenancePage.okBtn),"OK")
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await eventMaintenancePage.clickElementWithText(await (eventMaintenancePage.okBtn),"Ok")
        await eventSchedulePage.waitForPageLoad(eventSchedulePage.linkSubModule, emData.Event_Schedule);
        await expect(await eventSchedulePage.linkSubModule.getText()).toEqual(emData.Event_Schedule);
    })

        it('validateSFPActivationEventAlertMessage: Verify alert message on Selecting Pog/Commodities page when store is not selected on Step3:Store Selection', async () => {
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            await clickOn(storeSelectionPage.saveSchedule);
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            await clickOn(eventSchedulePage.goToStoreSelectionBtn);
            await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
            let storePage = await storeSelectionPage.linkSubModule.getText();
            await expect(storePage).toEqual(emData.Store_Selection);
            await clickOn(PogCommodityPage.uploadUPCPage);
            let pogAlertMessageCapital = await eventMaintenancePage.clickElementWithText(PogCommodityPage.uploadUPCAlertMessageCapital,"NO STORES SELECTED FOR SCHEDULED WEEK");
            await expect(pogAlertMessageCapital).toEqual(emData.NO_STORES_SELECTED);
            let pogAlertMessage = await eventMaintenancePage.clickElementWithText(PogCommodityPage.uploadUPCAlertMessageCapital,"Please Select Stores For Every Scheduled Week To Proceed with Upload UPC(s).")
            await expect(pogAlertMessage).toEqual(emData.Please_Select_Stores_UPC);
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            await eventMaintenancePage.clickElementWithText(await (eventMaintenancePage.okBtn),"OK")
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            await eventSchedulePage.waitForPageLoad(eventSchedulePage.linkSubModule, emData.Store_Selection);
            await expect(await eventSchedulePage.linkSubModule.getText()).toEqual(emData.Store_Selection);
        })

    it('Validate alert message when only one store is selected from multiple stores on Step3:Store Selection:', async () => {
        await clickOn(await eventSchedulePage.eventScheduleHeader);
        for(let i=0; i<1; i++){
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            await clickOn(storeSelectionPage.saveSchedule);
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
        }
        await clickOn(eventSchedulePage.goToStoreSelectionBtn);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
        let storePage = await storeSelectionPage.linkSubModule.getText();
        await expect(storePage).toEqual(emData.Store_Selection);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
       await clickOn(storeSelectionPage.saveStoreSchedule)
        let noStoreSelected = await storeSelectionPage.storeSelectionNoStoreSelectedTxt.getText();
        await expect( noStoreSelected).toEqual(emData.Please_select_at_least_one_store);
    })
})
