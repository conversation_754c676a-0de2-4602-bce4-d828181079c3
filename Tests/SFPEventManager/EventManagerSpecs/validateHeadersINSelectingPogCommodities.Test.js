const eventMaintenancePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const underscoreLib = require('underscore')

describe('validateHeadersINSelectingPogCommoditiesTest: validate Selecting Pog/commodities headers text in UI', () => {

    it('Should validate Selecting Pog/commodities table headers ', async () => {                                                           
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await eventMaintenancePage.open('EventMaintenance');   
        await eventMaintenancePage.waitBrowserToLoadPage(5000);
        await eventMaintenancePage.tableSecondPageBtn.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintenancePage.selectEventType('SFP Event')
        expect(await eventMaintenancePage.linkCreateSFPEvent.getText()).toHaveTextContaining(emData.Create_SFP_Event)
        await createSFPEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014)
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        await createSFPEventPage.selectDropdown('selectType', "Update");
        let description =  await selectPOG.stringGenerator("Event")
        await createSFPEventPage.enterEventResetDescription(description)
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSFPEventPage.submitEventDetails()
        let nextPage = await eventMaintenancePage.linkSubModule.getText()
        await expect(nextPage).toEqual(emData.Event_Schedule)
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        await createEventSchedulePage.genericClickDropdown('Fiscal Week')
        await clickOn(storeSelectionPage.saveSchedule)
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        let weekText = await storeSelectionPage.storeWeekSchedule.getText()
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await clickOn(storeSelectionPage.saveGoToStore)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Store Selection")
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(storePage).toEqual(emData.Store_Selection)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.clickCheckBoxText(emData.Store_353)
        await clickOn(storeSelectionPage.saveStoreSchedule)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Select Pogs")
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(pogPage).toEqual(emData.Select_Pogs)
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Department', emData.dept_01_GROCERY)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Commodity *', emData.commodity_01_GROCERY_01_GROC_ALL_OTHER_013_SPICES_EXTRACTS)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Commodity Group', emData.CommodityGroup_013_SPICES_EXTRACTS_013_SPICES_EXTRACTS)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        let headerArray = await selectPOG.getAllHeader()
        await expect(underscoreLib.isEqual(headerArray, emData.SelectedPOGSheaderlist)).toBe(true)
        await expect(underscoreLib.size(headerArray, emData.SelectedPOGSheaderlist)).toBe(emData.SelectedPOGSheaderlist.length) 
        await clickOn(selectPOG.selectChoosePogByCommodityBtn)  
    })
})
