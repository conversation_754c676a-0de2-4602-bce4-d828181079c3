const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const Result = require('../../../SQLConnection/server.js.ts');
const { assert } = require('chai');
const { DBNames } = require('../../../envConfig.js');

describe('validateSFPEventAisleDelete: validate Sfp event refresh Aisle Delete', () => {

    it('Should validate SFPEventRefreshFlow Aisle Delete page', async () => {  
         await eventMaintanencePage.waitBrowserToLoadPage(6000)                                                                     
        await eventMaintanencePage.open('EventMaintenance'); 
        await eventMaintanencePage.waitBrowserToLoadPage(6000)                                                            
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'});
        await eventMaintanencePage.selectEventType('SFP Refresh')
        expect(await eventMaintanencePage.linkCreateSFPEvent.getText()).toHaveTextContaining(emData.Create_SFP_Event)
        await createSFPEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014)
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_Remodel)
        let description = await selectPOG.stringGenerator("EventAisleDelete")
        await createSFPEventPage.enterEventResetDescription(description)
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSFPEventPage.clickCheckBox(emData.checkBox_Digital_Refresh_ClickList)
        await eventMaintanencePage.waitBrowserToLoadPage(5000)
        await createSFPEventPage.selectRadioButton(emData.radioBtn_aisleDelete)
        await createSFPEventPage.submitEventRefreshDetails()
        await eventMaintanencePage.waitBrowserToLoadPage(5000)
        await clickOn(await createSFPEventPage.sfpAppModalButton)
        let storeSelected = await createSFPEventPage.selectDropdown('Store', '336')
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        let clickedAisleOptionsCheckBoxText = await eventMaintanencePage.clickRandomCheckboxEventRefresh();
        // Below are hard coded  data; Might need this if some stores don't have pogs
        // let aisleOptions = 'CHECKLANE 1 [E]'
        // await createSFPEventPage.storeSelectRadioButton(aisleOptions)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await clickOn(await createSFPEventPage.pogSubmitBtn)
        await expect(browser).toHaveUrlContaining("EventMaintenance")
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters)
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        let eventDescription = await createSFPEventPage.getEventDescText(description)
        await expect(eventDescription).toEqual(true)
        let pogType = await eventMaintanencePage.getHeaderValue(emData.Header_Pog, description)
        let eventIdText = await eventMaintanencePage.getModalEventIdText();
        let numOfPogsOnModal = await eventMaintanencePage.getDigitsAfterSecondColon(await eventIdText);
        await expect(await pogType).toEqual(await numOfPogsOnModal);
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await eventMaintanencePage.clickCloseButton('Close')
        let scheduledStores = await eventMaintanencePage.getHeaderValue(emData.Header_ScheduledStr, description)
        await expect(scheduledStores).toEqual("1")
        await eventMaintanencePage.waitBrowserToLoadPage(4000)
        let text= await eventMaintanencePage.scheduledStore.getText()
        await expect(text).toEqual(storeSelected)
        await eventMaintanencePage.clickCloseButton('Close');
        let refreshType = await eventMaintanencePage.getHeaderValue(emData.Header_Refresh, description)
        await expect(refreshType).toEqual(emData.Event_refreshType_DAD)
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 70000 }, 'not displaying in given time ')
        let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, description)
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        let eventId = await eventMaintanencePage.getEventId(initialEventId)
        await expect(eventId).toEqual(initialEventId)
        let dataFromDb = await Result.getResult(qrString.SfpEvent_Eventid.replace('eventID', initialEventId))
        let corpEventStatus = await Result.getResult(qrString.SfpEvent_corpStatus.replace('eventID', initialEventId))
        await assert.equal(dataFromDb, initialEventId)
        await assert.equal(corpEventStatus, emData.corpEvent_Released)
        let stores = await Result.getResult(qrString.stores_ilp_area_delete.replace('@eventID', initialEventId), DBNames.ILP_STAGE)
        await assert.equal(eventId + ' - '+ clickedAisleOptionsCheckBoxText.substring(0, clickedAisleOptionsCheckBoxText.indexOf('[')).trim() , stores.join(''))
    })
})
