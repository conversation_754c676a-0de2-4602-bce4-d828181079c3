const eventMaintenancePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');

describe('validatePogStatusInSelectingPog.: validate Pog Status in Selecting Pog/commodities page', () => {

    it('Should validate Pog Status in Selecting Pog/commodities page', async () => {  
        await eventMaintenancePage.waitBrowserToLoadPage(6000)                                                                        
        await eventMaintenancePage.open('EventMaintenance'); 
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await eventMaintenancePage.tableSecondPageBtn.waitForExist({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await eventMaintenancePage.selectEventType('SFP Event')
        await expect((await (await eventMaintenancePage.linkCreateSFPEvent).getText()).toString()).toEqual(await emData.Create_SFP_Event)
        await createSFPEventPage.selectDropdown('division', emData.div_Columbus_Operating_Division_016)
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        await createSFPEventPage.selectDropdown('selectType', "Update");
        let description = await selectPOG.stringGenerator("AutomationEventPogStatus")
        await createSFPEventPage.enterEventResetDescription(description)
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSFPEventPage.submitEventDetails()
        let nextPage = await eventMaintenancePage.linkSubModule.getText()
        await expect(nextPage).toEqual(emData.Event_Schedule)
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        await createEventSchedulePage.selectDropdown('Fiscal Week');
        await clickOn(storeSelectionPage.saveSchedule)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await storeSelectionPage.saveGoToStoreSelection()
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(storePage).toEqual(emData.Store_Selection)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.clickRandomEnabledStoreCheckboxes(1, 2);
        await clickOn(storeSelectionPage.saveStoreSchedule)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, "Select Pogs")
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(pogPage).toEqual(emData.Select_Pogs)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Department', emData.dept_01_GROCERY)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER)
        await selectPOG.waitForPageLoad(await selectPOG.selectText,emData.Department)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.setMultipleValues('Commodity *', await emData.PogCommoditylist,'commList')
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        let itemSelected = (await selectPOG.selectCommodity).getText(); 
        await expect(await itemSelected).toEqual('1 Item Selected')
        await selectPOG.waitForPageLoad(await selectPOG.selectText,emData.Department)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await clickOn(selectPOG.selectChoosePogByCommodityBtn)
        await selectPOG.waitForPageLoad(await selectPOG.selectText,emData.Department)
        await selectPOG.waitForPageLoad(await selectPOG.selectText,emData.Department)
        let pogStatus = await selectPOG.getHeaderColumnValue(emData.POG_Status_Header)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        let uniqueStatus  = await selectPOG.getUniqueArrayValue(await pogStatus)
        await expect(await uniqueStatus).toEqual(await emData.PogCommodity_PogStatus)
    })
})
