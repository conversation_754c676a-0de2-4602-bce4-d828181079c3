const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const { assert } = require('chai');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');

let description;
beforeEach( async() => {
    await eventMaintanencePage.waitBrowserToLoadPage(6000)
    await eventMaintanencePage.open('EventMaintenance');
    await eventMaintanencePage.waitBrowserToLoadPage(6000)
    await eventMaintanencePage.selectEventType('SFP Event')
    expect(await (await eventMaintanencePage.linkCreateSFPEvent).getText()).toHaveTextContaining(emData.Create_SFP_Event)
    await createSFPEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014)
    await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
    await createSFPEventPage.selectDropdown('selectType', "Update");
    description = await selectPOG.stringGenerator("AutomationEventHeader")
    await createSFPEventPage.enterEventResetDescription(description)
    await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB)
})

describe('validateEventHeaderTabelForStripAndTag:validate Sfp event headertable for shelf strip and Tag type UI and DB', () => {

    it('Should validate Sfp event headertable for shelf strip type UI and DB ', async () => {
        await createSFPEventPage.clickCheckBox(emData.checkBox_RequestShelfStrips)
        await createSFPEventPage.submitEventDetails()
        let nextPage = await eventMaintanencePage.linkSubModule.getText()
        await expect(nextPage).toEqual(emData.Event_Schedule)
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 , timeoutMsg:'Error: Save Schedule button did not appear before timeout'})
        await createEventSchedulePage.selectDropdown('Fiscal Week');
        await clickOn(storeSelectionPage.saveSchedule)
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        let weekText = await storeSelectionPage.storeWeekSchedule.getText()
        await storeSelectionPage.saveGoToStoreSelection()
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(storePage).toEqual(emData.Store_Selection)
        await eventMaintanencePage.open(emData.EventMaintanceUrl);
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await expect(browser).toHaveUrlContaining("EventMaintenance")
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        let shelfStripType = await eventMaintanencePage.getHeaderValue(emData.Header_Strips, description)
        await expect(shelfStripType).toEqual(emData.Event_Strips_S_R)
        let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, description)
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        let eventId = await eventMaintanencePage.getEventId(initialEventId)
        await expect(eventId).toEqual(initialEventId)
        let eventSelfStrip = await (await result.getResult(qrString.SfpEventRequestShelfStripTypeQuery.replace('@eventID', initialEventId))).flat();
        await expect(eventSelfStrip[0]).toEqual(emData.EventShelf_Strip_type_R)
        await expect(eventSelfStrip[1]).toEqual(emData.Event_strip_type_Store)
        await assert.equal(eventSelfStrip.length, 2)
        let releaseEventEdit = await eventMaintanencePage.selectReleaseEvents(emData.ReleaseEvent_Edit, emData.Header_Action, description)
        await expect(releaseEventEdit).toEqual(true)
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await createSFPEventPage.clickCheckBox(emData.checkBox_RequestShelfStrips)
        await createSFPEventPage.submitEventDetails()
        await expect(nextPage).toEqual(emData.Event_Schedule)
        await eventMaintanencePage.open(emData.EventMaintanceUrl);
        await expect(browser).toHaveUrlContaining("EventMaintenance")
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        let eventType = await eventMaintanencePage.getHeaderValue(emData.Header_Strips, description)
        await expect(eventType).toEqual("-")
        let selfStripUnchecked = await (await result.getResult(qrString.SfpEventRequestShelfStripTypeQuery.replace('@eventID', initialEventId))).flat();
        await expect(selfStripUnchecked[0]).toEqual(null)
        await expect(selfStripUnchecked[1]).toEqual(null)
        await assert.equal(eventSelfStrip.length, 2)
    })

    it('Should validate HeaderTable for request Tag UI and DB', async () => {
        await createSFPEventPage.clickCheckBox(emData.checkBox_RequestTags)
        await createSFPEventPage.selectRadioButton(emData.radioBtn_Office)
        await createSFPEventPage.enterValue(emData.field_userHeading,await createSFPEventPage.stringGenerator("TestAutomationSet"))
        await createSFPEventPage.clickCheckBox(emData.checkBox_SystemHeading)
        await createSFPEventPage.submitEventDetails()
        let nextPage = await eventMaintanencePage.linkSubModule.getText()
        await expect(nextPage).toEqual(emData.Event_Schedule)
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 , timeoutMsg:'Error: Save Schedule button did not appear before timeout'})
        await createEventSchedulePage.selectDropdown('Fiscal Week');
        await createEventSchedulePage.clickSaveScheduleBtn()
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await storeSelectionPage.saveGoToStoreSelection()
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(storePage).toEqual(emData.Store_Selection)
        await eventMaintanencePage.open(emData.EventMaintanceUrl);
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await expect(browser).toHaveUrlContaining("EventMaintenance")
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        await (await eventMaintanencePage.tableFirstRow).waitForExist(({ timeout: 100000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'}))
        let requestTagType = await eventMaintanencePage.getHeaderValue(emData.Header_Tags, description)
        await expect(requestTagType).toEqual(emData.Event_Tags_O_OFC)
        await (await eventMaintanencePage.tableFirstRow).waitForExist(({ timeout: 100000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'}))
        let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, description)
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        let eventId = await eventMaintanencePage.getEventId(initialEventId)
        await expect(eventId).toEqual(initialEventId)
        let requestTags = await (await result.getResult(qrString.sfpEventRequestTagsType.replace('@eventID', initialEventId))).flat();
        await expect(requestTags[0]).toEqual(true)
        await assert.equal(requestTags.length, 1)
        let releaseEventEdit = await eventMaintanencePage.selectReleaseEvents(emData.ReleaseEvent_Edit, emData.Header_Action, description)
        await expect(releaseEventEdit).toEqual(true)
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await createSFPEventPage.clickCheckBox(emData.checkBox_SystemHeading)
        await createSFPEventPage.submitEventDetails()
        await expect(nextPage).toEqual(emData.Event_Schedule)
        await eventMaintanencePage.open(emData.EventMaintanceUrl);
        await expect(browser).toHaveUrlContaining("EventMaintenance")
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        let eventType = await eventMaintanencePage.getHeaderValue(emData.Header_Tags, description)
        await expect(eventType).toEqual(emData.Event_Tags_O_OFC)
        let requestTagTypeUnchecked = await (await result.getResult(qrString.sfpEventRequestTagsType.replace('@eventID', initialEventId))).flat();
        await expect(requestTagTypeUnchecked[0]).toEqual(false)
        await assert.equal(requestTagTypeUnchecked.length, 1)
    })
})
