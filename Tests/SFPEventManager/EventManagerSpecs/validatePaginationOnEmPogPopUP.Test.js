const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require("../../../TestData/EventManagerData/eventManagerUIData.json")
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');


describe('validatePaginationOnEmPogPopUP: validate Pogs selected in Pog/Commodities with EM page', () => {

    it('Should validate Pogs selected in Pog/Commodities with EM page', async () => {  
        await eventMaintanencePage.waitBrowserToLoadPage(6000)                                                                    
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.tableSecondPageBtn.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.selectEventType('SFP Event')
        expect(await eventMaintanencePage.linkCreateSFPEvent.getText()).toHaveTextContaining(emData.Create_SFP_Event)
        let sfpBreadCrumbText = await eventMaintanencePage.linkCreateSFPEvent.getText();
        await expect(sfpBreadCrumbText).toEqual(await emData.Create_SFP_Event);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await createSFPEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014);
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        await createSFPEventPage.selectDropdown('selectType', "Update");
        let description = await selectPOG.stringGenerator("AutomationEventPogCount");
        await createSFPEventPage.enterEventResetDescription(description);
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB);
        await createSFPEventPage.submitEventDetails();
        let nextPage = await eventMaintanencePage.linkSubModule.getText();
        await expect(nextPage).toEqual(emData.Event_Schedule);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Event Schedule");
        await createEventSchedulePage.genericClickDropdown('Fiscal Week');
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout');
        await clickOn(storeSelectionPage.saveSchedule);
        let weekText = await storeSelectionPage.storeWeekSchedule.getText();
        await clickOn(storeSelectionPage.saveGoToStore);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Store Selection");
        let storePage = await storeSelectionPage.linkSubModule.getText();
        await expect(storePage).toEqual(emData.Store_Selection);
        await storeSelectionPage.clickCheckBoxText(emData.Store_351);
        await clickOn(storeSelectionPage.saveStoreSchedule);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Select Pogs");
        let pogPage = await storeSelectionPage.linkSubModule.getText();
        await expect(pogPage).toEqual(emData.Select_Pogs)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await selectPOG.selectDropdown('Department', emData.dept_01_GROCERY)
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOG.selectDropdown('Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER);
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOG.selectDropdown('Commodity *', emData.ResetComplete_All);
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await clickOn(selectPOG.selectChoosePogByCommodityBtn);
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(30000);
        let spaceStationName = await selectPOG.getHeaderValues(emData.Space_Station_Name_Header)
        await (await selectPOG.gotoEventListButton).waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await clickOn(selectPOG.gotoEventListButton)
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await expect(browser).toHaveUrlContaining("EventMaintenance")
        await eventMaintanencePage.tableSecondPageBtn.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters);
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        let pogType = await eventMaintanencePage.getHeaderValue(emData.Header_Pog, description)
        let pogStringStoreNum = await eventMaintanencePage.extractingDigit(emData.PogString, emData.PogString, 1);
        await expect(pogType).toEqual(pogStringStoreNum)
        let pogID = await eventMaintanencePage.getPogHeaderValues(emData.Pog_Header)
        await expect(spaceStationName.length).toEqual(pogID.length)
        await expect( spaceStationName.sort()).toEqual( pogID)
        await eventMaintanencePage.clickCloseButton('Close');
    })
})
