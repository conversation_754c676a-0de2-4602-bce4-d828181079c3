const resetCompletionPage = require('../EventManagerPO/resetCompletion.page.js')
const emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page.js');
const autoActivationReport = require('../EventManagerPO/autoActivationReport.page.js');
const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const { getFilesCount, getPDFText } = require("../../../util/pdfReader");
const result = require("../../../SQLConnection/server.js.ts");
const qrString = require("../../../SQLConnection/queryStringEM");
const { assert } = require('chai');


describe('validatePDFDownloadOnResetComplete', () => {

    it('should validate PDF Download in Store review tab', async () => {
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await resetCompletionPage.open("EventMaintenance/resetCompletion");
        //expect(await resetCompletionPage.linkSubModule.getText()).toHaveTextContaining(emData.Admin_reset_completion)
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await resetCompletionPage.selectSingleValue(await emData.selectedDivision_ResetCompletion, '','divisionMultiSelect',emData.division_Cincinnati_Operating_Division_014)
        let div = (await resetCompletionPage.getTextOfElement(resetCompletionPage.selectDivisionTxt)).match(/\d/g).join("").toString()
        await expect(await resetCompletionPage.getTextOfElement(resetCompletionPage.selectDivision)).toEqual(emData.division_Cincinnati_Operating_Division_014_Txt)
        await eventMaintanencePage.waitBrowserToLoadPage(5000);
        await resetCompletionPage.setSingleValue(emData.selectedLocation_ResetCompletion, emData.selectedLocation_014_00920)
        let loc = (await resetCompletionPage.getTextOfElement(resetCompletionPage.selectLocationtext)).substring(6, 12).toString()
        await eventMaintanencePage.waitBrowserToLoadPage(5000);
        expect(await resetCompletionPage.getTextOfElement(resetCompletionPage.selectLocation)).toEqual(await emData.selectedLocation_014_00920_Text);
        await autoActivationReport.selectYPW(emData.Year_Input, emData.Year_2023)
        await expect(await resetCompletionPage.getTextOfElement(resetCompletionPage.selectYear)).toEqual(emData.Year_2023)
        await autoActivationReport.selectYPW(emData.Period_Input, emData.Period_Empty);
        await storeSelectionPage.clickOn(resetCompletionPage.searchBtn)
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await storeSelectionPage.clickOn(resetCompletionPage.storeReview)
        let ssn = await resetCompletionPage.getHeaderStoreReview("Space Station Name")
        let dbKey = await (await result.getResult(qrString.resetCompleteSnnvalue.replace('@div', div).replace('@loc', loc).replace('@ssn', ssn)))
        let initialFilesCount = getFilesCount()
        let parentGUID = browser.getWindowHandle()
        await eventMaintanencePage.getpdfLink("PDF")
        await eventMaintanencePage.waitBrowserToLoadPage(10000)
        await eventMaintanencePage.validateNewTab(parentGUID)
        let finalFilesCount = getFilesCount()
        await expect(finalFilesCount - initialFilesCount).toEqual(1)
        let data = await getPDFText();
        let pogDbKeyPdf = data.split('DBKey:')[1].substr(1, 7)
        if (typeof pogDbKeyPdf !== 'undefined' || pogDbKeyPdf.length === 0) {
            await assert.equal(pogDbKeyPdf, dbKey)
        } else {
            await assert.fail(0, 1, "Error: Could not read pdf content; data is empty !");
        }
        await expect(pogDbKeyPdf).toEqual(dbKey.join())
    })
})
