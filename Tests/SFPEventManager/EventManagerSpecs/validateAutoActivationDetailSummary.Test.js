const autoActivationReport = require('../EventManagerPO/autoActivationReport.page')
let emData = require('../../../Testdata/EventManagerdata/eventManagerUIdata.json')
const {excelDataCompareAllRow} = require("../../../util/excelReader");
const qrString = require("../../../SQLConnection/queryStringEM");
const result = require("../../../SQLConnection/server.js.ts");
const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page')
const {DBNames} = require("../../../envConfig.js");

describe('validateAutoActivationDetailSummary: validate Auto Activation RReport page', () => {

    it('Should validate Auto Activation Recap Report and Detail Summary', async () => {
        await autoActivationReport.open('EventMaintenance/autoActivationReport');
        await autoActivationReport.selectSingleValue('Division',emData.div_Cincinnati_Operating_Division_014)
        await autoActivationReport.selectYPW('Year', emData.Year_2019)
        await autoActivationReport.selectYPW('Period', emData.Period_Empty)
        await autoActivationReport.selectYPW('Week', emData.Week)
        await autoActivationReport.selectSingleValue('Error Code', emData.ErrorCode_4)
        let div = emData.div_Cincinnati_Operating_Division_014.split('- ')[1]
        let errorCode = emData.ErrorCode_4.split(' -')[0]
        let upc = await result.getResult(qrString.getRandomUPC.replace('@div',div).replace('@year',emData.Year_2019).replace('@error',errorCode) ,DBNames.SFP_STAGE);
        await autoActivationReport.enterUPC(upc.flat())
        await eventMaintanencePage.waitBrowserToLoadPage(8000)
        await autoActivationReport.exportToExcel()
        let data = await excelDataCompareAllRow(qrString.autoActivationRecap.replace('@upc',upc.toString()).replace('@div',div).replace('@year',emData.Year_2019).replace('@error',errorCode), DBNames.SFP_STAGE);
        await expect(data).toEqual(true)
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        let uiData = await autoActivationReport.getAllUIData()
        let validateData = await autoActivationReport.uiDataCompareAllRow(uiData,qrString.autoActivationSummary.replace(/@upc/g,upc.toString()).replace('@div',div).replace('@year',emData.Year_2019).replace('@error',errorCode), DBNames.SFP_STAGE);
        await expect(validateData).toEqual(true)
    })

    it('Should validate Change UPC functionality on Auto Activation Detail Summary', async () => {                 
        await autoActivationReport.open('EventMaintenance/autoActivationReport');
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await autoActivationReport.selectSingleValue('Division', emData.div_Cincinnati_Operating_Division_014)
        await autoActivationReport.selectYPW('Year', emData.Year_2019)
        await autoActivationReport.selectYPW('Period', emData.Period_Empty)
        await autoActivationReport.selectYPW('Week', emData.Week)
        await autoActivationReport.selectSingleValue('Error Code', emData.ErrorCode_3)
        let div = emData.div_Cincinnati_Operating_Division_014.split('- ')[1]
        let errorCode = emData.ErrorCode_3.split(' -')[0]
        let upc = await (await (await result.getResult(qrString.getRandomChangeUPC.replace('@div', div).replace('@year', emData.Year_2019).replace('@error', errorCode), DBNames.SFP_STAGE)));
        await autoActivationReport.enterUPC(upc.flat())
        await eventMaintanencePage.waitBrowserToLoadPage(8000)
        await autoActivationReport.changeUPC(upc.replace(upc[0], "1"))
        await expect (await autoActivationReport.getDisplayMessage()).toEqual(TSC+' record(s) updated successfully!')
    })

    it('Should validate Delete Error logs functionality on Auto Activation Detail Summary', async () => {                
        await autoActivationReport.open('EventMaintenance/autoActivationReport');
        let div = emData.div_Cincinnati_Operating_Division_014.split('- ')[1]
        let year=emData.Year_2019
        await autoActivationReport.selectSingleValue('Division', div)
        await autoActivationReport.selectYPW('Year', year)
        await autoActivationReport.selectYPW('Period', emData.Period_Empty)
        await autoActivationReport.selectYPW('Week', emData.Week)
        await autoActivationReport.selectSingleValue('Error Code', emData.ErrorCode_4)
        let errorCode = emData.ErrorCode_4.split(' -')[0]
       // Left this part for testing and fixing purpose because test is failing ==> ([].concat.apply([], await
        let upc = await (await ( await result.getResult(qrString.getRandomChangeUPC.replace('@div', div).replace('@year', year).replace('@error', errorCode), DBNames.SFP_STAGE)).toString()).flat();
        await autoActivationReport.enterUPC(upc)
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await autoActivationReport.deleteErrorLog()
        let toastMsg = await autoActivationReport.getDisplayMessage()
        let flag=false
        if(toastMsg.includes('error log(s) deleted successfully!')){
            flag=true
        }
        await expect (flag).toEqual(true)
        let deletedStatus = await( await result.getResult(qrString.deletedAutoActivationQuery.replace('@upc', upc).replace('@div', div).replace('@year', year).replace('@error', errorCode), DBNames.SFP_STAGE)).flat();
        for(let i=0;i<deletedStatus.length;i++) {
            await expect(deletedStatus[i]).toEqual('Y ')
        }
    })

    it('Should validate count info in UPC Summary message in Auto Activation Recap Report and Detail Summary', async () => {                 
        await autoActivationReport.open('EventMaintenance/autoActivationReport');
        await autoActivationReport.selectSingleValue('Division', emData.div_Cincinnati_Operating_Division_014)
        await autoActivationReport.selectYPW('Year', emData.Year_2019)
        await autoActivationReport.selectYPW('Period', emData.Period_Empty)
        await autoActivationReport.selectYPW('Week', emData.Week)
        await autoActivationReport.selectSingleValue('Error Code', emData.ErrorCode_5)
        let div = emData.div_Cincinnati_Operating_Division_014.split('- ')[1]
        let errorCode = emData.ErrorCode_5.split(' -')[0]
        let upc = await (await result.getResult(qrString.getRandomUPC.replace('@div', div).replace('@year', emData.Year_2019).replace('@error', errorCode), DBNames.SFP_STAGE)).flat();
        await autoActivationReport.enterUPC(upc)
        await eventMaintanencePage.waitBrowserToLoadPage(8000)
        await autoActivationReport.goToDetailSummaryReport('TSC')
        await eventMaintanencePage.waitBrowserToLoadPage(20000)
        let upcSummaryUI = (await autoActivationReport.msgUPCSummary.getText())
        let upcSummaryCountsUI=[]
        for (let i=0;i<5;i++){
            upcSummaryCountsUI.push(upcSummaryUI.match(/(\d[\d]*)/g)[i])
        }
        let upcSummaryDB=  await result.getResult(qrString.upcSummaryAutoActivation.replace('@upc', upc.toString()), DBNames.SFP_STAGE)
        let dbDataArr = Object.keys(upcSummaryDB).map(function (key) {
            return upcSummaryDB[key]
        });
        let upcSummaryCountsDB= await dbDataArr.flat()
        for(let j=0;j<upcSummaryCountsDB.length;j++){
            await expect (upcSummaryCountsDB[j].toString()).toEqual(upcSummaryCountsUI[j])
        }
    })

})
