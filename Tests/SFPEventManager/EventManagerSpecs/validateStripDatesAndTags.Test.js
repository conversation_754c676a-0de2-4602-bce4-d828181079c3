const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');

describe('validateStripDatesAndTags: validate the StripDatesAndTags for npe Event', () => {

    it('validate AutoActivation', async () => {
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.tableSecondPageBtn.waitForExist({ timeout: 90000, timeoutMsg: 'Error: Table contents for event maintenance page didn`t load before timeout' });
        await eventMaintanencePage.selectEventType('SFP Event');
        expect(await (await eventMaintanencePage.linkCreateSFPEvent).getText()).toHaveTextContaining(emData.Create_SFP_Event)
        await createSFPEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014)
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        await createSFPEventPage.selectDropdown('selectType', "Update");
        await createSFPEventPage.enterEventResetDescription('Event or Reset Description')
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSFPEventPage.clickRadioButtonByText("Activate CAO 1 week before Event Schedule")
        // Get the fiscal week value
        let fiscalWeek = await eventMaintanencePage.getFiscalWeekAndValue("For Activation 1 week prior to Event Date");
        // Extract the week number from fiscalWeek using a regular expression
        let extractedWeekNumber = fiscalWeek.match(/(\d+)/);
        // If a match is found, get the first capturing group (which is the week number)
        let expectedWeekNumber = extractedWeekNumber ? extractedWeekNumber[1] : null;
        await createSFPEventPage.submitEventDetails()
        let nextPage = await eventMaintanencePage.linkSubModule.getText()
        await expect(nextPage).toEqual(emData.Event_Schedule)
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000, timeoutMsg: 'Error: Save Schedule button did not appear before timeout' })
        // Get the FiscalWeekList
        let FiscalWeekList = await createEventSchedulePage.getFiscalWeekList();
        // Assert that fiscalWeek is present in FiscalWeekList
        await expect(FiscalWeekList.join().includes(expectedWeekNumber)).toBe(true);
        // Assert that fiscalWeek contains the expected week number "1"
        await expect(fiscalWeek.includes(expectedWeekNumber)).toBe(true);
        await eventMaintanencePage.clickOn(storeSelectionPage.goToAddEventDetail);
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await createSFPEventPage.clickRadioButtonByText("Activate CAO 2 weeks before Event Schedule")
        let fiscalWeek2 = await eventMaintanencePage.getFiscalWeekAndValue("For Activation 2 weeks prior to Event Date");
        let extractedWeekNumber1 = fiscalWeek2.match(/(\d+)/);
        await createSFPEventPage.clickCheckBox(emData.checkBox_CAO_2week_EventSchedule)
        let expectedWeekNumber1 = extractedWeekNumber1 ? extractedWeekNumber1[1] : null;
        await createSFPEventPage.submitEventDetails()
        // Assert that fiscalWeek 2 week is present in FiscalWeekList
        await expect(await FiscalWeekList.join().includes(await expectedWeekNumber1)).toBe(true);
        // Assert that fiscalWeek contains the expected week number "2"
        await expect(fiscalWeek2.includes(expectedWeekNumber1)).toBe(true);
        await eventMaintanencePage.clickOn(storeSelectionPage.goToAddEventDetail);
        let fiscalWeekShelfStrip = await eventMaintanencePage.getFiscalWeekAndValue("When ordering Shelf Strips");
        let extractedWeekShelfStrip = fiscalWeekShelfStrip.match(/(\d+)/);
        await createSFPEventPage.clickCheckBox(emData.checkBox_RequestShelfStrips)
        await createSFPEventPage.clickCheckBox(emData.checkBox_RequestTags)
        await createSFPEventPage.selectRadioButton(emData.radioBtn_Store)
        await createSFPEventPage.enterValue(emData.field_userHeading, await createSFPEventPage.stringGenerator("TestAutomationSet"))
        await createSFPEventPage.enterValue(emData.field_sets, 5)
        await createSFPEventPage.clickCheckBox(emData.checkBox_NewItemsOnly)
        await createSFPEventPage.enterUserComments(await createSFPEventPage.stringGenerator("TestAutomationComments "))
        let expectedWeekNumber2 = extractedWeekShelfStrip ? extractedWeekShelfStrip[1] : null;
        await createSFPEventPage.submitEventDetails()
        // Assert that fiscalWeek 2 week is present in FiscalWeekList
        await expect(await FiscalWeekList.join().includes(await expectedWeekNumber2)).toBe(true);
        // Assert that fiscalWeek contains the expected week number "3"
        await expect(fiscalWeekShelfStrip.includes(expectedWeekNumber2)).toBe(true);
    })
})