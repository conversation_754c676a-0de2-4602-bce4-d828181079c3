const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require("../EventManagerPO/storeSelection.page");
const {clickOn} = require("../../SFPEventManager/EventManagerPO/createSFPEvent.page");
const eventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const pogCommodityPage = require('../EventManagerPO/selectingPogCommodities.page');
const selectPOG = require("../EventManagerPO/selectingPogCommodities.page");

describe('validateSFPRefreshEventAlertMessage: Validate alert messages in SFP Refresh flow process', () => {
    
    beforeEach(async () => {
        await eventMaintanencePage.waitBrowserToLoadPage(4000)
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.waitBrowserToLoadPage(4000)
        await eventMaintanencePage.tableSecondPageBtn.waitForExist({ timeout: 90000, timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await eventMaintanencePage.selectEventType('SFP Refresh')
        await expect(await eventMaintanencePage.linkCreateSFPEvent.getText()).toEqual(emData.Event_Refresh);
        await createSfpEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014)
        await createSfpEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        let description = await selectPOG.stringGenerator("EventRefresh")
        await createSfpEventPage.enterEventResetDescription(description);
        await createSfpEventPage.selectDropdown('vendor', emData.vendor_AZB);
        await createSfpEventPage.clickCheckBox(emData.checkBox_Digital_Refresh_ClickList);
        await createSfpEventPage.submitEventRefreshDetails();
        await clickOn(await createSfpEventPage.sfpAppModalButton);
    })
    
    it('Verify alert message on StoreSelection and Selecting Pog/Commodities pages when saveSchedules on Step2:Scheduling is not selected', async () => {

        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await eventMaintanencePage.waitForPageLoad(await eventSchedulePage.linkCreateRefreshEventSchedule, emData.Event_Refresh);
        await clickOn(await storeSelectionPage.storeSelectionPage);
        await eventMaintanencePage.waitBrowserToLoadPage(6000);
        let storeAlertMessageCapital = await (await storeSelectionPage.storeSelectionAlertMessageCapital).getText();
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await expect(await storeAlertMessageCapital).toEqual(emData.NO_SCHEDULES_ASSIGNED);
        let storeAlertMessage = await (await storeSelectionPage.storeSelectionAlertMessage).getText();
        await expect(await storeAlertMessage).toEqual(emData.Please_Select_Stores);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await clickOn(await storeSelectionPage.storeSelectionAlertOKBtn);
        await eventMaintanencePage.waitForPageLoad(await eventSchedulePage.linkCreateRefreshEventSchedule, emData.Event_Refresh);
        await expect(await eventSchedulePage.linkCreateRefreshEventSchedule.getText()).toEqual(emData.Event_Refresh);
        await clickOn(await pogCommodityPage.pogCommodityPage);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.waitForPageLoad(await pogCommodityPage.pogCommodityAlertOKBtn, emData.OK_Text);
        let pogAlertMessageCapital = await pogCommodityPage.getArrayElementsText(await pogCommodityPage.pogCommodityAlertMessageCapitalList);
        await expect(await pogAlertMessageCapital[0].toString()).toEqual(emData.NO_STORES_SELECTED);
        let pogAlertMessage = await pogCommodityPage.getArrayElementsText(await pogCommodityPage.pogCommodityAlertMessageList);
        await expect(await pogAlertMessage[0].toString()).toEqual(emData.Please_Select_Stores);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await pogCommodityPage.iterateDropdownListAndClickValue(await pogCommodityPage.pogCommodityAlertOKBtnList, 'Ok');
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await expect(await (await storeSelectionPage.storeSelectionAlertMessageCapital).getText()).toEqual(emData.NO_SCHEDULES_ASSIGNED);
        await clickOn(await storeSelectionPage.storeSelectionAlertOKBtn);
        await eventSchedulePage.waitForPageLoad(await eventSchedulePage.linkCreateRefreshEventSchedule, emData.Event_Refresh);
        await expect(await eventSchedulePage.linkCreateRefreshEventSchedule.getText()).toEqual(emData.Event_Refresh);
        for(let index=0; index<=1; index++){
            await eventMaintanencePage.waitBrowserToLoadPage(2000);
            await clickOn(await storeSelectionPage.saveSchedule);
            await eventMaintanencePage.waitBrowserToLoadPage(2000);
        }
        await expect(await (await eventSchedulePage.saveScheduleFailedTxt).getText()).toEqual(emData.Save_Schedule_Failed_message);
    })

        it('Verify alert message on Selecting Pog/Commodities page when store is not selected on Step3:Store Selection', async () => {

            await eventMaintanencePage.waitBrowserToLoadPage(2000);
            await clickOn(await storeSelectionPage.saveSchedule);
            await eventMaintanencePage.waitBrowserToLoadPage(2000);
            await clickOn(await eventSchedulePage.goToStoreSelectionBtn);
            await eventMaintanencePage.waitBrowserToLoadPage(2000);
            await storeSelectionPage.waitForPageLoad(await eventSchedulePage.linkCreateRefreshEventSchedule, emData.Event_Refresh);
            let storePage = await eventSchedulePage.linkCreateRefreshEventSchedule.getText();
            await expect(await storePage).toEqual(emData.Event_Refresh);
            await clickOn(await pogCommodityPage.pogCommodityPage);
            await eventSchedulePage.waitForPageLoad(await pogCommodityPage.pogCommodityAlertOKBtn, emData.OK_Text);
            let pogAlertMessageCapital = await pogCommodityPage.pogCommodityAlertMessageCapital.getText();
            await expect(await pogAlertMessageCapital).toEqual(emData.NO_STORES_SELECTED);
            let pogAlertMessage = await pogCommodityPage.pogCommodityAlertMessage.getText();
            await expect(pogAlertMessage).toEqual(emData.Please_Select_Stores);
            await eventMaintanencePage.waitBrowserToLoadPage(2000);
            await clickOn(await pogCommodityPage.pogCommodityAlertOKBtn);
            await eventMaintanencePage.waitBrowserToLoadPage(2000);
            await eventSchedulePage.waitForPageLoad(await eventSchedulePage.linkCreateRefreshEventSchedule, emData.Event_Refresh);
            await expect(await eventSchedulePage.linkCreateRefreshEventSchedule.getText()).toEqual(emData.Event_Refresh);
            await clickOn(await storeSelectionPage.saveStoreSchedule);
            await expect(await storeSelectionPage.noStoreSelectedEventRefreshTxt.getText()).toEqual(emData.Please_select_at_least_one_store);
        })
})
