const eventMaintanencePage = require("../EventManagerPO/eventMaintanence.page");
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const UPLOrderPage = require('../EventManagerPO/UPLOrderFlow.page');
import {getTodayDate } from "../../../util/date"
const qrString = require('../../../SQLConnection/queryStringEM');
const Result = require('../../../SQLConnection/server.js.ts');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')

describe('validateCreateUPLOrderEvent: validate UPL orders event created UI and DB', () => {


    beforeEach( async() => {
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000, timeoutMsg: "Error: Table contents for event maintenance page did not load before timeout" });
        await eventMaintanencePage.selectEventType('UPL Orders')
        await eventMaintanencePage.selectEventType(emData.UPL_Orders);
        expect(await UPLOrderPage.linkSubModule.getText()).toHaveTextContaining(emData.Create_UPL_Orders);
        await UPLOrderPage.selectSingleValue(emData.Location, emData.Locations, emData.Loc00005_Oab_Albany_Fred_Meyer_Stores);
        await expect(await UPLOrderPage.selectLocationDropdown.getText()).toEqual(emData.Loc00005_Oab_Albany_Fred_Meyer_Stores);
        await eventMaintanencePage.waitBrowserToLoadPage(7000);
        await UPLOrderPage.selectSingleValue(emData.Department, emData.Departments, emData.Depart_96_GM_MISC);
        await expect(await UPLOrderPage.selectDepartmentDropdown.getText()).toEqual(emData.Depart_96_GM_MISC);
        await eventMaintanencePage.waitBrowserToLoadPage(7000);
        await UPLOrderPage.selectRadioButton(emData.Category_Name, emData.Charcoal);
        await eventMaintanencePage.waitBrowserToLoadPage(7000);
        let catagoryText = (await UPLOrderPage.selectCategoryDropdown).getText()
        await expect(await catagoryText).toEqual(emData.Charcoal);
        await UPLOrderPage.selectRadioButton(emData.Product_Group,  emData.FLG);
        await expect(await UPLOrderPage.selectProductGroupDropdown.getText()).toEqual(emData.FLG);
        await UPLOrderPage.inputSendDate(getTodayDate());
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await UPLOrderPage.setValue(UPLOrderPage.getMailLocationInput, await UPLOrderPage.stringGenerator('ML'));
        await UPLOrderPage.setValue(UPLOrderPage.getMailStopInput, await UPLOrderPage.stringGenerator('MS'));
        await UPLOrderPage.setValue(UPLOrderPage.getRoutingInitialsInput, await UPLOrderPage.stringGenerator('RI'));
    })

    it('Validate UPL creation using the "order by department" input category with numbers 1, 2, 3, or 7', async () => {

        let fpStatues = ["1","2","3","7"];
        for(let index = 0; index < fpStatues.length; index++) {
            await UPLOrderPage.setValue(UPLOrderPage.selectATTNToInput, await UPLOrderPage.stringGenerator('fp'));
            await UPLOrderPage.setValue(UPLOrderPage.getMailLocationInput, await UPLOrderPage.stringGenerator('ML'));
            await UPLOrderPage.setValue(UPLOrderPage.getMailStopInput, await UPLOrderPage.stringGenerator('MS'));
            await UPLOrderPage.setValue(UPLOrderPage.getRoutingInitialsInput, await UPLOrderPage.stringGenerator('RI'));
            await UPLOrderPage.setValue(UPLOrderPage.orderByDepartmentInput, fpStatues[index]);
            await UPLOrderPage.clickOn(await UPLOrderPage.getSetupAllPOGS);
            await eventMaintanencePage.waitBrowserToLoadPage(3000);
            await UPLOrderPage.alertPopupMessageValidation(await UPLOrderPage.selectProcessUPLRequest, emData.processing_complete);
        }
    })

    it('validate UPL orders event created UI and DB ', async () => {

        await UPLOrderPage.setValue(UPLOrderPage.selectATTNToInput, await UPLOrderPage.stringGenerator('upl9'));
        await eventMaintanencePage.waitBrowserToLoadPage(7000);
        let attnNoText =  (await UPLOrderPage.selectATTNToInput).getValue();
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await UPLOrderPage.setValue(UPLOrderPage.orderByDepartmentInput,"1");
        await storeSelectionPage.clickOn(await UPLOrderPage.getUpdateRequestTable);
        await UPLOrderPage.dataTable.waitForExist()
        await eventMaintanencePage.waitBrowserToLoadPage(7000);
        let attnNo = await UPLOrderPage.getAllColumnValue(emData.UPL_ATTN_No, attnNoText);
        await UPLOrderPage.clickOn(UPLOrderPage.selectProcessUPLRequest)
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        let UPLeventCreated = await Result.getResult(qrString.UPlEventcreated.replace('@AttNO', attnNo));
        let initialEventId = (await UPLeventCreated).flat().toString();
        await eventMaintanencePage.open(emData.EventMaintanceUrl);
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        let eventId = await eventMaintanencePage.getEventId(initialEventId)
        await expect(eventId).toEqual(initialEventId)
    })

   it('Validate UPL order warning with invalid order by department number, Validate clear button', async () => {

       await UPLOrderPage.setValue(UPLOrderPage.orderByDepartmentInput,"8");
       await UPLOrderPage.alertPopupMessageValidation(await UPLOrderPage.getSetupAllPOGS, emData.request_table_popup_type_warning_fpstatus);
       await eventMaintanencePage.waitBrowserToLoadPage(3000);
       await UPLOrderPage.sendDateFilled.waitForExist({timeout: 2000,timeoutMsg: "Error: Link did not appear before timeout"})
       await UPLOrderPage.clickOn(await UPLOrderPage.selectClearCurrentRequestBtn);
       await expect(await UPLOrderPage.GetText(await UPLOrderPage.selectLocationDropdown)).toEqual(emData.select_location_text);
       await expect(await UPLOrderPage.GetText(await UPLOrderPage.selectDepartmentDropdown)).toEqual(emData.Select_Department_text);
       await expect(await UPLOrderPage.GetText(await UPLOrderPage.selectCategoryDropdown)).toEqual(emData.select_category_name);
       await expect(await UPLOrderPage.GetText(await UPLOrderPage.selectProductGroupDropdown)).toEqual(emData.select_product_group);
       await expect(await UPLOrderPage.GetText(await UPLOrderPage.selectATTNToInput)).toEqual("");
       await expect(await UPLOrderPage.elementExists(await UPLOrderPage.sendDateFilled)).toEqual(false);
       await expect(await UPLOrderPage.GetText(await UPLOrderPage.orderByDepartmentInput)).toEqual("")
       await expect(await UPLOrderPage.GetText(await UPLOrderPage.getMailLocationInput)).toEqual("");
       await expect(await UPLOrderPage.GetText(await UPLOrderPage.getMailStopInput)).toEqual("");
       await expect(await UPLOrderPage.GetText(await UPLOrderPage.getRoutingInitialsInput)).toEqual("");
   })

    
})
