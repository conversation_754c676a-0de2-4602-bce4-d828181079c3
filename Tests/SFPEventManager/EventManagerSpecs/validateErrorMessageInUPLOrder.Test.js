let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const eventMaintanencePage = require("../EventManagerPO/eventMaintanence.page");
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const uplOrderPage = require('../EventManagerPO/UPLOrderFlow.page');
import { getTodayDate } from "../../../util/date"
const assert = require('assert')


describe('validateErrorMessageInUPLOrder: validate Error Messages in UPL order', () => {

    beforeEach(async () => {
        await eventMaintanencePage.waitBrowserToLoadPage(7000);
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.waitBrowserToLoadPage(7000);
        await eventMaintanencePage.selectEventType(emData.UPL_Orders);
        expect(await uplOrderPage.linkSubModule.getText()).toHaveTextContaining(emData.Create_UPL_Orders);
        await uplOrderPage.selectSingleValue(emData.Location, emData.Locations, emData.Loc00007_Fred_Meyer_Corvallis_Fred_Meyer_Stores);
        await expect(await uplOrderPage.selectLocationDropdown.getText()).toEqual(emData.Loc00007_Fred_Meyer_Corvallis_Fred_Meyer_Stores);
        await eventMaintanencePage.waitBrowserToLoadPage(7000);
        await uplOrderPage.selectSingleValue(emData.Department, emData.Departments, emData.Depart_Fuel);
        await expect(await uplOrderPage.selectDepartmentDropdown.getText()).toEqual(emData.Depart_Fuel);
        await eventMaintanencePage.waitBrowserToLoadPage(7000);
    })
    
    it('Should validate no records found in UPL Order ', async () => {
        await uplOrderPage.setValue(uplOrderPage.selectATTNToInput, await uplOrderPage.stringGenerator('upl9'));
        await eventMaintanencePage.waitBrowserToLoadPage(7000);
        await uplOrderPage.inputSendDate(getTodayDate());
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await uplOrderPage.setValue(uplOrderPage.orderByDepartmentInput, "2")
        await (await uplOrderPage.btnSetupAllPogsForDep).waitForDisplayed({ timeout: 590000 })
        await storeSelectionPage.clickOn(await uplOrderPage.btnSetupAllPogsForDep);
        await (await uplOrderPage.alertModal).isDisplayedInViewport()
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        const alertText = await (await uplOrderPage.alertModal).getText()
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await assert.deepEqual(alertText, emData.noRecordFound_alert)
    })

    it('Should validate error messgae for mandatory fileds', async () => {
        await uplOrderPage.inputSendDate(getTodayDate());
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await uplOrderPage.setValue(uplOrderPage.orderByDepartmentInput, "1")
        await (await uplOrderPage.btnSetupAllPogsForDep).waitForDisplayed({ timeout: 590000 })
        await storeSelectionPage.clickOn(await uplOrderPage.getUpdateRequestTable);
        await (await uplOrderPage.alertModal).isDisplayedInViewport()
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        const alertText = await (await uplOrderPage.alertModal).getText()
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await assert.deepEqual(await alertText, emData.enterAllFields_alert)
    })
})
