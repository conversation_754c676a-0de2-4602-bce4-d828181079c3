const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let Data = require('../../../TestData/data.json');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const Result = require('../../../SQLConnection/server.js.ts');
const { assert } = require('chai');
const { DBNames } = require('../../../envConfig.js');
const storeSelectionPage = require("../EventManagerPO/storeSelection.page");

describe('validateSFPEventAsileRebuild: validate Sfp event refresh Asile Rebuild', () => {

    it('Should validate SFPEventRefreshFlow Asile rebuild page', async () => {
        await eventMaintanencePage.waitBrowserToLoadPage(6000) 
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.waitBrowserToLoadPage(6000)   
        await eventMaintanencePage.tableSecondPageBtn.waitForExist({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'});
        await eventMaintanencePage.selectEventType('SFP Refresh')
        expect(await eventMaintanencePage.linkCreateSFPEvent.getText()).toHaveTextContaining(emData.Create_SFP_Event)
        await createSFPEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014)
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_Remodel)
        let description = await selectPOG.stringGenerator("AsileRebuild")
        await createSFPEventPage.enterEventResetDescription(description)
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSFPEventPage.clickCheckBox(emData.checkBox_Digital_Refresh_ClickList)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await createSFPEventPage.selectRadioButton(emData.radioBtn_aisleRebuild)
        await createSFPEventPage.submitEventRefreshDetails()
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await clickOn(createSFPEventPage.sfpAppModalButton)
        let storeSelected = await createSFPEventPage.selectDropdown('Store', '336')
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        let clickedAisleOptionsCheckBoxText = await eventMaintanencePage.clickRandomCheckboxEventRefresh();
        // Below are hard coded  data; Might need this if some stores don't have pogs
        // let aisleOptions = 'CHECKLANE 1 [E]'
        // await createSFPEventPage.storeSelectRadioButton(aisleOptions)
        await eventMaintanencePage.waitBrowserToLoadPage(4000)
        await clickOn(createSFPEventPage.pogSubmitBtn)
        await expect(browser).toHaveUrlContaining("EventMaintenance")
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters)
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        let eventDescription = await createSFPEventPage.getEventDescText(description)
        await expect(eventDescription).toEqual(true)
        let pogType = await eventMaintanencePage.getHeaderValue(emData.Header_Pog, description)
        let eventIdText = await eventMaintanencePage.getModalEventIdText();
        let numOfPogsOnModal = await eventMaintanencePage.getDigitsAfterSecondColon(await eventIdText);
        await expect(await pogType).toEqual(await numOfPogsOnModal);
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await eventMaintanencePage.clickCloseButton('Close')
        let scheduledStores = await eventMaintanencePage.getHeaderValue(emData.Header_ScheduledStr, description)
        await expect(scheduledStores).toEqual("1")
        await eventMaintanencePage.waitBrowserToLoadPage(4000)
        let text= (await eventMaintanencePage.scheduledStore).getText()
        await expect(await text).toEqual( storeSelected)
        await eventMaintanencePage.clickCloseButton('Close');
        let refreshType = await eventMaintanencePage.getHeaderValue(emData.Header_Refresh, description)
        await expect(refreshType).toEqual(emData.Event_refreshType)
        let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, description)
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        let eventId = await eventMaintanencePage.getEventId(initialEventId)
        await expect(eventId).toEqual(initialEventId)
        let dataFromDb = await Result.getResult(qrString.SfpEvent_Eventid.replace('eventID', initialEventId))
        let corpEventStatus = await Result.getResult(qrString.SfpEvent_corpStatus.replace('eventID', initialEventId))
        await assert.equal(dataFromDb, initialEventId)
        await assert.equal(corpEventStatus, emData.corpEvent_Released)
        let stores = await Result.getResult(qrString.stores_ilp_area_rebuild.replace('@eventID', initialEventId), DBNames.ILP_STAGE)
        await assert.equal(eventId + ' - '+ clickedAisleOptionsCheckBoxText.substring(0, clickedAisleOptionsCheckBoxText.indexOf('[')).trim() , stores.join(''))
    })
})
