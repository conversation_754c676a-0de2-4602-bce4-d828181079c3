const homePage = require('../../../GlobalObjectRepository/home.page');
let emData=require('../../../TestData/EventManagerData/eventManagerUIData.json')
const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');

describe('validateAdminTabInAutoActivation: SFP App application HomePage', () => {

    beforeEach( async() => {
        await homePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintenance');
    })
    
    it('Should hover to an AdminTab and validate Auto Activation Report', async () => {
        await homePage.waitBrowserToLoadPage(4000)
        await homePage.applicationMenuListText('Applications',emData.ApplicationsSubmenuSFPEventManager)
        await homePage.waitBrowserToLoadPage(4000)
        await homePage.Admin.moveTo()
        await expect(await homePage.selectAdminSubMenu(emData.AdminSubmenu_AutoActivationReport)).toEqual(false)
    })

    it('Should hover to an AdminTab and validate Reset Completion', async () => {
        await homePage.waitBrowserToLoadPage(4000)
        await homePage.applicationMenuListText('Applications',emData.ApplicationsSubmenuSFPEventManager)
        await homePage.Admin.moveTo();
        await expect(await homePage.selectAdminSubMenu(emData.AdminSubmenu_ResetCompletion)).toEqual(true);
        let label = await homePage.GetText(await homePage.eventPageLabel)
        await expect(await label).toEqual(emData.Event_Reset_Schedule_Maintenance)
    })
})
