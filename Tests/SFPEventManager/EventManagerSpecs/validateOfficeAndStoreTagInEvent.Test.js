const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json');
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page');
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const { assert } = require('chai');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
let description;


describe('validateOfficeAndStoreTagInEvent:validate Sfp event headertable for DefaultSetValueInTag UI and DB', () => {
    beforeEach( async() => {
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.tableSecondPageBtn.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')             
        await eventMaintanencePage.selectEventType('SFP Event')
        expect(await eventMaintanencePage.linkCreateSFPEvent.getText()).toHaveTextContaining(emData.Create_SFP_Event);
        await createSFPEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014);
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        await createSFPEventPage.selectDropdown('selectType', "Update");
        description = await selectPOG.stringGenerator("AutomationStoreTag");
        await createSFPEventPage.enterEventResetDescription(description);
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB);
        await createSFPEventPage.clickCheckBox(emData.checkBox_RequestTags);
    })
    
        it('Should validate HeaderTable for StoreTag UI and DB colunm', async () => {
            await createSFPEventPage.selectRadioButton(emData.radioBtn_Store);
            //await createSFPEventPage.selectHeadingPrefix(emData.heading_Prefix_RES);
            await createSFPEventPage.enterValue(emData.field_userHeading,await createSFPEventPage.stringGenerator("TestAutomationSet"));
            let text = await createSFPEventPage.tagsSetsText.getValue();
            await expect(await createSFPEventPage.tagsSetsText.getValue()).toEqual("1");
            await createSFPEventPage.submitEventDetails();
            await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout');
            await createEventSchedulePage.selectDropdown('Fiscal Week');
            await clickOn(storeSelectionPage.saveSchedule);
            await eventMaintanencePage.waitBrowserToLoadPage(2000)
            await clickOn(storeSelectionPage.goToStoreSelection);
            await eventMaintanencePage.waitBrowserToLoadPage(2000)
            await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
            let storePage = await storeSelectionPage.linkSubModule.getText();
            await expect(storePage).toEqual(emData.Store_Selection);
            await eventMaintanencePage.open(emData.EventMaintanceUrl);
            await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl);
            await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
            await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
            let requestTagType = await eventMaintanencePage.getHeaderValue(emData.Header_Tags, description);
            await expect(requestTagType).toEqual(emData.Event_Tags_S_RES);
            let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, description);
            await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
            let eventId = await eventMaintanencePage.getEventId(initialEventId);
            await expect(eventId).toEqual(initialEventId);
            // requestTags is single array of array [ [ '1' ] ]
            await eventMaintanencePage.waitBrowserToLoadPage(6000)
            let requestTags = await result.getResult( qrString.SfpEventTagstypeSets.replace('@eventID', initialEventId));
            await expect(requestTags[0].toString()).toEqual(text);
            assert.equal(requestTags.length, 1);
        })

        it('Should validate HeaderTable for OfficeTag UI and DB colunm', async () => {
            await createSFPEventPage.selectRadioButton(emData.radioBtn_Office);
            //await createSFPEventPage.selectHeadingPrefix(emData.heading_Prefix_OFC);
            await createSFPEventPage.enterValue(emData.field_userHeading,await createSFPEventPage.stringGenerator("TestAutomationSet"))
            let text = await createSFPEventPage.tagsSetsText.getValue();
            await expect(await createSFPEventPage.tagsSetsText.getValue()).toEqual("1");
            await createSFPEventPage.submitEventDetails();
            await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout');
            await createEventSchedulePage.selectDropdown('Fiscal Week');
            await clickOn(storeSelectionPage.saveSchedule);
            await eventMaintanencePage.waitBrowserToLoadPage(2000)
            await clickOn(storeSelectionPage.goToStoreSelection);
            await eventMaintanencePage.waitBrowserToLoadPage(2000)
            await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
            let storePage = await storeSelectionPage.linkSubModule.getText();
            await expect(storePage).toEqual(emData.Store_Selection);
            await eventMaintanencePage.open(emData.EventMaintanceUrl);
            await eventMaintanencePage.waitBrowserToLoadPage(6000);
            await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl);
            await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
            await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
            let requestTagType = await eventMaintanencePage.getHeaderValue(emData.Header_Tags, description);
            await expect(requestTagType).toEqual(emData.Event_Tags_O_OFC);
            let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, description);
            await eventMaintanencePage.waitBrowserToLoadPage(3000);
            await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
            let eventId = await eventMaintanencePage.getEventId(initialEventId);
            await expect(eventId).toEqual(initialEventId);
            // requestTags is single array of array [ [ '1' ] ]
            let requestTags = await result.getResult(qrString.SfpEventTagstypeSets.replace('@eventID', initialEventId));
            await expect(requestTags[0].toString()).toEqual(text);
            assert.equal(requestTags.length, 1);
        })

    })
