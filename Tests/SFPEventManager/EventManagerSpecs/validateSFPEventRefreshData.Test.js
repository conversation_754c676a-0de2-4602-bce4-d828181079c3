const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const selectingPogCommodities = require('../EventManagerPO/selectingPogCommodities.page')
const qrString = require('../../../SQLConnection/queryStringEM');
const Result = require('../../../SQLConnection/server.js.ts');
const compareAllRow = require("../../../util/excelReader");
const createSFPEventPage = require("../EventManagerPO/createSFPEvent.page");

let eventID;
describe('validateSFPEventRefreshFlow: validate Sfp event refresh', () => {

    it('Should validate SFPEventRefreshFlow page', async () => {
        await createSFPEventPage.waitBrowserToLoadPage(6000) 
        await createSFPEventPage.addEventDetails1('','SFP Refresh', emData.div_Cincinnati_Operating_Division_014, emData.eventType_KOMPASS, "", "EventRefresh", emData.vendor_AZB);
        await createSfpEventPage.clickCheckBox(emData.checkBox_Digital_Refresh_ClickList)
        await createSfpEventPage.submitEventRefreshDetails()
        await eventMaintanencePage.waitBrowserToLoadPage(5000)
        await eventMaintanencePage.clickOn(await createSfpEventPage.sfpAppModalButton)
        let nextPage = await createSfpEventPage.linkCreateSfpRefreshEvent.getText()
        await expect(nextPage).toEqual(emData.Event_Refresh)
        await createSFPEventPage.addEventScheduleStep2();
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkCreatSfpRefreshEvent, emData.Event_Refresh)
        let storePage = await storeSelectionPage.linkCreatSfpRefreshEvent.getText()
        await expect(storePage).toEqual(emData.Event_Refresh)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.clickCheckBoxText(emData.Store_351)
        await clickOn(await storeSelectionPage.saveStoreSchedule)
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkCreatSfpRefreshEvent, emData.Event_Refresh)
        let pogPage = await storeSelectionPage.linkCreatSfpRefreshEvent.getText()
        await expect(pogPage).toEqual(emData.Event_Refresh)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Department', emData.department_03_HBC)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Sub-Department', emData.subdept_03_HBC_03_HEALTH)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Commodity *', emData.SelectAll)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Commodity Group', emData.SelectAll)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await clickOn(await selectPOG.selectChoosePogByCommodityBtn)
        await eventMaintanencePage.waitBrowserToLoadPage(10000)
        // Note: getting eventID from url does not make sense here; Need to further fix the issues.
        let url = await browser.getUrl()
        let eventIDMatch = await url.match(/(\d[\d]*)/g);
        if (eventIDMatch) {
            eventID = eventIDMatch[0];
        } else {
            throw new Error("Error: Could not find event ID in the URL");
        }
        let uiData = await selectingPogCommodities.getAllEventRefreshUIData()
        let dbDataEventRefresh = await Result.getResult(qrString.sfpRefreshEventData.replace('@eventID', eventID));
        //converting object into array and printing values
        let arrayDbDataStoreReview = Object.keys(dbDataEventRefresh).map(function (key) {
            return dbDataEventRefresh[key]
        });
        await eventMaintanencePage.waitBrowserToLoadPage(5000)
        await expect(uiData.length).toEqual(await arrayDbDataStoreReview.length)
        //let compare = await compareAllRow.dataCompareToDb(arrayDbDataStoreReview.sort(), uiData.sort())
        let compare = await compareAllRow.dataCompare(arrayDbDataStoreReview.sort(), uiData.sort(), arrayDbDataStoreReview.length)
        await expect(compare).toEqual(true);
    })
})
