const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const CreateSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page.js');
const {clickOn} = require("../EventManagerPO/createSFPEvent.page");
const selectPOG = require("../EventManagerPO/selectingPogCommodities.page");
const Result = require('../../../SQLConnection/server.js.ts');
const qrString = require('../../../SQLConnection/queryStringEM');
const assert = require('chai').assert;

describe('validateEventMaintenanceFiltersDiv: Validate user is able to filter EM page results by Division', () => {
    it('Validate event on eventMaintenance is sortable and visible after filtering with division', async () => {
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintanence');
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await eventMaintanencePage.selectEventType('SFP Event');
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        expect(await eventMaintanencePage.linkCreateSFPEvent.getText()).toHaveTextContaining(emData.Create_SFP_Event);
        await CreateSFPEventPage.selectDropdown('division', emData.div_Columbus_Operating_Division_016);
        await CreateSFPEventPage.selectDropdown('event_type', emData.eventType_Remodel);
        let description =  await selectPOG.stringGenerator("FilterByDiv123456");
        await CreateSFPEventPage.enterEventResetDescription(description);
        await CreateSFPEventPage.selectDropdown('vendor', emData.vendor_AZB);
        await CreateSFPEventPage.submitEventDetails();
        let nextPage = await eventMaintanencePage.linkSubModule.getText();
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await expect(nextPage).toEqual(emData.Event_Schedule);
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        await clickOn(storeSelectionPage.saveSchedule);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await clickOn(storeSelectionPage.saveGoToStore);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
        let storePage = await storeSelectionPage.linkSubModule.getText();
        await expect(storePage).toEqual(emData.Store_Selection);
        await storeSelectionPage.clickCheckBoxText(emData.store_Selection_350);
        await storeSelectionPage.clickCheckBoxText(emData.store_Selection_531);
        await storeSelectionPage.clickCheckBoxText(emData.store_Selection_581);
        await clickOn(storeSelectionPage.saveStoreSchedule);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Select_Pogs);
        let pogPage = await storeSelectionPage.linkSubModule.getText();
        await expect(pogPage).toEqual(emData.Select_Pogs);
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department);
        await selectPOG.selectDropdown('Department', emData.selecting_POG_Department_15_DELI);
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOG.selectDropdown('Sub-Department', emData.selecting_POG_SubDepartment_15_DELI_49_SPECIALTY_CHSE);
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department);
        await selectPOG.selectDropdown('Commodity *', emData.selecting_POG_Commodity_462_SPECIALTY_CHEESE_PRE_PACK_49_SPECIALTY_CHSE_15_DELI);
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOG.selectDropdown('Commodity Group', emData.selecting_POG_CommodityGroup_462_SPECIALTY_CHEESE_PRE_PACK_462_SPECIALTY_CHEESE_PRE_PACK);
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department);
        await clickOn(await selectPOG.selectChoosePogByCommodityBtn);
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await clickOn(await selectPOG.gotoEventListButton);
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl);
        await clickOn(await eventMaintanencePage.refreshEventBtn);
        await eventMaintanencePage.waitForPageLoad(await eventMaintanencePage.filtersTxtBtn, emData.Filters_text);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, await description);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        let eventId = await eventMaintanencePage.getEventId(initialEventId);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await expect(eventId).toEqual(initialEventId);
        let dbEventId = await Result.getResult(qrString.SfpEvent_Eventid.replace(emData.eventID, initialEventId.toString()));
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await expect(eventId).toEqual(dbEventId.flat().toString());
        await clickOn(await eventMaintanencePage.refreshEventBtn);
        await clickOn(await eventMaintanencePage.filtersBtn);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        let dropdownValue = await eventMaintanencePage.selectFiltersDropdown(emData.selectedDivision_eventMaintenance,emData.div_Cincinnati_Operating_Division_014);
        let divNum = (dropdownValue.match(/\d/g).join("")).toString();
        await clickOn(await eventMaintanencePage.searchBtn);
        await eventMaintanencePage.waitForPageLoad(await eventMaintanencePage.filtersTxtBtn, emData.Filters_text);
        let divColumnList = await eventMaintanencePage.getColumnList(emData.DIV);
        let divColumnListUnique = await eventMaintanencePage.getUniqueArrayValue(divColumnList);
        await expect(divNum).toEqual(divColumnListUnique.toString());
        let initialEventIdAfterFilter = await eventMaintanencePage.getHeaderValue(emData.Event_ID, await description);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        let eventIdAfterFilter = await eventMaintanencePage.getEventId(initialEventIdAfterFilter);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        //validating new eventID after filtering with Div
        await expect(eventIdAfterFilter).toEqual(initialEventIdAfterFilter);
    })

    it('validateEventMaintenanceFiltersDiv: Validate user is able to filter EM page results by Division - comparing Div columnList before and after' +
        'filtering', async () => {
        await eventMaintanencePage.open('EventMaintanence');
        await eventMaintanencePage.waitForPageLoad(await eventMaintanencePage.filtersTxtBtn, emData.Filters_text);
        await clickOn(await eventMaintanencePage.refreshEventBtn);
        await clickOn(await eventMaintanencePage.filtersBtn);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        let divColumnList = await eventMaintanencePage.getColumnList(emData.DIV);
        let divColumnListUnique = await eventMaintanencePage.getUniqueArrayValue(divColumnList);
        let dropdownValue = await eventMaintanencePage.selectFiltersDropdown(emData.selectedDivision_eventMaintenance,emData.div_Cincinnati_Operating_Division_014);
        let divNum = (dropdownValue.match(/\d/g).join("")).toString();
        // comparing DIV columnList is different after filtering
        await assert.notEqual(divNum, divColumnListUnique.toString(), 'DIV column is not filtered !');
        await clickOn(await eventMaintanencePage.searchBtn);
        await eventMaintanencePage.waitForPageLoad(await eventMaintanencePage.filtersTxtBtn, emData.Filters_text);
        let divColumnListAfterFilter = await eventMaintanencePage.getColumnList(emData.DIV);
        let divColumnListUniqueAfterFilter = await eventMaintanencePage.getUniqueArrayValue(divColumnListAfterFilter);
         // compare DIV columnList matches with Div dropdown value
        await expect(divNum).toEqual(divColumnListUniqueAfterFilter.toString());
    })
})
