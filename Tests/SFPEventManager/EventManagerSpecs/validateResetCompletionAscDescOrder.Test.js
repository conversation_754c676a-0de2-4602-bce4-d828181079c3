const resetCompletionPage = require('../EventManagerPO/resetCompletion.page')
const autoActivationReport = require('../EventManagerPO/autoActivationReport.page')
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const eventMaintanencePage = require("../EventManagerPO/eventMaintanence.page");
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const ResetCompletionPage = require("../EventManagerPO/resetCompletion.page");

describe('validateResetCompletionAscending/DescendingOrderTest : validate Div,Location,ResetType,Period,Week page', () => {

    beforeEach(async () => {
        await resetCompletionPage.open('EventMaintanence/resetCompletion');
        await ResetCompletionPage.waitForPageLoad(await ResetCompletionPage.linkSubModule, emData.Admin_reset_completion);
        await eventMaintanencePage.waitBrowserToLoadPage(5000)
        await resetCompletionPage.selectSingleValue(emData.selectedDivision_ResetCompletion, emData.division_Cincinnati_Operating_Division_014)
        await resetCompletionPage.selectSingleValue(emData.selectedDepartment_ResetCompletion, emData.dept_01_GROCERY)
        await resetCompletionPage.setSingleValue(emData.selectedLocation_ResetCompletion, emData.location_014_00466)
        await resetCompletionPage.selectSingleValue(emData.selectedCompletionType_ResetCompletion, emData.ResetComplete_All)
       // await autoActivationReport.selectYPW(emData.Year_Input, emData.Year_2022);
        await autoActivationReport.selectYPW(emData.Period_Input, emData.Period_Empty);
        await storeSelectionPage.clickOn(resetCompletionPage.searchBtn);
        await eventMaintanencePage.waitBrowserToLoadPage(10000)
        await storeSelectionPage.clickOn(resetCompletionPage.storeReview);
        await eventMaintanencePage.waitBrowserToLoadPage(10000)
        await storeSelectionPage.waitForTableExist(await resetCompletionPage.storeReviewTable);
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await resetCompletionPage.getPaginationPageNumber(emData.PaginationPageNumber_100);
    })

    it('Should validate "Loc" column data is in Ascending/Descending Order ', async () => {
        await storeSelectionPage.clickOn(await resetCompletionPage.columnTitleLoc);
        let ascendingOrder = await resetCompletionPage.ascending(emData.Locations_ResetCompletion,'5','5');
        let ascendingTrue = await resetCompletionPage.validateAscendingOrder(ascendingOrder);
        await expect(ascendingTrue).toEqual(true);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await storeSelectionPage.clickOn(resetCompletionPage.backToFirstBtnPagination);
        let descendingOrder = await resetCompletionPage.descending(emData.Location_ResetCompletion, emData.Locations_ResetCompletion, '5','5'); //Descending
        let descendingTrue = await resetCompletionPage.validateDescendingOrder(descendingOrder);
        await expect(descendingTrue).toEqual(true);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await expect(ascendingOrder).toEqual(descendingOrder.sort());
    })

    it('Should validate "Vendor" column data is in Ascending/Descending Order ', async () => {
        await storeSelectionPage.clickOn(await resetCompletionPage.columnTitleVendor);
        let ascendingOrder = await resetCompletionPage.ascending(emData.VendorNames_ResetCompletion, "5",'5');
        let cleanAscendingOrder = await resetCompletionPage.removeEmptyStringFromArray(ascendingOrder);
        let ascendingTrue = await resetCompletionPage.validateAscendingOrder(cleanAscendingOrder);
        await expect(ascendingTrue).toEqual(true);
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await storeSelectionPage.clickOn(resetCompletionPage.backToFirstBtnPagination);
        let descendingOrder = await resetCompletionPage.descending(emData.VendorName_ResetCompletion, emData.VendorNames_ResetCompletion, "5");
        let cleanDescendingOrder = await resetCompletionPage.removeEmptyStringFromArray(descendingOrder);
        let descendingTrue = await resetCompletionPage.validateDescendingOrder(cleanDescendingOrder);
        await expect(descendingTrue).toEqual(true);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await expect(cleanAscendingOrder).toEqual(cleanDescendingOrder.sort());
    })

    it('Should validate "ResetType" column data is in Ascending/Descending Order ', async () => {
        await storeSelectionPage.clickOn(await resetCompletionPage.columnTitleResetType);
        let ascendingOrder = await resetCompletionPage.ascending(emData.ResetTypes_ResetCompletion, "5");
        let ascendingTrue = await resetCompletionPage.validateAscendingOrder(ascendingOrder);
        await expect(ascendingTrue).toEqual(true);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await storeSelectionPage.clickOn(resetCompletionPage.backToFirstBtnPagination);
        let descendingOrder = await resetCompletionPage.descending(emData.ResetType_ResetCompletion, emData.ResetTypes_ResetCompletion, "5");
        let descendingTrue = await resetCompletionPage.validateDescendingOrder(descendingOrder);
        await expect(descendingTrue).toEqual(true);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await expect(ascendingOrder).toEqual(descendingOrder.sort());
    })

    it('Should validate "Year" column data is in Ascending/Descending Order ', async () => {
        await storeSelectionPage.clickOn(await resetCompletionPage.columnTitleYear);
        let ascendingOrder = await resetCompletionPage.ascending(emData.Years_ResetCompletion, "5");
        let ascendingTrue = await resetCompletionPage.validateAscendingOrder(ascendingOrder);
        await expect(ascendingTrue).toEqual(true);
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await storeSelectionPage.clickOn(resetCompletionPage.backToFirstBtnPagination);
        let descendingOrder = await resetCompletionPage.descending(emData.Year_ResetCompletion, emData.Years_ResetCompletion, "5");
        await resetCompletionPage.validateDescendingOrder(descendingOrder);
        let descendingTrue = await resetCompletionPage.validateDescendingOrder(descendingOrder);
        await expect(descendingTrue).toEqual(true);
        await eventMaintanencePage.waitBrowserToLoadPage(4000)
        await expect(ascendingOrder).toEqual(descendingOrder.sort());
    })

    it('Should validate "Week" column data is in Ascending/Descending Order ', async () => {
        await storeSelectionPage.clickOn(await resetCompletionPage.columnTitleWeek);
        let ascendingOrder = await resetCompletionPage.ascending(emData.Weeks_ResetCompletion, "5");
        let ascendingTrue = await resetCompletionPage.validateAscendingOrder(ascendingOrder);
        await expect(ascendingTrue).toEqual(true);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await storeSelectionPage.clickOn(resetCompletionPage.backToFirstBtnPagination);
        let descendingOrder = await resetCompletionPage.descending(emData.Week_ResetCompletion, emData.Weeks_ResetCompletion,"5");
        await resetCompletionPage.validateDescendingOrder(descendingOrder)
        let descendingTrue = await resetCompletionPage.validateDescendingOrder(descendingOrder);
        await expect(descendingTrue).toEqual(true);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await expect(ascendingOrder).toEqual(descendingOrder.sort());
    })

})
