const eventMaintenancePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const { assert } = require('chai');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
let description;



describe('validateSFPEventEnableAutoActivation:validate Sfp event headertable for Enable Auto Activation?\nand Activate CAO 2 weeks before Event Schedule? UI and DB', () => {
    beforeEach(async () => {
         await eventMaintenancePage.waitBrowserToLoadPage(6000) 
        await eventMaintenancePage.open('EventMaintenance');
        await eventMaintenancePage.tableSecondPageBtn.waitForExist({ timeout: 90000, timeoutMsg: 'Error: Table contents for event maintenance page didn`t load before timeout' })
        await eventMaintenancePage.selectEventType('SFP Event')
        expect(await eventMaintenancePage.linkCreateSFPEvent.getText()).toHaveTextContaining(emData.Create_SFP_Event)
        await createSFPEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014)
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        await createSFPEventPage.selectDropdown('selectType', "Update");
        description = await selectPOG.stringGenerator("AutomationActivation")
        await createSFPEventPage.enterEventResetDescription(description)
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
    })

    it('Should validate HeaderTable for Enable Auto Activation? UI and DB colunm', async () => {
        await createSFPEventPage.clickRadioButtonByText("Activate CAO 1 week before Event Schedule")
        await createSFPEventPage.submitEventDetails()
        let nextPage = await eventMaintenancePage.linkSubModule.getText()
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        await createEventSchedulePage.selectDropdown('Fiscal Week');
        await clickOn(storeSelectionPage.saveSchedule)
        let weekText = await storeSelectionPage.storeWeekSchedule.getText()
        await clickOn(storeSelectionPage.saveGoToStore)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection)
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(storePage).toEqual(emData.Store_Selection)
        await eventMaintenancePage.open(emData.EventMaintanceUrl);
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        await eventMaintenancePage.clickFiltersWithDiscp(description.toString())
        let eventDescription = await selectPOG.getEventDescText(description, weekText.replace(', ', '/'))
        await expect(eventDescription).toEqual(true)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        let activationType = await eventMaintenancePage.getHeaderValue(emData.Header_Activation, description)
        await expect(activationType).toEqual(emData.Event_Activation_Yes)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        let initialEventId = await eventMaintenancePage.getHeaderValue(emData.Event_ID, description)
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await eventMaintenancePage.clickOn(await eventMaintenancePage.slideOutClose)
        let eventId = await eventMaintenancePage.getEventId(initialEventId)
        await expect(eventId).toEqual(initialEventId)
        let activationEventType = await (await result.getResult(qrString.SfpEvent_Auto_Activation.replace('@eventID', initialEventId))).flat();
        await expect(activationEventType[0]).toEqual(true)
        await expect(activationEventType[1]).toEqual(false)
        await assert.equal(activationEventType.length, 2)
        let releaseEventEdit = await eventMaintenancePage.selectReleaseEvents(emData.ReleaseEvent_Edit, emData.Header_Action, description)
        await expect(releaseEventEdit).toEqual(true)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await createSFPEventPage.clickRadioButtonByText("No Activation")
        await createSFPEventPage.submitEventDetails()
        await expect(nextPage).toEqual(emData.Event_Schedule)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await eventMaintenancePage.open(emData.EventMaintanceUrl);
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintenancePage.waitBrowserToLoadPage(20000)
        let activationTypeUncheckd = await eventMaintenancePage.getHeaderValue(emData.Header_Activation, description)
        await expect(activationTypeUncheckd).toEqual("-")
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        let ActivationEventChange = await (await result.getResult(qrString.SfpEvent_Auto_Activation.replace('@eventID', initialEventId))).flat();
        await expect(ActivationEventChange[0]).toEqual(false)
        await expect(ActivationEventChange[1]).toEqual(false)
        await assert.equal(ActivationEventChange.length, 2)
    })

    it('Should validate HeaderTable for Activate CAO 2 weeks before Event Schedule? UI and DB colunm', async () => {
        await createSFPEventPage.clickRadioButtonByText("Activate CAO 2 weeks before Event Schedule")
        await createSFPEventPage.submitEventDetails()
        let nextPage = await eventMaintenancePage.linkSubModule.getText()
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        await createEventSchedulePage.selectDropdown('Fiscal Week');
        await clickOn(storeSelectionPage.saveSchedule)
        let weekText = await storeSelectionPage.storeWeekSchedule.getText()
        await clickOn(storeSelectionPage.saveGoToStore)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection)
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(storePage).toEqual(emData.Store_Selection)
        await eventMaintenancePage.open(emData.EventMaintanceUrl);
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintenancePage.waitBrowserToLoadPage(3000)
        await eventMaintenancePage.clickFiltersWithDiscp(description.toString())
        let eventDescription = await selectPOG.getEventDescText(description, weekText.replace(', ', '/'))
        await expect(eventDescription).toEqual(true)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        let cctivateCao2 = await eventMaintenancePage.getHeaderValue(emData.Header_2_Weeks, description)
        await expect(cctivateCao2).toEqual(emData.Event_2Weeks_Yes)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        let initialEventId = await eventMaintenancePage.getHeaderValue(emData.Event_ID, description)
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await eventMaintenancePage.clickOn(await eventMaintenancePage.slideOutClose)
        let eventId = await eventMaintenancePage.getEventId(initialEventId)
        await expect(eventId).toEqual(initialEventId)
        let activationEventType = await (await result.getResult(qrString.SfpEvent_Auto_Activation.replace('@eventID', initialEventId))).flat();
        await expect(activationEventType[0]).toEqual(true)
        await expect(activationEventType[1]).toEqual(true)
        await assert.equal(activationEventType.length, 2)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        let releaseEventEdit = await eventMaintenancePage.selectReleaseEvents(emData.ReleaseEvent_Edit, emData.Header_Action, description)
        await expect(releaseEventEdit).toEqual(true)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await createSFPEventPage.clickRadioButtonByText("No Activation")
        await createSFPEventPage.submitEventDetails()
        await expect(nextPage).toEqual(emData.Event_Schedule)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await eventMaintenancePage.open(emData.EventMaintanceUrl);
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        await eventMaintenancePage.clickFiltersWithDiscp(description.toString())
        let activationTypeUnchecked = await eventMaintenancePage.getHeaderValue(emData.Header_2_Weeks, description)
        await expect(activationTypeUnchecked).toEqual("-")
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        let ActivationEventChange = await (await result.getResult(qrString.SfpEvent_Auto_Activation.replace('@eventID', initialEventId))).flat();
        await expect(ActivationEventChange[0]).toEqual(false)
        await expect(ActivationEventChange[1]).toEqual(false)
        await assert.equal(ActivationEventChange.length, 2)
    })

})






