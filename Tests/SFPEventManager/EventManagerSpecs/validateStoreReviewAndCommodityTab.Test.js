const resetCompletionPage = require('../EventManagerPO/resetCompletion.page.js')
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page.js');
const autoActivationReport = require('../EventManagerPO/autoActivationReport.page.js');
const result = require("../../../SQLConnection/server.js.ts");
const qrString = require("../../../SQLConnection/queryStringEM");
const compareAllRow = require("../../../util/excelReader");
const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');

describe('validateResetCompletion StoreReview and Commodity Recap tabs : compare UI data to DB', () => {

    it('should validate  Division, Location, Department, Reset Type , Commodity , Reset Complete dropdowns', async () => {
        await eventMaintanencePage.waitBrowserToLoadPage(7000)
        await resetCompletionPage.open('EventMaintenance/resetCompletion');
        await resetCompletionPage.waitForPageLoad(await resetCompletionPage.linkSubModule, emData.Admin_reset_completion);
        await resetCompletionPage.selectSingleValue('selectedResetType', 'Select All', 'resetTypeList', 'Select All');
        await resetCompletionPage.waitForPageLoad(resetCompletionPage.clearFiltersBtn, emData.ResetComplete_Clear_Filters);
        await resetCompletionPage.setSingleValue("selectedDivision", emData.division_Cincinnati_Operating_Division_014);
        await expect(await resetCompletionPage.selectDivision.getText()).toEqual(emData.division_Cincinnati_Operating_Division_014_Txt);
        await eventMaintanencePage.waitBrowserToLoadPage(6000);
        await resetCompletionPage.selectSingleValue(emData.selectedLocation_ResetCompletion, emData.selectedLocation_014_00351);
        await expect(await resetCompletionPage.selectLocation.getText()).toEqual(emData.selectedLocation_014_00351_Text);
        await eventMaintanencePage.waitBrowserToLoadPage(6000);
        await autoActivationReport.selectYPW(emData.Period_Input, emData.Period_Empty);
        await expect(await resetCompletionPage.selectPeriod.getText()).toEqual(emData.Period_Empty);
        await storeSelectionPage.clickOn(await resetCompletionPage.clearFiltersBtn);
        await resetCompletionPage.waitForPageLoad(resetCompletionPage.clearFiltersBtn, emData.ResetComplete_Clear_Filters);
        await expect(await resetCompletionPage.selectDivision.getText()).toEqual(emData.selectedDivision_UnSelected);
        await expect(await resetCompletionPage.selectLocation.getText()).toEqual(emData.selectedLocation_UnSelected);
        await expect(await resetCompletionPage.selectDepartment.getText()).toEqual(emData.selectedDepartment_UnSelected);
        await expect(await resetCompletionPage.selectResetType.getText()).toEqual(emData.selectedResetType_UnSelected);
        await expect(await resetCompletionPage.selectCommodity.getText()).toEqual(emData.selectedCommodity_UnSelected);
        await expect(await resetCompletionPage.selectResetComplete.getText()).toEqual(emData.selectedCompletionType_UnSelected);
    })

    it('Should validate comparing UI data to db on Store Review tab ', async () => {
        await eventMaintanencePage.waitBrowserToLoadPage(7000)
        await resetCompletionPage.open('EventMaintanence/resetCompletion');
        await resetCompletionPage.waitForPageLoad(await resetCompletionPage.linkSubModule, emData.Admin_reset_completion);
        await resetCompletionPage.selectSingleValue(emData.selectedCompletionType_ResetCompletion, emData.ResetComplete_All)
        await eventMaintanencePage.waitBrowserToLoadPage(6000);
        await resetCompletionPage.waitForPageLoad(resetCompletionPage.clearFiltersBtn, emData.ResetComplete_Clear_Filters);
        await resetCompletionPage.selectSingleValue(emData.selectedDivision_ResetCompletion, emData.division_Cincinnati_Operating_Division_014);
        await eventMaintanencePage.waitBrowserToLoadPage(6000);
        await resetCompletionPage.setSingleValue('selectedLocation', emData.store_Selection_335);
        await autoActivationReport.selectYPW(emData.Year_Input, emData.Year_2023)
        await autoActivationReport.selectYPW(emData.Period_Input, emData.Period_Empty);
        await eventMaintanencePage.waitBrowserToLoadPage(6000);
        await storeSelectionPage.clickOn(resetCompletionPage.storeReview);
        await storeSelectionPage.clickOn(resetCompletionPage.searchBtn);
        await resetCompletionPage.waitBrowserToLoadPage(6000)
        await resetCompletionPage.waitForPageLoad(resetCompletionPage.clearFiltersBtn, emData.ResetComplete_Clear_Filters);
        let dbDataStoreReview = await result.getResult(qrString.resetCompletionStoreReviewTab);
        //converting object into array and printing values
        let arrayDbDataStoreReview = Object.keys(dbDataStoreReview).map(function (key) {
            return dbDataStoreReview[key]
        });
        await resetCompletionPage.waitBrowserToLoadPage(4000)
        //  select page number 10
        await resetCompletionPage.getPaginationPageNumber(emData.PaginationPageNumber_10);
        await resetCompletionPage.waitBrowserToLoadPage(4000)
        let uiDataStoreReview = await resetCompletionPage.getStoreReviewAllUIData();
        await expect(uiDataStoreReview.length).toEqual(arrayDbDataStoreReview.length)
        //data is not sorted on db but on ui
        let compare = await compareAllRow.dataCompare(arrayDbDataStoreReview, uiDataStoreReview,4);
        await expect(compare).toEqual(true);
    })

    it('Should validate comparing UI data to db on Commodity Recap tab ', async () => {
        await resetCompletionPage.open('EventMaintenance/resetCompletion');
        await resetCompletionPage.waitForPageLoad(await resetCompletionPage.linkSubModule, emData.Admin_reset_completion);
        await resetCompletionPage.waitForPageLoad(resetCompletionPage.clearFiltersBtn, emData.ResetComplete_Clear_Filters);
        await resetCompletionPage.selectSingleValue(emData.selectedCompletionType_ResetCompletion, emData.ResetComplete_All)
        await resetCompletionPage.selectSingleValue(emData.selectedDivision_ResetCompletion, emData.division_Cincinnati_Operating_Division_014)
        await eventMaintanencePage.waitBrowserToLoadPage(6000);
        await resetCompletionPage.setSingleValue('selectedLocation', emData.store_Selection_00426);
        await eventMaintanencePage.waitBrowserToLoadPage(6000);
        await autoActivationReport.selectYPW(emData.Year_Input, emData.Year_2023);
        await autoActivationReport.selectYPW(emData.Period_Input, emData.Period_Empty);
        await storeSelectionPage.clickOn(resetCompletionPage.storeReview);
        await storeSelectionPage.clickOn(resetCompletionPage.searchBtn);
        await eventMaintanencePage.waitBrowserToLoadPage(6000);
        await storeSelectionPage.clickOn(resetCompletionPage.commodityRecapBtn);
        await resetCompletionPage.waitForPageLoad(resetCompletionPage.clearFiltersBtn, emData.ResetComplete_Clear_Filters);
        // result is array
        let uiData = await resetCompletionPage.getCommodityRecapAllUIData();
        // result is js object
        let dbData = await result.getResult(qrString.resetCompletionCommodityRecapTab)
        //converting object into array and printing values
        let arrayDbData = Object.keys(dbData).map(function (key) {
            return dbData[key]
        });
        await expect(uiData.length).toEqual(arrayDbData.length)
        let compare = await compareAllRow.dataCompare(arrayDbData.sort(), uiData.sort(),3);
        await expect(compare).toEqual(true);
    })
})
