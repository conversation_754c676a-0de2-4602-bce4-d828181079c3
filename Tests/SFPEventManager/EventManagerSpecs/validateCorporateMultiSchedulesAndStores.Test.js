const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const eventSchedulingPage = require('../EventManagerPO/eventScheduling.page')
const selectPOGPage = require("../EventManagerPO/selectingPogCommodities.page");
const createSFPEventPage = require("../EventManagerPO/createSFPEvent.page");

describe('validateCorporateMultiSchedulesAndStores: validate corporate Event created UI and DB', () => {

    it('Should validate corporate event created for multiple schedule and stores', async () => {

        let description = await createSFPEventPage.addEventDetails1('','SFP Event', emData.div_Corporate_060, emData.eventType_KOMPASS, "Update", "CorporateEvent", emData.vendor_AZB);
        await createSfpEventPage.submitEventDetails()
        let nextPage = await eventMaintanencePage.linkSubModule.getText();
        await expect(nextPage).toEqual(emData.Event_Schedule);
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        for (let index = 0; index <= 1; index++) {
            await eventMaintanencePage.waitBrowserToLoadPage(3000);
            await eventMaintanencePage.waitForPageLoad(eventSchedulingPage.saveScheduleBtn, emData.Save_Schedule);
            await clickOn(await storeSelectionPage.saveSchedule);
        }
        let week1Text = await (await storeSelectionPage.storeWeekSchedule.getText())
        let week2Text = await (await storeSelectionPage.storeWeekSchedule2.getText())
        let weekText = await (await storeSelectionPage.storeWeekSchedule2.getText()).replace('Week', '').replace(', ', '/')
        const week = week1Text.concat(' -', weekText).replace(', ', '/');
        await clickOn(await storeSelectionPage.saveGoToStore);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(storePage).toEqual(emData.Store_Selection)
        let divNum14 = (emData.div_Cincinnati_Operating_Division_014.match(/\d/g).join("")).toString();
        let dbScheduledStores = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, divNum14).replace('@num', '30'))).flat();
        await storeSelectionPage.clickPlusText(week1Text)
        await storeSelectionPage.clickPlusText("Division 014")
        await storeSelectionPage.clickCheckBoxText(await dbScheduledStores[5]);
        await storeSelectionPage.clickCheckBoxText(await dbScheduledStores[12]);
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.clickPlusText(week1Text)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.clickPlusText(await week2Text)
        await storeSelectionPage.clickPlusText("Division 016")
        let divNum16 = (emData.div_Columbus_Operating_Division_016.match(/\d/g).join("")).toString();
        let dbScheduledStoresDiv16 = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, divNum16).replace('@num', '30'))).flat();
        await storeSelectionPage.clickSecondSchedulethirdCheckbox(await dbScheduledStoresDiv16[0]);
        await storeSelectionPage.clickPlusText(await week2Text)
        await clickOn(storeSelectionPage.saveStoreSchedule)
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, emData.Select_Pogs)
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(pogPage).toEqual(emData.Select_Pogs)
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await selectPOGPage.selectDropdown('Department', emData.dept_01_GROCERY);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.selectDropdown('Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.selectDropdown('Commodity *', emData.ResetComplete_All);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await clickOn(selectPOGPage.selectChoosePogByCommodityBtn);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(9000);
        let spaceStationName = await selectPOG.getHeaderValues(emData.Space_Station_Name_Header)
        await (await selectPOG.gotoEventListButton).waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await clickOn(selectPOGPage.gotoEventListButton);
        await eventMaintanencePage.waitBrowserToLoadPage(7000);
        await expect(await eventMaintanencePage.eventCreationText.getText()).toEqual(emData.Event_Creation);
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await expect(browser).toHaveUrlContaining("EventMaintenance");
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters)
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        let eventDescription = await selectPOG.getEventDescText(await description, await week);
        await expect(eventDescription).toEqual(true)
        let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, description)
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        let eventId = await eventMaintanencePage.getEventId(initialEventId)
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await clickOn(await eventMaintanencePage.eventListFirstRowPog);
        let pogType = await eventMaintanencePage.getHeaderValue(emData.Header_Pog, description)
        let pogStringStoreNum = await eventMaintanencePage.extractingDigit(emData.PogString, emData.PogString, 1);
        await expect(pogType).toEqual(pogStringStoreNum)
        let pogID = await eventMaintanencePage.getPogHeaderValues(emData.Pog_Header)
        await expect(spaceStationName.length).toEqual(pogID.length)
        await expect(spaceStationName.sort()).toEqual(pogID)
        await eventMaintanencePage.clickCloseButton('Close');
        let dbUniquePogCount = (await result.getResult(qrString.uniquePogCount.replace('@evID', eventId))).flat().toString();
        await expect(pogStringStoreNum).toEqual(dbUniquePogCount)
    })
})
