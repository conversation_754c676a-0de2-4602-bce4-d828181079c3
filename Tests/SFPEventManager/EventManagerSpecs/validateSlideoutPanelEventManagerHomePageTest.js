const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require("../EventManagerPO/storeSelection.page");
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const selectPOG = require("../EventManagerPO/selectingPogCommodities.page");
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
let eventDescription;

describe('validateSlideoutPanelEventManagerHomePageTest: Validate Slideout Panel', () => {
   beforeEach(async () => {
        eventDescription = await createSfpEventPage.addEventDetails1('','SFP Event', emData.div_Cincinnati_Operating_Division_014, emData.eventType_KOMPASS, "Update", "npeEvent", emData.vendor_AZB);
        await createSfpEventPage.submitEventDetails();
        await createEventSchedulePage.scheduling(0);
        await storeSelectionPage.saveGoToStoreSelection();
        let div = (emData.div_Cincinnati_Operating_Division_014.match(/\d/g).join("")).toString();
        let dbScheduledStores = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, div).replace('@num', '30'))).flat();
        await storeSelectionPage.storeSelection(dbScheduledStores, 5, 1);
        await storeSelectionPage.storeSelection(dbScheduledStores, 6, 1);
        await storeSelectionPage.storeSelection(dbScheduledStores, 12, 1);
        await storeSelectionPage.saveStoreScheduleButton();
        await selectPOG.selectingPogCommodities('Department', emData.dept_01_GROCERY, 'Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER, 'Commodity *', emData.ResetComplete_All, '')
        await eventMaintanencePage.clickOn(selectPOG.selectChoosePogByCommodityBtn);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000, timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await eventMaintanencePage.clickOn(selectPOG.gotoEventListButton);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
    })

    it('Should validate Slideout Panel user information', async () => {
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 20000, timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'});
        await eventMaintanencePage.waitBrowserToLoadPage(5000);
        await eventMaintanencePage.clickFiltersWithDiscp(eventDescription);
        let eventId = await eventMaintanencePage.slideoutPanelEventId.getText();
        await eventMaintanencePage.clickOn(eventMaintanencePage.slideoutPanelEventId);
        await eventMaintanencePage.slideoutPanelCard.waitForDisplayed({timeout:3000, timeoutMsg: 'Error: Slideout Panel does not exist'})
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await expect(await eventMaintanencePage.eventIDheaderText.getText()).toEqual("Event: "+eventId);
        await eventMaintanencePage.slideoutPanelEventId.waitForExist({ timeout: 9000, timeoutMsg:'Error: Slideout Panel Event Id does not exist'});
        await eventMaintanencePage.clickOn(eventMaintanencePage.slideoutPanelEventId);
        await eventMaintanencePage.schedulesExpandPanel.waitForExist({ timeout: 9000, timeoutMsg:'Error: Schedules panel does not exist' });
        await eventMaintanencePage.clickOn(eventMaintanencePage.schedulesExpandPanel);
        await eventMaintanencePage.waitBrowserToLoadPage(5000);
        
        //Search pogName in the Slideout Panel
        await eventMaintanencePage.pogExpandPanel.waitForExist({ timeout: 9000, timeoutMsg:'Error: Pogs panel does not exist' });
        await eventMaintanencePage.clickOn(eventMaintanencePage.pogExpandPanel);
        let pogSearchNameTextBox = eventMaintanencePage.pogSearchBox;
        let pogSearchOption = emData.pogSearchOption;
        await pogSearchNameTextBox.setValue(pogSearchOption);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.pogNameTable.waitForExist({timeout: 9000, timeoutMsg:'Error: Pog doesnt exist'});
        await expect(await eventMaintanencePage.pogNameTable.getText()).toEqual(pogSearchOption);
        
        //Search stores in the Slideout Panel
        await eventMaintanencePage.storesExpandPanel.waitForExist({ timeout: 9000, timeoutMsg:'Error: Stores panel does not exist' });
        await eventMaintanencePage.clickOn(eventMaintanencePage.storesExpandPanel);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        let storesSearchNameTextBox = eventMaintanencePage.storesSearchBox;
        let storesSearchOption = emData.storesSearchOption;
        await storesSearchNameTextBox.setValue(storesSearchOption);
        await eventMaintanencePage.waitBrowserToLoadPage(7000);
        await (await eventMaintanencePage.storeNameTable).waitForExist({timeout: 9000, timeoutMsg:'Error: Store doesnt exist'});
        await expect(await eventMaintanencePage.storeNameTable.getText()).toEqual(await storesSearchOption);
        await eventMaintanencePage.clickOn(eventMaintanencePage.slideOutClose);
        await eventMaintanencePage.clickOn(eventMaintanencePage.clearAllFiltersBtn);
         })
    });