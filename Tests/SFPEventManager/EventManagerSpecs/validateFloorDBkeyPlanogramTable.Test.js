const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const eventSchedulingPage = require('../EventManagerPO/eventScheduling.page')
const selectPOGPage = require("../EventManagerPO/selectingPogCommodities.page");

describe('validate floorPlan DBKey for event in DB for multiple pogs Selected in UI', () => {

    it('Should validate floorDBKey for npe event in DB for multiple pogs', async () => {
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.waitBrowserToLoadPage(4000)
        await eventMaintanencePage.tableSecondPageBtn.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.selectEventType('SFP Event')
        await eventMaintanencePage.waitBrowserToLoadPage(4000)
        expect(await eventMaintanencePage.linkCreateSFPEvent.getText()).toHaveTextContaining(emData.Create_SFP_Event)
        await createSfpEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014)
        await createSfpEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        await createSfpEventPage.selectDropdown('selectType', "Update");
        let description = await selectPOG.stringGenerator("FloorDBKey")
        await createSfpEventPage.enterEventResetDescription(description)
        await createSfpEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSfpEventPage.submitEventDetails()
        let dbEventIdcreated = await (await result.getResult(qrString.corporateSplitEvent.replace('@discrp', description))).flat();
        let nextPage = await eventMaintanencePage.linkSubModule.getText();
        await expect(nextPage).toEqual(emData.Event_Schedule);
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        for (let index = 0; index <= 0; index++) {
            await eventMaintanencePage.waitBrowserToLoadPage(1000);
            await eventMaintanencePage.waitForPageLoad(eventSchedulingPage.saveScheduleBtn, emData.Save_Schedule);
            await clickOn(await storeSelectionPage.saveSchedule);
        }
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.saveGoToStoreSelection()
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(storePage).toEqual(emData.Store_Selection)
        let divNum14 = (emData.div_Cincinnati_Operating_Division_014.match(/\d/g).join("")).toString();
        let dbScheduledStores = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, divNum14).replace('@num', '30'))).flat();
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await storeSelectionPage.clickCheckBoxText(await dbScheduledStores[5]);
        await clickOn(storeSelectionPage.saveStoreSchedule)
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, emData.Select_Pogs)
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(pogPage).toEqual(emData.Select_Pogs)
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await selectPOGPage.selectDropdown('Department', emData.dept_01_GROCERY);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.selectDropdown('Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.selectDropdown('Commodity *', emData.commodity_01_GROCERY_01_GROC_ALL_OTHER_013_SPICES_EXTRACTS);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await clickOn(selectPOGPage.selectChoosePogByCommodityBtn);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        //await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        let dbKey = await (await selectPOG.getHeaderColumnValue(emData.Pog_Key_Header)).join();
        //get fp_dbkey value from sfp_event_planogram_tbl
        let floorPlanDBkey = await (await result.getResult(qrString.floorPlanDBKey.replace('@evID', dbEventIdcreated[0]))).flat().join();
        //get FP_DBKEY value from ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW
        let floorPlanDBkeyPlanogramMap = await (await result.getResult(qrString.floorPlanDBKeySTRPOGMAP.replace('@div', divNum14).replace('@STR','00'+dbScheduledStores[5]).replace('@dbkey',dbKey))).flat().join();
        await expect(await eventMaintanencePage.sortNumbersInString(floorPlanDBkey) ).toEqual(await eventMaintanencePage.sortNumbersInString(floorPlanDBkeyPlanogramMap));
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await clickOn(selectPOG.gotoEventListButton)
        await expect(browser).toHaveUrlContaining("EventMaintenance")
        await (await eventMaintanencePage.tableFirstRow).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters)
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        let releaseEventEnabled = await eventMaintanencePage.selectReleaseEvents(emData.ReleaseEvent_Corporate, emData.Header_Action, description)
        //await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.loadingSpinnerEventmaintenance, emData.releaseEventSpinnerText)
        await expect(releaseEventEnabled).toEqual(true)
       
    })

    it('Should validate floorPlan DBKey for Corporate event in DB for multiple pogs', async () => {
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.selectEventType('SFP Event')
        await expect((await (await eventMaintanencePage.linkCreateSFPEvent).getText()).toString()).toEqual(await emData.Create_SFP_Event)
        await createSfpEventPage.selectDropdown('division', emData.div_Corporate_060)
        await createSfpEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        await createSfpEventPage.selectDropdown('selectType', "Update");
        let description = await selectPOG.stringGenerator("FloorDBKey")
        await createSfpEventPage.enterEventResetDescription(description)
        await createSfpEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSfpEventPage.submitEventDetails()
        let dbEventIdcreated = await (await result.getResult(qrString.corporateSplitEvent.replace('@discrp', description))).flat();
        let nextPage = await eventMaintanencePage.linkSubModule.getText();
        await expect(nextPage).toEqual(emData.Event_Schedule);
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        for (let index = 0; index <= 0; index++) {
            await eventMaintanencePage.waitBrowserToLoadPage(2000);
            await eventMaintanencePage.waitForPageLoad(eventSchedulingPage.saveScheduleBtn, emData.Save_Schedule);
            await clickOn(await storeSelectionPage.saveSchedule);
        }
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        let weekText = await storeSelectionPage.storeWeekSchedule.getText()
        await storeSelectionPage.saveGoToStoreSelection()
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(storePage).toEqual(emData.Store_Selection)
        let divNum14 = (emData.div_Cincinnati_Operating_Division_014.match(/\d/g).join("")).toString();
        await storeSelectionPage.clickPlusText(weekText)
        await storeSelectionPage.clickPlusText("Division 014")
        let dbScheduledStores = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, divNum14).replace('@num', '30'))).flat()
        await storeSelectionPage.clickCheckBoxText(await dbScheduledStores[5]);
        await clickOn(storeSelectionPage.saveStoreSchedule)
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, emData.Select_Pogs)
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(pogPage).toEqual(emData.Select_Pogs)
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await selectPOGPage.selectDropdown('Department', emData.dept_01_GROCERY);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.selectDropdown('Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await selectPOGPage.selectDropdown('Commodity *', emData.commodity_01_GROCERY_01_GROC_ALL_OTHER_013_SPICES_EXTRACTS);
        await selectPOGPage.selectDropdown('Commodity *', emData.commodity_01_GROCERY_01_GROC_ALL_OTHER_003_CAN_BEANS);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await clickOn(selectPOGPage.selectChoosePogByCommodityBtn);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        let dbKey = await (await selectPOG.getHeaderColumnValue(emData.Pog_Key_Header)).join()
        //get fp_dbkey value from sfp_event_planogram_tbl
        let floorPlanDBkey = await (await result.getResult(qrString.floorPlanDBKey.replace('@evID', dbEventIdcreated[0]))).flat().join();
        //get FP_DBKEY value from ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW
        let floorPlanDBkeyPlanogramMap = await (await result.getResult(qrString.floorPlanDBKeySTRPOGMAP.replace('@div', divNum14).replace('@STR','00'+dbScheduledStores[5]).replace('@dbkey',dbKey))).flat().join();
        await expect(floorPlanDBkey).toEqual(floorPlanDBkeyPlanogramMap);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await clickOn(selectPOG.gotoEventListButton)
        await expect(browser).toHaveUrlContaining("EventMaintenance")
        await (await eventMaintanencePage.tableFirstRow).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters)
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        let releaseEventEnabled = await eventMaintanencePage.selectReleaseEvents(emData.ReleaseEvent_Corporate, emData.Header_Action, description)
        //await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.loadingSpinnerEventmaintenance, emData.releaseEventSpinnerText)
        await expect(releaseEventEnabled).toEqual(true)
    
    })

})
