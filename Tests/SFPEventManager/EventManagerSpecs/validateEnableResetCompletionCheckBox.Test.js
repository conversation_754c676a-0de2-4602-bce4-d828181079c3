const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const storeSelectionPage = require("../EventManagerPO/storeSelection.page");
const {clickOn} = require("../EventManagerPO/createSFPEvent.page");
const selectPOG = require("../EventManagerPO/selectingPogCommodities.page");
const eventMaintenancePage = require("../EventManagerPO/eventMaintanence.page");
const createSFPEventPage = require("../EventManagerPO/createSFPEvent.page");

let description;

describe('validateEnableResetCompletionCheckBox: Validate Enable Reset Completion checkbox before and after creating SFP event ', () => {

    it('validate Enable Reset Completion check box is unchecked and checked ', async () => {
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await eventMaintanencePage.tableSecondPageBtn.waitForExist({
            timeout: 90000,
            timeoutMsg: 'Error: Table contents for event maintenance page did not load before timeout'
        });
        await eventMaintanencePage.selectEventType('SFP Event');
        expect(await eventMaintanencePage.linkCreateSFPEvent.getText()).toHaveTextContaining(' Create SFP Event ');
        await createSfpEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014);
        await createSfpEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS);
        await createSfpEventPage.selectDropdown('selectType', "Update");
        description = await selectPOG.stringGenerator("CreateSFPEvent");
        await createSFPEventPage.enterEventResetDescription(description)
        await createSfpEventPage.selectDropdown('vendor', emData.vendor_AZB);
        await expect(await createSfpEventPage.singleCheckBoxIsSelected(await createSfpEventPage.additionalCriteriaCheckBoxList, 'ne_compliancy')).toEqual(true);
        await expect(await createSfpEventPage.clickAndValidateSingleCheckBox(await createSfpEventPage.additionalCriteriaCheckBoxList, 'ne_compliancy')).toEqual(false);
        await createSfpEventPage.submitEventDetails();
        let nextPage = await eventMaintanencePage.linkSubModule.getText();
        await expect(nextPage).toEqual('Event Schedule')
        await createEventSchedulePage.selectFiscalWeekDropdown('Fiscal Week',"scheduleEdit");
        await createEventSchedulePage.clickSaveScheduleBtn();
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await createSfpEventPage.sfpEventFirstStep.click();
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await expect(await createSfpEventPage.singleCheckBoxIsSelected(await createSfpEventPage.additionalCriteriaCheckBoxList, 'ne_compliancy')).toEqual(false);
        await createSfpEventPage.submitEventDetails();
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await createEventSchedulePage.clickDoneBtn();
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
        let storePage = await storeSelectionPage.linkSubModule.getText();
        await expect(storePage).toEqual(emData.Store_Selection);
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await createSfpEventPage.sfpEventFirstStep.click();
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await expect(await createSfpEventPage.singleCheckBoxIsSelected(await createSfpEventPage.additionalCriteriaCheckBoxList, 'ne_compliancy')).toEqual(false);
        await createSfpEventPage.submitEventDetails();
        await createEventSchedulePage.clickDoneBtn();
        await storeSelectionPage.clickCheckBoxText(emData.Store_353);
        await clickOn(storeSelectionPage.saveStoreSchedule)
        await selectPOG.selectingPogCommodities('Department', emData.dept_01_GROCERY, 'Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER, 'Commodity', emData.ResetComplete_All, '')
        await eventMaintanencePage.clickOn(selectPOG.selectChoosePogByCommodityBtn);
        await eventMaintanencePage.tableFirstRow.waitForExist({
            timeout: 9000,
            timeoutMsg: 'Error: Table contents for event maintenance page didn`t load before timeout'
        })
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(selectPOG.gotoEventListButton);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        let initialEventId = await eventMaintenancePage.getHeaderValue("Event Id", description);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await eventMaintenancePage.clickOn(await eventMaintenancePage.slideOutClose)
        let eventId = await eventMaintenancePage.getEventId(initialEventId)
        await expect(eventId).toEqual(initialEventId)
        let releaseEventEdit = await eventMaintanencePage.selectReleaseEvents(emData.ReleaseEvent_Edit, emData.Header_Action, description)
        await expect(releaseEventEdit).toEqual(true);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await expect(await createSfpEventPage.singleCheckBoxIsSelected(await createSfpEventPage.additionalCriteriaCheckBoxList, 'ne_compliancy')).toEqual(false);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await expect(await createSfpEventPage.clickAndValidateSingleCheckBox(await createSfpEventPage.additionalCriteriaCheckBoxList, 'ne_compliancy')).toEqual(true);
        await createSfpEventPage.submitEventDetails();
        await createSfpEventPage.sfpEventManagerBreadCrumb.click();
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        let releaseEventEditAfter = await eventMaintanencePage.selectReleaseEvents(emData.ReleaseEvent_Edit, emData.Header_Action, description)
        await expect(releaseEventEditAfter).toEqual(true);
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await expect(await createSfpEventPage.singleCheckBoxIsSelected(await createSfpEventPage.additionalCriteriaCheckBoxList, 'ne_compliancy')).toEqual(true);
    })


})

