const eventMaintenancePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const {clickOn} = require('../EventManagerPO/createSFPEvent.page');

let divValue;

describe('createEventsForResetComplete: This test is only responsible for creating events for ResetCompletion page, not part of regression', () => {
    const testIterations = [1,2,3,4,5];
    testIterations.forEach((iteration) => {

    it(`Should validate corporate event created  - Iteration: ${iteration}`, async () => {

        for (let i = 0; i < 2; i++) {
        for (let index = 0; index < 3; index++) {
            await eventMaintenancePage.waitBrowserToLoadPage(6000)
                await eventMaintenancePage.open('EventMaintenance');
                await eventMaintenancePage.waitBrowserToLoadPage(6000)
                await eventMaintenancePage.tableSecondPageBtn.waitForExist({
                    timeout: 90000,
                    timeoutMsg: 'Error: Table contents for event maintenance page did not load before timeout'
                })
                await eventMaintenancePage.selectEventType('SFP Event')
                await expect((await (await eventMaintenancePage.linkCreateSFPEvent).getText()).toString()).toEqual(emData.Create_SFP_Event);
              //  divValue = 'Columbus Operating Division - 016';
                // Use following division (014) for StoreUser; Don't delete;
                divValue = 'Cincinnati Operating Division - 014';
                await createSFPEventPage.selectDropdown('division', divValue);
               let scheduleType = ['KOMPASS', 'KOMPASS'];
                let eventType = [ 'Maintenance', 'Update', 'New Item Introduction (NII)'];
                await createSFPEventPage.selectDropdown('scheduleType', scheduleType[i]);
                await createSFPEventPage.selectDropdown('selectType', eventType[index]);
                let description = await selectPOG.stringGenerator(scheduleType[index] + "_" + eventType[index] + "-987654321");
                await createSFPEventPage.enterEventResetDescription(description);
                await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB);
                await createSFPEventPage.submitEventDetails();
                let nextPage = await eventMaintenancePage.linkSubModule.getText();
                await expect(nextPage).toEqual(emData.Event_Schedule);
                await eventMaintenancePage.waitBrowserToLoadPage(2000);
                if(await storeSelectionPage.saveSchedule.waitForClickable()){
                    await clickOn(await storeSelectionPage.saveSchedule);
                }
                await eventMaintenancePage.tableFirstRow.waitForExist({
                    timeout: 90000,
                    timeoutMsg: 'Error: Table contents for event maintenance page did not load before timeout'
                })
                await storeSelectionPage.saveGoToStoreSelection();
                let storePage = await storeSelectionPage.linkSubModule.getText();
                await expect(storePage).toEqual(emData.Store_Selection);
                await eventMaintenancePage.waitBrowserToLoadPage(3000);
                if (await divValue.match(/(\d[\d]*)/g)[0] === '014') {
                    await storeSelectionPage.clickCheckBoxText('359');
                } else {
                    await storeSelectionPage.clickRandomEnabledStoreCheckboxes(1, 5);
                }
                await eventMaintenancePage.waitBrowserToLoadPage(3000)
                await clickOn(await storeSelectionPage.saveStoreSchedule);
                await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Select Pogs");
                let pogPage = await storeSelectionPage.linkSubModule.getText();
                await expect(pogPage).toEqual(emData.Select_Pogs);
                await eventMaintenancePage.waitBrowserToLoadPage(2000)
                let intParams = await storeSelectionPage.generateTwoIntParams();
                await eventMaintenancePage.selectCommodityDropDownValuesByIndex(intParams[0], intParams[1])
                await eventMaintenancePage.waitBrowserToLoadPage(2000)
                await selectPOG.waitForPageLoad(await selectPOG.selectText, emData.Department)
                await eventMaintenancePage.waitBrowserToLoadPage(2000)
                await clickOn(await selectPOG.selectChoosePogByCommodityBtn)
                await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
                await eventMaintenancePage.waitBrowserToLoadPage(3000);
                await clickOn(await selectPOG.gotoEventListButton)
                //await expect(browser).toHaveUrlContaining("EventMaintenance")
                await eventMaintenancePage.waitForPageLoad(await eventMaintenancePage.filtersTxtBtn, emData.Filters)
                await eventMaintenancePage.clickFiltersWithDiscp(description.toString())
                await eventMaintenancePage.waitBrowserToLoadPage(2000);
                await eventMaintenancePage.clickOn(await eventMaintenancePage.slideOutClose)
                await eventMaintenancePage.waitBrowserToLoadPage(2000);
                let releaseEventEnabled = await eventMaintenancePage.selectReleaseEvents(emData.ReleaseEvent_Corporate, emData.Header_Action, description)
                await expect(releaseEventEnabled).toEqual(true);
            }
            await eventMaintenancePage.waitBrowserToLoadPage(1000);
        }
    })
    })
})
