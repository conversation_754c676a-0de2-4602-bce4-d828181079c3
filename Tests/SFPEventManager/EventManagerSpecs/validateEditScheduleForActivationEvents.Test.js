const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const eventSchedulingPage = require('../EventManagerPO/eventScheduling.page')
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const { assert } = require('chai')
const { setValue } = require('../../../GlobalObjectRepository/login.page');
const createSFPEventPage = require("../EventManagerPO/createSFPEvent.page");

let newScheduleStoreTblId,neweventScheduleDivTbl,neweventScheduleTbl,scheduleStoreTblId,eventScheduleDivTbl,eventScheduleTbl

describe('validateEditSchedulesForDivisionalEvents: validate edit schedules weeks changes the schedule ID DB', () => {

    before( async () => {

        let description = await createSFPEventPage.addEventDetails1('','SFP Activation', emData.div_Cincinnati_Operating_Division_014, emData.eventType_KOMPASS, "Activation", "editScheduleWeekAct", emData.vendor_AZB);
        await setValue(createSfpEventPage.defaultMin,3)
        await setValue(createSfpEventPage.defaultAllocation,1)
        await createSfpEventPage.submitEventDetails()
        let nextPage = await eventMaintanencePage.linkSubModule.getText();
        await expect(nextPage).toEqual(emData.Event_Schedule);
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 , timeoutMsg: 'Error: Save Schedule button did not appear before timeout'})
        for (let index = 0; index <= 2; index++) {
            await eventMaintanencePage.waitBrowserToLoadPage(3000);
            await eventMaintanencePage.waitForPageLoad(eventSchedulingPage.saveScheduleBtn, emData.Save_Schedule);
            await clickOn(await storeSelectionPage.saveSchedule);
        }
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        let weekText = await (await storeSelectionPage.storeWeekSchedule2.getText()).replace('Week', '').replace(', ', '/')
        await storeSelectionPage.saveGoToStoreSelection()
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, emData.Store_Selection)
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(storePage).toEqual(emData.Store_Selection)
        let divNum14 = (emData.div_Cincinnati_Operating_Division_014.match(/\d/g).join("")).toString()
        let dbScheduledStores = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, divNum14).replace('@num', '30'))).flat()
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await storeSelectionPage.clickScheduleStoreCheckbox(await dbScheduledStores[5],1)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.clickScheduleStoreCheckbox(await dbScheduledStores[12],2);
        await storeSelectionPage.clickScheduleStoreCheckbox(await dbScheduledStores[20],3)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await clickOn(await storeSelectionPage.saveStoreSchedule)
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, "Upload UPC(s)")
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(await pogPage).toEqual(emData.Upload_UPC)
        await createSfpEventPage.inputUPC.setValue(emData.UPC_Value_61076426171)
        await clickOn(createSfpEventPage.addUPC)
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 , timeoutMsg: 'Error: Table contents for event maintenance page didn`t load before timeout'})
        await clickOn(createSfpEventPage.saveUPC)
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 , timeoutMsg: 'Error: Table contents for event maintenance page didn`t load before timeout'})
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000, timeoutMsg: 'Error: Table contents for event maintenance page didn`t load before timeout'})
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, await description)
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        scheduleStoreTblId = await (await result.getResult(qrString.eventScheduleStoreTbl.replace('@evID', initialEventId))).flat()
        eventScheduleDivTbl = await (await result.getResult(qrString.eventScheduleDivTbl.replace('@evID', initialEventId))).flat()
        eventScheduleTbl = await (await result.getResult(qrString.eventScheduleTbl.replace('@evID', initialEventId))).flat()
        let releaseEventEdit = await eventMaintanencePage.selectReleaseEvents(emData.ReleaseEvent_Edit, emData.Header_Action, await description)
        await expect(releaseEventEdit).toEqual(true)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await createSfpEventPage.submitEventDetails()
        await createEventSchedulePage.selectDropdown('Fiscal Week')
        await eventMaintanencePage.waitBrowserToLoadPage(1000)
        await clickOn(await storeSelectionPage.saveSchedule);
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await createEventSchedulePage.clickDeleteEventBtn();
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await clickOn(await createEventSchedulePage.sfpAppModalButton)
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await storeSelectionPage.saveGoToStoreSelection();
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await storeSelectionPage.clickScheduleStoreCheckbox(await dbScheduledStores[5],1)
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await clickOn(await storeSelectionPage.saveStoreSchedule)
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, "Upload UPC(s)");
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 , timeoutMsg: 'Error: Table contents for event maintenance page didn`t load before timeout'})
        await clickOn(createSfpEventPage.saveUPC)
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters);
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 , timeoutMsg: 'Error: Table contents for event maintenance page didn`t load before timeout'})
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        let finalEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, await description)
        newScheduleStoreTblId = await (await result.getResult(qrString.eventScheduleStoreTbl.replace('@evID', await (finalEventId)))).flat();
        neweventScheduleDivTbl = await (await result.getResult(qrString.eventScheduleDivTbl.replace('@evID', await (finalEventId)))).flat();
        neweventScheduleTbl = await (await result.getResult(qrString.eventScheduleTbl.replace('@evID', await (finalEventId)))).flat();
        })

    it('Should validate edit schedules weeks changes the schedule ID in scheduleStoreTbl DB', async () => {
        //validate the deleted scheduleStoreTblId for store table
        for (let index = 0; index < newScheduleStoreTblId.length; index++) {
            await assert.notEqual(await newScheduleStoreTblId[index], await (scheduleStoreTblId[2].toString()), "Deleted Schedule ID found")
        }
    })

    it('Should validate edit schedules weeks changes the schedule ID in eventScheduleTbl DB', async () => {
        //validate the deleted eventScheduleTbl for store table
        for (let index = 0; index < neweventScheduleTbl.length; index++) {
            await assert.notEqual(await neweventScheduleTbl[index], (eventScheduleTbl[2].toString()), "Deleted Schedule ID found")
        }
    })

    it('Should validate edit schedules weeks changes the schedule ID in eventScheduleDivTbl DB', async () => {
        //validate the deleted eventScheduleTbl for store table
        for (let index = 0; index < neweventScheduleDivTbl.length; index++) {
            await assert.notEqual(await neweventScheduleDivTbl[index], (eventScheduleDivTbl[2]), "Deleted Schedule ID found")
            
        }
        for (let index = 0; index < eventScheduleDivTbl.length; index++) {
            await assert.notEqual(await eventScheduleDivTbl[index], (neweventScheduleDivTbl[2]), "Added Schedule ID not found")
            
        }
    })
})
