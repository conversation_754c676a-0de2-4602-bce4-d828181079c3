const eventMaintenancePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const { assert } = require('chai');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');

describe('validateCorporateSfpEvent: validate corporate Event created UI and DB', () => {

    it('Should validate corporate event cretated ', async () => {
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await eventMaintenancePage.open('EventMaintenance');
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await eventMaintenancePage.tableSecondPageBtn.waitForExist({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await eventMaintenancePage.selectEventType('SFP Event')
        await expect((await (await eventMaintenancePage.linkCreateSFPEvent).getText()).toString()).toEqual(await emData.Create_SFP_Event)
        await createSFPEventPage.selectDropdown('division', emData.div_Corporate_060)
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        await createSFPEventPage.selectDropdown('selectType', "Update");
        let description = await selectPOG.stringGenerator("CorporateEvent")
        await createSFPEventPage.enterEventResetDescription(description)
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSFPEventPage.submitEventDetails()
        let nextPage = await eventMaintenancePage.linkSubModule.getText()
        await expect(nextPage).toEqual(emData.Event_Schedule)
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 , timeoutMsg:'Error: Save Schedule button did not appear before timeout'})
        await createEventSchedulePage.selectDropdown('Fiscal Week');
        await clickOn(storeSelectionPage.saveSchedule)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        let weekText = await storeSelectionPage.storeWeekSchedule.getText()
        await storeSelectionPage.saveGoToStoreSelection()
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(storePage).toEqual(emData.Store_Selection)
        await eventMaintenancePage.waitBrowserToLoadPage(3000)
        await storeSelectionPage.clickPlusText(weekText)
        await storeSelectionPage.clickPlusText("Division 014")
        await storeSelectionPage.clickCheckBoxText(emData.Store_353);
        await clickOn(storeSelectionPage.saveStoreSchedule)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Select Pogs")
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(pogPage).toEqual(emData.Select_Pogs)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Department', emData.dept_01_GROCERY)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Commodity *', emData.commodity_01_GROCERY_01_GROC_ALL_OTHER_013_SPICES_EXTRACTS)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Commodity Group', emData.CommodityGroup_013_SPICES_EXTRACTS_013_SPICES_EXTRACTS)
        await clickOn( await selectPOG.selectChoosePogByCommodityBtn)
        await selectPOG.gotoEventListButton.click();
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await expect(browser).toHaveUrlContaining("EventMaintenance")
        await (await eventMaintenancePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.filtersTxtBtn, emData.Filters)
        await eventMaintenancePage.clickFiltersWithDiscp(description.toString())
        // validate the event description
        let eventDescription = await selectPOG.getEventDescText(description, weekText.replace(', ', '/'))
        await expect(eventDescription).toEqual(true)
        let initialEventId = await eventMaintenancePage.getHeaderValue(emData.Event_ID, description)
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await eventMaintenancePage.clickOn(await eventMaintenancePage.slideOutClose)
        let eventId = await eventMaintenancePage.getEventId(initialEventId)
        await expect(eventId).toEqual(initialEventId)
        let dataFromDb = await result.getResult(qrString.SfpEvent_Eventid.replace('eventID', initialEventId));
        await assert.equal(dataFromDb.length, 1)
        let releaseEventEnabled = await eventMaintenancePage.selectReleaseEvents(emData.ReleaseEvent_Corporate, emData.Header_Action, description)
        await expect(releaseEventEnabled).toEqual(true)
        await browser.pause(6000)
        let releaseText = await (await (eventMaintenancePage.eventReleaseTextAlert)).getText()
        await expect(releaseText).toEqual(emData.ReleaseEventMsg)
        let corpEventStatus = await result.getResult(qrString.SfpEvent_corpStatus.replace('eventID', initialEventId));
        await assert.equal(corpEventStatus, emData.corpEvent_Released)
    })
})
