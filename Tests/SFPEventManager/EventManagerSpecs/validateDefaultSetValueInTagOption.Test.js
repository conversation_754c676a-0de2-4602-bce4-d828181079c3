const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const { assert } = require('chai');

let description;
describe('validateDefaultSetValueInTagOption:validate Sfp event headertable for DefaultSetValueInTag UI and DB', () => {

    beforeEach(async () => {
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await eventMaintanencePage.tableSecondPageBtn.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout');
        await eventMaintanencePage.selectEventType('SFP Event');
        expect(await eventMaintanencePage.linkCreateSFPEvent.getText()).toHaveTextContaining(emData.Create_SFP_Event)
        await createSFPEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014)
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        await createSFPEventPage.selectDropdown('selectType', "Update");
        description = await selectPOG.stringGenerator("AutomationEventHeader")
        await createSFPEventPage.enterEventResetDescription(description)
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSFPEventPage.clickCheckBox(emData.checkBox_RequestTags)
        await createSFPEventPage.enterValue(emData.field_userHeading, await createSFPEventPage.stringGenerator("TestAutomationSet"))
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
    })

    it('Should validate HeaderTable for DefaultSetValueInTag UI and DB colunm', async () => {
        let text = createSFPEventPage.tagsSetsText.getValue();
        await expect(await createSFPEventPage.tagsSetsText.getValue()).toEqual("1");
        await createSFPEventPage.submitEventDetails();
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout');
        await clickOn(await storeSelectionPage.saveSchedule);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        let weekText = await storeSelectionPage.storeWeekSchedule.getText();
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await clickOn(await storeSelectionPage.saveGoToStore);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, emData.Store_Selection);
        let storePage = await storeSelectionPage.linkSubModule.getText();
        await expect(storePage).toEqual(emData.Store_Selection);
        await eventMaintanencePage.open(emData.EventMaintanceUrl);
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters)
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        let requestTagType = await eventMaintanencePage.getHeaderValue(emData.Header_Tags, description);
        await expect(requestTagType).toEqual(emData.Event_Tags_S_RES);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, description);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        let eventId = await eventMaintanencePage.getEventId(initialEventId);
        await expect(eventId).toEqual(initialEventId);
        let requestTags = await (await result.getResult(qrString.SfpEventTagstypeSets.replace('@eventID', initialEventId))).flat();
        await expect(requestTags[0].toString()).toEqual(await text);
        await assert.equal(requestTags.length, 1);
    })

    it('Should validate HeaderTable for Edited DefaultSetValueInTag UI and DB colunm', async () => {
        await createSFPEventPage.enterValue(emData.field_sets, 3);
        await expect(await createSFPEventPage.tagsSetsText.getValue()).toEqual("3");
        let newSetValue = createSFPEventPage.tagsSetsText.getValue();
        await createSFPEventPage.submitEventDetails();
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        await clickOn(storeSelectionPage.saveSchedule);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        let weekText = await storeSelectionPage.storeWeekSchedule.getText();
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await clickOn(storeSelectionPage.saveGoToStore);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, emData.Store_Selection)
        let storePage = await storeSelectionPage.linkSubModule.getText();
        await expect(storePage).toEqual(emData.Store_Selection);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await eventMaintanencePage.open(emData.EventMaintanceUrl);
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters)
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        let eventType = await eventMaintanencePage.getHeaderValue(emData.Header_Tags, description);
        await expect(eventType).toEqual(emData.Event_Tags_S_RES);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, description);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        let eventId = await eventMaintanencePage.getEventId(initialEventId);
        await expect(eventId).toEqual(initialEventId);
        let setValueChange = await (await result.getResult(qrString.SfpEventTagstypeSets.replace('@eventID', initialEventId))).flat();
        await expect(setValueChange[0].toString()).toEqual(await newSetValue);
        await assert.equal(setValueChange.length, 1);
    })

    it('Should validate HeaderTable for Tag unchecked UI and eventSets DB column', async () => {
        let text = createSFPEventPage.tagsSetsText.getValue();
        await expect(await createSFPEventPage.tagsSetsText.getValue()).toEqual("1");
        await createSFPEventPage.submitEventDetails();
        let nextPage = await eventMaintanencePage.linkSubModule.getText();
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout');
        await clickOn(storeSelectionPage.saveSchedule);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await clickOn(storeSelectionPage.saveGoToStore);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, emData.Store_Selection);
        let storePage = await storeSelectionPage.linkSubModule.getText();
        await expect(storePage).toEqual(emData.Store_Selection);
        await eventMaintanencePage.open(emData.EventMaintanceUrl);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters)
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        let requestTagType = await eventMaintanencePage.getHeaderValue(emData.Header_Tags, description);
        await expect(await requestTagType).toEqual(emData.Event_Tags_S_RES);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, description);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        let eventId = await eventMaintanencePage.getEventId(initialEventId);
        await expect(eventId).toEqual(initialEventId);
        let requestTags = await (await result.getResult(qrString.SfpEventTagstypeSets.replace('@eventID', initialEventId))).flat();
        await expect(requestTags[0].toString()).toEqual(await text);
        await assert.equal(requestTags.length, 1);
        let releaseEventEdit = await eventMaintanencePage.selectReleaseEvents(emData.ReleaseEvent_Edit, emData.Header_Action, description);
        await expect(await releaseEventEdit).toEqual(true);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await createSFPEventPage.clickCheckBox(emData.checkBox_RequestTags);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await createSFPEventPage.submitEventDetails();
        await expect(nextPage).toEqual(emData.Event_Schedule);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await eventMaintanencePage.open(emData.EventMaintanceUrl);
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl);
        await eventMaintanencePage.waitBrowserToLoadPage(10000);
        // let eventType = await eventMaintanencePage.getHeaderValue(emData.Header_Tags, description);
        // await expect(eventType).toEqual("-");
        let setValueChange = await (await result.getResult(qrString.SfpEventTagstypeSets.replace('@eventID', initialEventId))).flat();
        await expect(setValueChange[0]).toEqual(null);
        await assert.equal(setValueChange.length, 1);
    })
})

