const resetCompletionPage = require("../EventManagerPO/resetCompletion.page.js");
const emData = require("../../../TestData/EventManagerData/eventManagerUIData.json");
const autoActivationReport = require("../EventManagerPO/autoActivationReport.page.js");
const storeSelectionPage = require("../EventManagerPO/storeSelection.page.js");

let divNum = emData.Division_011_Atlanta_Division.match(/(\d[\d]*)/g)[0];

beforeEach(async () => {
    await resetCompletionPage.waitBrowserToLoadPage(6000)
    await resetCompletionPage.open('EventMaintenance/resetCompletion');
    await resetCompletionPage.waitForPageLoad(resetCompletionPage.searchBtn, emData.resetCompletion_Search);
    await resetCompletionPage.clearFiltersBtn.click();
    //await resetCompletionPage.waitForPageLoad(resetCompletionPage.selectDivisionTxt, emData.resetCompletion_SelectDivision);
    await resetCompletionPage.selectSingleValue(emData.selectedDivision_ResetCompletion, emData.Division_011_Atlanta_Division);
    await autoActivationReport.selectYPW(emData.Year_Input, emData.Year_2023);
    await autoActivationReport.selectYPW(emData.Period_Input, emData.Period_Empty);

})

describe('validateResetCompleteDropdownOnResetCompletion: validate the Reset Completion Dropdown options on UI', () => {

    it('validateResetCompleteSingleDropdownOptionYes: validate resetComplete dropdown options when only Yes value is selected on StoreReview UI table ', async () => {
        await resetCompletionPage.selectSingleValue(emData.selectedCompletionType_ResetCompletion, emData.ResetComplete_Yes)
        await storeSelectionPage.clickOn(resetCompletionPage.searchBtn);
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        await storeSelectionPage.clickOn(resetCompletionPage.storeReview);
        let resetCompleteYesTxt = await resetCompletionPage.selectResetComplete.getText();
        // validating 'Yes' option is select from ResetComplete Dropdown
        await expect(resetCompleteYesTxt).toEqual(emData.resetComplete_Yes);
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        let uiStoreReviewYes = await resetCompletionPage.getStoreReviewAllUIData("2");
        //[ 'Yes', 'Yes', 'Yes' ] capture all Yes values in an array
        let yesColumnArray = await resetCompletionPage.extractArrayFromArrays(uiStoreReviewYes, "13");
        //[ 'Yes' ] - capture unique value from above array
        let uniqueValueYes = await resetCompletionPage.getUniqueArrayValue(yesColumnArray);
        //validating 'Yes' values on StoreReview table
        await expect(resetCompleteYesTxt).toEqual(await uniqueValueYes.toString());
        })

    it('validateResetCompleteSingleDropdownOptionNo: validate resetComplete dropdown options when No value is selected on StoreReview UI table ', async () => {
        await resetCompletionPage.selectSingleValue(emData.selectedCompletionType_ResetCompletion, emData.resetComplete_No);
        await storeSelectionPage.clickOn(resetCompletionPage.searchBtn);
        await storeSelectionPage.clickOn(resetCompletionPage.storeReview);
        let resetCompleteNoTxt = await resetCompletionPage.selectResetComplete.getText()
        // validating 'No' option is select from ResetComplete Dropdown
        await expect(resetCompleteNoTxt).toEqual('No')
        let uiStoreReviewNo = await resetCompletionPage.getStoreReviewAllUIData("2")
        //[ 'No', 'No', 'No', 'No' ] capture all No values and push to array
        let noColumnArray = await resetCompletionPage.extractArrayFromArrays(uiStoreReviewNo, "13")
        //[ 'No'] Find unique value from above array
        let uniqueValueNo = await resetCompletionPage.getUniqueArrayValue(noColumnArray)
        //validating 'No' values on StoreReview table
        await expect(resetCompleteNoTxt).toEqual(uniqueValueNo.toString())
        })

    it('validateResetCompleteSingleDropdownOptionNoResponse: validate resetComplete dropdown options when NoResponse value is selected on StoreReview UI table ', async () => {
        await resetCompletionPage.selectSingleValue(emData.selectedCompletionType_ResetCompletion, emData.resetComplete_NoResponse);
        await storeSelectionPage.clickOn(resetCompletionPage.searchBtn);
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        await storeSelectionPage.clickOn(resetCompletionPage.storeReview);
        let resetCompleteNoResponseTxt = await resetCompletionPage.selectResetComplete.getText();
        // validating 'No Response' option is select from ResetComplete Dropdown
        await expect(resetCompleteNoResponseTxt).toEqual(emData.resetComplete_NoResponse);
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        await resetCompletionPage.waitForPageLoad(await resetCompletionPage.divTopRowValueStoreReview, divNum);
        let uiStoreReviewNoResponse = await resetCompletionPage.getStoreReviewAllUIData("2");
        let noResponseColumnArray = await resetCompletionPage.extractArrayFromArrays(uiStoreReviewNoResponse, "13");
        let uniqueValueNoResponse = await resetCompletionPage.getUniqueArrayValue(noResponseColumnArray);
        await expect(resetCompleteNoResponseTxt).toEqual(uniqueValueNoResponse.toString());
    })

    it('validateResetCompleteMultipleDropdownOptions: validate resetComplete dropdown options when two options are selected ', async () => {
        await resetCompletionPage.setMultipleValues(emData.selectedCompletionType_ResetCompletion, emData.ResetComplete_No_Yes);
        await storeSelectionPage.clickOn(resetCompletionPage.searchBtn);
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        await storeSelectionPage.clickOn(resetCompletionPage.storeReview);
        let resetCompleteYesNoTxt = await resetCompletionPage.selectResetComplete.getText();
        // validate two options are selected: // No_Yes
        await expect(resetCompleteYesNoTxt).toEqual('2 Items Selected');
        let uiStoreReviewYesNo = await resetCompletionPage.getStoreReviewAllUIData("2");
        // [ 'No', 'Yes', 'No', 'Yes' ]
        let noYesColumnArray = await resetCompletionPage.extractArrayFromArrays(uiStoreReviewYesNo, "13");
        // [ 'No', 'Yes' ] Unique values
        let uniqueValueNoResponse = await resetCompletionPage.getUniqueArrayValue(noYesColumnArray);
        //removing "Select All"
        let inputValueNoYes = await resetCompletionPage.removeElementFromArray(emData.ResetComplete_No_Yes,emData.resetComplete_SelectAll);
        // compare dropdown options to unique storeReview UI values
        await expect(inputValueNoYes).toEqual(uniqueValueNoResponse);
        await resetCompletionPage.waitForPageLoad(resetCompletionPage.searchBtn, emData.resetCompletion_Search);
        await autoActivationReport.selectYPW(emData.Year_Input, emData.Year_2023);
        await resetCompletionPage.selectSingleValue(emData.selectedLocation_ResetCompletion, emData.resetComplete_SelectAll);
        await resetCompletionPage.selectSingleValue(emData.selectedCompletionType_ResetCompletion, emData.resetComplete_SelectAll);
        await resetCompletionPage.setMultipleValues(emData.selectedCompletionType_ResetCompletion, emData.ResetComplete_No_Deleted);
        await expect(await resetCompletionPage.selectResetComplete.getText()).toEqual(emData.dropdown_2_Items_Selected);
        await storeSelectionPage.clickOn(resetCompletionPage.searchBtn);
        await storeSelectionPage.clickOn(resetCompletionPage.storeReview);
        let uiStoreReviewNoDeleted = await resetCompletionPage.getStoreReviewAllUIData(2);
        let noColumnArray = await resetCompletionPage.extractArrayFromArrays(uiStoreReviewNoDeleted, "13");
        let inputValueNo =  emData.ResetComplete_No_Deleted[0]
        await expect(noColumnArray[0].toString()).toEqual(inputValueNo);
        let deletedColumnArray = await resetCompletionPage.extractArrayFromArrays(uiStoreReviewNoDeleted, "14");
        let uniqueValueDeleted = await resetCompletionPage.getUniqueArrayValue(deletedColumnArray);
        let inputValueDeleted = emData.ResetComplete_No_Deleted[1];  
        await expect(uniqueValueDeleted.toString()).toEqual(inputValueDeleted);
    })
})
