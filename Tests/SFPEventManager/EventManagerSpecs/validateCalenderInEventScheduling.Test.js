import {getOnlyDay} from "../../../util/date"
const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const CreateEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');

const currentDate = new Date();
const formattedDate = currentDate.toLocaleDateString('en-US', {
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
});

describe('validateCalenderInEventScheduling: Validate the calender in Event Scheduling', () => {

    it('Should Validate the calender in Event Scheduling', async () => {
      await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintenance'); 
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.tableSecondPageBtn.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.selectEventType('SFP Event')
        expect(await eventMaintanencePage.linkCreateSFPEvent.getText()).toHaveTextContaining(' Create SFP Event ')
        await createSfpEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014)
        await createSfpEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        await createSfpEventPage.selectDropdown('selectType', "Update");
        await createSfpEventPage.enterEventResetDescription('EventSchedulingCalender')
        await createSfpEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSfpEventPage.clickCheckBox(emData.checkBox_RequestShelfStrips)
        await createSfpEventPage.selectRadioButton(emData.radioBtn_Office)
        await createSfpEventPage.selectRadioButton(emData.radioBtn_MaintenanceStripsChangesOnly)
        await createSfpEventPage.clickCheckBox(emData.checkBox_RequestTags)
        await createSfpEventPage.selectRadioButton(emData.radioBtn_Store)
        //await createSfpEventPage.selectHeadingPrefix(emData.heading_Prefix_RES)
        await createSfpEventPage.enterValue(emData.field_userHeading, await createSfpEventPage.stringGenerator("TestAutomationSet"))
        await createSfpEventPage.enterValue(emData.field_sets, 5)
        await createSfpEventPage.clickCheckBox(emData.checkBox_NewItemsOnly)
        await createSfpEventPage.enterUserComments(await createSfpEventPage.stringGenerator("TestAutomationComments "))
        await createSfpEventPage.clickCheckBox(emData.checkBox_ActivateCAO2weeksbeforeEventSchedule)
        await createSfpEventPage.submitEventDetails()
        let nextPage = await eventMaintanencePage.linkSubModule.getText()
        await expect(nextPage).toEqual('Event Schedule')
        //await CreateEventSchedulePage.selectFiscalWeekDropdown('Fiscal Week');
        await CreateEventSchedulePage.selectDropdown('Process Weekday', await emData.processWeekDay_Tuesday);
        await CreateEventSchedulePage.datePicker(getOnlyDay())
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        await CreateEventSchedulePage.clickSaveScheduleBtn()
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await CreateEventSchedulePage.waitScheduledDay(await emData.processWeekDay_Tuesday)
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await CreateEventSchedulePage.waitForPageLoad((await CreateEventSchedulePage.retailEffectiveDate), formattedDate)
        await clickOn(storeSelectionPage.goToStoreSelection)
    })
})
