const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const selectPOGPage = require("../EventManagerPO/selectingPogCommodities.page");
const { assert, expect } = require('chai')

let keysToFind;

describe('validateSlideOutPannelActivaionEvent: validate the Slide pannel in EM page', () => {

    it('Should validate he slide out pannel for Activation events', async () => {
        let eventDescription = await createSfpEventPage.addEventDetails1('','SFP Refresh', emData.div_Cincinnati_Operating_Division_014, emData.eventType_KOMPASS, "Update","RefreshSlideOut", emData.vendor_AZB, ["digitalRefresh","allocationRefresh","appRefresh"])
        await createSfpEventPage.submitEventRefreshDetails()
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await eventMaintanencePage.clickOn(createSfpEventPage.sfpAppModalButton)
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await createEventSchedulePage.scheduling()
        await storeSelectionPage.saveGoToStoreSelection()
        let div = (emData.div_Cincinnati_Operating_Division_014.match(/\d/g).join("")).toString()
        let dbScheduledStores = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, div).replace('@num', '30'))).flat();
        await storeSelectionPage.storeSelection(dbScheduledStores, 11, 1)
        await storeSelectionPage.saveStoreScheduleButton()
        await eventMaintanencePage.waitBrowserToLoadPage(8000);
        await selectPOGPage.selectingPogCommodities('Department', emData.dept_01_GROCERY, 'Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER, 'Commodity *', emData.commodity_003_CAN_BEANS_01_GROC_ALL_OTHER_01_GROCERY, '')
        await clickOn(selectPOGPage.selectChoosePogByCommodityBtn);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await (await selectPOGPage.gotoEventListButton).waitForExist({ timeout: 90000 }, 'Error: EventList button not visible')
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await clickOn(selectPOGPage.gotoEventListButton);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await expect(await eventMaintanencePage.eventCreationText.getText()).to.equal(emData.Event_Creation);
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.clickFiltersWithDiscp(eventDescription.toString())
        let eventDisp = await eventMaintanencePage.getHeaderValue(emData.Filters_Event_Desc, eventDescription)
        let pogType = await eventMaintanencePage.getHeaderValue(emData.Header_Pog, eventDisp)
        let pogStringStoreNum = await eventMaintanencePage.extractingDigit(emData.PogString, emData.PogString, 1);
        await expect(pogType).to.equal(pogStringStoreNum)
        let pogID = await eventMaintanencePage.getPogHeaderValues(emData.Pog_Header)
        await eventMaintanencePage.clickCloseButton('Close');
        let scheduled = await eventMaintanencePage.getHeaderValue(emData.Header_Scheduled, eventDescription)

        //stores
        let scheduledStrCount = await eventMaintanencePage.getHeaderValue(emData.Header_ScheduledStr, eventDescription)
        let popUpSchedule = (await eventMaintanencePage.getColumnValues(emData.event_Schedule_Popup_Schedule_Table, emData.event_Schedule_Popup_Schedule_List)).sort();
        let popUpScheduleDiv = await eventMaintanencePage.getColumnValues(emData.event_Schedule_Popup_Schedule_Table, emData.event_Schedule_Popup_Schedule_Div_List)
        const modifiedopUpScheduleDiv = popUpScheduleDiv.map((element) => element.replace(new RegExp(`^Div\\s*`), ''));
        await expect(popUpSchedule.length.toString()).to.equal(scheduledStrCount)
        await eventMaintanencePage.clickCloseButton('Close');
        let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, eventDescription)
        let eventIdInSlide = await (await eventMaintanencePage.getTextOfElement(eventMaintanencePage.slidePaneleventId));
        await expect(initialEventId).to.equal(await eventMaintanencePage.extractDigits(eventIdInSlide));
        await assert.strictEqual(eventDisp, await eventMaintanencePage.getTextOfElement(eventMaintanencePage.slidePanelDiscription))
        // assert slideOutPanel outside values 
        keysToFind = ['Schedule Type:', 'Type:', 'Activation'];
        const slidePanelTableValues = await eventMaintanencePage.getValuesByKeysSlidePanel("4", keysToFind)
        assert.strictEqual(slidePanelTableValues, emData.sildePanel_REF_KOM_Yes);
        // assert slideOutPanel outside values 
        keysToFind = ['Division:', 'UPC List:','Schedules:', 'Stores Scheduled:', 'Stores Unscheduled:'];
        const slidePanelTableValues1 = await eventMaintanencePage.getValuesByKeysSlidePanel("5", keysToFind)
        assert.strictEqual(slidePanelTableValues1, emData.sildePanelRef_014__1_1_104);

         //assert EM pog dropdown with slideOutPanel
         await eventMaintanencePage.slidePanelTabsClick(emData.slidePanelPOGs);
         const columnValues = eventMaintanencePage.getColumnValuesByHeaderNameSlidePanel(emData.slidePanelPOGs, emData.Space_Station_Name_Header);
         assert.deepEqual(pogID.sort(), (await columnValues).sort());
         await browser.pause(2000);
         (await eventMaintanencePage.slidePanelCloseButton).click()
         await eventMaintanencePage.slidePanelTabsClick(emData.slidePanelPOGs);

        //assert EM Schedule dropdown with slideOutPanel
        await eventMaintanencePage.slidePanelTabsClick(emData.slidePanelSchedules);
        const columnValues1 = eventMaintanencePage.getColumnValuesByHeaderNameSlidePanel(emData.slidePanelSchedules, emData.slidePanelStoreHeader);
        assert.deepEqual(scheduled.toString(), (await columnValues1).toString());
        await browser.pause(2000);
        (await eventMaintanencePage.slidePanelCloseButton).click()
        await eventMaintanencePage.slidePanelTabsClick(emData.slidePanelPOGs);

        //assert EM Stores dropdown with slideOutPanel
        await eventMaintanencePage.slidePanelTabsClick(emData.slidePanelStores);
        const stotreColumnValues = await eventMaintanencePage.getColumnValuesByHeaderNameSlidePanel(emData.slidePanelStores, emData.slidePanelColumnStores);
        const divisionColumnValues = await eventMaintanencePage.getColumnValuesByHeaderNameSlidePanel(emData.slidePanelStores, emData.slidePanelColumnStoresDiv);
        assert.deepEqual(popUpSchedule, stotreColumnValues);
        assert.deepEqual(modifiedopUpScheduleDiv, divisionColumnValues);
    })

})