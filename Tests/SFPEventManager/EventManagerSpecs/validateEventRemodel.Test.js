const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData=require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page.js');
const {clickOn} = require("../EventManagerPO/createSFPEvent.page");
const selectPOGPage = require("../EventManagerPO/selectingPogCommodities.page");
const result = require('../../../SQLConnection/server.js.ts');
const qrString = require('../../../SQLConnection/queryStringEM');
const eventSchedulingPage = require('../EventManagerPO/eventScheduling.page')

describe('validateEventRemodel: Validate Create SFP Event Page - Remodel flow with multiple stores and comparing to db', () => {

    beforeEach( async () =>{
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.tableSecondPageBtn.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout');
        await eventMaintanencePage.selectEventType('SFP Event');
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        expect(await (await eventMaintanencePage.linkCreateSFPEvent).getText()).toHaveTextContaining(emData.Create_SFP_Event)
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await createSFPEventPage.selectDropdown('division', emData.div_Atlanta_Operating_Division_011);
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_Remodel)
    })

    it('Validate Create SFP Event Remodel flow with selecting one Division and 3 stores; compare stores and dbKeys to db', async () => {
        let description =  await selectPOGPage.stringGenerator("EventRemodel");
        await createSFPEventPage.enterEventResetDescription(description);
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB);
        await createSFPEventPage.submitEventDetails();
        let nextPage = await eventMaintanencePage.linkSubModule.getText();
        await expect(nextPage).toEqual(emData.Event_Schedule);
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        for(let i=0; i<=1; i++){
            await eventMaintanencePage.waitBrowserToLoadPage(3000);
            await eventMaintanencePage.waitForPageLoad(eventSchedulingPage.saveScheduleBtn, emData.Save_Schedule);
            await clickOn(await storeSelectionPage.saveSchedule);
        }await eventMaintanencePage.waitBrowserToLoadPage(7000);
        await clickOn(storeSelectionPage.saveGoToStore);
        await eventMaintanencePage.waitBrowserToLoadPage(7000);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
        let storePage = await storeSelectionPage.linkSubModule.getText();
        await expect(storePage).toEqual(emData.Store_Selection);
        let divNum =  (emData.div_Atlanta_Operating_Division_011.match(/\d/g).join("")).toString();
        let dbRemodelStores = await (await result.getResult(qrString.remodelStores.replace(/@div/g, divNum).replace('@num','3'))).flat();
        await storeSelectionPage.clickCheckBoxText(await dbRemodelStores[0]);
        await storeSelectionPage.clickCheckBoxText(await dbRemodelStores[1]);
        await storeSelectionPage.clickSecondScheduleStoreCheckbox(await dbRemodelStores[2]);
        await clickOn(await storeSelectionPage.saveStoreSchedule);
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, emData.Select_Pogs);
        let pogPage = await storeSelectionPage.linkSubModule.getText();
        await expect(pogPage).toEqual(emData.Select_Pogs)
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText,emData.Department);
        await selectPOGPage.selectDropdown('Department', emData.selecting_POG_Department_15_DELI);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText,emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.selectDropdown('Sub-Department', emData.selecting_POG_Sub_Department);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText,emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.selectDropdown('Commodity *', emData.selecting_POG_Commodity);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText,emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.selectDropdown('Commodity Group', emData.selecting_POG_CommodityGroup);
        await clickOn(selectPOGPage.selectChoosePogByCommodityBtn);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText,emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        let uiPogDbKey = await eventMaintanencePage.getColumnValues(emData.selecting_POG_PogDBKey_Table, emData.selecting_POG_PogDBKey_List);
        let uiPogDbKeySort =  uiPogDbKey.sort();
        await clickOn(selectPOGPage.gotoEventListButton);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await expect(await eventMaintanencePage.eventCreationText.getText()).toEqual(emData.Event_Creation);
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintanencePage.tableSecondPageBtn.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters);
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        let eventDescription = await createSFPEventPage.getEventDescText(description);
        await expect(eventDescription).toEqual(true);
        await clickOn(await eventMaintanencePage.eventListFirstRowPog);
        let popUpDbKeySort = await eventMaintanencePage.getColumnValues(emData.event_Schedule_PopUp_DBKey_Table, emData.event_Schedule_PopUp_DBKey_List);
        let pogStringStoreNum = await eventMaintanencePage.extractingDigit(emData.PogString, emData.PogString, 1);
        let pogStringEventNum = await eventMaintanencePage.extractingDigit(emData.PogString, emData.PogString, 0);   
        await expect(uiPogDbKeySort.sort()).toEqual(popUpDbKeySort.sort());
        await expect(popUpDbKeySort.length).toEqual(parseInt(pogStringStoreNum)); 
        await eventMaintanencePage.clickCloseButton('Close');
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        let eventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, description);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        await expect(popUpDbKeySort.length).toEqual(parseInt(pogStringStoreNum) );
        await expect(eventId).toEqual(pogStringEventNum);
        let dbEventId = await result.getResult(qrString.SfpEvent_Eventid.replace('eventID', eventId.toString()));
        await expect(eventId).toEqual(dbEventId.flat().toString());
        let arrString = popUpDbKeySort.join("', '");
        let dbData = await result.getResult(qrString.sfpEvent_DbKeyNum.replace('@dbKey', arrString).replace('@eventID', eventId));
        let mergedDbArrayString = await eventMaintanencePage.convertObjectToStringArray(dbData);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await expect(arrString).toEqual(mergedDbArrayString);
        await eventMaintanencePage.clickCloseButton('Close');
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await clickOn(eventMaintanencePage.eventListFirstRowSchedStr);
        let popUpSchedule = (await eventMaintanencePage.getColumnValues(emData.event_Schedule_Popup_Schedule_Table, emData.event_Schedule_Popup_Schedule_List)).sort();
        let scheduleDivString = await eventMaintanencePage.extractingDigit(emData.Sched_Store, emData.Schedul_Div, emData.Index_0);
        await expect(divNum).toEqual(await scheduleDivString);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickCloseButton('Close');
        await expect(popUpSchedule).toEqual(await dbRemodelStores);
        let scheduledStores = await eventMaintanencePage.getHeaderValue(emData.Header_ScheduledStr, description);
        await expect(popUpSchedule.length).toEqual(parseInt(scheduledStores));
        let arrStringsStore = popUpSchedule.join("', '");
        let dbDataStore = await result.getResult(qrString.sfpEvent_storeNum.replace('@store', arrStringsStore).replace('@eventID', eventId));
        let mergedDbArrayStringStore  = await eventMaintanencePage.convertObjectToStringArray(dbDataStore);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await expect(arrStringsStore).toEqual(await mergedDbArrayStringStore);
    })

    it('Validate CreateSFPEvent Remodel flow with selecting two time frames and 5 stores : compare UI stores to db after expansion of Event', async () => {
        let description =  await selectPOGPage.stringGenerator("EventSchedule");
        await createSFPEventPage.enterEventResetDescription(description);
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB);
        await createSFPEventPage.submitEventDetails();
        let nextPage = await eventMaintanencePage.linkSubModule.getText();
        await expect(nextPage).toEqual(emData.Event_Schedule);
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        for(let i=0; i<=1; i++){
            await eventMaintanencePage.waitBrowserToLoadPage(2000);
            await eventMaintanencePage.waitForPageLoad(eventSchedulingPage.saveScheduleBtn, emData.Save_Schedule);
            await clickOn(await storeSelectionPage.saveSchedule);
        }
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await clickOn(storeSelectionPage.saveGoToStore);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
        let storePage = await storeSelectionPage.linkSubModule.getText();
        await expect(storePage).toEqual(emData.Store_Selection);
        let divNum =  (emData.div_Atlanta_Operating_Division_011.match(/\d/g).join("")).toString();
        let dbRemodelStores = await (await result.getResult(qrString.remodelStores.replace(/@div/g, divNum).replace('@num','5'))).flat();
        await storeSelectionPage.clickCheckBoxText(await dbRemodelStores[0]);
        await storeSelectionPage.clickCheckBoxText(await dbRemodelStores[1]);
        await storeSelectionPage.clickCheckBoxText(await dbRemodelStores[2]);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await storeSelectionPage.clickSecondScheduleStoreCheckbox(await dbRemodelStores[3]);
        await storeSelectionPage.clickSecondScheduleStoreCheckbox(await dbRemodelStores[4]);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await clickOn(await storeSelectionPage.saveStoreSchedule);
        await eventMaintanencePage.waitBrowserToLoadPage(5000);
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, emData.Select_Pogs);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        let pogPage = await storeSelectionPage.linkSubModule.getText();
        await expect(pogPage).toEqual(emData.Select_Pogs);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText,emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.selectDropdown('Department', emData.selecting_POG_Department_15_DELI);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText,emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.selectDropdown('Sub-Department', emData.selecting_POG_Sub_Department);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText,emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.selectDropdown('Commodity *', emData.selecting_POG_Commodity);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText,emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.selectDropdown('Commodity Group', emData.selecting_POG_CommodityGroup);
        await clickOn(selectPOGPage.selectChoosePogByCommodityBtn)
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText,emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(5000);
        await clickOn(await selectPOGPage.gotoEventListButton)
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintanencePage.waitBrowserToLoadPage(7000);
        //await expect(await (await eventMaintanencePage.eventCreationText).getText()).toEqual(emData.Event_Creation);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters);
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        let eventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, description);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        let dbEventID = await result.getResult(qrString.SfpEvent_Eventid.replace('eventID', eventId.toString()));
        await expect(eventId).toEqual(dbEventID.flat().toString());
        await eventMaintanencePage.waitBrowserToLoadPage(7000);
        await clickOn(await eventMaintanencePage.eventListFirstRowSchedStr);
        let popUpSchedule = await eventMaintanencePage.getColumnValues(emData.event_Schedule_Popup_Schedule_Table, emData.event_Schedule_Popup_Schedule_List);
        await eventMaintanencePage.clickCloseButton('Close');
        await expect(popUpSchedule.sort()).toEqual(dbRemodelStores.sort());
        let arrStringsStore = popUpSchedule.join("', '");
        let dbDataStore = await result.getResult(qrString.sfpEvent_storeNum.replace('@store', arrStringsStore).replace('@eventID', eventId));
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await expect(popUpSchedule.sort()).toEqual(await dbDataStore.flat().sort() );
        await eventMaintanencePage.expandEvent(eventId);
        let columnList = await eventMaintanencePage.getValuesOfClickedNum(emData.plus_Stores_header);
        await expect(await (await popUpSchedule).sort()).toEqual(await (await columnList.flat()).sort());
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await expect(columnList.flat().sort()).toEqual(dbDataStore.flat().sort());
    })

})
