const EventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const CreateSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const {getFilesCount,getPDFText} = require("../../../util/pdfReader");
const CreateEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const createSfpEventPage = require("../EventManagerPO/createSFPEvent.page");

describe('validatePOGDBKeyPDF: validate POG DBKey in PDF in Selecting Pog/commodities page', () => {

    it('Should validate POG DBKey in PDF', async () => {

        await createSfpEventPage.addEventDetails1('','SFP Event', emData.div_Cincinnati_Operating_Division_014, emData.eventType_KOMPASS, "Update", "npeEvent", emData.vendor_AZB)
        await CreateSFPEventPage.submitEventDetails()
        let nextPage = await EventMaintanencePage.linkSubModule.getText()
        await expect(nextPage).toEqual(emData.Event_Schedule)
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        await CreateEventSchedulePage.selectDropdown('Fiscal Week');
        await clickOn(storeSelectionPage.saveSchedule)
        await EventMaintanencePage.waitBrowserToLoadPage(2000)
        let weekText = await storeSelectionPage.storeWeekSchedule.getText()
        await EventMaintanencePage.waitBrowserToLoadPage(2000)
        await clickOn(storeSelectionPage.saveGoToStore)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Store Selection")
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(storePage).toEqual(emData.Store_Selection)
        await storeSelectionPage.clickCheckBoxText(emData.Store_351)
        await clickOn(storeSelectionPage.saveStoreSchedule)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Select Pogs")
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(pogPage).toEqual(emData.Select_Pogs)
        let initialFilesCount=getFilesCount()
        let parentGUID = browser.getWindowHandle()
        await selectPOG.waitForPageLoad(selectPOG.selectText,'Department')
        await selectPOG.selectDropdown('Department', emData.dept_01_GROCERY)
        await selectPOG.waitForPageLoad(selectPOG.selectText,'Department')
        await selectPOG.selectDropdown('Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER)
        await selectPOG.waitForPageLoad(selectPOG.selectText,'Department')
        await selectPOG.selectDropdown('Commodity *', emData.commodity_01_GROCERY_01_GROC_ALL_OTHER_013_SPICES_EXTRACTS)
        await selectPOG.waitForPageLoad(selectPOG.selectText,'Department')
        await selectPOG.selectDropdown('Commodity Group',emData.CommodityGroup_013_SPICES_EXTRACTS_013_SPICES_EXTRACTS)
        await clickOn(selectPOG.selectChoosePogByCommodityBtn)
        await selectPOG.waitForPageLoad(selectPOG.selectText,'Department')
        let pogDbKeyUi=await EventMaintanencePage.getLinkText("POG DBKey")
        await EventMaintanencePage.getLinkText("PDF")
        await EventMaintanencePage.waitBrowserToLoadPage(10000)
        await EventMaintanencePage.validateNewTab(parentGUID)
        let finalFilesCount=getFilesCount()
        await EventMaintanencePage.waitBrowserToLoadPage(5000)
        await expect(finalFilesCount-initialFilesCount).toEqual(1)
        let data = await getPDFText();
        let pogDbKeyPdf=data.split('DBKey:')[1].substr(1,7)
        await expect(pogDbKeyPdf).toEqual(pogDbKeyUi)
    })

})
