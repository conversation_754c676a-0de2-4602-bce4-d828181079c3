const interimShelfStrip = require('../../EventManagerPO/interimShelfStrip.page');
const {assert} = require('chai');
const eventMaintanencePage = require('../../EventManagerPO/eventMaintanence.page');
const customStoreGroup = require('../../EventManagerPO/customStoreGroup.page');


let dynamicDbKeyGenerator;
    describe('createInterimShelfStrip: Validate creating interim shelf strip with Pog DbKey value ' , async () => {

    it('Validate: 1)"Shelf Strip Request" dropdown sub-value exist under Admin; 2) entering incorrect data type into Pog Dbkey inputbox throws a warning message; 3) unclick slide out pannel ', async () => {

        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        const adminOtionList = await interimShelfStrip.getAdminDropdownSubMenue();
        const shelfStripOptionExists = await interimShelfStrip.isValueInArray(adminOtionList, 'Shelf Strip Requests');
        await expect(shelfStripOptionExists).toEqual(true);
        await interimShelfStrip.waitBrowserToLoadPage(1000);
        await eventMaintanencePage.open('EventMaintenance/shelfStripRequests');
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000, timeoutMsg: "Error: Table contents for event maintenance page did not load before timeout" });
        await interimShelfStrip.waitBrowserToLoadPage(2000);
        await interimShelfStrip.clickCreateNewRequest();
        let stringDbKeyGenerator = await interimShelfStrip.stringGenerator('InvalidString');
        await interimShelfStrip.enterSearchInput(await stringDbKeyGenerator);
        await interimShelfStrip.waitBrowserToLoadPage(1000);
        await interimShelfStrip.clickOn(await interimShelfStrip.submitButton);
        let shelfStripSuccessMessage = await (await interimShelfStrip.invalidPogDbKeyWarningMessage).getText();
        await interimShelfStrip.waitBrowserToLoadPage(1000);
        const inputBoxIsCleared = await interimShelfStrip.clearInputAndValidate();
        await expect(inputBoxIsCleared).toEqual(true);
        (await interimShelfStrip.closeSlideOutPannelButton).click();
        let buttonExist = await interimShelfStrip.elementExists(await interimShelfStrip.submitButton);
        await expect(buttonExist).toEqual(false);
})


it('Validate:  1) create interim shelf strip with pog DbKey; 2) newly created shelf strip exits on the table ', async () => {

    await interimShelfStrip.waitBrowserToLoadPage(2000);
    await interimShelfStrip.clickCreateNewRequest();
    dynamicDbKeyGenerator = await interimShelfStrip.generateRandomNumber(7);
    await interimShelfStrip.enterSearchInput(await dynamicDbKeyGenerator.toString());
    await interimShelfStrip.waitBrowserToLoadPage(1000);
    await interimShelfStrip.clickOn(await interimShelfStrip.submitButton);
    let shelfStripSuccessMessage = await (await interimShelfStrip.stripRequestSubmittedMessage).getText();
    await interimShelfStrip.waitBrowserToLoadPage(1000);
    if( await shelfStripSuccessMessage === 'Strip Request Submitted'){
        let topRowPodDbKey = await customStoreGroup.getColumnTopElement('POG DBKey');
        await expect(await dynamicDbKeyGenerator.toString()).toEqual(await topRowPodDbKey);
        await interimShelfStrip.waitBrowserToLoadPage(3000);
        await browser.deleteSession();
    } else{
        assert.fail(0,1,'Shelf Strip Request failed for uknown reason !')
    }
})

})