const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const qrString = require('../../../SQLConnection/queryStringEM');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const {expect } = require('chai')
const result = require("../../../SQLConnection/server.js.ts")

describe('validateAddEventDetailNPENII: validate the dropdown for npe Event for NII', () => {

    it('validate the npe event for select type NII', async () => {
        let eventDescription = await createSfpEventPage.addEventDetails1('',await 'SFP Event', emData.div_Cincinnati_Operating_Division_014, emData.eventType_KOMPASS, "New Item Introduction (NII)", "npeEventNII", emData.vendor_AZB)
        await createSfpEventPage.submitEventDetails()
        await createEventSchedulePage.scheduling(1)
        await storeSelectionPage.saveGoToStoreSelection()
        let div = (emData.div_Cincinnati_Operating_Division_014.match(/\d/g).join("")).toString()
        let dbScheduledStores = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, div).replace('@num', '30'))).flat();
        await storeSelectionPage.storeSelection(dbScheduledStores, 5, 1)
        await storeSelectionPage.storeSelection(dbScheduledStores, 12, 2)
        await storeSelectionPage.saveStoreScheduleButton()
        await selectPOG.selectingPogCommodities('Department', emData.dept_01_GROCERY, 'Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER, 'Commodity *', emData.ResetComplete_All, '')
        await eventMaintanencePage.clickOn(selectPOG.selectChoosePogByCommodityBtn);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000, timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await eventMaintanencePage.clickOn(selectPOG.gotoEventListButton);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await expect(await eventMaintanencePage.eventCreationText.getText()).to.equal(emData.Event_Creation);
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000, timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({timeout: 90000, timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await eventMaintanencePage.clickFiltersWithDiscp(eventDescription.toString())
        let eventDisp = await eventMaintanencePage.getHeaderValue(emData.Filters_Event_Desc, eventDescription)
        let scheduleType = await eventMaintanencePage.getHeaderValue(await emData.Header_scheduleType, eventDisp)
        await expect(scheduleType).to.equal(emData.eventType_KOMPASS)
        let type = await eventMaintanencePage.getHeaderValue(emData.Header_Type, eventDisp)
        await expect(type).to.equal(emData.SlideOuttype_NII)
        let pogType = await eventMaintanencePage.getHeaderValue(emData.Header_Pog, eventDisp)
        let pogStringStoreNum = await eventMaintanencePage.extractingDigit(emData.PogString, emData.PogString, 1);
        await expect(pogType).to.equal(pogStringStoreNum)
        await eventMaintanencePage.clickCloseButton('Close');
        let scheduledStrCount = await eventMaintanencePage.getHeaderValue(emData.Header_ScheduledStr, eventDescription)
        let popUpSchedule = (await eventMaintanencePage.getColumnValues(await emData.event_Schedule_Popup_Schedule_Table, emData.event_Schedule_Popup_Schedule_List)).sort();
        await expect(popUpSchedule.length.toString()).to.equal(scheduledStrCount)
    })
})
