// Delete before merge

// const resetCompletionPage = require('../EventManagerPO/resetCompletion.page.js')
// let emData = require('../../../Testdata/EventManagerdata/eventManagerUIdata.json')
// const storeSelectionPage = require('../EventManagerPO/storeSelection.page.js');
// const autoActivationReport = require('../EventManagerPO/autoActivationReport.page.js')
// const Result = require("../../../SQLConnection/server.js.ts");
// const qrString = require("../../../SQLConnection/queryStringEM");
// const { assert } = require('chai');
// const {clickOn} = require("../../../GlobalObjectRepository/login.page");
// const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page')
//
// describe('validateDeleteAndCancelFunctionOnResetCompletion: validate delete and cancel functions on UI, then validate on db', () => {
//
//     it('validateDeleteSingleRow: select one single row and delete it from UI table, then validate deletion both on UI and db', async () => {
//         await browser.pause(10000)
//         await resetCompletionPage.open('EventMaintanence/resetCompletion');
//         expect(await resetCompletionPage.linkSubModule.getText()).toHaveTextContaining(emData.Admin_reset_completion);
//         await clickOn(await resetCompletionPage.clearFiltersBtn);
//         await resetCompletionPage.selectSingleValue(emData.selectedDivision_ResetCompletion, emData.division_Cincinnati_Operating_Division_014);
//         await autoActivationReport.selectYPW(emData.Period_Input, emData.Period_Empty)
//         await resetCompletionPage.selectSingleValue(emData.selectedCompletionType_ResetCompletion, emData.ResetComplete_NoResponse);
//         await storeSelectionPage.clickOn(resetCompletionPage.storeReview)
//         await storeSelectionPage.clickOn(resetCompletionPage.searchBtn)
//         await browser.pause(10000)
//         let initialFirstRowList =  await resetCompletionPage.getFirstRowValue();
//         await clickOn(await resetCompletionPage.storeReviewCheckBoxOne);
//         await storeSelectionPage.waitForPageLoad(resetCompletionPage.storeReviewDeleteBtn, emData.StoreReview_Delete);
//         await resetCompletionPage.clickDeleteBtn();
//         await storeSelectionPage.waitForPageLoad(resetCompletionPage.searchBtn, emData.ResetComplete_Search);
//         let finalFirstRowList =  await resetCompletionPage.getFirstRowValue();
//         await assert.notEqual(initialFirstRowList, finalFirstRowList,'Error: test is failing - table row might not be deleted' );
//         let divisionValue = initialFirstRowList.splice(emData.Number_0,emData.Number_1).toString();
//         let storeValue = initialFirstRowList.slice(emData.Number_0,emData.Number_1).toString();
//         let ssnValue = initialFirstRowList.slice(emData.Number_7,emData.Number_8).toString();
//         let dbDataStoreReview = await Result.getResult(qrString.resetCompletionStoreReviewSingleValueDelete.replace('@div', divisionValue).replace('@store', storeValue).replace('@ssn', ssnValue));
//         let dbArrayOfArray = Object.keys(dbDataStoreReview).map(function (key) {
//             return dbDataStoreReview[key]
//         });
//         let dbArray =  dbArrayOfArray.flat();
//         let finalValue = await resetCompletionPage.arrayHasExpectedValue(dbArray, emData.ResetComplete_Deleted);
//         await expect(finalValue).toEqual(emData.ResetComplete_Deleted);
//     })
//
//     it.skip('validateDeleteMultipleRows: select multiple rows and delete them from UI table, then validate deletion on db', async () => {
//        //  This test is skipped to avoid from not having enough data on UI to test Delete validation; refer to above test for single row deletion
//         await resetCompletionPage.open('EventMaintanence/resetCompletion');
//         expect(await resetCompletionPage.linkSubModule.getText()).toHaveTextContaining(emData.Admin_reset_completion)
//         await eventMaintanencePage.waitBrowserToLoadPage(3000)
//         await clickOn(await resetCompletionPage.clearFiltersBtn);
//         await resetCompletionPage.selectSingleValue(emData.selectedDivision_ResetCompletion, emData.division_Cincinnati_Operating_Division_014)
//         await autoActivationReport.selectYPW(emData.Period_Input, emData.Period_Empty)
//         await resetCompletionPage.selectSingleValue(emData.selectedCompletionType_ResetCompletion, emData.ResetComplete_NoResponse);
//         await storeSelectionPage.clickOn(resetCompletionPage.storeReview);
//         await storeSelectionPage.clickOn(resetCompletionPage.searchBtn);
//         let initialStoreReviewUiData = await resetCompletionPage.getMultipleRowsUi(emData.Number_2);
//         let divColumnList = await resetCompletionPage.getColumnArrayList(emData.ResetComplete_StoreReview_Div,emData.Number_2);
//         let divString =  divColumnList.join("', '");
//         let locColumnList = await resetCompletionPage.getColumnArrayList(emData.ResetComplete_StoreReview_Loc,emData.Number_2);
//         let locString = locColumnList.join("', '");
//         let ssnColumnList = await resetCompletionPage.getColumnArrayList(emData.ResetComplete_SpaceStationName,emData.Number_2);
//         let ssnString =  ssnColumnList.join("', '");
//         //selects first two rows
//         await resetCompletionPage.clickCheckBoxes(emData.Number_3);
//         await resetCompletionPage.clickDeleteBtn();
//         await storeSelectionPage.waitForPageLoad(resetCompletionPage.searchBtn, emData.ResetComplete_Search);
//         let finalStoreReviewUiData = await resetCompletionPage.getMultipleRowsUi(emData.Number_2);
//         //make sure multi-values can be comparable
//         await assert.notEqual(initialStoreReviewUiData,finalStoreReviewUiData,'Error: rows have not deleted');
//         let dbDataStoreReview = await Result.getResult(qrString.resetCompletionStoreReviewMultipleValuesDelete.replace(`@div`, divString).replace(`@loc`, locString).replace(`@ssn`, ssnString));
//         // flattening array of array to array
//         let dbArray =  dbDataStoreReview.flat();
//         //count number of input values from db result => 'Deleted'
//         let countDeleted = dbArray.filter(x => x === emData.ResetComplete_Deleted).length;
//         await expect(countDeleted).toEqual(emData.Number_2)
//     })
//
//     it('validateSelectSingleAndMultipleCancelButton: validate select single and multiple Cancel checkboxes, then unselects them', async () => {
//         // Accessibility of account has been changed again; test account is working fine here.
//         // await eventMaintanencePage.waitBrowserToLoadPage(1000)
//         // await logoutPage.logout()
//         // await loginPage.login(envVariables.storeUserEM[ENV], envVariables.storePasswordEM[ENV]);
//         // await expect(browser).toHaveUrlContaining(envVariables.url[ENV]);
//         await eventMaintanencePage.waitBrowserToLoadPage(10000)
//         // await expect(browser).toHaveTitle(data.homeTitle);
//         await resetCompletionPage.open('EventMaintanence/resetCompletion');
//         expect(await resetCompletionPage.linkSubModule.getText()).toHaveTextContaining(emData.Admin_reset_completion)
//         // await storeSelectionPage.waitForPageLoad(resetCompletionPage.clearFiltersBtn, emData.ResetComplete_Clear_Filters);
//         await clickOn(await resetCompletionPage.clearFiltersBtn);
//         await resetCompletionPage.selectSingleValue(emData.selectedDivision_ResetCompletion, emData.division_Cincinnati_Operating_Division_014)
//         // No data left to delete from 2021
//         // await autoActivationReport.selectYPW(emData.Year_Input, emData.Year_2021)
//         await autoActivationReport.selectYPW(emData.Period_Input, emData.Period_Empty)
//         await storeSelectionPage.clickOn(resetCompletionPage.storeReview);
//         await storeSelectionPage.clickOn(resetCompletionPage.searchBtn);
//         await resetCompletionPage.clickCheckBoxes(emData.Number_4);
//         await storeSelectionPage.waitForPageLoad(resetCompletionPage.searchBtn, emData.ResetComplete_Search);
//         //validate checkboxes are selected
//         let  singleCheckBoxSelected = await resetCompletionPage.validateCheckBoxesSelected(emData.Number_4);
//         await expect(singleCheckBoxSelected).toEqual(true);
//         await storeSelectionPage.clickOn(resetCompletionPage.storeReviewCancelBtn);
//         let  singleCheckBoxUnSelected = await resetCompletionPage.validateCheckBoxesSelected(emData.Number_4);
//         await expect(singleCheckBoxUnSelected).toEqual(false);
//         await resetCompletionPage.clickCheckBoxes(emData.Number_3);
//         let multipleCheckBoxesSelected = await resetCompletionPage.validateCheckBoxesSelected(emData.Number_2);
//         await expect(multipleCheckBoxesSelected).toEqual(true);
//         await storeSelectionPage.clickOn(resetCompletionPage.storeReviewCancelBtn);
//         let multipleCheckBoxesUnSelected = await resetCompletionPage.validateCheckBoxesSelected(emData.Number_2);
//         await expect(multipleCheckBoxesUnSelected).toEqual(false);
//     })
//
// })
