import { getOnlyDay } from "../../../util/date"
const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const eventSchedule = require('../EventManagerPO/eventScheduling.page')
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const selectPOGPage = require("../EventManagerPO/selectingPogCommodities.page");
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')

describe('validateCalenderInEventScheduling: Validate the calender in Event Scheduling', () => {

    it('Should Validate the calender in Event Scheduling', async () => {
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.tableSecondPageBtn.waitForExist({ timeout: 90000, timeoutMsg: 'Error: Table contents for event maintenance page didn`t load before timeout' })
        await eventMaintanencePage.selectEventType('SFP Event')
        expect(await eventMaintanencePage.linkCreateSFPEvent.getText()).toHaveTextContaining(' Create SFP Event ')
        await createSfpEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014)
        await createSfpEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        await createSfpEventPage.selectDropdown('selectType', "Update");
        let description = await selectPOG.stringGenerator("editScheduleWeekMerge")
        await createSfpEventPage.enterEventResetDescription(description)
        await createSfpEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSfpEventPage.clickCheckBox(emData.checkBox_RequestShelfStrips)
        await createSfpEventPage.selectRadioButton(emData.radioBtn_Office)
        await createSfpEventPage.selectRadioButton(emData.radioBtn_MaintenanceStripsChangesOnly)
        await createSfpEventPage.clickCheckBox(emData.checkBox_RequestTags)
        await createSfpEventPage.selectRadioButton(emData.radioBtn_Store)
        await createSfpEventPage.enterValue(emData.field_userHeading, await createSfpEventPage.stringGenerator("TestAutomationSet"))
        await createSfpEventPage.enterValue(emData.field_sets, 5)
        await createSfpEventPage.clickCheckBox(emData.checkBox_NewItemsOnly)
        await createSfpEventPage.enterUserComments(await createSfpEventPage.stringGenerator("TestAutomationComments "))
        await createSfpEventPage.clickCheckBox(emData.checkBox_ActivateCAO2weeksbeforeEventSchedule)
        await createSfpEventPage.submitEventDetails()
        let nextPage = await eventMaintanencePage.linkSubModule.getText()
        await expect(nextPage).toEqual('Event Schedule')
        await createEventSchedulePage.selectFiscalWeekDropdown('Fiscal Week');
        await createEventSchedulePage.selectDropdown('Process Weekday', emData.processWeekDay_Tuesday)
        await createEventSchedulePage.datePicker(getOnlyDay())
        await createEventSchedulePage.clickSaveScheduleBtn()
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await createEventSchedulePage.clickSaveScheduleBtn()
        await createEventSchedulePage.waitBrowserToLoadPage(3000);
        let refD = await createEventSchedulePage.getHeaderValue("Retail Effective Date")
        await refD[1].toString()
        let processDay = await createEventSchedulePage.getHeaderValue("Process day of Week")
        await processDay[1].toString()
        let formattedDate = `${processDay[1]}, ${refD[1]}`;
        let week = await eventSchedule.getScheduledWeek()
        await storeSelectionPage.saveGoToStoreSelection()
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, emData.Store_Selection);
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(storePage).toEqual(emData.Store_Selection)
        let divNum14 = (emData.div_Cincinnati_Operating_Division_014.match(/\d/g).join("")).toString();
        let dbScheduledStores = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, divNum14).replace('@num', '30'))).flat();
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await storeSelectionPage.clickScheduleStoreCheckbox(await dbScheduledStores[5], 1);
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.clickScheduleStoreCheckbox(await dbScheduledStores[12], 2);
        await eventMaintanencePage.clickOn(storeSelectionPage.saveStoreSchedule)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Select Pogs")
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(pogPage).toEqual(emData.Select_Pogs)
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department)
        await eventMaintanencePage.clickOn(storeSelectionPage.goToScheduleSelectionPage);
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await eventMaintanencePage.clickOn(eventSchedule.editScheduleList)
        await createEventSchedulePage.selectFiscalWeekDropdown('Fiscal Week In Modal', "scheduleEdit");
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        let alertWeekText = await (await createEventSchedulePage.fiscalWeekAlert).getText();
        await expect(alertWeekText).toEqual(emData.fiscalWeekAlert)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await createEventSchedulePage.selectDropdown('Process Weekday And RetailEffective In Modal', await formattedDate);
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await eventMaintanencePage.clickCloseButton('Update');
        let editedWeek = await eventSchedule.getScheduledWeek()
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await expect(week).not.toEqual(editedWeek)
        await expect(await editedWeek.toString()).toContain('(Edited)');
        let Stores = await createEventSchedulePage.getHeaderValue("Stores")
        await expect(Stores.toString()).toEqual("2")
        await storeSelectionPage.saveGoToStoreSelection()
        await storeSelectionPage.saveStoreScheduleButton()
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectingPogCommodities('Department', emData.dept_01_GROCERY, 'Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER, 'Commodity *', emData.ResetComplete_All, '')
        await eventMaintanencePage.clickOn(selectPOGPage.selectChoosePogByCommodityBtn);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 9000, timeoutMsg: 'Error: Table contents for event maintenance page didn`t load before timeout' })
        await (await selectPOG.gotoEventListButton).waitForExist({ timeout: 9000, timeoutMsg: 'Error: EventList button not visible' })
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await eventMaintanencePage.clickOn(selectPOGPage.gotoEventListButton);
        await expect(await eventMaintanencePage.eventCreationText.getText()).toEqual(emData.Event_Creation);
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters);
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000, timeoutMsg: 'Error: Table contents for event maintenance page didn`t load before timeout' })
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())

    })
})
