const ResetCompletionPage = require("../EventManagerPO/resetCompletion.page.js");
const emData = require("../../../TestData/EventManagerData/eventManagerUIData.json");
const AutoActivationReport = require("../EventManagerPO/autoActivationReport.page.js");
const storeSelectionPage = require("../EventManagerPO/storeSelection.page.js");
const Result = require("../../../SQLConnection/server.js.ts");
const qrString = require("../../../SQLConnection/queryStringEM");
const assert = require("assert");

let div = emData.division_Cincinnati_Operating_Division_014.split('- ')[emData.index_0];
let year = emData.Year_2022;

describe('validateResetCompleteDropdownDeleteOption: validate DELETE dropdown option in resetComplete Filter both on StoreReview UI and DB', () => {
    it('validateDropdownDeleteOption: validate when DELETE dropdown option is selected, compare StoreReview UI to db  ', async () => {
        await ResetCompletionPage.waitBrowserToLoadPage(6000)
        await ResetCompletionPage.open('EventMaintanence/resetCompletion');
        await ResetCompletionPage.waitForPageLoad(await ResetCompletionPage.linkSubModule, emData.Admin_reset_completion);
        await ResetCompletionPage.waitForPageLoad(ResetCompletionPage.searchBtn, emData.resetCompletion_Search);
        await ResetCompletionPage.selectSingleValue(emData.selectedCompletionType_ResetCompletion, emData.ResetComplete_All)
        await ResetCompletionPage.selectSingleValue(emData.selectedDivision_ResetCompletion, emData.division_Cincinnati_Operating_Division_014);
        await AutoActivationReport.selectYPW(emData.Year_Input, emData.Year_2022);
        await AutoActivationReport.selectYPW(emData.Period_Input, emData.Period_Empty);
        await ResetCompletionPage.selectSingleValue(emData.selectedCompletionType_ResetCompletion, emData.resetComplete_Deleted);
        await storeSelectionPage.clickOn(ResetCompletionPage.searchBtn);
        await storeSelectionPage.clickOn(ResetCompletionPage.storeReview);
        let resetCompleteDeletedTxt = await ResetCompletionPage.selectResetComplete.getText();
        await expect(resetCompleteDeletedTxt).toEqual(emData.resetComplete_Deleted);
        //  select page number 10
        await ResetCompletionPage.getPaginationPageNumber(emData.PaginationPageNumber_10);
        let uiStoreReview = await ResetCompletionPage.getStoreReviewAllUIData();
        let deletedColumnArray = await ResetCompletionPage.extractArrayFromArrays(uiStoreReview, emData.index_12);
        let uniqueValueDeleted = await ResetCompletionPage.getUniqueArrayValue(deletedColumnArray);
        await expect(resetCompleteDeletedTxt).toEqual(uniqueValueDeleted.toString());
        // result is js object
        let dbData = await Result.getResult(qrString.resetComplete_Delete_StoreReview.replace('@div', div).replace('@year',year));
        // converts array of arrays into array
        let flattenArrayDb = await dbData.flat();
        let uniqueValueDb = await ResetCompletionPage.getUniqueArrayValue(flattenArrayDb);
        await assert.deepStrictEqual(uniqueValueDeleted,uniqueValueDb,'Arrays have unmatched values');
    })

})
