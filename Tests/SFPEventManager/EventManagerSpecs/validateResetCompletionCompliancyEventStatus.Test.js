const resetCompletionPage = require("../EventManagerPO/resetCompletion.page.js");
const emData = require("../../../TestData/EventManagerData/eventManagerUIData.json");
const autoActivationReport = require("../EventManagerPO/autoActivationReport.page.js");
const storeSelectionPage = require("../EventManagerPO/storeSelection.page.js");
const result = require("../../../SQLConnection/server.js.ts");
const qrString = require("../../../SQLConnection/queryStringEM");
const resetCompleteApiData = require('../../../Testdata/APIData/resetCompleteAPIData.json');
const postResetCompletionData = require('../../../util/requestAPICall.js')
const envConfig = require('../../../envConfig.js');
const { assert } = require('chai');

beforeEach(async () => {
    let resetCompletionResponseOpen = await postResetCompletionData.putData(
            resetCompleteApiData.recId,
            resetCompleteApiData.statusOpen,
            resetCompleteApiData.user,
            envConfig.url.stage,
            resetCompleteApiData.resetCompletionStageUrl,
            envConfig.resetCompletionUser.stage,
            envConfig.resetCompletionPassword.stage
        )
        await assert.notEqual(resetCompletionResponseOpen.data, null, "Error: The response returned was empty");
        await assert.equal(resetCompletionResponseOpen.status, 200, 'Error: Response status is not 200, call failed');
})
describe('validateResetCompletionCompliancyEventStatus: Validate data in UI and once locked, compliancy event should not be available', () => {
   
    it.skip('Validate data in UI and once locked, compliancy event should not be available', async () => {
        await resetCompletionPage.open('EventMaintanence/resetCompletion');
        expect(await resetCompletionPage.linkSubModule.getText()).toHaveTextContaining(emData.Admin_reset_completion);
        let dbData = await (await result.getResult(qrString.noResponseStore)).flat()
        let division = dbData[0]
        let location = dbData[1]
        await resetCompletionPage.clearFiltersBtn.click();
        await resetCompletionPage.setSingleValue('selectedDivision', division);
        await resetCompletionPage.setSingleValue('selectedLocation', location);
        await autoActivationReport.selectYPW(emData.Period_Input, emData.Period_Empty);
        await resetCompletionPage.selectSingleValue(emData.selectedCompletionType_ResetCompletion, emData.resetComplete_NoResponse);
        await (await (resetCompletionPage.searchBtn)).waitForClickable({ timeout: 60000 });
        await storeSelectionPage.clickOn(resetCompletionPage.searchBtn);
        await storeSelectionPage.waitBrowserToLoadPage(6000);
        await storeSelectionPage.clickOn(resetCompletionPage.storeReview);
        await storeSelectionPage.waitBrowserToLoadPage(6000);
        await storeSelectionPage.clickOn(resetCompletionPage.searchBtn);
        let resetCompleteNoResponseTxt = await resetCompletionPage.selectResetComplete.getText();
        // validating 'No Response' option is select from ResetComplete Dropdown
        await expect(resetCompleteNoResponseTxt).toEqual(emData.resetComplete_NoResponse);
        let uiStoreReviewNoResponse = await resetCompletionPage.getStoreReviewAllUIData();
        let noResponseColumnArray = await resetCompletionPage.extractArrayFromArrays(uiStoreReviewNoResponse, emData.index_11)
        let uniqueValueNoResponse = await resetCompletionPage.getUniqueArrayValue(noResponseColumnArray);
        await expect(uniqueValueNoResponse.toString()).toEqual(emData.resetComplete_NoResponse);
        let ssn = await resetCompletionPage.getHeaderStoreReview("Space Station Name")
        // result is js object
        let recId = await (await result.getResult(qrString.recordID.replace('@div', division).replace('@loc', location))).flat()
        await assert.equal(ssn,recId[3])
        let resetCompletionResponseLocked = await postResetCompletionData.putData(recId[0],
            resetCompleteApiData.statusLocked,
            resetCompleteApiData.user,
            envConfig.url.stage,
            resetCompleteApiData.resetCompletionStageUrl,
            envConfig.resetCompletionUser.stage,
            envConfig.resetCompletionPassword.stage
        )
        await assert.notEqual(resetCompletionResponseLocked.data, null, "Error: The response returned was empty");
        await assert.equal(resetCompletionResponseLocked.status, 200, 'Error: Response status is not 200, call failed');
        let statusLocked = await (await result.getResult(qrString.recordID.replace('@div', division).replace('@loc', location))).flat()
        await assert.equal(statusLocked[6],'LK')
        await resetCompletionPage.open('EventMaintanence/resetCompletion');
        expect(await resetCompletionPage.linkSubModule.getText()).toHaveTextContaining(emData.Admin_reset_completion);
        await resetCompletionPage.clearFiltersBtn.click()
        await resetCompletionPage.setSingleValue('selectedDivision', division);
        await resetCompletionPage.setSingleValue('selectedLocation', location);
        await autoActivationReport.selectYPW(emData.Period_Input, emData.Period_Empty)
        await (await (resetCompletionPage.searchBtn)).waitForClickable({ timeout: 6000 })
        await storeSelectionPage.clickOn(resetCompletionPage.searchBtn);
        let noRecord = await (await resetCompletionPage.zeroRecordsFoundText).getText()
        await expect(noRecord).toEqual("Zero Records Found")
    })

    it.skip('Validate data in UI and once Completed, compliancy event should be available with status DN', async () => {
        await resetCompletionPage.open('EventMaintanence/resetCompletion');
        expect(await resetCompletionPage.linkSubModule.getText()).toHaveTextContaining(emData.Admin_reset_completion);
        let dbData = await (await result.getResult(qrString.noResponseStore)).flat()
        let division = dbData[0]
        let location = dbData[1]
        await resetCompletionPage.clearFiltersBtn.click();
        await resetCompletionPage.setSingleValue('selectedDivision', division);
        await resetCompletionPage.setSingleValue('selectedLocation', location);
        await autoActivationReport.selectYPW(emData.Period_Input, emData.Period_Empty);
        await resetCompletionPage.selectSingleValue(emData.selectedCompletionType_ResetCompletion, emData.resetComplete_NoResponse);
        await (await (resetCompletionPage.searchBtn)).waitForClickable({ timeout: 6000 });
        await storeSelectionPage.clickOn(resetCompletionPage.searchBtn);
        await storeSelectionPage.waitBrowserToLoadPage(6000);
        await storeSelectionPage.clickOn(resetCompletionPage.storeReview);
        let resetCompleteNoResponseTxt = await resetCompletionPage.selectResetComplete.getText();
        // validating 'No Response' option is select from ResetComplete Dropdown
        await expect(resetCompleteNoResponseTxt).toEqual(emData.resetComplete_NoResponse);
        let uiStoreReviewNoResponse = await resetCompletionPage.getStoreReviewAllUIData();
        let noResponseColumnArray = await resetCompletionPage.extractArrayFromArrays(uiStoreReviewNoResponse, emData.index_11);
        let uniqueValueNoResponse = await resetCompletionPage.getUniqueArrayValue(noResponseColumnArray);
        await expect(uniqueValueNoResponse.toString()).toEqual(emData.resetComplete_NoResponse);
        let ssn = await resetCompletionPage.getHeaderStoreReview("Space Station Name")
        // result is js object
        let recId = await (await result.getResult(qrString.recordID.replace('@div', division).replace('@loc', location))).flat()
        await assert.equal(ssn,recId[3])
        let resetComplitionResponseCompleted = await postResetCompletionData.putData(
            recId[0],
            resetCompleteApiData.statusCompleted,
            resetCompleteApiData.user,
            envConfig.url.stage,
            resetCompleteApiData.resetCompletionStageUrl,
            envConfig.resetCompletionUser.stage,
            envConfig.resetCompletionPassword.stage
        )
        await assert.notEqual(resetComplitionResponseCompleted.data, null, "Error: The response returned was empty");
        await assert.equal(resetComplitionResponseCompleted.status, 200, 'Error: Response status is not 200, call failed');
        let statusLocked = await (await result.getResult(qrString.recordID.replace('@div', division).replace('@loc', location))).flat()
        await assert.equal(statusLocked[6],'DN')
        await resetCompletionPage.open('EventMaintanence/resetCompletion');
        expect(await resetCompletionPage.linkSubModule.getText()).toHaveTextContaining(emData.Admin_reset_completion);
        await resetCompletionPage.clearFiltersBtn.click()
        await resetCompletionPage.setSingleValue('selectedDivision', division);
        await resetCompletionPage.setSingleValue('selectedLocation', location);
        await autoActivationReport.selectYPW(emData.Period_Input, emData.Period_Empty)
        await (await (resetCompletionPage.searchBtn)).waitForClickable({ timeout: 6000 })
        await resetCompletionPage.selectSingleValue(emData.selectedCompletionType_ResetCompletion, emData.ResetComplete_Yes)
        await storeSelectionPage.clickOn(resetCompletionPage.searchBtn);
        await storeSelectionPage.waitBrowserToLoadPage(6000);
        await storeSelectionPage.clickOn(resetCompletionPage.storeReview);
        let resetCompleteYesTxt = await resetCompletionPage.selectResetComplete.getText();
        // validating 'Yes' option is select from ResetComplete Dropdown
        await expect(resetCompleteYesTxt).toEqual(emData.resetComplete_Yes);
        let uiStoreReviewYes = await resetCompletionPage.getStoreReviewAllUIData();
        //[ 'Yes', 'Yes', 'Yes' ] capture all Yes values in an array
        let yesColumnArray = await resetCompletionPage.extractArrayFromArrays(uiStoreReviewYes, emData.index_11);
        //[ 'Yes' ] - capture unique value from above array
        let uniqueValueYes = await resetCompletionPage.getUniqueArrayValue(yesColumnArray);
        //validating 'Yes' values on StoreReview table
        await expect(resetCompleteYesTxt).toEqual(uniqueValueYes.toString());
    })
})