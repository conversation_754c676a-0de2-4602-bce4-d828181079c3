const resetCompletionPage = require('../EventManagerPO/resetCompletion.page.js')
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page.js');
const AutoActivationReport = require('../EventManagerPO/autoActivationReport.page.js')
const excelReader = require("../../../util/excelReader.js");
const { clickOn } = require('../../../GlobalObjectRepository/login.page');
const qrString = require("../../../SQLConnection/queryStringEM");
const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page')
const { DBNames } = require('../../../envConfig.js');


describe('validateCommodityRecapExcelDbData : validate Complete,Not complete, ' +
    'No-response and Deleted column data', () => {
        beforeEach(async () => {
            await eventMaintanencePage.waitBrowserToLoadPage(6000)
            await resetCompletionPage.open('EventMaintenance/resetCompletion');
            await eventMaintanencePage.waitBrowserToLoadPage(6000)
            expect(await resetCompletionPage.linkSubModule.getText()).toHaveTextContaining(emData.Admin_reset_completion);
            await resetCompletionPage.waitForPageLoad(resetCompletionPage.clearFiltersBtn, emData.ResetComplete_Clear_Filters);
            await clickOn(await resetCompletionPage.clearFiltersBtn);
            await eventMaintanencePage.waitBrowserToLoadPage(3000)
            await resetCompletionPage.selectSingleValue(emData.selectedDivision_ResetCompletion, emData.division_Cincinnati_Operating_Division_014);
            await eventMaintanencePage.waitBrowserToLoadPage(3000)
            await resetCompletionPage.selectSingleValue(emData.selectedLocation_ResetCompletion, emData.selectedLocation_014_00351);
            await AutoActivationReport.selectYPW(emData.Year_Input, emData.Year_2023);
            await AutoActivationReport.selectYPW(emData.Period_Input, emData.Period_Empty);
            // await storeSelectionPage.clickOn(resetCompletionPage.storeReview);
            // await storeSelectionPage.clickOn(resetCompletionPage.searchBtn);
            await storeSelectionPage.clickOn(resetCompletionPage.commodityRecapBtn);
            await storeSelectionPage.clickOn(resetCompletionPage.searchBtn);
            // Waiting for data to load
            await eventMaintanencePage.waitBrowserToLoadPage(4000);
            await resetCompletionPage.waitForPageLoad(await resetCompletionPage.clearFiltersBtn, emData.ResetComplete_Clear_Filters);
            await storeSelectionPage.clickOn(resetCompletionPage.exportToExcelBtn);
        })

        
        it('validateCommodityRecapExcelDataToUi: Should download excel and compares excel data from CommodityRecap to UI ', async () => {
            // Waiting until excel file finishes downloading
            await eventMaintanencePage.waitBrowserToLoadPage(7000);
            // gets two pages of data
            let uiDataCommodity = await resetCompletionPage.getCommodityRecapNthPageUIData('3');
            await eventMaintanencePage.waitBrowserToLoadPage(2000);
            let excelUiCompareBoolean = await excelReader.excelUiDataCompareNthRowCommodityRecap(uiDataCommodity, 10);
            await eventMaintanencePage.waitBrowserToLoadPage(2000);
            // fails if false
            await expect(excelUiCompareBoolean).toEqual(true);
        })

        it('Should validate download excel file and compare the excel data with DB data  ', async () => {
            await resetCompletionPage.waitForPageLoad(await resetCompletionPage.clearFiltersBtn, emData.ResetComplete_Clear_Filters);
            await eventMaintanencePage.waitBrowserToLoadPage(4000);
            let data = await excelReader.validateExcelDataMultiSheetIgnoreColumns(qrString.resetCompletionCommodityRecapTab,'data', DBNames.SFP_STAGE,1);
            await expect(data).toEqual(true)
        })
    })

