let eventMaintenancePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
const emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const { setValue } = require('../../../GlobalObjectRepository/login.page');
const { assert } = require('chai');
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const createSfpEventPage = require("../EventManagerPO/createSFPEvent.page.js");

let divValue, description;
describe('validateActivationSfpEvent: validate the Event is deleted in EventMaintenance page UI and DB', async () => {

    it(' 1)Should validate the Activation Event is created in EventMaintenance page UI and DB;' +
        '2)Should validate the Activation Event is deleted in EventMaintenance page UI and DB', async () => {

        await eventMaintenancePage.waitBrowserToLoadPage(6000);
        await eventMaintenancePage.open('EventMaintenance');
        await eventMaintenancePage.waitBrowserToLoadPage(6000);
        divValue = emData.div_Cincinnati_Operating_Division_014;
        await eventMaintenancePage.selectEventType('SFP Activation')
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        description = await createSfpEventPage.addEventDetails1('', 'SFP Activation', divValue, emData.eventType_KOMPASS, "Activation", "AutomationEventActivation", emData.vendor_AZB)
        await setValue(createSFPEventPage.defaultMin, 3)
        await setValue(createSFPEventPage.defaultAllocation, 1)
        await createSFPEventPage.submitEventDetails()
        await expect(await eventMaintenancePage.linkSubModule.getText()).toEqual(emData.Event_Schedule)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Page did not appear before timeout')
        await createEventSchedulePage.genericClickDropdown('Fiscal Week')
        await clickOn(storeSelectionPage.saveSchedule)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        let weekText = await storeSelectionPage.storeWeekSchedule.getText()
        await storeSelectionPage.saveGoToStoreSelection()
        await storeSelectionPage.clickCheckBoxText(emData.Store_353);
        await clickOn(storeSelectionPage.saveStoreSchedule)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Upload UPC(s)")
        let pogPage = (await storeSelectionPage.linkSubModule).getText()
        await expect(await pogPage).toEqual(await emData.Upload_UPC)
        await createSFPEventPage.inputUPC.setValue(emData.UPC_Value_61076426171)
        await clickOn(createSFPEventPage.addUPC)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await clickOn(createSFPEventPage.saveUPC)
        await expect(browser).toHaveUrlContaining("EventMaintenance")
        await (await eventMaintenancePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.filtersTxtBtn, emData.Filters)
        await eventMaintenancePage.clickFiltersWithDiscp(description.toString())
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        let initialEventId = await eventMaintenancePage.getHeaderValue("Event Id", description)
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await eventMaintenancePage.clickOn(await eventMaintenancePage.slideOutClose)
        let eventId = await eventMaintenancePage.getEventId(initialEventId)
        await expect(eventId).toEqual(initialEventId)
        let dataFromDb = await result.getResult(qrString.SfpEvent_Eventid.replace('eventID', initialEventId))
        await assert.equal(dataFromDb.length, 1);
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 70000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        //deleting event
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await eventMaintenancePage.deleteActionBtn('OK')
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 70000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        let finalEventId = await eventMaintenancePage.getEventId(await initialEventId);
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await assert.notEqual(await finalEventId, await initialEventId, 'Error: initialEventID should not exist after deletion ! ')
        let dataFromDbAfterDeletion = await result.getResult(qrString.SfpEvent_Eventid.replace('eventID', initialEventId))
        // validate the no records coming from DB as event is deleted
        await assert.equal(dataFromDbAfterDeletion.length, 0)
        })

})
