const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const selectPOGPage = require("../EventManagerPO/selectingPogCommodities.page");
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const { assert, expect } = require('chai')

let keysToFind;

describe('validateSlideOutPannelNpeEvent: validate the Slide pannel in EM page', () => {

    it('Should validate he slide out pannel for NPE events', async () => {
        let eventDescription = await createSfpEventPage.addEventDetails1('',await emData.SFP_EVENT, await emData.div_Cincinnati_Operating_Division_014, await emData.eventType_KOMPASS, await "Update", await "npeEvent", await emData.vendor_AZB,'',"Activate CAO 2 weeks before Event Schedule")
        await createSfpEventPage.submitEventDetails()
        await createEventSchedulePage.scheduling(1)
        await storeSelectionPage.saveGoToStoreSelection()
        let div = (emData.div_Cincinnati_Operating_Division_014.match(/\d/g).join("")).toString()
        let dbScheduledStores = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, div).replace('@num', '30'))).flat();
        await storeSelectionPage.storeSelection(dbScheduledStores, 6, 1)
        await storeSelectionPage.storeSelection(dbScheduledStores, 12, 2)
        await storeSelectionPage.saveStoreScheduleButton()
        await selectPOG.selectingPogCommodities('Department', emData.dept_01_GROCERY, 'Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER, 'Commodity *', emData.ResetComplete_All, '')
        await clickOn(selectPOGPage.selectChoosePogByCommodityBtn);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await (await selectPOG.gotoEventListButton).waitForExist({ timeout: 90000 }, 'Error: EventList button not visible')
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await clickOn(selectPOGPage.gotoEventListButton);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await expect(await eventMaintanencePage.eventCreationText.getText()).to.equal(emData.Event_Creation);
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.clickFiltersWithDiscp(eventDescription.toString())
        let eventDisp = await eventMaintanencePage.getHeaderValue(emData.Filters_Event_Desc, eventDescription)
        let pogType = await eventMaintanencePage.getHeaderValue(emData.Header_Pog, eventDisp)
        let pogStringStoreNum = await eventMaintanencePage.extractingDigit(emData.PogString, emData.PogString, 1);
        await expect(pogType).to.equal(pogStringStoreNum)
        let pogID = await eventMaintanencePage.getPogHeaderValues(emData.Pog_Header)
        await eventMaintanencePage.clickCloseButton('Close');

        //stores
        let scheduledStrCount = await eventMaintanencePage.getHeaderValue(emData.Header_ScheduledStr, eventDescription)
        let popUpSchedule = (await eventMaintanencePage.getColumnValues(emData.event_Schedule_Popup_Schedule_Table, emData.event_Schedule_Popup_Schedule_List)).sort();
        let popUpScheduleDiv = await eventMaintanencePage.getColumnValues(emData.event_Schedule_Popup_Schedule_Table, emData.event_Schedule_Popup_Schedule_Div_List)
        const modifiedopUpScheduleDiv = popUpScheduleDiv.map((element) => element.replace(new RegExp(`^Div\\s*`), ''));
        await expect(popUpSchedule.length.toString()).to.equal(scheduledStrCount)
        await eventMaintanencePage.clickCloseButton('Close');
        let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, eventDescription)
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        let eventIdInSlide = await (await eventMaintanencePage.getTextOfElement(eventMaintanencePage.slidePaneleventId));
        await expect(initialEventId).to.equal(await eventMaintanencePage.extractDigits(eventIdInSlide));
        await assert.strictEqual(eventDisp, await eventMaintanencePage.getTextOfElement(eventMaintanencePage.slidePanelDiscription))

        // assert slideOutPanel outside values 
        keysToFind = ['Schedule Type:', 'Type:', 'Activation'];
        const slidePanelTableValues = await eventMaintanencePage.getValuesByKeysSlidePanel("4", keysToFind)
        assert.strictEqual(slidePanelTableValues, emData.sildePanel_NPE_KOM_Yes);
        // assert slideOutPanel outside values 
        keysToFind = ['Division:', 'Schedules:', 'Stores Scheduled:', 'Stores Unscheduled:'];
        const slidePanelTableValues1 = await eventMaintanencePage.getValuesByKeysSlidePanel("5", keysToFind)
        assert.strictEqual(slidePanelTableValues1, emData.sildePanelNpe_014_2_2_111);

        //assert EM pog dropdown with slideOutPanel
        await eventMaintanencePage.slidePanelTabsClick(emData.slidePanelPOGs);
        const columnValues = eventMaintanencePage.getColumnValuesByHeaderNameSlidePanel(emData.slidePanelPOGs, emData.Space_Station_Name_Header);
        assert.deepEqual(pogID.sort(), (await columnValues).sort());
        await browser.pause(2000);
        (await eventMaintanencePage.slidePanelCloseButton).click()
        await eventMaintanencePage.slidePanelTabsClick(emData.slidePanelPOGs);

        //assert EM Stores dropdown with slideOutPanel
        await eventMaintanencePage.slidePanelTabsClick(emData.slidePanelStores);
        const stotreColumnValues = await eventMaintanencePage.getColumnValuesByHeaderNameSlidePanel(emData.slidePanelStores, emData.slidePanelColumnStores);
        const divisionColumnValues = await eventMaintanencePage.getColumnValuesByHeaderNameSlidePanel(emData.slidePanelStores, emData.slidePanelColumnStoresDiv);
        assert.deepEqual(popUpSchedule.sort(), stotreColumnValues.sort());
        assert.deepEqual(modifiedopUpScheduleDiv, divisionColumnValues);
    })

})