const eventMaintenancePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const { setValue } = require('../../../GlobalObjectRepository/login.page');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');


describe('validateReleaseEventEnabled: validate ReleaseEvent Enabled/Disabled For Sfp Event', () => {

    it('validate ReleaseEvent Enabled/Disabled For Sfp Event', async () => {
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await eventMaintenancePage.open('EventMaintenance');
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        await eventMaintenancePage.tableSecondPageBtn.waitForExist({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})             
        await eventMaintenancePage.selectEventType('SFP Event')
        await expect(await eventMaintenancePage.linkCreateSFPEvent.getText()).toEqual(emData.Create_SFP_Event)
        await createSFPEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014)
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        await createSFPEventPage.selectDropdown('selectType', "Update");
        let description = await selectPOG.stringGenerator("SfpEventRelease")
        await createSFPEventPage.enterEventResetDescription(description)
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSFPEventPage.submitEventDetails()
        let nextPage = await eventMaintenancePage.linkSubModule.getText()
        await expect(nextPage).toEqual(emData.Event_Schedule)
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        await createEventSchedulePage.selectDropdown('Fiscal Week');
        await clickOn(storeSelectionPage.saveSchedule)
        await storeSelectionPage.storeWeekSchedule.waitForExist({timeout: 20000}, 'Error: week did not appear')
        let weekText = await storeSelectionPage.storeWeekSchedule.getText()
        await storeSelectionPage.saveGoToStoreSelection()
        await eventMaintenancePage.open('EventMaintenance');
        await (await eventMaintenancePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.filtersTxtBtn, emData.Filters)
        await eventMaintenancePage.clickFiltersWithDiscp(description.toString())
        let releaseEventDisabled = await eventMaintenancePage.selectReleaseEvents(emData.ReleaseEvent_Corporate, emData.Header_Action, description)
        await expect(releaseEventDisabled).toEqual(false)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        let releaseEventEdit = await eventMaintenancePage.selectReleaseEvents(emData.ReleaseEvent_Edit, emData.Header_Action, description)
        await expect(releaseEventEdit).toEqual(true)
        await createSFPEventPage.submitEventDetails()
        await createEventSchedulePage.selectDropdown('Fiscal Week');
        await storeSelectionPage.saveGoToStoreSelection()
        await storeSelectionPage.clickCheckBoxText(emData.Store_353);
        await clickOn(storeSelectionPage.saveStoreSchedule)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Select Pogs");
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(pogPage).toEqual(emData.Select_Pogs)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department)
        await selectPOG.selectDropdown('Department', emData.dept_01_GROCERY)
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department)
        await selectPOG.selectDropdown('Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER)
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department)
        await selectPOG.selectDropdown('Commodity *', emData.commodity_003_CAN_BEANS_01_GROC_ALL_OTHER_01_GROCERY)
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department)
        await selectPOG.selectDropdown('Commodity Group', emData.CommodityGroup_013_SPICES_EXTRACTS_013_SPICES_EXTRACTS)
        await clickOn(selectPOG.selectChoosePogByCommodityBtn)
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department)
        let text = await selectPOG.getPogRowText(emData.DBkey_7239932);
        await expect(text).toEqual(true)
        await clickOn(selectPOG.gotoEventListButton)
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.filtersTxtBtn, emData.Filters)
        await eventMaintenancePage.clickFiltersWithDiscp(description.toString())
        let eventDescription = await selectPOG.getEventDescText(description, weekText.replace(', ', '/'))
        await expect(eventDescription).toEqual(true)
        let releaseEventEnabled = await eventMaintenancePage.selectReleaseEvents(emData.ReleaseEvent_Corporate, emData.Header_Action, description)
       // await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.loadingSpinnerEventmaintenance, emData.releaseEventSpinnerText)
        await expect(releaseEventEnabled).toEqual(true)
        await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.eventReleaseTextAlert, emData.ReleaseEventMsg)
        let releaseText= await (await (eventMaintenancePage.eventReleaseTextAlert)).getText()
        await expect(releaseText).toEqual(emData.ReleaseEventMsg)
    })

    it('validate ReleaseEvent Enabled/Disabled For Sfp Event Remodel', async () => {                                                                  
        await eventMaintenancePage.open('EventMaintenance');  
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintenancePage.selectEventType('SFP Event')
        expect(await eventMaintenancePage.linkCreateSFPEvent.getText()).toEqual(emData.Create_SFP_Event)
        await createSFPEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014)
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_Remodel)
        let description1 = await selectPOG.stringGenerator("SfpRemodelEventRelease")
        await createSFPEventPage.enterEventResetDescription(description1)
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSFPEventPage.submitEventDetails()
        let nextPage = await eventMaintenancePage.linkSubModule.getText()
        await expect(nextPage).toEqual(emData.Event_Schedule)
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        await createEventSchedulePage.selectDropdown('Fiscal Week');
        await clickOn(storeSelectionPage.saveSchedule)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        let weekText = await storeSelectionPage.storeWeekSchedule.getText()
        await storeSelectionPage.saveGoToStoreSelection()
        await eventMaintenancePage.open('EventMaintenance'); 
        await (await eventMaintenancePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.filtersTxtBtn, emData.Filters)
        await eventMaintenancePage.clickFiltersWithDiscp(description1.toString())
        let releaseEventDisabled2 = await eventMaintenancePage.selectReleaseEvents(emData.ReleaseEvent_Corporate, emData.Header_Action, description1)
        await expect(releaseEventDisabled2).toEqual(false)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        let releaseEventEdit2 = await eventMaintenancePage.selectReleaseEvents(emData.ReleaseEvent_Edit, emData.Header_Action, description1)
        await expect(releaseEventEdit2).toEqual(true)
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await createSFPEventPage.submitEventDetails()
        await createEventSchedulePage.selectDropdown('Fiscal Week');
        await storeSelectionPage.saveGoToStoreSelection()
        await storeSelectionPage.clickCheckBoxText(emData.Store_351)
        await clickOn(storeSelectionPage.saveStoreSchedule)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Select Pogs")
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(pogPage).toEqual(emData.Select_Pogs)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department)
        await selectPOG.selectDropdown('Department', emData.dept_01_GROCERY)
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department)
        await selectPOG.selectDropdown('Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER)
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department)
        await selectPOG.selectDropdown('Commodity *', emData.commodity_01_GROCERY_01_GROC_ALL_OTHER_013_SPICES_EXTRACTS)
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department)
        await clickOn(selectPOG.selectChoosePogByCommodityBtn)
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await clickOn(await selectPOG.gotoEventListButton)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.filtersTxtBtn, emData.Filters)
        await eventMaintenancePage.clickFiltersWithDiscp(description1.toString())
        // validate the event description
        let eventDescription = await selectPOG.getEventDescText(description1, weekText.replace(', ', '/'))
        await expect(eventDescription).toEqual(true)
        let releaseEventEnabled2 = await eventMaintenancePage.selectReleaseEvents(emData.ReleaseEvent_Corporate, emData.Header_Action, description1)
        //await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.loadingSpinnerEventmaintenance, emData.releaseEventSpinnerText)
        await expect(releaseEventEnabled2).toEqual(true)
        await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.eventReleaseTextAlert, emData.ReleaseEventMsg)
        let releaseText= await (await (eventMaintenancePage.eventReleaseTextAlert)).getText()
        await expect(releaseText).toEqual(emData.ReleaseEventMsg)
    })

    it('validate ReleaseEvent Enabled/Disabled For Sfp Activation', async () => {
        await eventMaintenancePage.open('EventMaintenance');
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await eventMaintenancePage.tableSecondPageBtn.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintenancePage.selectEventType('SFP Activation')
        expect(await eventMaintenancePage.linkCreateSFPEvent.getText()).toEqual(emData.Create_SFP_Activation_Event)
        await createSFPEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014)
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        let description2 = await selectPOG.stringGenerator("EventActivationRelease")
        await createSFPEventPage.enterEventResetDescription(description2)
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await setValue(createSFPEventPage.defaultMin,3)
        await setValue(createSFPEventPage.defaultAllocation,1)
        await createSFPEventPage.submitEventDetails()
        let nextPage = await eventMaintenancePage.linkSubModule.getText()
        await expect(nextPage).toEqual(emData.Event_Schedule)
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Page did not appear before timeout')
        await createEventSchedulePage.selectDropdown('Fiscal Week');
        await clickOn(storeSelectionPage.saveSchedule)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        let weekText = await storeSelectionPage.storeWeekSchedule.getText()
        await storeSelectionPage.saveGoToStoreSelection()
        await eventMaintenancePage.open('EventMaintenance'); 
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.filtersTxtBtn, emData.Filters)
        await (await eventMaintenancePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintenancePage.clickFiltersWithDiscp(description2.toString())
        let disabled = await eventMaintenancePage.selectReleaseEvents(emData.ReleaseEvent_Corporate, emData.Header_Action, description2)
        await expect(disabled).toEqual(false)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        let releaseEventEdit3 = await eventMaintenancePage.selectReleaseEvents(emData.ReleaseEvent_Edit, emData.Header_Action, description2)
        await expect(await releaseEventEdit3).toEqual(true)
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await createSFPEventPage.submitEventDetails()
        await createEventSchedulePage.selectDropdown('Fiscal Week');
        await storeSelectionPage.saveGoToStoreSelection()
        await storeSelectionPage.clickCheckBoxText(emData.Store_353);
        await clickOn(storeSelectionPage.saveStoreSchedule)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Upload UPC(s)");
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(pogPage).toEqual(emData.Upload_UPC)
        await createSFPEventPage.inputUPC.setValue(emData.UPC_Value_61076426171)
        await clickOn(createSFPEventPage.addUPC)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await clickOn(createSFPEventPage.saveUPC)
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.filtersTxtBtn, emData.Filters)
        await eventMaintenancePage.clickFiltersWithDiscp(description2.toString())
        let releaseEventEnabled1 = await eventMaintenancePage.selectReleaseEvents(emData.ReleaseEvent_Corporate, emData.Header_Action, description2)
        //await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.loadingSpinnerEventmaintenance, emData.releaseEventSpinnerText)
        await expect(releaseEventEnabled1).toEqual(true)
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.eventReleaseTextAlert, emData.ReleaseEventMsg)
        let releaseText= await (await (eventMaintenancePage.eventReleaseTextAlert)).getText()
        await expect(releaseText).toEqual(emData.ReleaseEventMsg)
    })

})


