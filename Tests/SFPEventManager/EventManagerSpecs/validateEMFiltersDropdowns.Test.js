const eventMaintenancePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page.js');
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page');
const assertValue = require('chai').expect;
let initialDescription = '';
let eventID = '';
let div = emData.div_Columbus_Operating_Division_016.split('- ')[1];

describe('validateEMFiltersDropdowns: Validate new event is included in the EventID column list when user Filters EventMaintenance table by dropdowns ', () => {
    before(async () => {
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintenancePage.open('EventMaintenance');
        //wait until dropdowns are populated with data
        await eventMaintenancePage.waitBrowserToLoadPage(6000);
        await eventMaintenancePage.selectEventType('SFP Event');
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        //await expect(await eventMaintenancePage.linkCreateSFPEvent.getText()).toHaveTextContaining(emData.Create_SFP_Event);
        await createSFPEventPage.selectDropdown('division', emData.div_Columbus_Operating_Division_016);
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_Remodel);
        initialDescription = await selectPOG.stringGenerator('sfpEvent123456');
        await createSFPEventPage.enterEventResetDescription(initialDescription);
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB);
        await createSFPEventPage.submitEventDetails();
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        let nextPage = await eventMaintenancePage.linkSubModule.getText();
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await expect(nextPage).toEqual(emData.Event_Schedule);
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        await clickOn(storeSelectionPage.saveSchedule);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await storeSelectionPage.storeWeekSchedule.getText();
        await clickOn(storeSelectionPage.saveGoToStore);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
        let storePage = await storeSelectionPage.linkSubModule.getText();
        await expect(storePage).toEqual(emData.Store_Selection);
        await storeSelectionPage.clickCheckBoxText(emData.store_Selection_531);
        await storeSelectionPage.clickCheckBoxText(emData.store_Selection_581);
        await clickOn(await storeSelectionPage.saveStoreSchedule);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Select_Pogs);
        let pogPage = await storeSelectionPage.linkSubModule.getText();
        await expect(pogPage).toEqual(emData.Select_Pogs);
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await selectPOG.selectDropdown('Department', emData.selecting_POG_Department_15_DELI);
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await selectPOG.selectDropdown('Sub-Department', emData.selecting_POG_SubDepartment_15_DELI_49_SPECIALTY_CHSE);
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await selectPOG.selectDropdown('Commodity *', emData.selecting_POG_Commodity_462_SPECIALTY_CHEESE_PRE_PACK_49_SPECIALTY_CHSE_15_DELI);
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await selectPOG.selectDropdown('Commodity Group', emData.selecting_POG_CommodityGroup_462_SPECIALTY_CHEESE_PRE_PACK_462_SPECIALTY_CHEESE_PRE_PACK);
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await clickOn(await selectPOG.selectChoosePogByCommodityBtn);
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await clickOn(selectPOG.gotoEventListButton);
        await eventMaintenancePage.waitBrowserToLoadPage(1000);
        await expect(await eventMaintenancePage.eventCreationText.getText()).toEqual(emData.Event_Creation);
        //await clickOn(await eventMaintenancePage.refreshEventBtn);
        //await eventMaintenancePage.tableSecondPageBtn.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        //await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.filtersTxtBtn, emData.Filters);
        await eventMaintenancePage.clickFiltersWithDiscp(initialDescription.toString())
        await eventMaintenancePage.waitForPageLoad(await eventMaintenancePage.filtersTxtBtn, emData.Filters_text);
        let initialEventID = await eventMaintenancePage.getHeaderValue(emData.Event_ID, initialDescription);
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        await eventMaintenancePage.clickOn(await eventMaintenancePage.slideOutClose)
        eventID = await eventMaintenancePage.getEventId(initialEventID);
        await expect(eventID).toEqual(initialEventID);
    })

    it('Validate DIV column is filtered by Division dropdown and new Event is included in DIV column list ', async () => {
       // await eventMaintenancePage.waitBrowserToLoadPage(4000);
        // clickOn(await eventMaintenancePage.filtersBtn);
        // clickOn(await eventMaintenancePage.filtersBtn);
        await eventMaintenancePage.selectFiltersDropdown(emData.selectedDivision_eventMaintenance, emData.div_016_Columbus_Operating_Division,"divisionSingleSelect");
        await clickOn(await eventMaintenancePage.searchBtn);
        //await eventMaintenancePage.waitForPageLoad(await eventMaintenancePage.filtersTxtBtn, emData.Filters_text);
        let eventDivList = await eventMaintenancePage.getColumnList(emData.Division_text);
        let uniqueDivValue = await eventMaintenancePage.getUniqueArrayValue(eventDivList);
        await expect(div).toEqual(uniqueDivValue.toString());
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        let eventIDListFilteredWithDiv = await eventMaintenancePage.getColumnList(emData.Event_ID);
        await assertValue(eventIDListFilteredWithDiv).to.include(eventID, 'The NewEvent is not included in Division-filtered EventID column');
    })

    it('Validate new Event is included in DIV column list when filtered by (Department) dropdown ', async () => {
        await eventMaintenancePage.selectFiltersDropdown(emData.selectedDepartment_eventMaintenance, emData.Filters_Depart_15_DELI,"selectedDepartmentDropdown");
        await clickOn(await eventMaintenancePage.searchBtn);
        let eventIDListFilteredByDepart = await eventMaintenancePage.getColumnList(emData.Event_ID);
        await assertValue(eventIDListFilteredByDepart).to.include(eventID, 'The NewEvent is not included in Department-filtered EventID column');
    })

    it('Validate new Event is included in DIV column list when filtered by (Sub_Department) dropdown ', async () => {
        await eventMaintenancePage.selectFiltersDropdown(emData.Filters_selectedSubDept, emData.selecting_POG_SubDepartment_15_DELI_49_SPECIALTY_CHSE,"subDepartmentDropdown");
        await clickOn(await eventMaintenancePage.searchBtn);
        let eventIDListFilteredBySubDepart = await eventMaintenancePage.getColumnList(emData.Event_ID);
        await assertValue(eventIDListFilteredBySubDepart).to.include(eventID, 'The NewEvent is not included in Sub-Department-filtered EventID column');
    })

    it('Validate new Event is included in DIV column list when filtered by (Commodity) dropdown ', async () => {
        await eventMaintenancePage.selectFiltersDropdown(emData.Filters_selectedCommodity, emData.filters_Commodity_15_DELI_462_SPECIALTY_CHEESE_PRE_PACK,"selectedCommodityDropdown");
        await clickOn(await eventMaintenancePage.searchBtn);
        let eventIDListFilteredByCommodity = await eventMaintenancePage.getColumnList(emData.Event_ID);
        await assertValue(eventIDListFilteredByCommodity).to.include(eventID, 'The NewEvent is not included in Commodity-filtered EventID column');
    })

    it('Validate new Event when filtered by unique description via (Event Desc) inputBox', async () => {
        await eventMaintenancePage.setValue(await eventMaintenancePage.eventDescInput, initialDescription);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await clickOn(await eventMaintenancePage.searchBtn);
        let eventIDFilteredByEventDesc = await eventMaintenancePage.getColumnList(emData.Event_ID);
        await expect(eventIDFilteredByEventDesc.toString()).toEqual(eventID);
        await expect(eventIDFilteredByEventDesc.length.toString()).toEqual('1');

    })
})
