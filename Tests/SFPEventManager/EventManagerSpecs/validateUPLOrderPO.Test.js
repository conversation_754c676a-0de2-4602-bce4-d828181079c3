import{getTodayDate} from './../../../util/date.js';
const UPLOrderPage = require('../EventManagerPO/UPLOrderFlow.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const eventMaintanencePage = require("../EventManagerPO/eventMaintanence.page");
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');

describe('validateUPLOrderPO: validate Loc/Department dropdowns and input values ', () => {

    beforeEach( async() => {
        await eventMaintanencePage.waitBrowserToLoadPage(5000);
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.waitBrowserToLoadPage(5000);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout');
        await eventMaintanencePage.selectEventType(emData.UPL_Orders);
        expect(await UPLOrderPage.linkSubModule.getText()).toHaveTextContaining(emData.Create_UPL_Orders);
    })

    it('validate "Loc", "Department" dropdowns and validate remove table row functionality', async () => {

        await UPLOrderPage.selectSingleValue(emData.Location, emData.Locations, emData.Loc00005_Oab_Albany_Fred_Meyer_Stores);
        await expect(await UPLOrderPage.selectLocationDropdown.getText()).toEqual(emData.Loc00005_Oab_Albany_Fred_Meyer_Stores);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await UPLOrderPage.selectSingleValue(emData.Department, emData.Departments, emData.Depart_96_GM_MISC);
        await expect(await UPLOrderPage.selectDepartmentDropdown.getText()).toEqual(emData.Depart_96_GM_MISC);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await UPLOrderPage.selectRadioButton(emData.Category_Name, emData.Charcoal);
        await expect(await UPLOrderPage.selectCategoryDropdown.getText()).toEqual(emData.Charcoal);
        await UPLOrderPage.selectRadioButton(emData.Product_Group,  emData.FLG);
        await expect(await UPLOrderPage.selectProductGroupDropdown.getText()).toEqual(emData.FLG);
        await UPLOrderPage.setValue(UPLOrderPage.selectATTNToInput, await UPLOrderPage.stringGenerator('uplOrder9'));
        await UPLOrderPage.inputSendDate(getTodayDate());
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await UPLOrderPage.setValue(UPLOrderPage.getMailLocationInput, await UPLOrderPage.stringGenerator('ML123'));
        await UPLOrderPage.setValue(UPLOrderPage.getMailStopInput, await UPLOrderPage.stringGenerator('MS2'));
        await UPLOrderPage.setValue(UPLOrderPage.getRoutingInitialsInput, await UPLOrderPage.stringGenerator('RI3'));
        await UPLOrderPage.alertPopupMessageValidation(await UPLOrderPage.getUpdateRequestTable, emData.request_table_popup_type_success);
        await eventMaintanencePage.waitBrowserToLoadPage(8000);
        await UPLOrderPage.dataTable.waitForExist();
        await UPLOrderPage.alertPopupMessageValidation(await UPLOrderPage.getUpdateRequestTable, emData.request_table_popup_type_warning_event_exists);
        await expect(await UPLOrderPage.locatorDisabled(await UPLOrderPage.getRemoveSelectedRecordsBtn)).toEqual(true);
        await UPLOrderPage.clickOn(await UPLOrderPage.dataTableFirstCheckBox);
        await expect(await UPLOrderPage.locatorDisabled(await UPLOrderPage.getRemoveSelectedRecordsBtn)).toEqual(false);
        await UPLOrderPage.clickOn(await UPLOrderPage.dataTableFirstCheckBox);
        let validateCheckUnCheckHeaderCheckBox = await UPLOrderPage.validateHeaderCheckbox();
        await expect(validateCheckUnCheckHeaderCheckBox).toEqual(true);
        await UPLOrderPage.getPaginationPageNumber('15');
        let tableRowsLocatorInitial = await UPLOrderPage.dataTableFirstPageRows;
        let initialCount = tableRowsLocatorInitial.length;
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await UPLOrderPage.removeSelectedRecords();
        await UPLOrderPage.firstButtonPagination;
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        let tableRowsLocatorFinal = await UPLOrderPage.dataTableFirstPageRows;
        let finalCount = tableRowsLocatorFinal.length;
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await expect(initialCount-1).toEqual(finalCount);
    })

    it('Clean up before or after next Test', async () => {

        try {

            await UPLOrderPage.waitBrowserToLoadPage(3000);
            let isDisabled = await UPLOrderPage.locatorDisabled(UPLOrderPage.dataTableCheckBoxList);
            if(!isDisabled) {
                await UPLOrderPage.clickOn(await UPLOrderPage.dataTableCheckBoxList);
                await UPLOrderPage.waitBrowserToLoadPage(3000);
                await UPLOrderPage.clickOn(await UPLOrderPage.getRemoveSelectedRecordsBtn);
                await UPLOrderPage.clickOn(await UPLOrderPage.proceedOkBtn)
            }
        }
        catch(error){
            console.error("it.only( Clean up before or after next Test,...  errored out");
        }

    })


})
