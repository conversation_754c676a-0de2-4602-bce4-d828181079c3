const eventMaintenancePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');

describe('validateSelectingPogCommodities: validate Selecting Pog/commodities page', () => {

    it('Should validate Selecting Pog/commodities page', async () => {

        let description = await createSFPEventPage.addEventDetails1('','SFP Event', emData.div_Cincinnati_Operating_Division_014, emData.eventType_KOMPASS, "Update", "EventPOg/Commodity", emData.vendor_AZB);
        await createSFPEventPage.submitEventDetails()
        let nextPage = await eventMaintenancePage.linkSubModule.getText()
        await expect(nextPage).toEqual(emData.Event_Schedule)
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        await createEventSchedulePage.selectDropdown('Fiscal Week');
        await clickOn(storeSelectionPage.saveSchedule)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        let weekText = await storeSelectionPage.storeWeekSchedule.getText()
        await storeSelectionPage.saveGoToStoreSelection()
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(await storePage).toEqual(emData.Store_Selection)
        await createSFPEventPage.selectRandomStoresStep3();
        await clickOn(await storeSelectionPage.saveStoreSchedule)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Select Pogs")
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(await pogPage).toEqual(emData.Select_Pogs);
        await createSFPEventPage.selectPogCommoditiesStep4();
        await eventMaintenancePage.open('EventMaintenance'); 
        await eventMaintenancePage.waitBrowserToLoadPage(30000)
        let eventDescription = await selectPOG.getEventDescText(await description, await weekText.replace(', ','/'))
        await expect(eventDescription).toEqual(true)
    })
})
