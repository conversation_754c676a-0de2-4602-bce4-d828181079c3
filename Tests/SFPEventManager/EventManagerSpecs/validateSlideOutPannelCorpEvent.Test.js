const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const result = require('../../../SQLConnection/server.js.ts');
const qrString = require('../../../SQLConnection/queryStringEM');
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const { assert, expect } = require('chai');


describe('validateSlideOutPannelCorpEvent: validate the slide panel in Event Manager Page', () => {

    it('Should validate the slide out pannel for corp event', async () => {

        /* ----- Create an Corp event ------*/
        /* Add all details in Add details page */
        let eventDescription = await createSfpEventPage.addEventDetails1('',await emData.SFP_EVENT, await emData.div_Corporate_060, await emData.eventType_KOMPASS, await emData.type_NII, "CorpEventSlideOut", await emData.vendor_AZB,'',"Activate CAO 2 weeks before Event Schedule");
        await createSfpEventPage.submitEventDetails();
        /* Create schedules */
        await createEventSchedulePage.scheduling(1);
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        /* Select the stores in Store Selection page */
        let week1Text = await (await storeSelectionPage.storeWeekSchedule.getText());
        let week2Text = await (await storeSelectionPage.storeWeekSchedule2.getText());
        let weekText = await (await storeSelectionPage.storeWeekSchedule2.getText()).replace('Week', '').replace(', ', '/');
        const Week = week1Text.concat(' -', weekText).replace(', ', '/');
        await storeSelectionPage.saveGoToStoreSelection();
        let divNum14 = (emData.div_Cincinnati_Operating_Division_014.match(/\d/g).join("")).toString();
        let dbScheduledStores = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, divNum14).replace('@num', '30'))).flat();
        let storeDiveNum14 = await dbScheduledStores[5];
        let divNum16 = (emData.div_Columbus_Operating_Division_016.match(/\d/g).join("")).toString();
        let corpNum06 = (emData.div_Corporate_060.match(/\d/g).join("")).toString();
        let dbScheduledStoresDiv16 = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, divNum16).replace('@num', '30'))).flat();
        let storeDivNum16 = await dbScheduledStoresDiv16[3];
        await storeSelectionPage.selectStoresOfCorpEventSchedules(week1Text, emData.Division_014, storeDiveNum14);
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await storeSelectionPage.selectStoresOfCorpEventSchedules(week2Text, emData.Division_016, storeDivNum16);
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await storeSelectionPage.saveStoreScheduleButton();
        /* Select department, Sub-Department, Commodity */
        await selectPOG.selectingPogCommodities('Department', emData.dept_01_GROCERY, 'Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER, 'Commodity *', emData.ResetComplete_All, '')
        await clickOn(selectPOG.selectChoosePogByCommodityBtn);
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await clickOn(selectPOG.gotoEventListButton);
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters);
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        /* Search the created corp event by using description in filters */
        await eventMaintanencePage.clickFiltersWithDiscp(eventDescription);
        /* Fetch eventID of corp event and number of Scheduled stores */
        let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, eventDescription);
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose);
        let scheduledStrCount = await eventMaintanencePage.getHeaderValue(emData.Header_ScheduledStr, eventDescription);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose);
        let eventId = await eventMaintanencePage.getEventId(initialEventId);
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await clickOn(await eventMaintanencePage.eventListFirstRowSchedStr);
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        let actualScheduleCount = (await eventMaintanencePage.popUpScheduleDivCount).length;
        /* Asserting number of schedules */
        await expect(actualScheduleCount.toString()).to.equal(scheduledStrCount.toString());
        let popUpSchedule = await eventMaintanencePage.getColumnValues(emData.event_Schedule_Popup_Schedule_Table, emData.event_Schedule_Popup_Schedule_List);
        //await clickOn(await eventMaintanencePage.popUpScheduleDivClose);
        await eventMaintanencePage.clickCloseButton('Close');
        await expect(popUpSchedule[0]).to.equal(storeDiveNum14);
        await expect(popUpSchedule[1]).to.equal(storeDivNum16);

        /* Expand the Corp event */
        await eventMaintanencePage.expandEvent(eventId);

        /* -----Validating the first child data of Corp event----- */
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        let childEventID1 = await eventMaintanencePage.childEventID[0].getText();
        /* -----Below variable has never been used in the test ???----- */
        let childEventID2 = await eventMaintanencePage.childEventID[1].getText();
        await eventMaintanencePage.expandEventPlus(childEventID1);
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await clickOn(await eventMaintanencePage.childEventID[0]);
        let keysToFind_sched_type_activation = ['Schedule Type:', 'Type:', 'Activation'];
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        const slidePanelTableValuesFirstChildEvent = await eventMaintanencePage.getValuesByKeysSlidePanel("4", keysToFind_sched_type_activation);
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        assert.strictEqual(slidePanelTableValuesFirstChildEvent, emData.sildePanel_Corp_KOM_Yes, "Schedule Type, Type, and Activation data mismatch");
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        let keysToFind = ['Division:', 'UPC List:','Schedules:', 'Stores Scheduled:', 'Stores Unscheduled:'];
        const slidePanelTableValuesFirstChildEventDiv = await eventMaintanencePage.getValuesByKeysSlidePanel("5", keysToFind);
        assert.strictEqual(slidePanelTableValuesFirstChildEventDiv, emData.slidePanel_Corp_Div014_Schedule, "Division, UPC List, schedules, store schedules, Stores Unscheduled mismatch");
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.modalStore);
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        let modalDivStoreValue = await eventMaintanencePage.childStoreValueFromModal.getText();
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose);
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.childStore);
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        let storeValue = await eventMaintanencePage.divStoreValue.getText();
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await eventMaintanencePage.clickCloseButton('Close');
        assert.strictEqual(storeValue, modalDivStoreValue, "Store Values mismatch")
        // await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await clickOn(await eventMaintanencePage.firstChildMinusBtn);

        /* Validating second child data of Corp event */
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.secondChildPlusBtn);
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await clickOn(await eventMaintanencePage.childEventID[1]);
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        const slidePanelTableValuesSecondChildEvent = await eventMaintanencePage.getValuesByKeysSlidePanel("4", keysToFind_sched_type_activation);
        assert.strictEqual(slidePanelTableValuesSecondChildEvent, emData.sildePanel_Corp_KOM_Yes, "Schedule Type, Type, and Activation data");
        const slidePanelTableValuesSecondChildEventDiv = await eventMaintanencePage.getValuesByKeysSlidePanel("5", keysToFind);
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.modalStore);
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        let modalDivStoreValue1 = await eventMaintanencePage.childStoreValueFromModal.getText();
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose);
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.childStore);
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        let storeValue1 = await eventMaintanencePage.divStoreValue.getText();
        assert.strictEqual(storeValue1, modalDivStoreValue1, "Store Values mismatch")
        assert.strictEqual(slidePanelTableValuesSecondChildEventDiv, emData.slidePanel_Corp_Div016_Schedule, "Division, UPC List, schedules, store schedules, Stores Unscheduled mismatch");
        await eventMaintanencePage.clickCloseButton('Close');
        await clickOn(await eventMaintanencePage.secondChildMinusBtn);
    })
})
