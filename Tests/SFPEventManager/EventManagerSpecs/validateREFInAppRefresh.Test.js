const eventMaintenancePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const { assert } = require('chai');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');


describe('validateREFInAppRefresh: validate the RefreshEvents in header table UI', () => {

    beforeEach( async() => {
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await eventMaintenancePage.open('EventMaintenance');
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        await eventMaintenancePage.tableSecondPageBtn.waitForExist({ timeout: 90000, timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await eventMaintenancePage.selectEventType('SFP Refresh')
        await expect(await eventMaintenancePage.linkCreateSFPEvent.getText()).toEqual(emData.Event_Refresh);
        await createSfpEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014)
    })

    it('Should validate the Refresh_Type B-V-D-APP Event in header table UI', async () => {
        await createSfpEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        let description = await selectPOG.stringGenerator("EventRef_Type")
        await createSfpEventPage.enterEventResetDescription(description)
        await createSfpEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSfpEventPage.clickCheckBox(emData.Vestcom_Refresh_Tag_Sequence)
        await createSfpEventPage.clickCheckBox(emData.Digital_Refresh_ClickList)
        await createSfpEventPage.clickCheckBox(emData.Allocation)
        await createSfpEventPage.clickCheckBox(emData.APP_Refresh)
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        await createSfpEventPage.submitEventRefreshDetails()
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        await clickOn(createSfpEventPage.sfpAppModalButton)
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        let nextPage = await createSfpEventPage.linkCreateSfpRefreshEvent.getText()
        await expect(nextPage).toEqual(emData.Event_Refresh)
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 , timeoutMsg:'Error: Save Schedule button did not appear before timeout'})
        await createEventSchedulePage.selectDropdown('Fiscal Week');
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 , timeoutMsg:'Error: Save Schedule button did not appear before timeout'})
        await clickOn(storeSelectionPage.saveSchedule)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        let weekText = await storeSelectionPage.storeWeekSchedule.getText()
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 , timeoutMsg:'Error: Save Schedule button did not appear before timeout'})
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await clickOn(storeSelectionPage.saveGoToStore)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkCreatSfpRefreshEvent, emData.Event_Refresh)
        let storePage = await storeSelectionPage.linkCreatSfpRefreshEvent.getText()
        await expect(storePage).toEqual(emData.Event_Refresh)
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await storeSelectionPage.clickCheckBoxText(emData.Store_351);
        await clickOn(storeSelectionPage.saveStoreSchedule)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkCreatSfpRefreshEvent, emData.Event_Refresh)
        let pogPage = await storeSelectionPage.linkCreatSfpRefreshEvent.getText()
        await expect(pogPage).toEqual(emData.Event_Refresh)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await selectPOG.selectDropdown('Department', emData.dept_01_GROCERY)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Commodity *', emData.commodity_003_CAN_BEANS_01_GROC_ALL_OTHER_01_GROCERY)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Commodity Group', emData.CommodityGroup_013_SPICES_EXTRACTS_013_SPICES_EXTRACTS)
        await clickOn(selectPOG.selectChoosePogByCommodityBtn)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await (await selectPOG.gotoEventListButton).waitForClickable({ timeout: 70000 , timeoutMsg:'Error: Event maintenance page did not load before timeout '})
        await clickOn(await selectPOG.gotoEventListButton)
        await expect(browser).toHaveUrlContaining("EventMaintenance")
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        await eventMaintenancePage.clickFiltersWithDiscp(description.toString())
        let pogType = await eventMaintenancePage.getHeaderValue(emData.Header_Pog, description)
        await expect(pogType).toEqual("1")
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        await eventMaintenancePage.clickCloseButton('Close');
        let refreshType = await eventMaintenancePage.getHeaderValue(emData.Header_Refresh, description)
        await expect(refreshType).toEqual(emData.REF_Type_B_V_D_APP)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 70000 , timeoutMsg:'Error: EM table did not display before timeout'})
        let initialEventId = await eventMaintenancePage.getHeaderValue(emData.Event_ID, description)
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await eventMaintenancePage.clickOn(await eventMaintenancePage.slideOutClose)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 70000 , timeoutMsg:'Error: EM table did not display before timeout'})
        let eventId = await eventMaintenancePage.getEventId(initialEventId)
        await assert.equal(eventId, initialEventId,'EventID not found')
        let dataFromDB = await result.getResult(qrString.SfpEvent_Eventid.replace('eventID', initialEventId))
        await assert.equal(dataFromDB.length, 1)
    })

    it('Should validate the Refresh_Type B-DAD-APP Event in header table UI', async () => {
        await createSfpEventPage.selectDropdown('scheduleType', emData.eventType_Remodel)
        let description = await selectPOG.stringGenerator("EventRef_Type")
        await createSfpEventPage.enterEventResetDescription(description)
        await createSfpEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSfpEventPage.clickCheckBox(emData.Digital_Refresh_ClickList)
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        await createSfpEventPage.selectRadioButton(emData.radioBtn_aisleDelete)
        await createSfpEventPage.clickCheckBox(emData.Allocation)
        await createSfpEventPage.clickCheckBox(emData.APP_Refresh)
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        await createSfpEventPage.submitEventRefreshDetails()
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        await clickOn(createSfpEventPage.sfpAppModalButton)
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        await eventMaintenancePage.open('EventMaintenance'); 
        await expect(browser).toHaveUrlContaining("EventMaintenance")
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        await eventMaintenancePage.clickFiltersWithDiscp(description.toString())
        let initialEventId = await eventMaintenancePage.getHeaderValue(emData.Event_ID, description)
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await eventMaintenancePage.clickOn(await eventMaintenancePage.slideOutClose)
        let eventId = await eventMaintenancePage.getEventId(initialEventId)
        await expect(eventId).toEqual(initialEventId)
        let refreshType = await eventMaintenancePage.getHeaderValue(emData.Header_Refresh, description)
        await expect(refreshType).toEqual(emData.REF_Type_B_DAD_APP)
    })

    it('Should validate the Refresh_Type B-V-APP Event in header table UI', async () => {
        await createSfpEventPage.selectDropdown('scheduleType', emData.eventType_Remodel)
        let description = await selectPOG.stringGenerator("EventRef_Type")
        console.log('$$$ description', description)
        await createSfpEventPage.enterEventResetDescription(description)
        await createSfpEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSfpEventPage.clickCheckBox(emData.Vestcom_Refresh_Tag_Sequence)
        await createSfpEventPage.clickCheckBox(emData.Allocation)
        await createSfpEventPage.clickCheckBox(emData.APP_Refresh)
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        await createSfpEventPage.submitEventRefreshDetails()
        await eventMaintenancePage.waitBrowserToLoadPage(5000)
        await eventMaintenancePage.clickOn(createSfpEventPage.sfpAppModalButton) 
        let nextPage = await createSfpEventPage.linkCreateSfpRefreshEvent.getText()
        await expect(nextPage).toEqual(emData.Event_Refresh)
        const scheduleDropdownValue = await createEventSchedulePage.clickAndValidateSingleDropdownValue(await createEventSchedulePage.fiscalWeekSelect);
        const scheduleDropdownValueWeekDigit = await (await scheduleDropdownValue.toString()).split('-')[0];
        const weekNumberFromAttentionMessage = await createEventSchedulePage.getTextOfElement(await createEventSchedulePage.schedulingAttentionMessage);
        await expect(await scheduleDropdownValueWeekDigit.trim()).toEqual(await createEventSchedulePage.extractDigitsByIndex(weekNumberFromAttentionMessage,0));
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        await eventMaintenancePage.waitBrowserToLoadPage(3000)
        await clickOn(storeSelectionPage.saveSchedule)
        await eventMaintenancePage.waitBrowserToLoadPage(3000)
        let weekText = await storeSelectionPage.storeWeekSchedule.getText()
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await eventMaintenancePage.open('EventMaintenance'); 
        await expect(browser).toHaveUrlContaining("EventMaintenance")
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        await eventMaintenancePage.clickFiltersWithDiscp(description.toString())
        let initialEventId = await eventMaintenancePage.getHeaderValue(emData.Event_ID, description)
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await eventMaintenancePage.clickOn(await eventMaintenancePage.slideOutClose)
        let eventId = await eventMaintenancePage.getEventId(initialEventId)
        await expect(eventId).toEqual(initialEventId)
        let refreshType = await eventMaintenancePage.getHeaderValue(emData.Header_Refresh, description)
        await expect(refreshType).toEqual(emData.REF_Type_B_V_APP)
    })
})
