const eventMaintenancePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require("../EventManagerPO/storeSelection.page");
const { clickOn } = require("../EventManagerPO/createSFPEvent.page");
const eventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const pogCommodityPage = require('../EventManagerPO/selectingPogCommodities.page');


describe('validateSFPEventAlertMessage: validate all alert messages during SFP Event creation process', () => {

    beforeEach(async () => {

        await createSFPEventPage.addEventDetails1('','SFP Event', emData.div_Cincinnati_Operating_Division_014, emData.eventType_KOMPASS, "Update", "VerifySFPEventAlerts", emData.vendor_AZB);
        await createSFPEventPage.submitEventDetails();
        let nextPage = await eventMaintenancePage.linkSubModule.getText();
        await expect(nextPage).toEqual(emData.Event_Schedule);
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
    })

    it('Verify alert message on StoreSelection and Selecting Pog/Commodities pages when saveSchedules on Step2:Scheduling is not selected', async () => {
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await eventSchedulePage.waitForPageLoad(eventSchedulePage.linkSubModule, emData.Event_Schedule);
        await clickOn(storeSelectionPage.storeSelectionPage);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        let storeAlertMessageCapital = await eventMaintenancePage.clickElementWithText(await storeSelectionPage.storeSelectionAlertMessageCapital, "NO SCHEDULES ASSIGNED FOR THIS EVENT");
        await expect(await storeAlertMessageCapital).toEqual(emData.NO_SCHEDULES_ASSIGNED);
        let storeAlertMessage = await eventMaintenancePage.clickElementWithText(await storeSelectionPage.storeSelectionAlertMessage, "Please Select Stores For Every Scheduled Week To Proceed with Pog Selection.")
        await expect(storeAlertMessage).toEqual(emData.Please_Select_Stores);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await eventMaintenancePage.clickElementWithText(await (eventMaintenancePage.okBtn), "Ok")
        await eventSchedulePage.waitForPageLoad(eventSchedulePage.linkSubModule, emData.Event_Schedule);
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        await expect(await eventSchedulePage.linkSubModule.getText()).toEqual(emData.Event_Schedule);
        await clickOn(pogCommodityPage.pogCommodityPage);
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        let pogAlertMessageCapital = await eventMaintenancePage.clickElementWithText(await storeSelectionPage.storeSelectionAlertMessageCapital, "NO STORES SELECTED FOR SCHEDULED WEEK");
        await expect(await pogAlertMessageCapital).toEqual(emData.NO_STORES_SELECTED);
        let scheduleStoreAlert = await eventMaintenancePage.storeSelectionAlert
        scheduleStoreAlert=await scheduleStoreAlert.getText()
        await expect(await scheduleStoreAlert).toEqual(emData.NO_STORES_SELECTED);
        let pogAlertMessage = await pogCommodityPage.pogCommodityAlertMessage.getText();
        await expect(pogAlertMessage).toEqual(emData.Please_Select_Stores_NPE);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await eventMaintenancePage.clickElementWithText(await (eventMaintenancePage.okBtn), "Ok")
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        await expect(await storeAlertMessageCapital).toEqual(emData.NO_SCHEDULES_ASSIGNED);
        await eventMaintenancePage.clickElementWithText(await (eventMaintenancePage.okBtn), "Ok")
        await eventSchedulePage.waitForPageLoad(eventSchedulePage.linkSubModule, emData.Event_Schedule);
        await expect(await eventSchedulePage.linkSubModule.getText()).toEqual(emData.Event_Schedule);
    })

    it('Validate alert message on when clicking Selecting Pog/Commodities while store is not selected on Step3:Store Selection', async () => {
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await clickOn(storeSelectionPage.saveSchedule);
        await eventMaintenancePage.waitBrowserToLoadPage(1000);
        await clickOn(eventSchedulePage.goToStoreSelectionBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(1000);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
        let storePage = await storeSelectionPage.linkSubModule.getText();
        await expect(storePage).toEqual(emData.Store_Selection);
        await clickOn(pogCommodityPage.pogCommodityPage);
        let pogAlertMessageCapital = await eventMaintenancePage.clickElementWithText(await pogCommodityPage.pogCommodityAlertMessageHeader, emData.NO_STORES_SELECTED);
        await expect(await pogAlertMessageCapital).toEqual(emData.NO_STORES_SELECTED);
        let pogAlertMessage = await pogCommodityPage.pogCommodityAlertMessage.getText();
        await expect(pogAlertMessage).toEqual(emData.Please_Select_Stores_NPE);
        await eventMaintenancePage.clickElementWithText(await (eventMaintenancePage.okBtn), "Ok")
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await eventSchedulePage.waitForPageLoad(eventSchedulePage.linkSubModule, emData.Store_Selection);
        await expect(await eventSchedulePage.linkSubModule.getText()).toEqual(emData.Store_Selection);
    })

    it('Validate alert message when only one store is selected from multiple stores on Step3:Store Selection:', async () => {
        await clickOn(await eventSchedulePage.eventScheduleHeader);
        for (let i = 0; i < 2; i++) {
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            await clickOn(await storeSelectionPage.saveSchedule);
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
        }
        await clickOn(eventSchedulePage.goToStoreSelectionBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
        let storePage = await storeSelectionPage.linkSubModule.getText();
        await expect(storePage).toEqual(emData.Store_Selection);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await clickOn(await storeSelectionPage.saveStoreSchedule);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        let noStoreSelected = await storeSelectionPage.storeSelectionNoStoreSelectedTxt.getText();
        await expect(noStoreSelected).toEqual(emData.Please_select_at_least_one_store);
    })
})
