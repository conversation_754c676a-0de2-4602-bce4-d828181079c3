const resetCompletionPage = require('../../EventManagerPO/resetCompletion.page.js')
let emData = require('../../../../TestData/EventManagerData/eventManagerUIData.json')
const envVariables = require("../../UserRoles/roles");
const ENV = process.env.ENV
const eventMaintenancePage = require('../../EventManagerPO/eventMaintanence.page');
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const properties = require("../../../../ConfigFiles/wdio.conf");

let div016 = emData.div_016_Columbus_Operating_Division.split(' - ')[0];
let corpDiv60 = emData.div_Corporate_060.split(" - ")[1];

before(async () => {

    await testSetup(envVariables.divisionalUserEM[ENV], envVariables.divisionalPasswordEM[ENV]);
})

describe('validateEventManagerDivisionUser: Validate SFP Event Manager page has restrictions for single division user ', () => {

    beforeEach(async () => {

        await expect(browser).toHaveUrlContaining(properties.config.baseUrl);
        await expect(browser).toHaveTitle(emData.homeTitle);
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        await resetCompletionPage.open(emData.EventMaintanceUrl);
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        await expect(await eventMaintenancePage.GetText(eventMaintenancePage.emHomePageText)).toEqual(emData.ApplicationsSubmenuSFPEventManager);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
    })

    it('validate Event Add, division dropdown, and table divisions shows correct division. Includes corporate division ', async () => {

        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await eventMaintenancePage.clickOn(eventMaintenancePage.addEventDropdown);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        let addEventDropDownValuesUI = await eventMaintenancePage.addEventDropDownList;
        let eventAddValues = [emData.SFP_EVENT, emData.SFP_Activation, emData.SFP_Refresh];
        await expect(addEventDropDownValuesUI.length ).toEqual(eventAddValues.length);
        let eventAddDropDownValuesUIText = [];
        for(let index = 0; index < addEventDropDownValuesUI.length; index++){
            eventAddDropDownValuesUIText.push( await eventMaintenancePage.GetText(addEventDropDownValuesUI[index]));
        }
        eventAddValues.sort();
        eventAddDropDownValuesUIText.sort();
        for(let index = 0; index < eventAddValues.length; index++){
            await expect(eventAddValues[index]).toEqual(eventAddDropDownValuesUIText[index]);
        }
        await eventMaintenancePage.clickOn(eventMaintenancePage.filtersBtn);
        await expect(await resetCompletionPage.locatorDisabled(await eventMaintenancePage.divisionDropDown, emData.attributeDisabled)).toEqual(true);
        await expect(await resetCompletionPage.locatorDisabled(await eventMaintenancePage.storeDropDown, emData.attributeDisabled)).toEqual(false);
        await expect(await eventMaintenancePage.GetText(await eventMaintenancePage.divisionDropDown)).toEqual(emData.div_016_Columbus_Operating_Division);
        await expect(await eventMaintenancePage.GetText(await eventMaintenancePage.storeDropDown)).toEqual(emData.select_store);
        let divColumnBeforeClick = await eventMaintenancePage.tableColumnByIndex(await eventMaintenancePage.getEventManagerTableHeaderIndex("EventManager", "Division") + 1);
        for(let index = 0; index < divColumnBeforeClick.length; index++){
             await expect(await eventMaintenancePage.GetText(await divColumnBeforeClick[index]) === corpDiv60 || await eventMaintenancePage.GetText(await divColumnBeforeClick[index]) === div016).toEqual(true);
        }
        await eventMaintenancePage.clickOn(await (eventMaintenancePage.tableHeaders)[emData.Number_3]);
        await eventMaintenancePage.waitBrowserToLoadPage(5000);
        let divColumnAfterClick = await eventMaintenancePage.tableColumnByIndex(await eventMaintenancePage.getEventManagerTableHeaderIndex("EventManager", "Division") + 1);
        for(let index = 0; index < divColumnAfterClick.length; index++){
             await expect(await eventMaintenancePage.GetText(await divColumnAfterClick[index]) === corpDiv60 || await eventMaintenancePage.GetText(await divColumnAfterClick[index]) === div016).toEqual(true);
        }
    })

    it('validate (clear filter) table divisions shows correct division. Includes corporate division ', async () => {

        await eventMaintenancePage.clickOn(eventMaintenancePage.filtersBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(5000);
        let paginationLabelBefore = await eventMaintenancePage.GetText( await eventMaintenancePage.paginationLabel);
        let paginationLabelBeforeElements = paginationLabelBefore.toString().split(" ");
        await eventMaintenancePage.clickOn(eventMaintenancePage.clearAllFiltersBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(6000);
        await expect(await eventMaintenancePage.GetText(await eventMaintenancePage.divisionDropDown)).toEqual(emData.div_016_Columbus_Operating_Division);
        await expect(await resetCompletionPage.locatorDisabled(await eventMaintenancePage.divisionDropDown, emData.attributeDisabled)).toEqual(true);
        await expect(await resetCompletionPage.locatorDisabled(await eventMaintenancePage.storeDropDown, emData.attributeDisabled)).toEqual(false);
        let paginationLabelAfter = await eventMaintenancePage.GetText( await eventMaintenancePage.paginationLabel);
        let paginationLabelAfterElements = paginationLabelAfter.toString().split(" ");
        await expect(paginationLabelBeforeElements[5]).toEqual(paginationLabelAfterElements[5]);
    })

})