const resetCompletionPage = require('../../EventManagerPO/resetCompletion.page.js')
let emData = require('../../../../TestData/EventManagerData/eventManagerUIData.json')
const envVariables = require("../../UserRoles/roles.js");
const eventMaintenancePage = require('../../EventManagerPO/eventMaintanence.page.js');
const storeSelectionPage = require('../../EventManagerPO/storeSelection.page');
const createSfpEventPage = require('../../EventManagerPO/createSFPEvent.page');
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const properties = require("../../../../ConfigFiles/wdio.conf.js");
const { clickOn } = require('../../EventManagerPO/createSFPEvent.page');
const eventSchedule = require('../../EventManagerPO/eventScheduling.page')
const result = require('../../../../SQLConnection/server.js.ts');
const qrString = require('../../../../SQLConnection/queryStringEM');
const selectPOGPage = require("../../EventManagerPO/selectingPogCommodities.page");
const selectPOG = require('../../EventManagerPO/selectingPogCommodities.page')
const ENV = process.env.ENV
const createEventSchedulePage = require('../../EventManagerPO/eventScheduling.page');

before(async () => {

    await testSetup(envVariables.divisionalUserEM[ENV], envVariables.divisionalPasswordEM[ENV]);
})

describe('validateEventManagerDivisionUser for Strips Error: Validate SFP Event Manager page For Non Schedule Week,Add Event Detail Page,EM HomePage And SlideOutPannel ', () => {

    beforeEach(async () => {
        await expect(browser).toHaveUrlContaining(properties.config.baseUrl);
        await expect(browser).toHaveTitle(emData.homeTitle);
        await resetCompletionPage.open(emData.EventMaintanceUrl);
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        await expect(await eventMaintenancePage.GetText(eventMaintenancePage.emHomePageText)).toEqual(emData.ApplicationsSubmenuSFPEventManager);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
    })

    it('validate Event With Strip Error For Non Schedule Week,Add Event Detail Page,EM HomePage And SlideOutPannel', async () => {

        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await eventMaintenancePage.clickOn(eventMaintenancePage.addEventDropdown);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        let addEventDropDownValuesUI = await eventMaintenancePage.addEventDropDownList;
        let eventAddValues = [emData.SFP_EVENT, emData.SFP_Activation, emData.SFP_Refresh];
        await expect(addEventDropDownValuesUI.length).toEqual(eventAddValues.length);
        let eventDescription = await createSfpEventPage.addEventDetails1('div', 'SFP Event', emData.div_Columbus_Operating_Division_016, emData.eventType_KOMPASS, "New Item Introduction (NII)", "stripErrordivUserDivEvent", emData.vendor_AZB)
        //await createSfpEventPage.clickShelfStripCheckBox(emData.Event_Strip_V);
        let fiscalWeekShelfStrip = await eventMaintenancePage.getFiscalWeekAndValue("When ordering Shelf Strips");
        let extractedWeekShelfStrip = fiscalWeekShelfStrip.match(/(\d+)/);
        await createSfpEventPage.submitEventDetails();
        let nextPage = await eventMaintenancePage.linkSubModule.getText();
        await expect(nextPage).toEqual(emData.Event_Schedule);
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout');
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await clickOn(await storeSelectionPage.saveSchedule);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await clickOn(await storeSelectionPage.goToAddEventDetail);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await createSfpEventPage.clickCheckBox(emData.checkBox_RequestShelfStrips);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        //await createSfpEventPage.clickShelfStripCheckBox(emData.Event_Strip_V);
        await (await eventMaintenancePage.stripErrorToolTipTrigger).waitForDisplayed({ timeout: 5000 });
        await eventMaintenancePage.stripErrorToolTipTrigger.moveTo();
        await eventMaintenancePage.waitBrowserToLoadPage(7000);
        //Wait for the tooltip to be displayed
        await (await eventMaintenancePage.tooltipElement).waitForDisplayed({ timeout: 5000 });
        //Verify the tooltip's text
        const tooltipText = (await eventMaintenancePage.tooltipElement).getText();
        await expect(await tooltipText).toBe(emData.stripErrorToolTipText);
        await createSfpEventPage.submitEventDetails();
        const stripErrorText = (await eventMaintenancePage.stripErrorElement).getText();
        const StripsInLineError = /Strips will not be delivered for this event\. Adjust the schedule to at least Week \d{1,2}, \d{4} to guarantee successful delivery of strips to the store\(s\)\./;
        await expect(StripsInLineError.test(await stripErrorText)).toBe(true);
        await expect((await stripErrorText).toString().includes(fiscalWeekShelfStrip)).toBe(true);
        await eventMaintenancePage.tooltipElementScheduling.moveTo();
        await eventMaintenancePage.waitBrowserToLoadPage(7000);
        await eventMaintenancePage.clickOn(eventSchedule.editScheduleList);
        await eventMaintenancePage.waitBrowserToLoadPage(7000);
        const scheduleInLineText = (await eventSchedule.stripScheduleEditErrorText).getText()
        const stripErrorInSchedulingEdit = /Adjust the schedule to at least Week \d{1,2}, \d{4} to guarantee successful delivery of strips to the store\(s\)\./;
        await expect(stripErrorInSchedulingEdit.test(await scheduleInLineText)).toBe(true);
        await eventMaintenancePage.clickCloseButton('Cancel');
        await storeSelectionPage.saveGoToStoreSelection()
        let div = (emData.div_Columbus_Operating_Division_016.match(/\d/g).join("")).toString()
        let dbScheduledStores = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, div).replace('@num', '30'))).flat();
        await storeSelectionPage.storeSelection(dbScheduledStores, 6, 1)
        await storeSelectionPage.saveStoreScheduleButton()
        await selectPOG.selectingPogCommodities('Department', emData.dept_01_GROCERY, 'Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER, 'Commodity *', emData.ResetComplete_All, '')
        await clickOn(selectPOGPage.selectChoosePogByCommodityBtn);
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await (await selectPOG.gotoEventListButton).waitForExist({ timeout: 90000 }, 'Error: EventList button not visible')
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        await clickOn(selectPOGPage.gotoEventListButton);
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        let eventCreationText = await eventMaintenancePage.eventCreationText.getText();
        await expect(eventCreationText).toBe(emData.Event_Creation);
        await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.filtersTxtBtn, emData.Filters);
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await (await eventMaintenancePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintenancePage.clickFiltersWithDiscp(eventDescription.toString());
        (await eventMaintenancePage.tooltipElementEventID).moveTo();
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        let initialEventId = await eventMaintenancePage.getHeaderValue(emData.Event_ID, eventDescription)
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        let eventIdInSlide = await (await eventMaintenancePage.getTextOfElement(eventMaintenancePage.slidePaneleventId));
        expect(await initialEventId).toBe(await eventMaintenancePage.extractDigits(eventIdInSlide));
        (await eventMaintenancePage.slideOutTooltip).moveTo()
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        let slideOUtInlineErrorText = (await eventMaintenancePage.slideOutInlineErrorText).getText()
        const slideOutInlineError = /Strips will not be delivered for one or more schedules in this event\. Remove the strips request or adjust the schedule\(s\) to at least Week \d{1,2}, \d{4} to guarantee successful delivery of strips to the store\(s\)\./;
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await expect(slideOutInlineError.test(await slideOUtInlineErrorText)).toBe(true);
        await clickOn(eventMaintenancePage.slideOutReleaseButton)
        let eventWarningText = (await eventMaintenancePage.releaseEventWarning).getText()
        expect(await eventWarningText).toBe(emData.releaseEventWarnning)
        await eventMaintenancePage.clickCloseButton('Edit Event');
        await createSfpEventPage.submitEventDetails();
        await createEventSchedulePage.clickDeleteEventBtn();
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await clickOn(await storeSelectionPage.goToAddEventDetail);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        const doesExist = await (await eventMaintenancePage.stripErrorToolTipTrigger).isExisting()
        expect(doesExist).toBe(false);
        await createSfpEventPage.submitEventDetails();
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await clickOn(await storeSelectionPage.saveSchedule);
        await storeSelectionPage.saveGoToStoreSelection()
        await storeSelectionPage.storeSelection(dbScheduledStores, 6, 1)
        await storeSelectionPage.saveStoreScheduleButton()
        await selectPOG.selectingPogCommodities('Department', emData.dept_01_GROCERY, 'Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER, 'Commodity *', emData.ResetComplete_All, '')
        await clickOn(selectPOGPage.selectChoosePogByCommodityBtn);
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await (await selectPOG.gotoEventListButton).waitForExist({ timeout: 90000 }, 'Error: EventList button not visible')
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        await clickOn(selectPOGPage.gotoEventListButton);
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.filtersTxtBtn, emData.Filters);
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await (await eventMaintenancePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintenancePage.clickFiltersWithDiscp(eventDescription.toString());
        let releaseEventEnabled1 = await eventMaintenancePage.selectReleaseEvents(emData.ReleaseEvent_Corporate, emData.Header_Action, eventDescription)
        await expect(releaseEventEnabled1).toEqual(true)
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.eventReleaseTextAlert, emData.ReleaseEventMsg)
        let releaseText = await (await (eventMaintenancePage.eventReleaseTextAlert)).getText()
        await expect(releaseText).toEqual(emData.ReleaseEventMsg)
    })
})
