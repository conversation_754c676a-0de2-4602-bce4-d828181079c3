const resetCompletionPage = require('../../EventManagerPO/resetCompletion.page.js')
let emData = require('../../../../TestData/EventManagerData/eventManagerUIData.json')
const envVariables = require("../../UserRoles/roles");
const ENV = process.env.ENV
const eventMaintenancePage = require('../../EventManagerPO/eventMaintanence.page');
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');

let div016 = emData.div_016_Columbus_Operating_Division.split(' - ')[0];
let div021 = emData.div_021_Central_Operating_Division.split(' - ')[0];
let div025 = emData.div_025_Delta_Operating_Division.split(' - ')[0];
let div16  = div016.substring(1);

before(async () => {

    await testSetup(envVariables.multipleDivisionalUserEM[ENV], envVariables.multipleDivisionalPasswordEM[ENV]);
})

describe('validateResetCompletionMultiDivisionUser:  validate Multi DivisionUsers div displays correctly ', () => {

    beforeEach(async () => {

         await resetCompletionPage.navigateWithHoverEventManager();
         await resetCompletionPage.waitBrowserToLoadPage(5500);
    })


    it('validate Commodity Recap (Clear filter): Division and location dropdowns disabled, table data with only correct div and verify a location ' , async () => {

        await expect(await resetCompletionPage.GetText(await resetCompletionPage.divisionDropDown)).toEqual(emData.Select_Division_text);
        await expect(await resetCompletionPage.GetText(await resetCompletionPage.selectLocationtext)).toEqual(emData.select_location_text);
        await expect((await resetCompletionPage.commodityRecapBtn.getAttribute("tabindex"))).toEqual("0");
        await expect((await resetCompletionPage.storeReview.getAttribute("tabindex"))).toEqual("-1");
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.divisionDropDown)).toEqual(false);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.selectLocationtext)).toEqual(false);
        await expect(await resetCompletionPage.resetCompletionDivisionDropDownMultiDivisionMatch([emData.ResetComplete_All, emData.div_016_Columbus_Operating_Division, emData.div_021_Central_Operating_Division, emData.div_025_Delta_Operating_Division])).toEqual(true);
        await resetCompletionPage.clickOn(resetCompletionPage.selectLocationtext);
        let storeList = await resetCompletionPage.resetCompletionLocatorList;
        let divisionMatch = true;
        for(let index = 1; index < storeList.length; index++){
            let divisionStore = await eventMaintenancePage.GetText(await storeList[index]);
            let division = divisionStore.toString().split(" - ")[0];
            let divisionMatch = division === div016 || division === div021 || division === div025;
            if(divisionMatch === false){
                break;
            }
        }
        await expect(divisionMatch).toEqual(true);
        await resetCompletionPage.clickOn(await resetCompletionPage.divisionDropDown);
        await resetCompletionPage.clickOn(await resetCompletionPage.divisionDropDownElements[1]);
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        await expect(await resetCompletionPage.commodityRecapDivisionColumnCompare(div016, div16)).toEqual(true);
        await resetCompletionPage.clickOn(resetCompletionPage.clearFiltersBtn);
        await resetCompletionPage.waitForLongSpinner(resetCompletionPage.loadingSpinnerRestCompletion);
        await expect(await resetCompletionPage.GetText(await resetCompletionPage.divisionDropDown)).toEqual(emData.Select_Division_text);
        await expect(await resetCompletionPage.GetText(await resetCompletionPage.selectLocationtext)).toEqual(emData.select_location_text);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.divisionDropDown)).toEqual(false);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.selectLocationtext)).toEqual(false);
        await expect(await resetCompletionPage.GetText(await resetCompletionPage.resetCompleteDropDown)).toEqual(emData.selectedCompletionType_UnSelected);
        await resetCompletionPage.clickOn(await resetCompletionPage.divisionDropDown);
        await resetCompletionPage.clickOn(await resetCompletionPage.divisionDropDownElements[1]);
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        for(let index = 0; index < 2; index++){
            await resetCompletionPage.clickOn(resetCompletionPage.commodityRecapTableHeaders[1]);
        }
        await expect(await resetCompletionPage.commodityRecapDivisionColumnCompare(div016, div16)).toEqual(true);
        await resetCompletionPage.clickOn(await resetCompletionPage.divisionDropDown);
        await resetCompletionPage.clickOn(await resetCompletionPage.divisionDropDownElements[0]);
        await resetCompletionPage.waitBrowserToLoadPage(3000);

    })

    it('validate restore review: store and division fields do not display other stores and divisions for Store Review tab ', async () => {

        await resetCompletionPage.clickOn(resetCompletionPage.storeReview);
        await resetCompletionPage.waitBrowserToLoadPage(4000);

        //adding Delete update cancel
        await expect(await resetCompletionPage.GetText(await resetCompletionPage.divisionDropDown)).toEqual(emData.Select_Division_text);
        await expect(await resetCompletionPage.GetText(await resetCompletionPage.selectLocationtext)).toEqual(emData.select_location_text);
        await expect((await resetCompletionPage.commodityRecapBtn.getAttribute("tabindex"))).toEqual("-1");
        await expect((await resetCompletionPage.storeReview.getAttribute("tabindex"))).toEqual("0");
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.selectLocationtext)).toEqual(false);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.divisionDropDown)).toEqual(false);
        await expect(await resetCompletionPage.resetCompletionDivisionDropDownMultiDivisionMatch([emData.ResetComplete_All, emData.div_016_Columbus_Operating_Division, emData.div_021_Central_Operating_Division, emData.div_025_Delta_Operating_Division])).toEqual(true);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.storeReviewCancelBtn)).toEqual(true);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.btnUpdate)).toEqual(true);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.storeReviewDeleteBtn)).toEqual(true);
        await resetCompletionPage.clickOn(resetCompletionPage.selectLocationtext);
        let storeList = await resetCompletionPage.resetCompletionLocatorList;
        let divisionMatch = true;
        for(let index = 1; index < storeList.length; index++){
            let divisionStore = await eventMaintenancePage.GetText(await storeList[index]);
            let division = divisionStore.toString().split(" - ")[0];
            let divisionMatch = division === div016 || division === div021 || division === div025;
            if(divisionMatch === false){
                break;
            }
        }
        await expect(divisionMatch).toEqual(true);
        // //TODO: bug ERSD-26450 - Store Review Tab - Excel column order does not match UI column order
        // //let storeReviewRecapTableData = await resetCompletionPage.getStoreReviewTableFirstPage();
        // //await resetCompletionPage.clickOn(resetCompletionPage.exportToExcelBtn);
        // //await eventMaintenancePage.waitBrowserToLoadPage(7000);
        // //let excelUiCompareBoolean = await excelReader.excelUiDataCompareNthRowCommodityRecap(storeReviewRecapTableData, 5);
        // //await expect(excelUiCompareBoolean).toEqual(true);
        await resetCompletionPage.clickOn(await resetCompletionPage.divisionDropDown);
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        await resetCompletionPage.clickOn(await resetCompletionPage.divisionDropDownElements[1]);
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        let resetIDDivStore = await resetCompletionPage.firstRowResetIDDivStore();
        let locationTable = resetIDDivStore.split("|")[2];
        await resetCompletionPage.clickOn(await resetCompletionPage.selectLocationtext);
        await resetCompletionPage.waitBrowserToLoadPage(6000);
        let resetCompletionLocations = await resetCompletionPage.resetCompletionLocatorList;
        let isLocationMatch = false;
        for(let index = 1; index < resetCompletionLocations.length; index++){
             let locationDropDown =  await resetCompletionPage.GetText(resetCompletionLocations[index]);
             let locationDropDownStore = locationDropDown.toString().split(" - ")[1];
             if(locationDropDownStore === locationTable ){
                 isLocationMatch = true;
                 break;
             }
         }
        await expect(isLocationMatch).toEqual(true);
        await expect(true).toEqual(await resetCompletionPage.storeReviewDivisionColumnCompare(div016, div16));
        for(let index = 0; index < 2; index++){
             await resetCompletionPage.clickOn(resetCompletionPage.tableHeaders[await resetCompletionPage.getTableHeaderIndex(emData.storeReviewTableName, emData.ResetComplete_StoreReview_Div)]);
        }
        await expect(await resetCompletionPage.storeReviewDivisionColumnCompare(div16, div016)).toEqual(true);
    })

})
