const resetCompletionPage = require("../../EventManagerPO/resetCompletion.page.js");
let emData = require('../../../../TestData/EventManagerData/eventManagerUIData.json')

let divisionDropdownList, departmentDropdownList;
describe('validateResetCompleteCommodityDropdownCascade: validate the Reset Completion "commodity" dropdown is cascaded ', () => {

    it('Validate "Commodity" dropdown displays values only corresponding to selected "Department" dropdown  ', async () => {
        await resetCompletionPage.waitBrowserToLoadPage(6000);
        await resetCompletionPage.open('EventMaintenance/resetCompletion');
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        await resetCompletionPage.waitForPageLoad(resetCompletionPage.searchBtn, emData.resetCompletion_Search);
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        divisionDropdownList = await resetCompletionPage.getResetCompleteDropdownList('selectedDivision', 'divisionMultiSelect');
        if(await divisionDropdownList.length>0){
            await resetCompletionPage.waitBrowserToLoadPage(3000);
            await resetCompletionPage.selectSingleValue('selectedDivision', 'scheduleType', 'divisionMultiSelect', await divisionDropdownList[1]);
            departmentDropdownList = await resetCompletionPage.getResetCompleteDropdownList('selectedDepartment', 'departmentList');
            for(let index=1; index<departmentDropdownList.length -19;index++){
                await resetCompletionPage.waitBrowserToLoadPage(3000);
                await resetCompletionPage.selectSingleValue('selectedDepartment', 'scheduleType', 'departmentList', await departmentDropdownList[index]);
                let commodityDropdownList = await resetCompletionPage.getResetCompleteDropdownList('selectedCommodity', 'commodityList');
                let finalCommodityList = []
                for(let commodityValue=0; commodityValue<commodityDropdownList.length; commodityValue++){
                    let textList = await commodityDropdownList[commodityValue];
                    let departValue = await resetCompletionPage.extractValueAfterPattern(await textList, 0," - ");
                    finalCommodityList.push(departValue)
                }
                await expect( await (await resetCompletionPage.getUniqueArrayValue(finalCommodityList)).toString()).toEqual(await departmentDropdownList[index]);
                await resetCompletionPage.clickResetCompleteDropdownCloseBtn('departmentList');
            }
        }
    })

})
