const resetCompletionPage = require('../../EventManagerPO/resetCompletion.page.js')
let emData = require('../../../../TestData/EventManagerData/eventManagerUIData.json')
const envVariables = require("../../UserRoles/roles");
const ENV = process.env.ENV
const eventMaintenancePage = require('../../EventManagerPO/eventMaintanence.page');
const {testSetup} = require('../../../../helpers/testSetupTeardown.js');
const {assert} = require("chai");
const {getExcelDataByColumnName} = require("../../../../util/excelReader");
const result = require("../../../../SQLConnection/server.js.ts");
const qrString = require("../../../../SQLConnection/queryStringEM");

let resetStatusDropdownList, resetIdAfterCompleted, resetIdOriginal;

before(async () => {

    await testSetup(envVariables.divisionalUserEM[ENV], envVariables.divisionalPasswordEM[ENV]);
})

describe('validateEditCompassActiveDropdownsForDivUser: validate resets are editable when COMPASS scheduleType and "Not Completed" and "No Response" ResetStatus values are selected', async () => {
    
    beforeEach(async () => {
    
        await resetCompletionPage.open('EventMaintenance/resetCompletion');
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        await resetCompletionPage.clickOn(await resetCompletionPage.storeReview);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.cancelResetBtn)).toEqual(true);
        await resetCompletionPage.selectSingleValue('selectedResetType', 'scheduleType', 'resetTypeList', 'KOMPASS');
        resetStatusDropdownList = await resetCompletionPage.getResetCompleteDropdownList('selectedCompletionType', 'resetCompleteList');
    })

    it('validate Edit ResetStatus and ResetStatusReason column dropdowns when ScheduleType "COMPASS" and ResetStatus "Not Completed" are selected : 1) validate default selected Reason column dropdown value can be changed to different value; ' +
        ' 2) validate default selected ResetStatus "Not Completed" change be changed to "Completed" and "No Response"; 3) validate resetId does not exist on UI and excel after resetStatus dropdown value is changed ', async () => {

        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await resetCompletionPage.selectSingleValue('selectedCompletionType', 'scheduleType', 'resetCompleteList', await resetStatusDropdownList[2]);
        await eventMaintenancePage.clickOn(resetCompletionPage.searchBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await resetCompletionPage.clickOn(await resetCompletionPage.editModeBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.saveChangesBtn)).toEqual(true);
        await expect(await resetCompletionPage.editModeMessage.getText()).toEqual(emData.editModeMessage);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await resetCompletionPage.updateReasonColumnDefaultSelectedDropdownValue('Type', 'Maintenance');
        if (await resetCompletionPage.changesSavedMessage.getText() !== null ) {
            await expect(await resetCompletionPage.validateChangeSavedMessage()).toEqual(true);
        } else {
            await assert.fail(0, 1, "Error: Type column does not have Maintenance data !");
        }
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        for(let round=0;round<2;round++){
            await eventMaintenancePage.waitBrowserToLoadPage(1000);
            await resetCompletionPage.clickOn(await resetCompletionPage.editModeBtn);
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            resetIdOriginal = await resetCompletionPage.getColumnArrayList('Reset Id');
            if(round === 0){
                 await resetCompletionPage.dynamicSelectResetStatusNotCompletedToCompleted( 'Type', 'Maintenance');
            } if(round === 1){
                 await resetCompletionPage.dynamicSelectResetStatusNotCompletedToNoResponse( 'Type', 'Maintenance');
            }
            await expect(await resetCompletionPage.validateChangeSavedMessage()).toEqual(true);
            resetIdAfterCompleted = await resetCompletionPage.getColumnArrayList('Reset Id');
            assert.notDeepEqual(await resetIdOriginal, await resetIdAfterCompleted, 'Two ResetId values are the same but they should not be !')
            let completedResetIdUINotHave = await resetCompletionPage.returnDifferentElementInFirstArray(resetIdOriginal, resetIdAfterCompleted);
            let updatedResetStatus = await (await result.getResult(qrString.resetCompletionStatusResetId.replace('@rec_id', await completedResetIdUINotHave))).flat();
            if( updatedResetStatus === 'Completed'){
                await expect(await updatedResetStatus.toString()).toEqual("Completed");
            }
            if( updatedResetStatus === 'Not Completed'){
                await expect(await updatedResetStatus.toString()).toEqual("No Response");
            }
            await resetCompletionPage.clickOn(await resetCompletionPage.exportToExcelBtnDivUser);
            await eventMaintenancePage.waitBrowserToLoadPage(8000);
            let resetIdCExcelColumnData = await getExcelDataByColumnName('Reset Id');
            await eventMaintenancePage.waitBrowserToLoadPage(5000);
            let completedResetIdExcelNotHave = await resetCompletionPage.returnDifferentElementInFirstArray(resetIdOriginal, resetIdCExcelColumnData);
            await expect(await completedResetIdUINotHave).toEqual(await completedResetIdExcelNotHave)
         }
    })

    it('validate Edit ResetStatusReason column dropdown values when ScheduleType COMPASS and ResetStatus "No Response" dropdown values are selected:' +
        ' 1) validate "No Response" can be changed to "Completed" and "Not Completed" status; 2) validate updated resets do not exist on the current UI table;' +
        '3) validate updated resets do not exist on downloaded excel; 4) validate updated statuses of resets using query ', async () => {

        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await resetCompletionPage.selectSingleValue('selectedCompletionType', 'scheduleType', 'resetCompleteList', await resetStatusDropdownList[3]);
        await eventMaintenancePage.clickOn(resetCompletionPage.searchBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await resetCompletionPage.clickOn(await resetCompletionPage.editModeBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.saveChangesBtn)).toEqual(true);
        await expect(await resetCompletionPage.editModeMessage.getText()).toEqual(emData.editModeMessage);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        let resetIdColumnListBefore = await resetCompletionPage.getColumnArrayList('Reset Id');
        await resetCompletionPage.dynamicSelectResetStatusDropdownKomNoResponseToNotCompleted('Type', 'Maintenance');
        await expect(await resetCompletionPage.validateChangeSavedMessage()).toEqual(true);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        let resetNotCompletedStatus = await (await result.getResult(qrString.resetCompletionStatusResetId.replace('@rec_id', await resetIdColumnListBefore[0]))).flat();
        await expect(resetNotCompletedStatus.toString()).toEqual("Not Completed");
        let resetIdColumnListAfter = await resetCompletionPage.getColumnArrayList('Reset Id');
        await assert.notDeepEqual(await resetIdColumnListBefore, await resetIdColumnListAfter, 'Two ResetId values are the same but they should not be !')
        await resetCompletionPage.clickOn(await resetCompletionPage.editModeBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await resetCompletionPage.dynamicSelectResetStatusDropdownKomNoResponseToCompleted('Type', 'Maintenance');
        await expect(await resetCompletionPage.validateChangeSavedMessage()).toEqual(true);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        let resetIdColumnListFinal = await resetCompletionPage.getColumnArrayList('Reset Id');
        await assert.notDeepEqual(resetIdColumnListBefore, resetIdColumnListFinal, 'Two ResetId values are the same but they should not be !')
        let resetCompletedStatus = await (await result.getResult(qrString.resetCompletionStatusResetId.replace('@rec_id', await resetIdColumnListAfter[0]))).flat();
        await expect(resetCompletedStatus.toString()).toEqual("Completed");
    })

    it('validate table ResetStatus dropdown is not initiated for Update and INN reset types when ScheduleType COMPASS and ResetStatus "Not Completed" dropdown values are selected ', async () => {

        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await resetCompletionPage.selectSingleValue('selectedCompletionType', 'scheduleType', 'resetCompleteList', await resetStatusDropdownList[2]);
        await eventMaintenancePage.clickOn(resetCompletionPage.searchBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await resetCompletionPage.clickOn(await resetCompletionPage.editModeBtn);
        await expect(await resetCompletionPage.editModeMessage.getText()).toEqual(emData.editModeMessage);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        let validateUpdateTypeInactive = await resetCompletionPage.validateInactiveResetStatusColumnValues('Type', 'Update');
        await expect(validateUpdateTypeInactive).toEqual(true);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        let validateNIITypeInactive = await resetCompletionPage.validateInactiveResetStatusColumnValues('Type', 'New Item Introduction');
        await expect(validateNIITypeInactive).toEqual(true);
    })
})
