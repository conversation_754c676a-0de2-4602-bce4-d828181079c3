const resetCompletionPage = require('../../EventManagerPO/resetCompletion.page.js')
const envVariables = require("../../UserRoles/roles");
const ENV = process.env.ENV
const eventMaintenancePage = require('../../EventManagerPO/eventMaintanence.page');
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const { assert } = require("chai");

let resetStatusDropdownList, firstRowCheckBoxDisabled;

before(async () => {

    await testSetup(envVariables.divisionalUserEM[ENV], envVariables.divisionalPasswordEM[ENV]);
})

describe('validateCheckboxesInactiveForUnCancellableResetsDivUser:  validate inactive row checkboxes for DivisionalUser ', () => {

    it('validate row checkboxes on the first page are not clickable when ScheduleType (NON-KOMPASS and COMPASS ) and ResetStatus dropdown values (Completed, Cancelled and Not Completed) are selected ', async () => {

        await resetCompletionPage.open('EventMaintenance/resetCompletion');
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        await resetCompletionPage.waitForPageLoad(resetCompletionPage.searchBtn, 'Search');
        await resetCompletionPage.clickOn(await resetCompletionPage.storeReview);
        await eventMaintenancePage.waitBrowserToLoadPage(5000);
        let scheduleTypeDropdownList = await resetCompletionPage.getResetCompleteDropdownList('selectedResetType', 'resetTypeList');
        for (let index = 0; index < await scheduleTypeDropdownList.length - 1; index++) {
            await resetCompletionPage.selectSingleValue('selectedResetType', 'scheduleType', 'resetTypeList', await scheduleTypeDropdownList[index]);
            resetStatusDropdownList = await resetCompletionPage.getResetCompleteDropdownList('selectedCompletionType', 'resetCompleteList');
            for (let resetStatusValue = 0; resetStatusValue < resetStatusDropdownList.length - 2; resetStatusValue++) {
                await resetCompletionPage.selectSingleValue('selectedCompletionType', 'scheduleType', 'resetCompleteList', await resetStatusDropdownList[resetStatusValue]);
                await eventMaintenancePage.waitBrowserToLoadPage(3000);
                await eventMaintenancePage.clickOn(resetCompletionPage.searchBtn);
                await eventMaintenancePage.waitBrowserToLoadPage(2000);
                // headerCheckBox locator issue:  headerCheckBoxDisabled = await resetCompletionPage.locatorDisabled(await resetCompletionPage.headerCheckBoxStoreReview);
                firstRowCheckBoxDisabled = await resetCompletionPage.locatorDisabled(await resetCompletionPage.firstRowCheckBoxStoreReview);
                await eventMaintenancePage.waitBrowserToLoadPage(3000);
                if (await firstRowCheckBoxDisabled === true) {
                    await eventMaintenancePage.waitBrowserToLoadPage(3000);
                    let storeReviewCheckBoxList = await resetCompletionPage.storeReviewCheckBoxList;
                    for (let CheckBox = 0; CheckBox < storeReviewCheckBoxList.length; CheckBox++) {
                        let headerCheckBoxDisabled = await resetCompletionPage.locatorDisabled(await storeReviewCheckBoxList[CheckBox]);
                        await eventMaintenancePage.waitBrowserToLoadPage(3000);
                        await expect(headerCheckBoxDisabled).toEqual(true);
                    }
                } else {
                    await assert.fail(0, 1, "Error: Something wrong with row checkboxes; they should not be checked !")
                }
                await eventMaintenancePage.waitBrowserToLoadPage(2000);
                await resetCompletionPage.clickResetCompleteDropdownCloseBtn('resetCompleteList')
                await eventMaintenancePage.waitBrowserToLoadPage(2000);
            }
        }
    })
})
