const resetCompletionPage = require('../../EventManagerPO/resetCompletion.page.js')
let emData = require('../../../../TestData/EventManagerData/eventManagerUIData.json')
const envVariables = require("../../UserRoles/roles");
const ENV = process.env.ENV
const eventMaintenancePage = require('../../EventManagerPO/eventMaintanence.page');
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const {assert} = require("chai");
const result = require("../../../../SQLConnection/server.js.ts");
const qrString = require("../../../../SQLConnection/queryStringEM");

let resetStatusDropdownList;

before(async () => {
    await testSetup(envVariables.divisionalUserEM[ENV], envVariables.divisionalPasswordEM[ENV]);
})

describe('validateEditRemodelScheduleTypeForDivUser:  validate resets are editable or not when REMODEL schedule type combined with various ResetStatuses ', () => {
    
    beforeEach(async () => {

        await resetCompletionPage.open('EventMaintenance/resetCompletion');
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        await resetCompletionPage.clickOn(await resetCompletionPage.storeReview);
        await eventMaintenancePage.waitBrowserToLoadPage(5000);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.cancelResetBtn)).toEqual(true);
        await resetCompletionPage.selectSingleValue('selectedResetType', 'scheduleType', 'resetTypeList', 'Remodel');
        resetStatusDropdownList = await resetCompletionPage.getResetCompleteDropdownList('selectedCompletionType', 'resetCompleteList');
    })

    it('validate Edit Mode button when "Remodel" Schedule Type along with "Completed", "Canceled" and "Locked" ResetStatus dropdown values are selected:' +
        '1) validate all of those ResetStatus options should NOT be activate (do not initiate dropdown) ResetStatus dropdown from StoreReview table; ' +
        '2) validate all of those ResetStatus options filter table data according to selected ResetStatus option; ' , async () => {

        const  resetStatusListToBeTested = ['Completed', 'Canceled', 'Locked'];
        const selectedTextList = await resetCompletionPage.extractMatchingValuesFromArray(await resetStatusDropdownList, resetStatusListToBeTested);
        for(let index=0; index < await selectedTextList.length; index++){
            if(await selectedTextList.length === 3){
                await eventMaintenancePage.waitBrowserToLoadPage(3000);
                await resetCompletionPage.selectSingleValue('selectedCompletionType', 'scheduleType', 'resetCompleteList',  await selectedTextList[index]);
                await eventMaintenancePage.clickOn(resetCompletionPage.searchBtn);
                await eventMaintenancePage.waitBrowserToLoadPage(3000);
                await resetCompletionPage.clickOn(await resetCompletionPage.editModeBtn);
                await eventMaintenancePage.waitBrowserToLoadPage(3000);
                await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.saveChangesBtn)).toEqual(true);
                await expect(await resetCompletionPage.editModeMessage.getText()).toEqual(emData.editModeMessage);
                let resetStatusColumnList = await resetCompletionPage.getColumnArrayList('Reset Status');
                if (resetStatusColumnList.length > 0) {
                let resetStatusColumnListUnique = await (await resetCompletionPage.getUniqueArrayValue(await resetStatusColumnList)).toString();
                await expect(await resetStatusColumnListUnique).toEqual(await selectedTextList[index]);
                if(await resetStatusColumnListUnique === 'Completed' || await resetStatusColumnListUnique === 'Canceled' || await resetStatusColumnListUnique === 'Locked') {
                    await eventMaintenancePage.waitBrowserToLoadPage(2000);
                    let resetStatusColumnList = await resetCompletionPage.returnStoreReviewColumnIndex('Reset Status', 1);
                    for(let listValue=0; listValue< await resetStatusColumnList.length; listValue++){
                        let elementClickable = await resetCompletionPage.resetCompleteElementIsClickable(await resetStatusColumnList[listValue]);
                        await expect(elementClickable).toEqual(false);
                    }
                    await resetCompletionPage.clickOn(await resetCompletionPage.clearChangesBtn);
                    await resetCompletionPage.clickResetCompleteDropdownCloseBtn('resetCompleteList');
                    await eventMaintenancePage.waitBrowserToLoadPage(2000);
                } else{
                    await assert.fail(0, 1, "Error: ResetStatus column list should be unique !");
                }
                }else{
                    await eventMaintenancePage.clickOn(resetCompletionPage.searchBtn);
                    await eventMaintenancePage.waitBrowserToLoadPage(1000);
                    let zeroRecordsMessage = await resetCompletionPage.zeroRecordsMessage.getText();
                    await expect(zeroRecordsMessage).toEqual(emData.zero_records_found);
                    await resetCompletionPage.clickResetCompleteDropdownCloseBtn('resetCompleteList');
                    await eventMaintenancePage.waitBrowserToLoadPage(2000);
                    console.error('Error: Table does not have data for ResetStatus ' + selectedTextList[index])
                }
            }else{
                await assert.fail(0, 1, "Error: Something wrong with new array values. There should be 3 values in the list !");
            }
        }
    })

    it('validate Edit Mode button when "Remodel" schedule type and  "Not Completed" ResetStatus  are selected:' +
        'validate StoreReview ResetStatus default selected "Not Completed" value can be changed to : 1) Completed ; 2) No Response;' +
        '3) validate updated statuses of ResetStatus using query; 4) validate resetIds of updated statuses does not exist on the current table;' , async () => {

        let resetStatusNotCompleted = resetStatusDropdownList[2];
        await resetCompletionPage.selectSingleValue('selectedCompletionType', 'scheduleType', 'resetCompleteList', resetStatusNotCompleted);
        await eventMaintenancePage.clickOn(resetCompletionPage.searchBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        let resetStatusColumnListUniqueBefore = await resetCompletionPage.getUniqueUnsortedArrayValue(await resetCompletionPage.getColumnArrayList('Reset Status'));
        await expect(resetStatusColumnListUniqueBefore.toString()).toEqual(await resetStatusNotCompleted)
        await resetCompletionPage.clickOn(await resetCompletionPage.editModeBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.saveChangesBtn)).toEqual(true);
        await expect(await resetCompletionPage.editModeMessage.getText()).toEqual(emData.editModeMessage);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        let resetStatusList = await resetCompletionPage.getStoreReviewTableDropdownListText(await resetCompletionPage.resetStatusColumnFirstChevron, 'Reset Status');
        await eventMaintenancePage.waitBrowserToLoadPage(5000);
        let storeReviewResetStatusDropdownListClean = await resetCompletionPage.removeElementByText(resetStatusList, 'Not Completed');
        for(let resetStatusValue=0; resetStatusValue< await storeReviewResetStatusDropdownListClean.length; resetStatusValue++){
            await eventMaintenancePage.clickOn(resetCompletionPage.searchBtn);
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            if(await resetCompletionPage.elementIsClickable(await resetCompletionPage.editModeBtn)) {
                await eventMaintenancePage.waitBrowserToLoadPage(3000);
                await resetCompletionPage.clickOn(await resetCompletionPage.editModeBtn);
                await eventMaintenancePage.waitBrowserToLoadPage(2000);
            }
            let editModeConfirmationMessage = await resetCompletionPage.getTextOfElement(await resetCompletionPage.editModeMessage)
            await expect(await editModeConfirmationMessage).toEqual(emData.editModeMessage);
            let clickCompleted = await resetCompletionPage.selectStoreReviewResetStatusDropdown(await storeReviewResetStatusDropdownListClean[resetStatusValue]);
            await expect(await clickCompleted).toEqual(true);
            let firstRowResetIdBefore = await resetCompletionPage.getColumnArrayList('Reset Id');
            let firstRowResetStatus = await resetCompletionPage.getColumnArrayList('Reset Status Reason', 1);
            if(await firstRowResetStatus[resetStatusValue] === 'Reset Completed'){
                await expect(await firstRowResetStatus[0]).toEqual('Reset Completed');
                await eventMaintenancePage.waitBrowserToLoadPage(3000);
                await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.saveChangesBtn)).toEqual(false);
                await resetCompletionPage.saveChangesBtn.click();
                await eventMaintenancePage.waitBrowserToLoadPage(3000);
                let resetCompletionDeleteStatus = await (await result.getResult(qrString.resetCompletionStatusResetId.replace('@rec_id', await firstRowResetIdBefore[0]))).flat();
                await expect(await resetCompletionDeleteStatus.toString()).toEqual("Completed");
                let firstRowResetIdAfter = await resetCompletionPage.getColumnArrayList('Reset Id');
                await assert.notDeepEqual(await firstRowResetIdBefore[0], await firstRowResetIdAfter[0], 'Two ResetId values are the same but they should not be !')
            }
            if(await firstRowResetStatus[resetStatusValue] === 'Available for reset'){
                await expect(await firstRowResetStatus[0]).toEqual('Available for reset');
                let firstRowResetIdAfter = await resetCompletionPage.getColumnArrayList('Reset Id');
                await eventMaintenancePage.waitBrowserToLoadPage(3000);
                await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.saveChangesBtn)).toEqual(false);
                await resetCompletionPage.saveChangesBtn.click();
                await eventMaintenancePage.waitBrowserToLoadPage(3000);
                let resetCompletionDeleteStatus = await (await result.getResult(qrString.resetCompletionStatusResetId.replace('@rec_id', await firstRowResetIdAfter[0]))).flat();
                await expect(await resetCompletionDeleteStatus.toString()).toEqual("No Response");
                let firstRowResetIdFinal = await resetCompletionPage.getColumnArrayList('Reset Id');
                await assert.notDeepEqual(await firstRowResetIdBefore[0], await firstRowResetIdFinal[0], 'Two ResetId values are the same but they should not be !')
            }
        }
    })

    it('validate StoreReview ResetStatusReason column default selected value can be updated with random reason dropdown value except the default value; '  , async () => {

        let resetStatusNotCompleted = resetStatusDropdownList[2];
        await resetCompletionPage.selectSingleValue('selectedCompletionType', 'scheduleType', 'resetCompleteList', resetStatusNotCompleted);
        await eventMaintenancePage.clickOn(resetCompletionPage.searchBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        let resetStatusColumnListUniqueBefore = await resetCompletionPage.getUniqueUnsortedArrayValue(await resetCompletionPage.getColumnArrayList('Reset Status'));
        await expect(resetStatusColumnListUniqueBefore.toString()).toEqual(await resetStatusNotCompleted)
        await resetCompletionPage.clickOn(await resetCompletionPage.editModeBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.saveChangesBtn)).toEqual(true);
        await expect(await resetCompletionPage.editModeMessage.getText()).toEqual(emData.editModeMessage);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        let reasonDropdownValueBefore = await resetCompletionPage.getColumnArrayList('Reset Status Reason');
        await resetCompletionPage.updateReasonColumnDefaultSelectedDropdownValue('Type', 'Remodel');
        if (await resetCompletionPage.changesSavedMessage.getText() !== null ) {
            await expect(await resetCompletionPage.validateChangeSavedMessage()).toEqual(true);
        } else {
            await assert.fail(0, 1, "Error: Type column does not have Remodel data !");
        }
        await resetCompletionPage.saveChangesBtn.click();
        await expect(await resetCompletionPage.validateChangeSavedMessage()).toEqual(true);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        let reasonDropdownValueAfter = await resetCompletionPage.getColumnArrayList('Reset Status Reason');
        await assert.notDeepEqual(await reasonDropdownValueBefore[0], await reasonDropdownValueAfter[0], 'Two ResetId values are the same but they should not be !')
    })

    it('validate Edit Mode button when "Remodel" schedule type and  "No Response" ResetStatus  are selected:' +
        '1) validate "No Response" can be changed to "Completed"; 2) validate "No Response" can be changed to "Not Completed", then select Reason column dropdown;' +
        '3) validate resetIds of updated resetStatus do not exist on the current table;' , async () => {

        await resetCompletionPage.selectSingleValue('selectedCompletionType', 'scheduleType', 'resetCompleteList',  await resetStatusDropdownList[3]);
        await eventMaintenancePage.clickOn(resetCompletionPage.searchBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        let resetStatusColumnListUniqueBefore = await resetCompletionPage.getUniqueUnsortedArrayValue(await resetCompletionPage.getColumnArrayList('Reset Status'));
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await expect(resetStatusColumnListUniqueBefore.toString()).toEqual(await resetStatusDropdownList[3])
        let firstRowResetIdBefore = await resetCompletionPage.getColumnArrayList('Reset Id');
        await resetCompletionPage.clickOn(await resetCompletionPage.editModeBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(1000);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.saveChangesBtn)).toEqual(true);
        await expect(await resetCompletionPage.editModeMessage.getText()).toEqual(emData.editModeMessage);
        let storeReviewResetStatusDropdownList = await resetCompletionPage.getStoreReviewTableDropdownListText(await resetCompletionPage.resetStatusColumnFirstChevron, 'Reset Status');
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await resetCompletionPage.resetStatusColumnFirstChevron.click();
        let storeReviewResetStatusDropdownListClean = await resetCompletionPage.removeElementByText(storeReviewResetStatusDropdownList, resetStatusColumnListUniqueBefore.toString());
        if(await storeReviewResetStatusDropdownListClean[0]){
            let clickCompleted = await resetCompletionPage.selectStoreReviewResetStatusDropdown( await storeReviewResetStatusDropdownListClean[0]);
            await expect(clickCompleted).toEqual(true);
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.saveChangesBtn)).toEqual(false);
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            let firstRowResetStatus = await resetCompletionPage.getColumnArrayList('Reset Status Reason', 1);
            await expect(firstRowResetStatus[0]).toEqual('Reset Completed');
            await resetCompletionPage.saveChangesBtn.click();
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            await expect(await resetCompletionPage.validateChangeSavedMessage()).toEqual(true);
            await eventMaintenancePage.waitBrowserToLoadPage(3000);
            let firstRowResetIdAfter = await resetCompletionPage.getColumnArrayList('Reset Id',1);
            await assert.notDeepEqual(await firstRowResetIdBefore[0], await firstRowResetIdAfter[0], 'Two ResetId values are the same but they should not be !')
        }
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        if(await storeReviewResetStatusDropdownListClean[1]){
            await resetCompletionPage.clickOn(await resetCompletionPage.editModeBtn);
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.saveChangesBtn)).toEqual(true);
            await expect(await resetCompletionPage.editModeMessage.getText()).toEqual(emData.editModeMessage);
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            let clickNotCompleted = await resetCompletionPage.selectStoreReviewResetStatusDropdown(await storeReviewResetStatusDropdownListClean[1]);
            await expect(clickNotCompleted).toEqual(true);
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            let firstRowResetStatusReason = await resetCompletionPage.resetStatusReasonColumnFirstActiveChevron.getText();
            await expect(firstRowResetStatusReason).toEqual(emData.selectOneMessage);
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            let completedSelected = await resetCompletionPage.selectStoreReviewReasonDropdown(emData.storeManagerPostponedOption);
            await expect(completedSelected).toEqual(true);
            await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.saveChangesBtn)).toEqual(false);
            await resetCompletionPage.saveChangesBtn.click();
            await expect(await resetCompletionPage.validateChangeSavedMessage()).toEqual(true);
            await eventMaintenancePage.waitBrowserToLoadPage(3000);
            let firstRowResetIdAfter = await resetCompletionPage.getColumnArrayList('Reset Id',1);
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            await assert.notDeepEqual(await firstRowResetIdBefore[0], await firstRowResetIdAfter[0], 'Two ResetId values are the same but they should not be !')
        }
    })
})
