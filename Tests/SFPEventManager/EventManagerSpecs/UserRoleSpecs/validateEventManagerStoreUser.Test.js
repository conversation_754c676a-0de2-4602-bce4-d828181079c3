const resetCompletionPage = require('../../EventManagerPO/resetCompletion.page.js')
let emData = require('../../../../TestData/EventManagerData/eventManagerUIData.json')
const envVariables = require("../../UserRoles/roles");
const ENV = process.env.ENV
const eventMaintenancePage = require('../../EventManagerPO/eventMaintanence.page');
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const properties = require("../../../../ConfigFiles/wdio.conf");
const HomePage = require("../../../../GlobalObjectRepository/home.page");

let div14 = emData.division_Cincinnati_Operating_Division_014.split(' - ')[0];
let corpDiv = emData.div_Corporate_060.split(" - ")[1];

before(async () => {

    await testSetup(envVariables.storeUserEM[ENV], envVariables.storePasswordEM[ENV]);
})

describe('validateEventManagerStoreUser: Validate SFP Event Manager page has restrictions for store user role ', () => {
    
    beforeEach(async () => {

        await expect(browser).toHaveUrlContaining(properties.config.baseUrl);
        await expect(browser).toHaveTitle(emData.homeTitle);
        await resetCompletionPage.open(emData.EventMaintanceUrl);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await expect(await eventMaintenancePage.GetText(eventMaintenancePage.emHomePageText)).toEqual(emData.ApplicationsSubmenuSFPEventManager);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
    })

    it('validate Division and store dropdowns disabled. validate divisions only shows 060 and store user division ', async () => {

        await expect(await resetCompletionPage.searchAttribute(await eventMaintenancePage.addEventDropdown, emData.attributeDisabled)).toEqual(true);
        await eventMaintenancePage.clickOn(eventMaintenancePage.filtersBtn);
        await expect(await resetCompletionPage.searchAttribute(await eventMaintenancePage.divisionDropDown, emData.attributeDisabled)).toEqual(true);
        await expect(await resetCompletionPage.searchAttribute(await eventMaintenancePage.storeDropDown, emData.attributeDisabled)).toEqual(true);
        await expect(await eventMaintenancePage.GetText(await eventMaintenancePage.divisionDropDown)).toEqual(emData.division_Cincinnati_Operating_Division_014);
        await expect(await eventMaintenancePage.GetText(await eventMaintenancePage.storeDropDown)).toEqual(emData.one_item_selected);
        await eventMaintenancePage.clickOn(eventMaintenancePage.filtersBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        let divColumnBeforeClick = await eventMaintenancePage.tableColumnByIndex(await eventMaintenancePage.getEventManagerTableHeaderIndex("EventManager", "Division") + 1);
        for(let index = 0; index < divColumnBeforeClick.length; index++){
            await expect(await eventMaintenancePage.GetText(await divColumnBeforeClick[index]) === corpDiv || await eventMaintenancePage.GetText(await divColumnBeforeClick[index]) === div14).toEqual(true);
        }
        await eventMaintenancePage.clickOn(await (eventMaintenancePage.tableHeaders)[emData.Number_3]);
        await eventMaintenancePage.waitBrowserToLoadPage(5000);
        let divColumnAfterClick = await eventMaintenancePage.tableColumnByIndex(await eventMaintenancePage.getEventManagerTableHeaderIndex("EventManager", "Division") + 1);
        for(let index = 0; index < divColumnAfterClick.length; index++){
            await expect(await eventMaintenancePage.GetText(await divColumnAfterClick[index]) === corpDiv || await eventMaintenancePage.GetText(await divColumnAfterClick[index]) === div14).toEqual(true);
        }
    })

    it('validate (clear filter) when clear filter is used divisions and stores are not in table ', async () => {

        await eventMaintenancePage.clickOn(eventMaintenancePage.filtersBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(6000);
        let paginationLabelBefore = await eventMaintenancePage.GetText( await eventMaintenancePage.paginationLabel);
        let paginationLabelBeforeElements = paginationLabelBefore.toString().split(" ");
        await eventMaintenancePage.clickOn(eventMaintenancePage.clearAllFiltersBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(6000);
        await expect(await eventMaintenancePage.GetText(await eventMaintenancePage.storeDropDown)).toEqual(emData.one_item_selected);
        await expect(await eventMaintenancePage.GetText(await eventMaintenancePage.divisionDropDown)).toEqual(emData.division_Cincinnati_Operating_Division_014);
        await expect(await resetCompletionPage.searchAttribute(await eventMaintenancePage.divisionDropDown, emData.attributeDisabled)).toEqual(true);
        await expect(await resetCompletionPage.searchAttribute(await eventMaintenancePage.storeDropDown, emData.attributeDisabled)).toEqual(true);
        await expect(await eventMaintenancePage.GetText(await eventMaintenancePage.addEventDropdown)).toEqual(emData.Select_Event_Type);
        let paginationLabelAfter = await eventMaintenancePage.GetText( await eventMaintenancePage.paginationLabel);
        let paginationLabelAfterElements = paginationLabelAfter.toString().split(" ");
        await expect(paginationLabelBeforeElements[5]).toEqual(paginationLabelAfterElements[5]);
    })

})
