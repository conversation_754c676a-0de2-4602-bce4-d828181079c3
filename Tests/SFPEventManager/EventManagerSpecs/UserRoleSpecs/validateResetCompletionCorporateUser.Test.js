const resetCompletionPage = require('../../EventManagerPO/resetCompletion.page.js')
let emData = require('../../../../TestData/EventManagerData/eventManagerUIData.json')
const envVariables = require("../../UserRoles/roles");
const ENV = process.env.ENV
const eventMaintenancePage = require('../../EventManagerPO/eventMaintanence.page');
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const HomePage = require("../../../../GlobalObjectRepository/home.page");


let div014 = emData.division_Cincinnati_Operating_Division_014.split(' - ')[0];
let div14  = div014.substring(1);
let div14Index = 2;

before(async () => {

    await testSetup(envVariables.CorpUserEM[ENV], envVariables.CorpPasswordEM[ENV]);
})

describe('validateResetCompletionCorporateUser:  validate Multi DivisionUsers div displays correctly ', () => {
    
    beforeEach(async () => {

        await resetCompletionPage.navigateWithHoverEventManager();
        await resetCompletionPage.waitBrowserToLoadPage(5600);
        await expect(await HomePage.dropdownSingleItem('Applications')).toEqual(true);
        await expect( (await resetCompletionPage.GetText(await resetCompletionPage.linkSubModule)).toString().trim() === emData.Admin_reset_completion ).toEqual(true);
        await resetCompletionPage.waitBrowserToLoadPage(5500);
    })

    it('Corporate User: validate Commodity Recap (Clear filter): Division and location dropdowns disabled, table data with only correct div and verify a location ' , async () => {

        await expect(await resetCompletionPage.GetText(await resetCompletionPage.divisionDropDown)).toEqual(emData.Select_Division_text);
        await expect(await resetCompletionPage.GetText(await resetCompletionPage.selectLocationtext)).toEqual(emData.select_location_text);
        await expect((await resetCompletionPage.commodityRecapBtn.getAttribute("tabindex"))).toEqual("0");
        await expect((await resetCompletionPage.storeReview.getAttribute("tabindex"))).toEqual("-1");
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.divisionDropDown)).toEqual(false);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.selectLocationtext)).toEqual(false);
        await eventMaintenancePage.clickOn(await resetCompletionPage.divisionDropDown);
        let divisionList = await resetCompletionPage.divisionDropDownElements;
        await expect(divisionList.length > 20).toEqual(true);
        await resetCompletionPage.clickOn(resetCompletionPage.selectLocationtext);
        let storeList = await resetCompletionPage.resetCompletionLocatorList;
        await expect(storeList.length > 2200).toEqual(true);
        await resetCompletionPage.clickOn(await resetCompletionPage.divisionDropDown);
        await resetCompletionPage.clickOn(await resetCompletionPage.divisionDropDownElements[div14Index]);
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        await expect(await resetCompletionPage.commodityRecapDivisionColumnCompare(div014, div14)).toEqual(true);
        await resetCompletionPage.clickOn(resetCompletionPage.clearFiltersBtn);
        await resetCompletionPage.waitForLongSpinner(resetCompletionPage.loadingSpinnerRestCompletion);
        await expect(await resetCompletionPage.GetText(await resetCompletionPage.divisionDropDown)).toEqual(emData.Select_Division_text);
        await expect(await resetCompletionPage.GetText(await resetCompletionPage.selectLocationtext)).toEqual(emData.select_location_text);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.divisionDropDown)).toEqual(false);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.selectLocationtext)).toEqual(false);
        await expect(await resetCompletionPage.GetText(await resetCompletionPage.resetCompleteDropDown)).toEqual(emData.selectedCompletionType_UnSelected);
        await resetCompletionPage.clickOn(await resetCompletionPage.divisionDropDown);
        await resetCompletionPage.clickOn(await resetCompletionPage.divisionDropDownElements[div14Index]);
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        for(let index = 0; index < 2; index++){
            await resetCompletionPage.clickOn(resetCompletionPage.commodityRecapTableHeaders[div14Index]);
        }
        await expect(await resetCompletionPage.commodityRecapDivisionColumnCompare(div014, div14)).toEqual(true);
        await resetCompletionPage.clickOn(await resetCompletionPage.divisionDropDown);
        await resetCompletionPage.clickOn(await resetCompletionPage.divisionDropDownElements[0]);
        await resetCompletionPage.waitBrowserToLoadPage(3000);

    })

    it('Corporate User: restore review: store and division fields do not display other stores and divisions for Store Review tab ', async () => {

        await resetCompletionPage.clickOn(resetCompletionPage.storeReview);
        await resetCompletionPage.waitBrowserToLoadPage(4000);
        await expect(await resetCompletionPage.GetText(await resetCompletionPage.divisionDropDown)).toEqual(emData.Select_Division_text);
        await expect(await resetCompletionPage.GetText(await resetCompletionPage.selectLocationtext)).toEqual(emData.select_location_text);
        await expect((await resetCompletionPage.commodityRecapBtn.getAttribute("tabindex"))).toEqual("-1");
        await expect((await resetCompletionPage.storeReview.getAttribute("tabindex"))).toEqual("0");
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.selectLocationtext)).toEqual(false);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.divisionDropDown)).toEqual(false);
        await eventMaintenancePage.clickOn(await resetCompletionPage.divisionDropDown);
        let divisionList = await resetCompletionPage.divisionDropDownElements;
        await expect(divisionList.length > 20).toEqual(true);
        await resetCompletionPage.clickOn(resetCompletionPage.selectLocationtext);
        let storeList = await resetCompletionPage.resetCompletionLocatorList;
        await expect(storeList.length > 2200).toEqual(true);
        await resetCompletionPage.clickOn(await resetCompletionPage.divisionDropDown);
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        await resetCompletionPage.clickOn(await resetCompletionPage.divisionDropDownElements[div14Index]);
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        let resetIDDivStore = await resetCompletionPage.firstRowResetIDDivStore();
        await resetCompletionPage.clickOn(await resetCompletionPage.selectLocationtext);
        await resetCompletionPage.waitBrowserToLoadPage(6000);
        await expect(true).toEqual(await resetCompletionPage.storeReviewDivisionColumnCompare(div014, div14));
        for(let index = 0; index < 2; index++){
             await resetCompletionPage.clickOn(resetCompletionPage.tableHeaders[await resetCompletionPage.getTableHeaderIndex(emData.storeReviewTableName, emData.ResetComplete_StoreReview_Div)]);
        }
        await expect(await resetCompletionPage.storeReviewDivisionColumnCompare(div14, div014)).toEqual(true);
    })

})
