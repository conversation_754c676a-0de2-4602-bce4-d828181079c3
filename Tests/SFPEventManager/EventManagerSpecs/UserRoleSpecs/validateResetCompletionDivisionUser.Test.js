const resetCompletionPage = require('../../EventManagerPO/resetCompletion.page.js')
let emData = require('../../../../TestData/EventManagerData/eventManagerUIData.json')
const envVariables = require("../../UserRoles/roles");
const ENV = process.env.ENV
const eventMaintenancePage = require('../../EventManagerPO/eventMaintanence.page');
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const HomePage = require("../../../../GlobalObjectRepository/home.page");
const excelReader = require("../../../../util/excelReader");
const autoActivationReport = require('../../EventManagerPO/autoActivationReport.page.js')
const storeSelectionPage = require('../../EventManagerPO/storeSelection.page.js');

let div016 = emData.div_016_Columbus_Operating_Division.split(' - ')[0];
let div16  = div016.substring(1);
let divisionDropDownText;

before(async () => {

    await testSetup(envVariables.divisionalUserEM[ENV], envVariables.divisionalPasswordEM[ENV]);
})

describe('validateResetCompletionDivisionUser:  validate DivisionUsers div displays correctly ', () => {
    
    beforeEach(async () => {
        await resetCompletionPage.waitBrowserToLoadPage(5500);
        await resetCompletionPage.navigateWithHoverEventManager();
        await resetCompletionPage.waitBrowserToLoadPage(5500);
        await expect(await HomePage.dropdownSingleItem('Applications')).toEqual(true);
        await expect( (await resetCompletionPage.GetText(await resetCompletionPage.linkSubModule)).toString().trim() === emData.Admin_reset_completion ).toEqual(true);
        await resetCompletionPage.waitBrowserToLoadPage(3000);
    })


    it('validate Commodity Recap (Clear filter): Division and location dropdowns disabled, table data with only correct div and verify a location ' , async () => {

        await eventMaintenancePage.waitBrowserToLoadPage(3000);
         divisionDropDownText = await resetCompletionPage.GetText(await resetCompletionPage.divisionDropDown);
await expect(["1 Item Selected", "Select Division"]).toContain(divisionDropDownText);

        //await expect(await resetCompletionPage.GetText(await resetCompletionPage.divisionDropDown)).toEqual('Select Division');
        await expect((await resetCompletionPage.commodityRecapBtn.getAttribute("tabindex"))).toEqual("0");
        await expect((await resetCompletionPage.storeReview.getAttribute("tabindex"))).toEqual("-1");
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.divisionDropDown)).toEqual(true);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.selectLocationtext)).toEqual(false);
        await autoActivationReport.selectYPW(emData.Period_Input, emData.Period_Empty);
        await storeSelectionPage.clickOn(resetCompletionPage.searchBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(7000);
        await expect(await resetCompletionPage.commodityRecapDivisionColumnCompare(div016, div16)).toEqual(true);
        for(let index = 0; index < 2; index++){
            await resetCompletionPage.clickOn(resetCompletionPage.commodityRecapTableHeaders[1]);
        }
        await expect(await resetCompletionPage.commodityRecapDivisionColumnCompare(div016, div16)).toEqual(true);
        let commodityRecapTableData = await resetCompletionPage.getCommodityRecapTableFirstPage();
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        await resetCompletionPage.clickOn(await resetCompletionPage.exportToExcelBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        let excelUiCompareBoolean = await excelReader.excelUiDataCompareNthRowCommodityRecap(commodityRecapTableData, 5);
        await expect(excelUiCompareBoolean).toEqual(true);
        await resetCompletionPage.clickOn(resetCompletionPage.clearFiltersBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(6000);
        divisionDropDownText = await resetCompletionPage.GetText(await resetCompletionPage.divisionDropDown);
        await expect(["1 Item Selected", "Select Division"]).toContain(divisionDropDownText);

        //await expect(await resetCompletionPage.GetText(await resetCompletionPage.divisionDropDown)).toEqual(emData.one_item_selected);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.selectLocationtext)).toEqual(false);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.divisionDropDown)).toEqual(true);
        await expect(await resetCompletionPage.commodityRecapDivisionColumnCompare(div016, div16)).toEqual(true);
        await resetCompletionPage.clickOn(resetCompletionPage.commodityRecapTableHeaders[1]);
        await resetCompletionPage.waitBrowserToLoadPage(4000);
        await expect(await resetCompletionPage.commodityRecapDivisionColumnCompare(div016, div16)).toEqual(true);


    })

    it('validate restore review: store and division fields do not display other stores and divisions for Store Review tab ', async () => {
       await browser.refresh();
       await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await resetCompletionPage.clickOn(resetCompletionPage.storeReview);
        await autoActivationReport.selectYPW(emData.Period_Input, emData.Period_Empty);
        await storeSelectionPage.clickOn(resetCompletionPage.searchBtn);
        await resetCompletionPage.waitBrowserToLoadPage(6000);
        divisionDropDownText = await resetCompletionPage.GetText(await resetCompletionPage.divisionDropDown);
        await expect(["1 Item Selected", "Select Division"]).toContain(divisionDropDownText);
       // await expect(await resetCompletionPage.GetText(await resetCompletionPage.divisionDropDown)).toEqual(emData.one_item_selected)
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.divisionDropDown)).toEqual(true);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.selectLocationtext)).toEqual(false);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.btnUpdate)).toEqual(true);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.storeReviewCancelBtn)).toEqual(true);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.storeReviewDeleteBtn)).toEqual(true);
        //TODO: bug ERSD-26450 - Store Review Tab - Excel column order does not match UI column order
        let storeReviewRecapTableData = await resetCompletionPage.getStoreReviewTableFirstPage();
        await resetCompletionPage.clickOn(resetCompletionPage.exportToExcelBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(7000);
        let excelUiCompareBoolean = await excelReader.excelUiDataCompareNthRowCommodityRecap(storeReviewRecapTableData, 1);
        await expect(excelUiCompareBoolean).toEqual(true);
        let resetIDDivStore = await resetCompletionPage.firstRowResetIDDivStore();
        let locationTable = resetIDDivStore.split("|")[2];
        await resetCompletionPage.clickOn(await resetCompletionPage.selectLocationtext);
        await resetCompletionPage.waitBrowserToLoadPage(6000);
        let resetCompletionLocations = await resetCompletionPage.resetCompletionLocatorList;
        let isLocationMatch = false;
        for(let index = 1; index < resetCompletionLocations.length; index++){
            let locationDropDown =  await resetCompletionPage.GetText(resetCompletionLocations[index]);
            let locationDropDownStore = locationDropDown.toString().split(" - ")[1];
            if(locationDropDownStore === locationTable ){
                isLocationMatch = true;
                break;
            }
        }
        await expect(isLocationMatch).toEqual(true);
        await expect(await resetCompletionPage.storeReviewDivisionColumnCompare(div016, div16)).toEqual(true);
        for(let index = 0; index < 2; index++){
            await resetCompletionPage.clickOn(resetCompletionPage.tableHeaders[await resetCompletionPage.getTableHeaderIndex(emData.storeReviewTableName, emData.ResetComplete_StoreReview_Div)]);
        }
        await expect(await resetCompletionPage.storeReviewDivisionColumnCompare(div16, div016)).toEqual(true);
        await resetCompletionPage.clickOn(resetCompletionPage.clearFiltersBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(6000);
        await expect(await resetCompletionPage.storeReviewDivisionColumnCompare(div016, div16)).toEqual(true);
        for(let index = 0; index < 2; index++){
            await resetCompletionPage.clickOn(resetCompletionPage.tableHeaders[await resetCompletionPage.getTableHeaderIndex(emData.storeReviewTableName, emData.ResetComplete_StoreReview_Div)]);
        }
        await expect(await resetCompletionPage.storeReviewDivisionColumnCompare(div016, div16)).toEqual(true);
    })

})
