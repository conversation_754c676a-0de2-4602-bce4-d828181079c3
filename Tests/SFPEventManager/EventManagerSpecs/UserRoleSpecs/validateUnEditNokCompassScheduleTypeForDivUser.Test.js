const resetCompletionPage = require('../../EventManagerPO/resetCompletion.page.js')
let emData = require('../../../../TestData/EventManagerData/eventManagerUIData.json')
const envVariables = require("../../UserRoles/roles");
const ENV = process.env.ENV
const eventMaintenancePage = require('../../EventManagerPO/eventMaintanence.page');
const {testSetup} = require('../../../../helpers/testSetupTeardown.js');
const {assert} = require("chai");

let resetStatusDropdownList;

before(async () => {

    await testSetup(envVariables.divisionalUserEM[ENV], envVariables.divisionalPasswordEM[ENV]);
})

describe('validateUnEditNokCompassScheduleTypeForDivUser: validate resets are NOT editable when NON-COMPASS and COMPASS schedule types are combined with ResetStatuses: Completed, Cancelled, Locked ', () => {
    
    it('validate Edit Mode button with "Canceled" and "Locked" ResetStatus dropdown values ', async () => {
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        await resetCompletionPage.open('EventMaintenance/resetCompletion');
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        await resetCompletionPage.clickOn(await resetCompletionPage.storeReview);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.cancelResetBtn)).toEqual(true);
        let scheduleTypeDropdownList = await resetCompletionPage.getResetCompleteDropdownList('selectedResetType', 'resetTypeList');
        let scheduleTypeList = await resetCompletionPage.removeValueFromArray(scheduleTypeDropdownList, 'Remodel');
        for (let scheduleType = 0; scheduleType < await scheduleTypeList.length; scheduleType++) {
            await resetCompletionPage.selectSingleValue('selectedResetType', 'scheduleType', 'resetTypeList', scheduleTypeList[scheduleType]);
            resetStatusDropdownList = await resetCompletionPage.getResetCompleteDropdownList('selectedCompletionType', 'resetCompleteList');
            const resetStatusListToBeTested = ['Completed', 'Locked', 'Canceled'];
            const selectedTextList = await resetCompletionPage.extractMatchingValuesFromArray(await resetStatusDropdownList, resetStatusListToBeTested);
            for (let index = 0; index < await selectedTextList.length; index++) {
                if (await selectedTextList.length > 0) {
                    await eventMaintenancePage.waitBrowserToLoadPage(3000);
                    await resetCompletionPage.selectSingleValue('selectedCompletionType', 'scheduleType', 'resetCompleteList', await selectedTextList[index]);
                    await eventMaintenancePage.clickOn(resetCompletionPage.searchBtn);
                    await eventMaintenancePage.waitBrowserToLoadPage(3000);
                    await resetCompletionPage.clickOn(await resetCompletionPage.editModeBtn);
                    await eventMaintenancePage.waitBrowserToLoadPage(3000);
                    await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.saveChangesBtn)).toEqual(true);
                    await expect(await resetCompletionPage.editModeMessage.getText()).toEqual(emData.editModeMessage);
                    await resetCompletionPage.clickOn(await resetCompletionPage.clearChangesBtn);
                    let resetStatusColumnList = await resetCompletionPage.getColumnArrayList('Reset Status');
                    if (resetStatusColumnList.length > 0) {
                        let resetStatusColumnListUnique = await (await resetCompletionPage.getUniqueArrayValue(await resetStatusColumnList)).toString();
                        await expect(resetStatusColumnListUnique).toEqual(await selectedTextList[index])
                        if (await resetStatusColumnListUnique === 'Completed' || await resetStatusColumnListUnique === 'Canceled' || await resetStatusColumnListUnique === 'Locked') {
                            await eventMaintenancePage.waitBrowserToLoadPage(1000);
                            let resetStatusColumnArray = await resetCompletionPage.resetStatusColumnList;
                            for (let listValue = 0; listValue < await resetStatusColumnArray.length; listValue++) {
                                let elementClickable = await resetCompletionPage.resetCompleteElementIsClickable(await resetStatusColumnArray[listValue]);
                                await expect(elementClickable).toEqual(false);
                            }
                        } else {
                            await assert.fail(0, 1, "Error: ResetStatus column list should be unique !");
                        }
                        await resetCompletionPage.clickResetCompleteDropdownCloseBtn('resetCompleteList');
                    } else {
                        await eventMaintenancePage.clickOn(resetCompletionPage.searchBtn);
                        await eventMaintenancePage.waitBrowserToLoadPage(1000);
                        let zeroRecordsMessage = await resetCompletionPage.zeroRecordsMessage.getText();
                        await expect(zeroRecordsMessage).toEqual(emData.zero_records_found);
                        await resetCompletionPage.clickResetCompleteDropdownCloseBtn('resetCompleteList');
                        await eventMaintenancePage.waitBrowserToLoadPage(2000);
                    }
                }
            }
            await resetCompletionPage.clickResetCompleteDropdownCloseBtn('resetTypeList');
            await resetCompletionPage.clickResetCompleteDropdownCloseBtn('resetCompleteList');
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
        }
    })

})
