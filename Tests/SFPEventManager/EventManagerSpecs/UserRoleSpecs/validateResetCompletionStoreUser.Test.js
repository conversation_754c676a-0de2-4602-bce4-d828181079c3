const resetCompletionPage = require('../../EventManagerPO/resetCompletion.page.js')
let emData = require('../../../../TestData/EventManagerData/eventManagerUIData.json')
const envVariables = require("../../UserRoles/roles");
const ENV = process.env.ENV
const eventMaintenancePage = require('../../EventManagerPO/eventMaintanence.page');
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const HomePage = require("../../../../GlobalObjectRepository/home.page");
const autoActivationReport = require('../../EventManagerPO/autoActivationReport.page.js')
const storeSelectionPage = require('../../EventManagerPO/storeSelection.page.js');

let div = emData.division_Cincinnati_Operating_Division_014.split(' - ')[0];
let store = emData.resetCompleteLoc_014_00359.split(" - ")[1];

before(async () => {

    await testSetup(envVariables.storeUserEM[ENV], envVariables.storePasswordEM[ENV]);
})

describe('validateResetCompletionStoreUser:  validate storeUsers div and store displays correctly ', () => {
    
    beforeEach(async () => {

        await resetCompletionPage.navigateWithHoverEventManager();
        await resetCompletionPage.waitBrowserToLoadPage(6000);
        await expect(await HomePage.dropdownSingleItem('Applications')).toEqual(true);
        await expect( (await resetCompletionPage.GetText(await resetCompletionPage.linkSubModule)).toString().trim() === emData.Admin_reset_completion ).toEqual(true);
        await resetCompletionPage.waitBrowserToLoadPage(3000);
    })

    afterEach( async () => {
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        await autoActivationReport.selectYPW(emData.Year_Input, emData.Year_2024);
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        await autoActivationReport.selectYPW(emData.Period_Input, emData.Period_Empty);
        await storeSelectionPage.clickOn(resetCompletionPage.searchBtn);
        let resetIDDivStoreBeforeSort = await resetCompletionPage.firstRowResetIDDivStore();
        await expect(resetIDDivStoreBeforeSort.split("|")[1]).toEqual(div);
        await expect(resetIDDivStoreBeforeSort.split("|")[2]).toEqual(store);
        for(let index = 0; index < 2; index++){
            await resetCompletionPage.clickOn(resetCompletionPage.tableHeaders[await resetCompletionPage.getTableHeaderIndex(emData.storeReviewTableName, emData.ResetComplete_StoreReview_Div)]);
        }
        await eventMaintenancePage.waitBrowserToLoadPage(5000);
        let resetIDDivStoreAfterDivSort = await resetCompletionPage.firstRowResetIDDivStore();
        await expect(resetIDDivStoreAfterDivSort).toEqual(resetIDDivStoreBeforeSort);
        for(let index = 0; index < 2; index++){
            await resetCompletionPage.clickOn(resetCompletionPage.tableHeaders[await resetCompletionPage.getTableHeaderIndex(emData.storeReviewTableName, emData.ResetComplete_StoreReview_Loc)]);
        }
        await eventMaintenancePage.waitBrowserToLoadPage(5000);
        let resetIDDivStoreAfterStoreSort  = await resetCompletionPage.firstRowResetIDDivStore();
        await expect(resetIDDivStoreAfterStoreSort).toEqual(resetIDDivStoreBeforeSort);
        await resetCompletionPage.clickOn(resetCompletionPage.commodityRecapBtn);
        await expect((await resetCompletionPage.commodityRecapBtn.getAttribute("tabindex"))).toEqual("0");
        await expect((await resetCompletionPage.storeReview.getAttribute("tabindex"))).toEqual("-1");
        let divElementsBeforeDivSort = await resetCompletionPage.commodityRecapColumnValue(await resetCompletionPage.getTableHeaderIndex(emData.commodityRecapTableName, emData.ResetComplete_StoreReview_Div) + 1);
        for (let index = 0; index < divElementsBeforeDivSort.length; index++) {
            await expect(await resetCompletionPage.GetText(divElementsBeforeDivSort[index])).toEqual(div);
        }
        await resetCompletionPage.clickOn(resetCompletionPage.commodityRecapTableHeaders[1]);
        await resetCompletionPage.clickOn(resetCompletionPage.commodityRecapTableHeaders[1]);
        await eventMaintenancePage.waitBrowserToLoadPage(5000);
        let divElementsAfterDivSort = await resetCompletionPage.commodityRecapColumnValue(emData.Number_2);
        for (let index = 0; index < divElementsAfterDivSort.length; index++) {
            await expect(await resetCompletionPage.GetText(divElementsAfterDivSort[index])).toEqual(div);
        }
    })

    it('validate (clear filter) storeUsers div and store displays correctly when using the Clear Filters button ' , async () => {

            await eventMaintenancePage.waitBrowserToLoadPage(3000);
            await resetCompletionPage.validateDropdownText(await resetCompletionPage.divisionDropDown, ["1 Item Selected", "Select Division"], "Division Dropdown");
            await resetCompletionPage.validateDropdownText(await resetCompletionPage.selectLocationtext, ["1 Item Selected", "Select Store"], "Location Dropdown");
            await expect(await resetCompletionPage.searchAttribute(await resetCompletionPage.divisionDropDown, emData.attributeDisabled)).toEqual(true);
            await expect(await resetCompletionPage.searchAttribute(await resetCompletionPage.selectLocationtext, emData.attributeDisabled)).toEqual(true);
            await resetCompletionPage.clickOn(resetCompletionPage.clearFiltersBtn);
            await eventMaintenancePage.waitBrowserToLoadPage(6000);
            await resetCompletionPage.validateDropdownText(await resetCompletionPage.divisionDropDown, ["1 Item Selected", "Select Division"], "Division Dropdown");
            await resetCompletionPage.validateDropdownText(await resetCompletionPage.selectLocationtext, ["1 Item Selected", "Select Store"], "Location Dropdown");
            await expect(await resetCompletionPage.searchAttribute(await resetCompletionPage.divisionDropDown, emData.attributeDisabled)).toEqual(true);
            await expect(await resetCompletionPage.searchAttribute(await resetCompletionPage.selectLocationtext, emData.attributeDisabled)).toEqual(true);
            await expect((await resetCompletionPage.commodityRecapBtn.getAttribute("tabindex"))).toEqual("-1");
            await expect((await resetCompletionPage.storeReview.getAttribute("tabindex"))).toEqual("0");

        })

        it('validate store and division fields do not display other stores and divisions for Store Review tab and Commodity Review ', async () => {

            await resetCompletionPage.clickOn(resetCompletionPage.storeReview);
            await expect(await resetCompletionPage.searchAttribute(await resetCompletionPage.divisionDropDown, emData.attributeDisabled)).toEqual(true);
            await expect(await resetCompletionPage.searchAttribute(await resetCompletionPage.selectLocationtext, emData.attributeDisabled)).toEqual(true);
            await expect(await resetCompletionPage.doIsDisplay(resetCompletionPage.btnUpdate)).toEqual(true);
            await expect(await resetCompletionPage.doIsDisplay(resetCompletionPage.storeReviewCancelBtn)).toEqual(true);
        })

})
