const resetCompletionPage = require('../../EventManagerPO/resetCompletion.page.js')
const envVariables = require("../../UserRoles/roles");
const ENV = process.env.ENV
const eventMaintenancePage = require('../../EventManagerPO/eventMaintanence.page');
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const assert = require('chai').assert;
const { getExcelDataByColumnName } = require("../../../../util/excelReader");
const emData = require("../../../../TestData/EventManagerData/eventManagerUIData.json");
const result = require("../../../../SQLConnection/server.js.ts");
const qrString = require("../../../../SQLConnection/queryStringEM");

let resetStatusDropdownList;

before(async () => {

    await testSetup(await envVariables.divisionalUserEM[ENV], await envVariables.divisionalPasswordEM[ENV]);
})

describe('validateCancelResetBtnForDivisionUser:  validate Cancel Reset button features under StoreReview table for DivisionalUser ', () => {

    beforeEach(async () => {

        await resetCompletionPage.waitBrowserToLoadPage(5000);
        await resetCompletionPage.open('EventMaintenance/resetCompletion');
        await resetCompletionPage.waitBrowserToLoadPage(5000);
        await resetCompletionPage.waitForPageLoad(resetCompletionPage.searchBtn, emData.resetCompletion_Search);
        await resetCompletionPage.clickOn(await resetCompletionPage.storeReview);
        resetStatusDropdownList = await resetCompletionPage.getResetCompleteDropdownList('selectedCompletionType', 'resetCompleteList');
    })

    it('validate 1) CancelReset and EditMode buttons exist; 2) buttons have correct texts; 3) CancelReset button is disabled ', async () => {

        const existingButtonLocatorList = [await resetCompletionPage.cancelResetBtn, await resetCompletionPage.editModeButton]
        const existingButtonList = ['Cancel Reset(s)', 'Edit Mode']
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        for (let button = 0; button < existingButtonLocatorList.length; button++) {
            const buttonExist = await resetCompletionPage.elementExists(existingButtonLocatorList[button]);
            await expect(buttonExist).toEqual(true);
            let ButtonText = await existingButtonLocatorList[button].getText();
            await expect(ButtonText).toEqual(existingButtonList[button]);
            await expect(await resetCompletionPage.locatorDisabled(await existingButtonLocatorList[0])).toEqual(true);
        }
    })

    it('validate 1) Cancel Reset button is highlighted when single and multiple row checkboxes are checked after "No Response"  ResetStatus dropdown value is selected ;' +
        '2) Cancel Reset button is highlighted when single and multiple row checkboxes are checked after "Locked" ResetStatus dropdown value is selected;' +
        '3) clicking "No, keep reset(s)" button did not delete the reset and unchecks all row checkboxes, including Cancel Reset button is not highlighted; ' +
        '4) validate aborting reset cancel will not delete selected resets ', async () => {

            let countSelectedCheckBox;
            for (let resetStatusValue = 3; resetStatusValue < await resetStatusDropdownList.length; resetStatusValue++) {
                await resetCompletionPage.clickOn(resetCompletionPage.clearFiltersBtn);
                await resetCompletionPage.selectSingleValue('selectedCompletionType', 'scheduleType', 'resetCompleteList', await resetStatusDropdownList[resetStatusValue]);
                await eventMaintenancePage.waitBrowserToLoadPage(3000);
                await eventMaintenancePage.clickOn(resetCompletionPage.searchBtn);
                await eventMaintenancePage.waitBrowserToLoadPage(3000);
                let resetIdColumnListBefore = (await resetCompletionPage.getColumnArrayList('Reset Id'));
                let totalPageRowCount = await resetIdColumnListBefore.length;
                if (await totalPageRowCount >= 3) {
                    let numOfSelectedCheckBoxes = [totalPageRowCount - 1, totalPageRowCount - 3];
                    for (let index = 0; index <= numOfSelectedCheckBoxes.length - 1; index++) {
                        await resetCompletionPage.clickCheckBox(await resetCompletionPage.storeReviewTable, await resetCompletionPage.storeReviewCheckBoxList, numOfSelectedCheckBoxes[index]);
                        await resetCompletionPage.waitBrowserToLoadPage(3000);
                        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.storeReviewCancelResetBtn)).toEqual(false);
                        await resetCompletionPage.waitBrowserToLoadPage(3000);
                        countSelectedCheckBox = await resetCompletionPage.returnNumOfSelectedCheckBox(await resetCompletionPage.storeReviewTable, await resetCompletionPage.storeReviewCheckBoxList);
                        await resetCompletionPage.clickOn(await resetCompletionPage.cancelResetBtn);
                        await resetCompletionPage.waitBrowserToLoadPage(3000);
                        let popUpCancelResetText = await resetCompletionPage.getTextOfElement(await resetCompletionPage.popUpCancelResetText);
                        await expect(await popUpCancelResetText).toEqual('Cancel Reset(s)');
                        let resetCancelAffirmationMessage = await resetCompletionPage.getTextOfElement(await resetCompletionPage.popUpAffirmationMessage);
                        let extractedDigitsFromMessage = await resetCompletionPage.extractDigits(await resetCancelAffirmationMessage);
                        await expect(countSelectedCheckBox.toString()).toEqual(extractedDigitsFromMessage);
                        await resetCompletionPage.clickButtonByLabel(await resetCompletionPage.noYesCancelResetBtnList, emData.no_keep_resets);
                        await resetCompletionPage.waitBrowserToLoadPage(2000);
                        let countSelectedCheckBoxAfter = await resetCompletionPage.returnNumOfSelectedCheckBox(await resetCompletionPage.storeReviewTable, await resetCompletionPage.storeReviewCheckBoxList);
                        await expect(countSelectedCheckBoxAfter).toEqual(0);
                        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.storeReviewCancelResetBtn)).toEqual(true);
                        let resetIdColumnListAfter = (await resetCompletionPage.getColumnArrayList('Reset Id'));
                        if (resetIdColumnListAfter.length > 0) {
                            await expect(resetIdColumnListAfter).toEqual(resetIdColumnListAfter);
                        } else {
                            await assert.fail(0, 1, "Error: Table did not show data after Reset has been aborted !");
                        }
                    }
                } else {
                    await assert.fail(0, 1, "Error: Table did not have enough data !");
                }
            }
        })

    it('validate: 1) reset can be cancelled when "No Response" value is selected from ResetStatus dropdown;' +
        '2) reset can be cancelled when "Locked" value is selected from ResetStatus dropdown; ' +
        '3) row checkboxes are unselected and "Cancel Reset(s)" button is not highlighted after cancellation is complete;' +
        '4) downloaded excel data does not include cancelled Reset IDs for single and multiple reset cancellations; ' +
        '5) ResetID column list is different before and after cancelling reset;' +
        '6) cancelled reset status with specific resetId using query ', async () => {

            let countSelectedCheckBox;
            for (let resetStatusValue = 3; resetStatusValue < await resetStatusDropdownList.length; resetStatusValue++) {
                await resetCompletionPage.clickOn(resetCompletionPage.clearFiltersBtn);
                await resetCompletionPage.selectSingleValue('selectedCompletionType', 'scheduleType', 'resetCompleteList', await resetStatusDropdownList[resetStatusValue]);
                await eventMaintenancePage.waitBrowserToLoadPage(3000);
                await eventMaintenancePage.clickOn(resetCompletionPage.searchBtn);
                await eventMaintenancePage.waitBrowserToLoadPage(3000);
                let resetIdColumnListBefore = (await resetCompletionPage.getColumnArrayList('Reset Id'));
                let totalPageRowCount = resetIdColumnListBefore.length;
                if (totalPageRowCount >= 2) {
                    // Note: Don't delete: To prevent cancelling multiple resets from depleting the table data, this part of code is commented out: [totalPageRowCount - 2]
                    let numOfSelectedCheckBoxes = [totalPageRowCount - 1];
                    for (let index = 0; index <= numOfSelectedCheckBoxes.length - 1; index++) {
                        await resetCompletionPage.clickCheckBox(await resetCompletionPage.storeReviewTable, await resetCompletionPage.storeReviewCheckBoxList, numOfSelectedCheckBoxes[index]);
                        await resetCompletionPage.waitBrowserToLoadPage(3000);
                        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.storeReviewCancelResetBtn)).toEqual(false);
                        await resetCompletionPage.waitBrowserToLoadPage(3000);
                        countSelectedCheckBox = await resetCompletionPage.returnNumOfSelectedCheckBox(await resetCompletionPage.storeReviewTable, await resetCompletionPage.storeReviewCheckBoxList);
                        await resetCompletionPage.clickOn(await resetCompletionPage.cancelResetBtn);
                        await resetCompletionPage.waitBrowserToLoadPage(3000);
                        let popUpCancelResetText = await resetCompletionPage.getTextOfElement(await resetCompletionPage.popUpCancelResetText);
                        await expect(await popUpCancelResetText).toEqual('Cancel Reset(s)');
                        let resetCancelAffirmationMessage = await resetCompletionPage.getTextOfElement(await resetCompletionPage.popUpAffirmationMessage);
                        let extractedDigitsFromMessage = await resetCompletionPage.extractDigits(await resetCancelAffirmationMessage);
                        await expect(countSelectedCheckBox.toString()).toEqual(extractedDigitsFromMessage);
                        await resetCompletionPage.clickButtonByLabel(await resetCompletionPage.noYesCancelResetBtnList, emData.yes_cancel_resets);
                        await resetCompletionPage.waitBrowserToLoadPage(5000);
                        let resetCancelledMessage = await resetCompletionPage.getTextOfElement(await resetCompletionPage.selectedResetCancelledMessage);
                        await expect(await resetCancelledMessage).toEqual(emData.resetCancelledSuccessMessage);
                        let resetIdColumnListAfter = (await resetCompletionPage.getColumnArrayList('Reset Id'));
                        await assert.notDeepEqual(resetIdColumnListBefore[0], resetIdColumnListAfter[0], 'ResetID column lists are the same; selected resets did not get to cancel !')
                        await resetCompletionPage.waitBrowserToLoadPage(2000);
                        let countSelectedCheckBoxAfter = await resetCompletionPage.returnNumOfSelectedCheckBox(await resetCompletionPage.storeReviewTable, await resetCompletionPage.storeReviewCheckBoxList);
                        await expect(countSelectedCheckBoxAfter).toEqual(0);
                        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.storeReviewCancelResetBtn)).toEqual(true);
                        await resetCompletionPage.clickOn(await resetCompletionPage.exportToExcelBtnDivUser);
                        await eventMaintenancePage.waitBrowserToLoadPage(8000);
                        let resetIdCExcelColumnData = await getExcelDataByColumnName('Reset Id');
                        await eventMaintenancePage.waitBrowserToLoadPage(8000);
                        await assert.notDeepEqual(resetIdColumnListBefore[0], resetIdCExcelColumnData[0], 'The arrays are equal; deleted reset still exists in excel !')
                        let resetCompletionDeleteStatus = await (await result.getResult(qrString.resetCompleteDeleteStatusResetId.replace('@rec_id', await resetIdColumnListBefore[0]))).flat();
                        await expect(await resetCompletionDeleteStatus.toString()).toEqual("Deleted");
                    }
                }
            }

        })

})
