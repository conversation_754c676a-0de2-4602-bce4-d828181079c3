const resetCompletionPage = require('../../EventManagerPO/resetCompletion.page.js')
let emData = require('../../../../TestData/EventManagerData/eventManagerUIData.json')
const envVariables = require("../../UserRoles/roles");
const ENV = process.env.ENV
const eventMaintenancePage = require('../../EventManagerPO/eventMaintanence.page');
const {testSetup} = require('../../../../helpers/testSetupTeardown.js');
const {assert} = require("chai");

let resetStatusDropdownList;
before(async () => {

    await testSetup(envVariables.divisionalUserEM[ENV], envVariables.divisionalPasswordEM[ENV]);
})

describe('validateEditNokActiveDropdownsForDivUser: validate Edit Mode when Nok Schedule Type and "Not Completed" and "No Response" ResetStatus dropdown values are selected for DivisionalUser ', () => {

    beforeEach(async () => {

        await resetCompletionPage.waitBrowserToLoadPage(1000);
        await resetCompletionPage.open('EventMaintenance/resetCompletion');
        await resetCompletionPage.waitBrowserToLoadPage(3000);
        await resetCompletionPage.clickOn(await resetCompletionPage.storeReview);
        await eventMaintenancePage.waitBrowserToLoadPage(5000);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.cancelResetBtn)).toEqual(true);
        resetStatusDropdownList = await resetCompletionPage.getResetCompleteDropdownList('selectedCompletionType', 'resetCompleteList');
        await resetCompletionPage.selectSingleValue('selectedResetType', 'scheduleType', 'resetTypeList', 'Non - KOMPASS');
    })

    it('validate ResetStatusReason column dropdown value can be updated when NOK schedule type and "Not Completed" ResetStatus dropdown values are selected ', async () => {

        let dropdownValueNotCompleted = resetStatusDropdownList[2];
        await resetCompletionPage.selectSingleValue('selectedCompletionType', 'scheduleType', 'resetCompleteList', dropdownValueNotCompleted);
        await eventMaintenancePage.clickOn(await resetCompletionPage.searchBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        let resetStatusColumnListUniqueBefore = await resetCompletionPage.getUniqueUnsortedArrayValue(await resetCompletionPage.getColumnArrayList('Reset Status'));
        await expect(resetStatusColumnListUniqueBefore.toString()).toEqual(await dropdownValueNotCompleted)
        await resetCompletionPage.clickOn(await resetCompletionPage.editModeBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.saveChangesBtn)).toEqual(true);
        await expect(await resetCompletionPage.editModeMessage.getText()).toEqual(emData.editModeMessage);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        let reasonColumnFirstValueBefore = await resetCompletionPage.resetStatusReasonColumnFirstValue.getText();
        let reasonDropdownList = await resetCompletionPage.getStoreReviewTableDropdownListText(await resetCompletionPage.resetStatusReasonColumnFirstChevron, 'Reset Status Reason');
        let reasonDropdownListClean = await resetCompletionPage.removeElementByText(reasonDropdownList, reasonColumnFirstValueBefore);
        let reasonDropdownListCleanRandomValue = await resetCompletionPage.getRandomArrayValue(await reasonDropdownListClean);
        await resetCompletionPage.resetStatusColumnFirstChevron.click();
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        let completedSelected = await resetCompletionPage.selectStoreReviewReasonDropdown(await reasonDropdownListCleanRandomValue);
        await expect(completedSelected).toEqual(true);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await resetCompletionPage.saveChangesBtn.click();
        let changeSavedConfirmationMessage = await resetCompletionPage.changesSavedMessage.getText();
        await expect(await changeSavedConfirmationMessage).toEqual(emData.changedSavedMessage);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        let reasonColumnFirstValueAfter = await resetCompletionPage.resetStatusReasonColumnFirstStaticValue.getText();
        await assert.notDeepEqual(reasonColumnFirstValueBefore, reasonColumnFirstValueAfter, 'Error: Two ResetId values should not be the same !')
    })

    it('validate Edit Mode feature when NOK scheduleType and  "Not Completed" ResetStatus dropdown values are selected', async () => {

        let dropdownValueNotCompleted = resetStatusDropdownList[2];
        await resetCompletionPage.selectSingleValue('selectedCompletionType', 'scheduleType', 'resetCompleteList', dropdownValueNotCompleted);
        await eventMaintenancePage.clickOn(await resetCompletionPage.searchBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        let resetStatusColumnListUniqueBefore = await resetCompletionPage.getUniqueUnsortedArrayValue(await resetCompletionPage.getColumnArrayList('Reset Status'));
        await expect(resetStatusColumnListUniqueBefore.toString()).toEqual(await dropdownValueNotCompleted)
        await resetCompletionPage.clickOn(await resetCompletionPage.editModeBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.saveChangesBtn)).toEqual(true);
        await expect(await resetCompletionPage.editModeMessage.getText()).toEqual(emData.editModeMessage);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await resetCompletionPage.clickOn(await resetCompletionPage.editModeBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        let resetStatusList = await resetCompletionPage.getStoreReviewTableDropdownListText(await resetCompletionPage.resetStatusColumnFirstChevron, 'Reset Status');
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        let resetStatusListNoEmptyStrings = await resetCompletionPage.removeEmptyStringFromArray(await resetStatusList);
        let storeReviewResetStatusDropdownListClean = await resetCompletionPage.removeElementByText(resetStatusListNoEmptyStrings, 'Not Completed');
        for (let index = 0; index < storeReviewResetStatusDropdownListClean.length; index++) {
            await eventMaintenancePage.clickOn(await resetCompletionPage.searchBtn);
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            if (await resetCompletionPage.elementIsClickable(await resetCompletionPage.editModeBtn)) {
                await eventMaintenancePage.waitBrowserToLoadPage(3000);
                await resetCompletionPage.clickOn(await resetCompletionPage.editModeBtn);
                await eventMaintenancePage.waitBrowserToLoadPage(2000);
            }
            let editModeConfirmationMessage = await resetCompletionPage.getTextOfElement(await resetCompletionPage.editModeMessage)
            await expect(await editModeConfirmationMessage).toEqual(emData.editModeMessage);
            let clickCompleted = await resetCompletionPage.selectStoreReviewResetStatusDropdown(await storeReviewResetStatusDropdownListClean[index]);
            await expect(clickCompleted).toEqual(true);
            let firstRowResetIdBefore = await resetCompletionPage.getColumnArrayList('Reset Id');
            let firstRowResetStatus = await resetCompletionPage.getColumnArrayList('Reset Status Reason', 1);
            if (firstRowResetStatus[index] === 'Reset Completed') {
                await expect(firstRowResetStatus[0]).toEqual('Reset Completed');
            }
            if (firstRowResetStatus[index] === emData.available_for_reset) {
                await expect(firstRowResetStatus[0]).toEqual(emData.available_for_reset);
            }
            await eventMaintenancePage.waitBrowserToLoadPage(3000);
            await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.saveChangesBtn)).toEqual(false);
            await resetCompletionPage.saveChangesBtn.click();
            await eventMaintenancePage.waitBrowserToLoadPage(3000);
            let firstRowResetIdAfter = await resetCompletionPage.getColumnArrayList('Reset Id');
            await assert.notDeepEqual(await firstRowResetIdBefore[0], await firstRowResetIdAfter[0], 'Error: Two ResetId values are the same but they should not be !')

        }
    })

    it('validate: 1) Edit Mode feature when NOK scheduleType and  "No Response" ResetStatus dropdown values are selected;' +
        '2) validate Rev By column value when converting NoResponse to Completed', async () => {

        let firstRowResetIdBefore
        await resetCompletionPage.selectSingleValue('selectedCompletionType', 'scheduleType', 'resetCompleteList', await resetStatusDropdownList[3]);
        await eventMaintenancePage.clickOn(resetCompletionPage.searchBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        let resetStatusColumnListUniqueBefore = await resetCompletionPage.getUniqueUnsortedArrayValue(await resetCompletionPage.getColumnArrayList('Reset Status'));
        await expect(resetStatusColumnListUniqueBefore.toString()).toEqual(await resetStatusDropdownList[3])
        firstRowResetIdBefore = await resetCompletionPage.getColumnArrayList('Reset Id');
        let firstRowRevByBefore = await resetCompletionPage.getColumnArrayList('Rev By');
        await resetCompletionPage.clickOn(await resetCompletionPage.editModeBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.saveChangesBtn)).toEqual(true);
        await expect(await resetCompletionPage.editModeMessage.getText()).toEqual(emData.editModeMessage);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        let storeReviewResetStatusDropdownList = await resetCompletionPage.getStoreReviewTableDropdownListText(await resetCompletionPage.resetStatusColumnFirstChevron, 'Reset Status');
        await eventMaintenancePage.waitBrowserToLoadPage(5000);
        await resetCompletionPage.resetStatusColumnFirstChevron.click();
        let storeReviewResetStatusDropdownListClean = await resetCompletionPage.removeElementByText(storeReviewResetStatusDropdownList, resetStatusColumnListUniqueBefore.toString());
        if (await storeReviewResetStatusDropdownListClean[0]) {
            let clickCompleted = await resetCompletionPage.selectStoreReviewResetStatusDropdown(await storeReviewResetStatusDropdownListClean[0]);
            await expect(clickCompleted).toEqual(true);
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.saveChangesBtn)).toEqual(false);
            await eventMaintenancePage.waitBrowserToLoadPage(3000);
            let firstRowResetStatus = await resetCompletionPage.getColumnArrayList('Reset Status Reason', 1);
            await expect(firstRowResetStatus[0]).toEqual('Reset Completed');
            await resetCompletionPage.saveChangesBtn.click();
            await eventMaintenancePage.waitBrowserToLoadPage(5000);
            let firstRowResetIdAfter = await resetCompletionPage.getColumnArrayList('Reset Id', 1);
            await assert.notDeepEqual(await firstRowResetIdBefore[0], await firstRowResetIdAfter[0], 'Error: Two ResetId values are the same but they should not be !');
            await resetCompletionPage.clearResetStatusAndSelect(await resetStatusDropdownList[0])
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            await resetCompletionPage.getPaginationPageNumber(emData.PaginationPageNumber_100);
            let resetIdListAfter = await resetCompletionPage.getColumnArrayList('Reset Id');
            let indexNumberModifiedReset = await (resetIdListAfter.indexOf( await firstRowResetIdBefore[0]));
            let firstRowRevByAfter = await (await resetCompletionPage.getColumnArrayList('Rev By'));
            await assert.notDeepEqual(await firstRowRevByBefore[0], await firstRowRevByAfter[indexNumberModifiedReset], 'Error: Two Rev By values should not be the same !');
            await resetCompletionPage.clearResetStatusAndSelect(await resetStatusDropdownList[3]);
        }
        await eventMaintenancePage.waitBrowserToLoadPage(5000);
        if (await storeReviewResetStatusDropdownListClean[1]) {
            await resetCompletionPage.clickOn(await resetCompletionPage.editModeBtn);
            await eventMaintenancePage.waitBrowserToLoadPage(3000);
            await expect(await resetCompletionPage.editModeMessage.getText()).toEqual(emData.editModeMessage);
            await eventMaintenancePage.waitBrowserToLoadPage(3000);
            let clickNotCompleted = await resetCompletionPage.selectStoreReviewResetStatusDropdown(await storeReviewResetStatusDropdownListClean[1]);
            await expect(clickNotCompleted).toEqual(true);
            await eventMaintenancePage.waitBrowserToLoadPage(3000);
            let firstRowResetStatusReason = await resetCompletionPage.resetStatusReasonColumnFirstValueActive.getText();
            await eventMaintenancePage.waitBrowserToLoadPage(3000);
            await expect(firstRowResetStatusReason).toEqual(emData.selectOneMessage);
            let completed = await resetCompletionPage.selectStoreReviewReasonDropdown(emData.storeManagerPostponedOption);
            await expect(completed).toEqual(true);
            await expect(await resetCompletionPage.locatorDisabled(await resetCompletionPage.saveChangesBtn)).toEqual(false);
            await resetCompletionPage.saveChangesBtn.click();
            await eventMaintenancePage.waitBrowserToLoadPage(8000);
            let firstRowResetIdAfter = await resetCompletionPage.getColumnArrayList('Reset Id', 1);
            await eventMaintenancePage.waitBrowserToLoadPage(3000);
            await assert.notDeepEqual(await firstRowResetIdBefore[0], await firstRowResetIdAfter[0], 'Error: Two ResetId values are the same but they should not be !');
        }
    })

})
