const resetCompletionPage = require('../../EventManagerPO/resetCompletion.page.js')
let emData = require('../../../../TestData/EventManagerData/eventManagerUIData.json')
const envVariables = require("../../UserRoles/roles");
const ENV = process.env.ENV
const eventMaintenancePage = require('../../EventManagerPO/eventMaintanence.page');
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const properties = require("../../../../ConfigFiles/wdio.conf");

let div016 = emData.div_016_Columbus_Operating_Division.split(' - ')[0];
let div021 = emData.div_021_Central_Operating_Division.split(' - ')[0];
let div025 = emData.div_025_Delta_Operating_Division.split(' - ')[0];
let corpDiv60 = emData.div_Corporate_060.split(" - ")[1];

before(async () => {

    await testSetup(envVariables.multipleDivisionalUserEM[ENV], envVariables.multipleDivisionalPasswordEM[ENV]);
})

describe('validateEventManagerMultiDivisionUser: Validate SFP Event Manager page has restrictions for single division user ', () => {

    
    it('validate Event Add, division dropdown, and table divisions shows correct division. Includes corporate division ', async () => {

        await expect(browser).toHaveUrlContaining(properties.config.baseUrl);
        await expect(browser).toHaveTitle(emData.homeTitle);
        await resetCompletionPage.open(emData.EventMaintanceUrl);
        await eventMaintenancePage.waitBrowserToLoadPage(3500);
        await expect(await eventMaintenancePage.GetText(eventMaintenancePage.emHomePageText)).toEqual(emData.ApplicationsSubmenuSFPEventManager);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await eventMaintenancePage.clickOn(eventMaintenancePage.addEventDropdown);
        let addEventDropDownValuesUI = await eventMaintenancePage.addEventDropDownList;
        let eventAddValues = [emData.SFP_EVENT, emData.SFP_Activation, emData.SFP_Refresh];
        let eventAddDropDownValuesUIText = [];
        await expect(addEventDropDownValuesUI.length ).toEqual(eventAddValues.length);
        for(let index = 0; index < addEventDropDownValuesUI.length; index++){
            eventAddDropDownValuesUIText.push( await eventMaintenancePage.GetText(addEventDropDownValuesUI[index]));
        }
        eventAddDropDownValuesUIText.sort();
        eventAddValues.sort();
        for(let index = 0; index < eventAddValues.length; index++){
            await expect(eventAddValues[index]).toEqual(eventAddDropDownValuesUIText[index]);
        }
        await eventMaintenancePage.clickOn(await eventMaintenancePage.filtersBtn);
        await expect(await resetCompletionPage.locatorDisabled(await eventMaintenancePage.divisionDropDown, emData.attributeDisabled)).toEqual(false);
        await expect(await resetCompletionPage.locatorDisabled(await eventMaintenancePage.storeDropDown, emData.attributeDisabled)).toEqual(true);
        await expect(await eventMaintenancePage.GetText(await eventMaintenancePage.storeDropDown)).toEqual(emData.select_store);
        await expect(await eventMaintenancePage.eventManagerDivisionDropDownMultiDivisionMatch([emData.div_016_Columbus_Operating_Division, emData.div_021_Central_Operating_Division, emData.div_025_Delta_Operating_Division])).toEqual(true);
        let selectedDivision = await eventMaintenancePage.GetText(await eventMaintenancePage.divisionDropdownEventManList[1]);
        selectedDivision = selectedDivision.toString().split(" - ")[0];
        await eventMaintenancePage.clickOn(await eventMaintenancePage.divisionDropdownEventManList[1]);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await eventMaintenancePage.clickOn(await eventMaintenancePage.storeDropDown);
        let storeList = await eventMaintenancePage.storeDropDownList;
         for(let index = 1; index < storeList.length; index++){
             let store = await eventMaintenancePage.GetText(await eventMaintenancePage.storeDropDownList[index]);
             await expect(store.toString().split(" - ")[0]).toEqual(selectedDivision);
         }
        let paginationLabelStart = await eventMaintenancePage.GetText( await eventMaintenancePage.paginationLabel);
        let paginationLabelStartElements = paginationLabelStart.toString().split(" ");
        await eventMaintenancePage.clickOn(await eventMaintenancePage.searchBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        let divColumnAfterClick = await eventMaintenancePage.tableColumnByIndex(await eventMaintenancePage.getEventManagerTableHeaderIndex("EventManager", "Division") + 1);
        let isMatch = false;
        for(let index = 0; index < divColumnAfterClick.length; index++){
            let division = await eventMaintenancePage.GetText(await divColumnAfterClick[index])
            isMatch = division === div021 || division === corpDiv60;
            if(isMatch === false){
                break;
            }
        }
        await expect(isMatch).toEqual(true);
        let paginationLabelBefore = await eventMaintenancePage.GetText( await eventMaintenancePage.paginationLabel);
        let paginationLabelBeforeElements = paginationLabelBefore.toString().split(" ");
        await eventMaintenancePage.clickOn(await eventMaintenancePage.clearAllFiltersBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(6000);
        await expect(await eventMaintenancePage.GetText(await eventMaintenancePage.divisionDropDown)).toEqual(emData.Select_Division_text);
        let paginationLabelAfter = await eventMaintenancePage.GetText( await eventMaintenancePage.paginationLabel);
        let paginationLabelAfterElements = paginationLabelAfter.toString().split(" ");
        await expect(Number(paginationLabelBeforeElements[5]) < Number(paginationLabelAfterElements[5])).toEqual(true);
        await expect((Math.abs(Number(paginationLabelStartElements[5]) - Number(paginationLabelAfterElements[5]))) < 100).toEqual(true);

    })

})
