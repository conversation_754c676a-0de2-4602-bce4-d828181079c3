const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const selectPOGPage = require("../EventManagerPO/selectingPogCommodities.page");
const { assert } = require('chai');
const { GetText } = require('../../../GlobalObjectRepository/login.page');
const createSFPEventPage = require("../EventManagerPO/createSFPEvent.page");

describe('validatePogCountInPog/CommodityTab: validate PogCount in an event on UI and DB', () => {

    it('Should: 1) validate PogCount in an event on UI and DB; 2) validate after removing Pog on UI and DB', async () => {

        let description = await createSFPEventPage.addEventDetails1('','SFP Event', emData.div_Cincinnati_Operating_Division_014, emData.eventType_KOMPASS, "Update", "PogCount", emData.vendor_AZB);
        await createSfpEventPage.submitEventDetails();
        let dbEventIdCreated = await (await result.getResult(qrString.corporateSplitEvent.replace('@discrp', description))).flat();
        let nextPage = await eventMaintanencePage.linkSubModule.getText();
        await expect(await nextPage).toEqual(emData.Event_Schedule);
        await createSfpEventPage.addEventScheduleStep2();
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(storePage).toEqual(emData.Store_Selection)
        let divNum14 = (emData.div_Cincinnati_Operating_Division_014.match(/\d/g).join("")).toString();
        let dbScheduledStores = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, divNum14).replace('@num', '30'))).flat();
        let div14 = emData.div_Cincinnati_Operating_Division_014.replace('Cincinnati ', '').replace('- ', '').toString().trim()
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await storeSelectionPage.clickCheckBoxText(await dbScheduledStores[5]);
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await clickOn(storeSelectionPage.saveStoreSchedule)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, emData.Select_Pogs)
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(await pogPage).toEqual(emData.Select_Pogs)
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await selectPOGPage.selectDropdown('Department', emData.dept_01_GROCERY);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.selectDropdown('Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.selectDropdown('Commodity *', emData.ResetComplete_All);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await clickOn(selectPOGPage.selectChoosePogByCommodityBtn);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.waitForPageLoad(await selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 9000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await (await selectPOG.gotoEventListButton).waitForExist({ timeout: 9000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        let footerString = await createSfpEventPage.getTextOfElement(await createSfpEventPage.footerEntryString);
        const totalEntryNumber = await createSfpEventPage.extractDigitsByIndex(await footerString, 2);
        let dbPogCountBefore = await (await result.getResult(qrString.pogCount.replace('@evID', dbEventIdCreated[0]))).flat();
        await assert.equal(await dbPogCountBefore.toString(), totalEntryNumber.toString(),'Error: Those two values should be same 1' );
        await clickOn(await selectPOGPage.selectAllCheckBoxes);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await clickOn(await selectPOG.selectRemoveSelectedPogButton)
        await (await selectPOG.pogDeleteMsg).waitForExist({ timeout: 9000 }, 'Error: Table contents for event maintenance page didn`t load before timeout');
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        let pogDelete = await GetText(await selectPOG.pogDeleteMsg);
        await expect(await pogDelete).toEqual(emData.pogDeleteMsg);
        let dbPogCountAfter = await (await result.getResult(qrString.pogCount.replace('@evID', dbEventIdCreated[0]))).flat();
        await assert.notEqual(dbPogCountBefore,dbPogCountAfter,'Error: Selected Pogs did not get deleted !' )
        const tableFirstPageRowNumber = await selectPOGPage.tableRowNumber.length;
        await expect(dbPogCountAfter[0]).toEqual(dbPogCountBefore[0] - tableFirstPageRowNumber);
    })

})
