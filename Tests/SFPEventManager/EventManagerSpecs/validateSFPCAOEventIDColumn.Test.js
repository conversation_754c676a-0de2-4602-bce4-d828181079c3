const eventMaintenancePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const result = require('../../../SQLConnection/server.js.ts');
const qrString = require('../../../SQLConnection/queryStringEM');
const autoActivationReport = require("../EventManagerPO/autoActivationReport.page.js");

describe('validateSFPCAOEventIDColumn: SFPCAOAEventIDColumn in the EM HeaderTable', () => {

    it('validate SFPCAOAEventIDColumn in the EM HeaderTable', async () => {
         await eventMaintenancePage.waitBrowserToLoadPage(6000) 
        await eventMaintenancePage.open('EventMaintenance');
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        await eventMaintenancePage.tableSecondPageBtn.waitForExist({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})             
        await eventMaintenancePage.selectEventType('SFP Event')
        expect(await eventMaintenancePage.linkCreateSFPEvent.getText()).toHaveTextContaining(emData.Create_SFP_Event)
        await createSfpEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014)
        await createSfpEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        await createSfpEventPage.selectDropdown('selectType', "Update");
        let description = await selectPOG.stringGenerator("Automation2weeks")
        await createSfpEventPage.enterEventResetDescription(description)
        await createSfpEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await createSfpEventPage.clickRadioButtonByText("Activate CAO 2 weeks before Event Schedule")
        await createSfpEventPage.submitEventDetails()
        let nextPage = await eventMaintenancePage.linkSubModule.getText()
        await expect(nextPage).toEqual(emData.Event_Schedule)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await clickOn(storeSelectionPage.saveSchedule)
        await eventMaintenancePage.waitBrowserToLoadPage(5000)
        let weekText = await storeSelectionPage.storeWeekSchedule.getText()
        await eventMaintenancePage.waitBrowserToLoadPage(3000)
        await clickOn(storeSelectionPage.saveGoToStore)
        await eventMaintenancePage.waitBrowserToLoadPage(3000)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Store Selection")
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(storePage).toEqual(emData.Store_Selection)
        await eventMaintenancePage.open(emData.EventMaintanceUrl);
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.filtersTxtBtn, emData.Filters);
        await eventMaintenancePage.clickFiltersWithDiscp(description.toString())
        let eventDescription = await selectPOG.getEventDescText(description, weekText.replace(', ', '/'))
        await expect(eventDescription).toEqual(true)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        let activateCao2 = await eventMaintenancePage.getHeaderValue(emData.Header_2_Weeks, description)
        await expect(activateCao2).toEqual(emData.Event_2Weeks_Yes)
        let initialEventId = await eventMaintenancePage.getHeaderValue(emData.Event_ID, description)
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await eventMaintenancePage.clickOn(await eventMaintenancePage.slideOutClose)
        let eventId = await eventMaintenancePage.getEventId(initialEventId)
        await expect(eventId).toEqual(initialEventId)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await clickOn(eventMaintenancePage.firstRowExpandIcon)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        let caoEventId = await eventMaintenancePage.getHeaderTextOnly(emData.Header_CAOEventID)
        await expect(caoEventId).toEqual(emData.Header_CAOEventID)
    })

    it.skip('validate SFPCAOAEventIDColumn in the EM HeaderTable and DB', async () => {
        let caOEventDscp = (await result.getResult(qrString.caOEventDscp)).flat();
        await eventMaintenancePage.open(emData.EventMaintanceUrl);
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.filtersTxtBtn, emData.Filters);
        await eventMaintenancePage.clickFiltersWithDiscp(caOEventDscp[125].toString())
        await autoActivationReport.selectYPW(emData.Year_Input, emData.Year_2022);
        await eventMaintenancePage.clickOn( eventMaintenancePage.searchBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        let initialEventId = await eventMaintenancePage.getHeaderValue(emData.Event_ID, caOEventDscp[179].toString())
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await eventMaintenancePage.clickOn(await eventMaintenancePage.slideOutClose)
        let eventId = await eventMaintenancePage.getEventId(initialEventId)
        await expect(eventId).toEqual(initialEventId)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await clickOn(eventMaintenancePage.firstRowExpandIcon)
        let caoEventIdNum = await eventMaintenancePage.getPlusTableHeaderValue(emData.plus_Stores_header_CAOID);
        let caoEventId = (await result.getResult(qrString.caOEventID.replace('@evID', initialEventId))).flat();
        await expect(caoEventId.toString()).toEqual(caoEventIdNum.toString())
    })

})
