const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page.js');
const { clickOn } = require("../EventManagerPO/createSFPEvent.page");
const selectPOGPage = require("../EventManagerPO/selectingPogCommodities.page");
const result = require('../../../SQLConnection/server.js.ts');
const qrString = require('../../../SQLConnection/queryStringEM');
const eventSchedulingPage = require('../EventManagerPO/eventScheduling.page')
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page');


describe('validateEventWithMultipleSchedule:CreateSFPEvent flow with multiple schedule selected', () => {

    it('Validate CreateSFPEvent flow with multiple schedule selected in scheduling tab', async () => {
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout');
        await eventMaintanencePage.selectEventType('SFP Event');
        await createSfpEventPage.selectDropdown('division',emData.div_Cincinnati_Operating_Division_014)
        await createSfpEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS);
        await createSfpEventPage.selectDropdown('selectType', "Update");
        let description = await selectPOGPage.stringGenerator("MultiEventSchedule");
        await createSfpEventPage.enterEventResetDescription(description);
        await createSfpEventPage.selectDropdown('vendor', emData.vendor_AZB);
        await createSfpEventPage.submitEventDetails();
        let nextPage = await eventMaintanencePage.linkSubModule.getText();
        await expect(nextPage).toEqual(emData.Event_Schedule);
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        for (let index = 0; index <= 1; index++) {
            await eventMaintanencePage.waitBrowserToLoadPage(2000);
            await eventMaintanencePage.waitForPageLoad(eventSchedulingPage.saveScheduleBtn, emData.Save_Schedule);
            await clickOn(await storeSelectionPage.saveSchedule);
        }
        let week1Text = await (await storeSelectionPage.storeWeekSchedule.getText())
        let week2Text = await (await storeSelectionPage.storeWeekSchedule2.getText())
        let weekText = (await week2Text).replace('Week', '').replace(', ', '/')
        const Week = week1Text.concat(' -', weekText).replace(', ', '/')
        await clickOn(storeSelectionPage.saveGoToStore);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(storePage).toEqual(emData.Store_Selection)
        let divNum14 = (emData.div_Cincinnati_Operating_Division_014.match(/\d/g).join("")).toString();
        let dbScheduledStores = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, divNum14).replace('@num', '30'))).flat();
        await storeSelectionPage.clickCheckBoxText(await dbScheduledStores[5]);
        await storeSelectionPage.clickCheckBoxText(await dbScheduledStores[12]);
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.clickSecondScheduleStoreCheckbox(await dbScheduledStores[27]);
        await clickOn(storeSelectionPage.saveStoreSchedule)
        await eventMaintanencePage.waitBrowserToLoadPage(5000)
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, emData.Select_Pogs)
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(pogPage).toEqual(emData.Select_Pogs)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await selectPOG.selectDropdown('Department', emData.dept_01_GROCERY)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await selectPOG.selectDropdown('Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER);
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await selectPOG.selectDropdown('Commodity *', emData.ResetComplete_All);
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await eventMaintanencePage.clickOn(selectPOG.selectChoosePogByCommodityBtn) 
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(30000);
        await (await selectPOG.gotoEventListButton).waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await clickOn(selectPOG.gotoEventListButton)
        await expect(browser).toHaveUrlContaining("EventMaintenance");
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        let eventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, description);
        let dbEventID = await result.getResult(qrString.SfpEvent_Eventid.replace('eventID', eventId.toString()));
        await expect(eventId).toEqual(dbEventID.flat().toString());
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        let scheduledStrCount = await eventMaintanencePage.getHeaderValue(emData.Header_ScheduledStr, description)
        let popUpSchedule = (await eventMaintanencePage.getColumnValues(emData.event_Schedule_Popup_Schedule_Table, emData.event_Schedule_Popup_Schedule_List)).sort();
        await expect(popUpSchedule.length.toString()).toEqual(scheduledStrCount)
        await expect(popUpSchedule[0]).toEqual(dbScheduledStores[5]);
        await expect(popUpSchedule[1]).toEqual(dbScheduledStores[12]);
        await expect(popUpSchedule[2]).toEqual(dbScheduledStores[27]);
        await eventMaintanencePage.clickCloseButton('Close');
        let arrStringsStore = popUpSchedule.join("', '");
        let dbDataStore = await result.getResult(qrString.sfpEvent_storeNum.replace('@store', arrStringsStore).replace('@eventID', eventId));
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await expect(popUpSchedule.sort()).toEqual(await dbDataStore.flat().sort());
        await eventMaintanencePage.expandEvent(eventId);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        // getHeaderValuesExpanded
        let columnList = await eventMaintanencePage.getValuesOfClickedNum(emData.plus_Stores_header);
        await expect(popUpSchedule.sort()).toEqual( (columnList.flat()).sort());
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await expect(columnList.flat().sort()).toEqual(dbDataStore.flat().sort());
    })

})
