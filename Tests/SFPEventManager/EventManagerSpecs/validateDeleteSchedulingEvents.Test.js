const eventMaintenancePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');


describe('validateDeleteSchedulingEvents: validate user is able to create and delete schedules and move forward from Scheduling to StoreSelection', () => {
   
    it('validateDeleteScheduleAndMoveForwardToStoreSelection', async () => {
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await eventMaintenancePage.open('EventMaintenance');
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await eventMaintenancePage.tableSecondPageBtn.waitForExist({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'});
        await eventMaintenancePage.selectEventType('SFP Event')
        await expect(await eventMaintenancePage.linkCreateSFPEvent.getText()).toEqual(emData.Create_SFP_Event);
        await createSfpEventPage.selectDropdown('division', emData.div_Columbus_Operating_Division_016)
        await createSfpEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        await createSfpEventPage.selectDropdown('selectType', "Update");
        let description = await selectPOG.stringGenerator("ValidateDelete")
        await createSfpEventPage.enterEventResetDescription(description)
        await createSfpEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSfpEventPage.submitEventDetails();
        let nextPage = await eventMaintenancePage.linkSubModule.getText()
        await expect(nextPage).toEqual(emData.Event_Schedule)
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        await createEventSchedulePage.selectDropdown(emData.Fiscal_Week);
        for(let i=0; i<=2; i++){
            await eventMaintenancePage.waitBrowserToLoadPage(1000)
            await clickOn(await storeSelectionPage.saveSchedule);
        }
        await createEventSchedulePage.getFiscalWeekList();
        let initialRowNumber = await storeSelectionPage.arrayLength(await createEventSchedulePage.tableRows);
        for(let i=0; i<=2; i++){
            await eventMaintenancePage.waitBrowserToLoadPage(1000)
            await createEventSchedulePage.clickDeleteEventBtn();
        }
        await createEventSchedulePage.getFiscalWeekList();
        let finalRowNumber = await storeSelectionPage.arrayLength(await createEventSchedulePage.tableRows);
        await createEventSchedulePage.waitForPageLoad(storeSelectionPage.saveSchedule,emData.Save_Schedule);
        await expect(initialRowNumber - finalRowNumber).toEqual(emData.Number_3);
        // always selects the last array value from dropdown
        await createEventSchedulePage.selectFiscalWeekDropdown(emData.Fiscal_Week);
        await clickOn(await storeSelectionPage.saveSchedule);
        // used instead of waitUntil which did not work here
        await createEventSchedulePage.getFiscalWeekList();
        await clickOn(storeSelectionPage.saveGoToStore);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
        let storePage = await storeSelectionPage.linkSubModule.getText();
        await expect(storePage).toEqual(emData.Store_Selection);
    })
    
})
