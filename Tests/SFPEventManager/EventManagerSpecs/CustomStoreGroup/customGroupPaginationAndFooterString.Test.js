const customStoreGroupPage = require('../../EventManagerPO/customStoreGroup.page')
const {assert} = require("chai");
const result = require("../../../../SQLConnection/server.js.ts");
const qrString = require("../../../../SQLConnection/queryStringEM");
const eventMaintanencePage = require('../../EventManagerPO/eventMaintanence.page');

    describe('customGroupPaginationAndFooterString: paginate customGroup table and compare ui data to db  ', () => {

        beforeEach(async () => {
            await eventMaintanencePage.open('EventMaintenance/storeGroupManagement');
            await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000, timeoutMsg: "Error: Table contents for event maintenance page did not load before timeout" });
            await customStoreGroupPage.waitForPageLoad(await customStoreGroupPage.storeGroupManagementText, 'Store Group Management');
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            await customStoreGroupPage.selectCategoryColumnDropdownValue('Event Store Group');
        })

        it('Validate: 1) pages paginate; 2) paginate less than 5 pages ', async () => {
            const uiColumnList = await (await customStoreGroupPage.paginateCustomGroupColumn('Group Name', '3')).slice(1);
            await customStoreGroupPage.waitBrowserToLoadPage(2000);

            let dbColumnData = await (await result.getResult(qrString.customGroupColumnTopList)).flat();
            if (dbColumnData.length > 1) {
            for (let i = 0; i < uiColumnList.length; i++) {
                const uiValue = await uiColumnList[i];

                if (!dbColumnData.includes(uiValue)) {
                await assert.fail(0, 1, `Value "${uiValue}" from UI not found in DB result`);
                }
            }
            } else {
            await assert.fail(0, 1, "Error: table did not paginate or UI data did not match with DB data!");
            }
        })

        it('Validate: 1) page dropdown list; 2) footer string', async () => {

            const pageDropdownTextList = await customStoreGroupPage.getCustomGroupPageDropdownList();
            for (let index = 0; index < pageDropdownTextList.length; index++) {
                await customStoreGroupPage.waitBrowserToLoadPage(2000)
                await customStoreGroupPage.customGroupSelectPageNumber(await pageDropdownTextList[index]);
                await customStoreGroupPage.waitBrowserToLoadPage(2000)
                let rowCount = await customStoreGroupPage.customGroupRowList.length;
                const rowCountFromFooterString = await customStoreGroupPage.getPageRowNumFromFooterString();
                if (rowCount > 0) {
                    await assert.isAtLeast(parseInt(await pageDropdownTextList[index]), parseInt(rowCount), 'PageNumber index value should be equal or bigger than table row number');
                    await expect(rowCount).toEqual(await rowCountFromFooterString);
                } else {
                    await assert.fail(0, 1, "Error: Store Group Management table does not have data !");
                }
            }
        })
    })
