const customStoreGroupPage = require('../../EventManagerPO/customStoreGroup.page')
const emData = require("../../../../TestData/EventManagerData/eventManagerUIData.json");
const createSFPEventPage = require("../../EventManagerPO/createSFPEvent.page");
const storeSelectionPage = require("../../EventManagerPO/storeSelection.page.js");
const {clickOn} = require("../../EventManagerPO/createSFPEvent.page");
const eventMaintenancePage = require("../../EventManagerPO/eventMaintanence.page");
const {assert} = require("chai");
const createSfpEventPage = require("../../EventManagerPO/createSFPEvent.page.js");

let preSelectedStores, divValue;
describe('applyCustomGroupToEventCreation: Validate creating customStore group on step 3 of  Event Creation - StoreSelection   ', () => {

    it('Validate: 1) StoreSelection Override Warning on Store Selection of Event Creation  ', async () => {

        divValue = emData.div_Cincinnati_Operating_Division_014;
        await customStoreGroupPage.waitBrowserToLoadPage(3000);
        await customStoreGroupPage.open('EventMaintenance/storeGroupManagement');
        await customStoreGroupPage.waitBrowserToLoadPage(3000);
        let eventDescription = await createSfpEventPage.addEventDetails1('', 'SFP Event', divValue, emData.eventType_KOMPASS, "Update", 'KOMPASS'+ " -98765", emData.vendor_AZB)
        await createSFPEventPage.submitEventDetails();
        await expect(await eventMaintenancePage.linkSubModule.getText()).toEqual(emData.Event_Schedule);
        await createSfpEventPage.addEventScheduleStep2();
        await expect(await storeSelectionPage.linkSubModule.getText()).toEqual(emData.Store_Selection);
        await createSfpEventPage.selectRandomStoresStep3();
        preSelectedStores = await storeSelectionPage.returnSelectedStores();
        await createSfpEventPage.selectCustomStoreGroup();
        const storeSelectionOverrideMessage = await customStoreGroupPage.getTextOfElement(await customStoreGroupPage.storeSelectionOverrideWarningMessage)
        if(storeSelectionOverrideMessage === emData.storeSelection_Override_Warning_message){
            await customStoreGroupPage.clickSingleButtonFromList(await customStoreGroupPage.storeSelectionModalFooterButtonList, 'Continue');
        }else{
            assert.fail(0,1,'Custom Store modal did not display for override warning message !')
        }
        let customSelectedStores = await storeSelectionPage.returnSelectedStores();
        if(customSelectedStores !== await preSelectedStores){
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            await clickOn(storeSelectionPage.saveStoreSchedule);
        }else{
            assert.fail(0,1,'Custom Store did not get to selected on Store Selection page !')
        }
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, "Select Pogs");
        await expect(await storeSelectionPage.linkSubModule.getText()).toEqual(emData.Select_Pogs);
        await createSfpEventPage.selectPogCommoditiesStep4();
        await expect(browser).toHaveUrlContaining("EventMaintenance");
        await eventMaintenancePage.waitForPageLoad(await eventMaintenancePage.filtersTxtBtn, emData.Filters);
        await eventMaintenancePage.clickFiltersWithDiscp(eventDescription.toString());
        let storeListLengthEmMainPage = await eventMaintenancePage.getLinkText("S-Str");
        let storeListUi = await eventMaintenancePage.getArrayElementsText(await eventMaintenancePage.scheduledStoreModalList);
        if(await storeListUi.length > 1){
            await eventMaintenancePage.clickCloseButton("Close");
            await expect(customSelectedStores).toEqual(await storeListUi.sort());
            await expect(storeListLengthEmMainPage).toEqual((customSelectedStores.length).toString())
            let releaseEventEnabled = await eventMaintenancePage.selectReleaseEvents(emData.ReleaseEvent_Corporate, emData.Header_Action, await eventDescription);
            await expect(releaseEventEnabled).toEqual(true);
        } else{
            await assert.fail(0, 1, "Could not read store values from EM main page !");
        }
       })

})
