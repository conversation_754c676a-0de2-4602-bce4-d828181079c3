const customStoreGroupPage = require('../../EventManagerPO/customStoreGroup.page')
const {assert} = require("chai");
const emData = require("../../../../TestData/EventManagerData/eventManagerUIData.json");
const eventMaintanencePage = require('../../EventManagerPO/eventMaintanence.page');

let unselectedRandomDivisionValue, categoryDropdownTextBefore, groupCategoryType;
groupCategoryType = ['Shelf Strip Exclusion', 'Kompass Exclusions'];
for (let index = 0; index < groupCategoryType.length; index++) {

describe('createShelfStripAndCompassExclusions: Validate shelf strip exclusion ' + `${index}` + ' ' +  `${groupCategoryType[index]}`, () => {

    beforeEach(async () => {

        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintenance/storeGroupManagement');
        await eventMaintanencePage.tableFirstRow.waitForExist({
            timeout: 90000,
            timeoutMsg: "Error: Table contents for event maintenance page did not load before timeout"
        });
        await customStoreGroupPage.waitForPageLoad(await customStoreGroupPage.storeGroupManagementText, 'Store Group Management');
        await customStoreGroupPage.waitBrowserToLoadPage(5000);
        categoryDropdownTextBefore = await customStoreGroupPage.categoryFilterDropdown.getText();
    })

    it('Validate: 1)create ShelfStrip/Kompass Exclusion with unique division value ; 2) validate warning messages; ' + 'ExclusionType:  ' +  `${groupCategoryType[index]}`, async () => {

        await customStoreGroupPage.selectCategoryColumnDropdownValue(await groupCategoryType[index]);
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        const categoryDropdownTextAfter = await customStoreGroupPage.categoryFilterDropdown.getText();
        await assert.notEqual(categoryDropdownTextBefore, categoryDropdownTextAfter, 'Error: Category dropdowns did not get to select !')
        const tableDivisionList = await customStoreGroupPage.getCustomGroupColumnList('Division');
        const tableDivisionListUnique = await customStoreGroupPage.getUniqueArrayValue(await tableDivisionList);
        await customStoreGroupPage.clickCreateNewGroup();
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        let createDivisionDropdownList = await customStoreGroupPage.getAllDropdownValues(await customStoreGroupPage.selectDivisionDropdown, await customStoreGroupPage.divisionDropdownList)
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        unselectedRandomDivisionValue = await customStoreGroupPage.getRandomUniqueValue(await tableDivisionListUnique, await createDivisionDropdownList)
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        await customStoreGroupPage.selectCustomStoreDivisionValue(await unselectedRandomDivisionValue);
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        const dynamicString = await customStoreGroupPage.randomStringGenerator( 15);
        console.log('$$$ dynamicString', dynamicString)
        await customStoreGroupPage.genericFillGroupNameInputBox('createdInputBox', await dynamicString);
        await customStoreGroupPage.waitBrowserToLoadPage(5000);
        await customStoreGroupPage.randomClickStoreCheckBox();
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        await customStoreGroupPage.submitButton.click();
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        const groupCategoryWarningMessage = await customStoreGroupPage.getTextOfElement(await customStoreGroupPage.groupCategoryErrorMessage);
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        if (groupCategoryWarningMessage === emData.groupCategory_must_be_selected_message) {
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            await customStoreGroupPage.selectGroupCategory(await groupCategoryType[index]);
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            await customStoreGroupPage.submitButton.click();
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            let successMessage = await customStoreGroupPage.customGroupCreatedMessage.getText();
            await customStoreGroupPage.waitBrowserToLoadPage(1000);
            await expect(successMessage).toEqual(emData.storeGroup_successfully_created_message);
            const tableDivisionListAfterSseCreated = await customStoreGroupPage.getCustomGroupColumnList('Division');
            const arrayContainsValue = await customStoreGroupPage.isValueInArray(tableDivisionListAfterSseCreated, await unselectedRandomDivisionValue);
            await expect(arrayContainsValue).toEqual(true);
        } else {
            assert.fail(0, 1, 'Error: groupCategoryWarningMessage did not appear')
        }
    })

    it('Validate ShelfStrip/Kompass Exclusion dropdown value is disabled when existing division is used to create new group  ' + 'ExclusionType:  ' +  `${groupCategoryType[index]}`,  async () => {

        await customStoreGroupPage.selectCategoryColumnDropdownValue(await groupCategoryType[index]);
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        const tableDivisionList = await customStoreGroupPage.getCustomGroupColumnList('Division');
        await customStoreGroupPage.clickCreateNewGroup();
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        await customStoreGroupPage.selectCustomStoreDivisionValue(await tableDivisionList[0].toString());
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        const disabledDropdownValue = await customStoreGroupPage.findDisabledOption();
        await expect(await disabledDropdownValue).toEqual(await groupCategoryType[index]);
    })


    it('Validate GroupCategory column filter for EventStoreGroup ShelfStripExclusion, KompassExclusions; ' + 'ExclusionType:  ' +  `${groupCategoryType[index]}`, async () => {

        const groupCategoryType = ['Event Store Group', 'Shelf Strip Exclusion','Kompass Exclusions'];
        for(let index=0; index < await (groupCategoryType.length);index++){
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            await customStoreGroupPage.selectCategoryColumnDropdownValue(groupCategoryType[index]);
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            let tableCategoryList = await customStoreGroupPage.getCustomGroupColumnList('Group Category');
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            let tableCategoryListUnique = await customStoreGroupPage.getUniqueArrayValue(await tableCategoryList);
            await expect(await tableCategoryListUnique.toString()).toEqual(groupCategoryType[index]);
            await customStoreGroupPage.groupCategoryColumnClear.click();
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
        }
    })

})
}