let emData = require('../../../../TestData/EventManagerData/eventManagerUIData.json')
const envVariables = require("../../UserRoles/roles.js");
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const result = require('../../../../SQLConnection/server.js.ts');
const qrString = require('../../../../SQLConnection/queryStringEM');
const ENV = process.env.ENV
const customStoreGroupPage = require("../../EventManagerPO/customStoreGroup.page");
const {assert} = require("chai");
const eventMaintanencePage = require('../../EventManagerPO/eventMaintanence.page');

let successMessage, selectedStore, dynamicStoreGroupName, storeValueFromTableUi, groupNameColumnListUnique,topSingleGroupNameColumnBeforeDelete,divisionDefaultSelectedValue ;

 before(async () => {

        await testSetup(envVariables.divisionalUserEM[ENV], envVariables.divisionalPasswordEM[ENV]);
    })

describe('customStoreGroupDivisionalUser: Validate creating custom store group for divisional user when division dropdown is disabled ', async () => {

   
    beforeEach(async () => {
        await eventMaintanencePage.open('EventMaintenance/storeGroupManagement');
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000, timeoutMsg: "Error: Table contents for event maintenance page did not load before timeout" });
        await customStoreGroupPage.waitForPageLoad(await customStoreGroupPage.storeGroupManagementText, 'Store Group Management');
        await customStoreGroupPage.waitBrowserToLoadPage(3000);
        divisionDefaultSelectedValue = emData.div_016_Columbus_Operating_Division;
        await customStoreGroupPage.selectCategoryColumnDropdownValue('Event Store Group');
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
    })

    it('validate 1) Division dropdown is default selected; 2)Division dropdown is disabled; 3) Clicking clear division dropdown' +
        'should not clear out default selected division dropdown value; 4) select store value from storeList matches with newly created custom' +
        'store group store value on the table', async () => {

        await customStoreGroupPage.clickCreateNewGroup();
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        const divDefaultSelected = await customStoreGroupPage.locatorDisabled(await customStoreGroupPage.divisionDropdownDefaultSelectedValue);
        await expect(await divDefaultSelected).toEqual(true); 
        let divDefaultSelectedValueBefore = await customStoreGroupPage.getTextOfElement(await customStoreGroupPage.divisionDropdownDefaultSelectedValue);
        await expect(divDefaultSelectedValueBefore).toEqual(await divisionDefaultSelectedValue);
        await customStoreGroupPage.clickOn(await customStoreGroupPage.divisionDropdownClearValueIcon);
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        let divDefaultSelectedValueAfter = await customStoreGroupPage.getTextOfElement(await customStoreGroupPage.divisionDropdownDefaultSelectedValue);
        await expect(divDefaultSelectedValueBefore).toEqual(divDefaultSelectedValueAfter);
        dynamicStoreGroupName = await customStoreGroupPage.stringGenerator('DivisionUser - ');
        await customStoreGroupPage.genericFillGroupNameInputBox('createdInputBox', await dynamicStoreGroupName);
        await customStoreGroupPage.waitBrowserToLoadPage(1000);
        await customStoreGroupPage.selectGroupCategory('Event Store Group');
        selectedStore = await customStoreGroupPage.randomClickStoreCheckBox();
        await customStoreGroupPage.waitBrowserToLoadPage(3000);
        await customStoreGroupPage.clickSingleButtonFromList(await customStoreGroupPage.submitCloseButtonList, 'Submit');
        await customStoreGroupPage.waitBrowserToLoadPage(1000);
        successMessage = await customStoreGroupPage.customGroupCreatedMessage.getText();
        if (successMessage.includes(emData.storeGroup_successfully_created_message)) {
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            await customStoreGroupPage.enterSearchInput('Group Name', dynamicStoreGroupName);
            const groupNameColumnList = await customStoreGroupPage.getCustomGroupColumnList('Group Name');
            groupNameColumnListUnique = await customStoreGroupPage.getUniqueArrayValue(groupNameColumnList);
            await expect(groupNameColumnListUnique.toString()).toEqual(dynamicStoreGroupName);
            storeValueFromTableUi = await customStoreGroupPage.returnTopRowStoreValue();
            await expect(storeValueFromTableUi).toEqual(selectedStore.toString());
            let createCustomStoreDbData = await (await result.getResult(qrString.singleCustomGroupName.replace('@customGroupName', await dynamicStoreGroupName)));
            await expect(await (createCustomStoreDbData.flat()).toString()).toEqual(await dynamicStoreGroupName);
        } else {
            await assert.fail(0, 1, "Store is not selected or success message did not appear !");
        }
    })

    it('Validate clicking "Yes,delete it" button deletes the custom store ', async () => {

        topSingleGroupNameColumnBeforeDelete = await (await customStoreGroupPage.getCustomGroupColumnList('Group Name'))[0];
        await customStoreGroupPage.clickDeleteCustomStore();
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        await customStoreGroupPage.clickSingleButtonFromList(await customStoreGroupPage.deleteStoreModalFooterButtonList, emData.yes_delete_it);
        const topSingleGroupNameColumnAfterDelete = await (await customStoreGroupPage.getCustomGroupColumnList('Group Name'))[0];
        let deletedDbData = await (await result.getResult(qrString.singleCustomGroupName.replace('@customGroupName', await topSingleGroupNameColumnBeforeDelete)));
        if(deletedDbData.length === 0){
            await assert.notEqual(topSingleGroupNameColumnBeforeDelete, topSingleGroupNameColumnAfterDelete, 'Error: selected store is not deleted !');
        } else{
            await assert.fail(0, 1, "Top custom store group is not deleted !");
        }
    })

    it('Validate edit customStoreGroup row data: 1) edit group Name; 2)edit store number', async () => {

        await customStoreGroupPage.clickEditNewGroup();
        await customStoreGroupPage.waitBrowserToLoadPage(3000);
        await expect(await customStoreGroupPage.locatorDisabled(await customStoreGroupPage.defaultDivisionSelectValue)).toEqual(true);
        const generateStringForUpdate = await customStoreGroupPage.stringGenerator("update - ");
        await customStoreGroupPage.updateGroupNameValue(generateStringForUpdate);
        await customStoreGroupPage.randomClickStoreCheckBox();
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        await customStoreGroupPage.clickSingleButtonFromList(await customStoreGroupPage.submitCloseButtonList, 'Save Changes');
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        const updatedMessage = await customStoreGroupPage.getTextOfElement(await customStoreGroupPage.storeGroupUpdatedMessage);
        if(await updatedMessage.includes(emData.store_group_updated_message)){
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            await customStoreGroupPage.genericFillGroupNameInputBox('tableInputBox', generateStringForUpdate);
            await customStoreGroupPage.waitBrowserToLoadPage(1000);
        } else{
            await assert.fail(0, 1, "Store Group Updated message did not display !");
        }
        let groupNameColumnListUpdated = await customStoreGroupPage.getCustomGroupColumnList('Group Name');
        await assert.notEqual(await groupNameColumnListUnique, groupNameColumnListUpdated, 'Error: Two values should not be the same !')
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        const storeValueFromTableUiUpdated = await customStoreGroupPage.returnTopRowStoreValue();
        await customStoreGroupPage.waitBrowserToLoadPage(1000);
        let customStoreNameUpdatedDbData = await (await result.getResult(qrString.singleCustomGroupName.replace('@customGroupName', groupNameColumnListUpdated.toString())));
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        if (await (await customStoreNameUpdatedDbData.flat()).toString() === groupNameColumnListUpdated.toString()) {
            await customStoreGroupPage.waitBrowserToLoadPage(3000);
            await assert.notEqual(await storeValueFromTableUi, storeValueFromTableUiUpdated, 'Error:Two values should not be the same !')
        } else {
            await assert.fail(0, 1, "Group Name is not updated from backend !");
        }
    })

    it('Validate Division column only contains values match to default selected value ', async () => {

        const divisionColumnList = await (await customStoreGroupPage.getCustomGroupColumnList('Division'));
        const divisionColumnListUnique = await customStoreGroupPage.getUniqueArrayValue(await divisionColumnList);
        if(divisionColumnListUnique.length === 1){
         await expect(divisionColumnListUnique.toString()).toEqual(await divisionDefaultSelectedValue);
        } else{
            await assert.fail(0, 1, "There are other division values exist on the table other than 016 !");
        }
    })

    it('Validate Division dropdown has only one value when click dropdown SelectAll checkbox', async () => {

        const divisionDropdownTextBefore = await customStoreGroupPage.divisionFilterDropdown.getText();
        await customStoreGroupPage.selectAllDivisionDropdownValues();
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        let countSelectedCheckBoxAfter = await customStoreGroupPage.returnNumOfSelectedCheckBox(await customStoreGroupPage.divisionCloseDropdownButton, await customStoreGroupPage.customGroupDivisionDropdownList);
        if(countSelectedCheckBoxAfter === 1){
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            const divisionDropdownTextAfter = await customStoreGroupPage.divisionFilterDropdown.getText();
            await assert.notEqual(divisionDropdownTextBefore, divisionDropdownTextAfter, 'Error: Division dropdowns did not get to select !');
            await expect(divisionDropdownTextAfter).toEqual(await divisionDefaultSelectedValue);
        } else{
            await assert.fail(0, 1, "There are other division values exist in the dropdown list other than 016 !");
        }
    })

})
