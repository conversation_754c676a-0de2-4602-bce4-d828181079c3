const customStoreGroupPage = require('../../EventManagerPO/customStoreGroup.page')
const resetCompletionPage = require("../../EventManagerPO/resetCompletion.page.js");
const {assert} = require("chai");
const emData = require("../../../../TestData/EventManagerData/eventManagerUIData.json");
const eventMaintanencePage = require('../../EventManagerPO/eventMaintanence.page');

let divisionNumber;
describe('validateWarningMessages: Validate warning messages ', () => {

    beforeEach( async () =>{

        divisionNumber = emData.division_Cincinnati_Operating_Division_014;
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintenance/storeGroupManagement');
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000, timeoutMsg: "Error: Table contents for event maintenance page did not load before timeout" });
        await customStoreGroupPage.waitForPageLoad(await customStoreGroupPage.storeGroupManagementText,'Store Group Management');
        await customStoreGroupPage.clickCreateNewGroup();
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        await customStoreGroupPage.selectCustomStoreDivisionValue(await divisionNumber);
         await customStoreGroupPage.waitBrowserToLoadPage(2000);
         
    })

    it('Validate: 1)cancel selectedDropdownValue button; 2) warning messages when GroupCategory, GroupName and Division dropdowns are not selected; 3)then warning messages disappear after selecting values;' +
        '3)Stores are not selected warning message ;4)close button closes the CustomGroup slide out panel;  ', async () => {

        const textBeforeClickDropdownCancelValue = await customStoreGroupPage.selectDivisionDropdown.getText();
        await customStoreGroupPage.divisionDropdownClearButton.click();
        await customStoreGroupPage.waitBrowserToLoadPage(1000);
        const textAfterClickDropdownCancelValue = await customStoreGroupPage.selectDivisionDropdown.getText();
        await assert.notEqual(textBeforeClickDropdownCancelValue, textAfterClickDropdownCancelValue,'Error: two values should NOT be the same !')
        const warningMessageList = ['Group must be named.','Division must be selected to create a new group.','Group category must be selected.' ];
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        await customStoreGroupPage.submitButton.click();
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        const groupUnselectedMessage = await customStoreGroupPage.getArrayElementsText(await customStoreGroupPage.groupAndDivisionMustBeSelectedWarningMessage);
        await expect(warningMessageList).toEqual(groupUnselectedMessage);
        const dynamicString = await customStoreGroupPage.stringGenerator('Custom WarningMessage - ');
        await customStoreGroupPage.genericFillGroupNameInputBox('createdInputBox', await dynamicString);
        await customStoreGroupPage.selectCustomStoreDivisionValue(await divisionNumber);
        await customStoreGroupPage.waitBrowserToLoadPage(1000);
        await customStoreGroupPage.selectGroupCategory('Event Store Group');
        for(let index=0;index<warningMessageList.length;index++){
            const warningMessageExist = await resetCompletionPage.elementExists(warningMessageList[index]);
            await expect(warningMessageExist).toEqual(false);
        }
        await customStoreGroupPage.submitButton.click();
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        const storeUnselectedMessage = await customStoreGroupPage.getTextOfElement(await customStoreGroupPage.selectStoreWarningMessage);
        if(storeUnselectedMessage === emData.store_not_selected_message){
            await customStoreGroupPage.randomClickStoreCheckBox();
            const storeMessageExist = await resetCompletionPage.elementExists(await customStoreGroupPage.selectStoreWarningMessage);
            await expect(storeMessageExist).toEqual(false);
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
        } else{
            await assert.fail(0, 1, "Error: Store unselected warning message still exists after selecting stores ! ")
        }
        await customStoreGroupPage.closeSlideOutPanelButton.click();
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        const closeSlitOutPanelButtonExist = await customStoreGroupPage.elementExists(await customStoreGroupPage.closeSlideOutPanelButton);
        await expect(await closeSlitOutPanelButtonExist).toEqual(false);
    })

    it('Validate store list values are sorted in ascending order ', async () => {

        const storeListNumbers = await customStoreGroupPage.getArrayElementsText(await customStoreGroupPage.storeNumberList);
        await customStoreGroupPage.waitBrowserToLoadPage(5000);
        const ascendingOrder = await customStoreGroupPage.isOrdered(storeListNumbers, 'ascending');
        await expect(await ascendingOrder).toEqual(true);
    })

    it('Validate 1)SelectAll checkBox button; 2) CancelButton cancels slideOut panel; ', async () => {

        await customStoreGroupPage.selectAllStoresCheckBox.click();
        let countSelectedCheckBoxAfter = await customStoreGroupPage.returnNumOfSelectedCheckBox(await customStoreGroupPage.selectedStoresNumberText, await customStoreGroupPage.storeNumberCheckBoxHighLightList);
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        const selectedStoreNumberText = await customStoreGroupPage.selectedStoresNumberText.getText();
        const selectedStoreNumberTextInt = await customStoreGroupPage.extractDigits(selectedStoreNumberText);
        await expect(await countSelectedCheckBoxAfter.toString()).toEqual(await selectedStoreNumberTextInt);
        await customStoreGroupPage.cancelButton.click();
        const cancelButtonExist = await customStoreGroupPage.elementExists(await customStoreGroupPage.cancelButton);
        await expect(await cancelButtonExist).toEqual(false);
    })

    it('Validate SelectedStores string on CreateNewGroup slide out panel: store number (1,3,5,7)', async () => {

        let totalStoreCount = await customStoreGroupPage.storeNumberList.length;
        if (totalStoreCount >= 15) {
            let numOfSelectedCheckBoxes = [totalStoreCount - 1, totalStoreCount - 4, totalStoreCount - 8, totalStoreCount - 12];
            for (let index = 0; index <= numOfSelectedCheckBoxes.length - 1; index++) {
                await customStoreGroupPage.clickCheckBox(await customStoreGroupPage.selectedStoresNumberText, await customStoreGroupPage.storeNumberCheckBoxHighLightList, numOfSelectedCheckBoxes[index]);
                await customStoreGroupPage.waitBrowserToLoadPage(3000);
                let countHighLightedStoreCheckBox = await customStoreGroupPage.returnNumOfSelectedCheckBox(await customStoreGroupPage.selectedStoresNumberText, await customStoreGroupPage.storeNumberCheckBoxHighLightList);
                const selectedStoreNumberText = await customStoreGroupPage.selectedStoresNumberText.getText();
                const selectedStoreNumberTextInt = await customStoreGroupPage.extractDigits(selectedStoreNumberText);
                await expect(countHighLightedStoreCheckBox.toString()).toEqual(await selectedStoreNumberTextInt);
            }
        } else{
            await assert.fail(0, 1, "Error: store list either did not appear or did not have enough stores for selection ! ")
        }
    })

})
