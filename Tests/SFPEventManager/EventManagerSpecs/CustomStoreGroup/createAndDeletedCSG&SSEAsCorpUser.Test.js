const envVariables = require("../../UserRoles/roles.js");
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const result = require('../../../../SQLConnection/server.js.ts');
const qrString = require('../../../../SQLConnection/queryStringEM');
const ENV = process.env.ENV
const customStoreGroupPage = require("../../EventManagerPO/customStoreGroup.page");
const { assert } = require("chai");
const emData = require('../../../../TestData/EventManagerData/eventManagerUIData.json')

let successMessage, selectedStore, dynamicStoreGroupName, storeValueFromTableUi, groupNameColumnListUnique, topSingleGroupNameColumnBeforeDelete,
    topGroupNameValue, storeValueFromTopCustomGroup, groupCategoryType, tableDivisionList;

before(async () => {
    await testSetup(await envVariables.CorpUserEM[ENV], await envVariables.CorpPasswordEM[ENV]);
})

        describe('createAndDeletedCSG&SSEAsCorpUser: Validate create and delete CustomStoreGroup and ShelfStripExclusion for CorpUser  '  , async () => {

        
        it('Validate: 1) store value matched and unmatched warning message ; 2) create custom store group for CorpUser; ' , async () => {

            groupCategoryType =  ['Event Store Group', 'Shelf Strip Exclusion'];
              for(let index=0; index<groupCategoryType.length;index++) {

            await customStoreGroupPage.waitBrowserToLoadPage(3000);
            await customStoreGroupPage.open('EventMaintenance/storeGroupManagement');
            await customStoreGroupPage.waitBrowserToLoadPage(3000);
            await customStoreGroupPage.waitForPageLoad(customStoreGroupPage.storeGroupManagementText, 'Store Group Management');
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            await customStoreGroupPage.selectCategoryColumnDropdownValue(groupCategoryType[index]);
            topGroupNameValue = (await customStoreGroupPage.getCustomGroupColumnList('Group Name'))[0];
            tableDivisionList = await customStoreGroupPage.getCustomGroupColumnList('Division');
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            storeValueFromTopCustomGroup = (await customStoreGroupPage.returnTopRowStoreValue()).split('\n');
            if (groupCategoryType[index] === 'Shelf Strip Exclusion') {
                await customStoreGroupPage.waitBrowserToLoadPage(2000);
                const tableDivisionListUnique = await customStoreGroupPage.getUniqueArrayValue(tableDivisionList);
                await customStoreGroupPage.waitBrowserToLoadPage(1000);
                await customStoreGroupPage.clickCreateNewGroup();
                await customStoreGroupPage.waitBrowserToLoadPage(2000);
                let createDivisionDropdownList = await customStoreGroupPage.getAllDropdownValues(customStoreGroupPage.selectDivisionDropdown, customStoreGroupPage.divisionDropdownList);
                await customStoreGroupPage.waitBrowserToLoadPage(2000);
                let unselectedRandomDivisionValue = await customStoreGroupPage.getRandomUniqueValue(tableDivisionListUnique, createDivisionDropdownList);
                await customStoreGroupPage.waitBrowserToLoadPage(2000);
                await customStoreGroupPage.selectCustomStoreDivisionValue(unselectedRandomDivisionValue);
            } else {
                await customStoreGroupPage.clickCreateNewGroup();
                await customStoreGroupPage.waitBrowserToLoadPage(2000);
                await customStoreGroupPage.selectCustomStoreDivisionValue(tableDivisionList[0]);
            }
            await customStoreGroupPage.waitBrowserToLoadPage(1000);
            dynamicStoreGroupName = await customStoreGroupPage.stringGenerator('Create - CorpUser - ');
            await customStoreGroupPage.genericFillGroupNameInputBox('createdInputBox', dynamicStoreGroupName);
            await customStoreGroupPage.waitBrowserToLoadPage(1000);
            await customStoreGroupPage.selectGroupCategory(groupCategoryType[index]);
            await customStoreGroupPage.selectMultipleStoreValues(storeValueFromTopCustomGroup);
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            if (groupCategoryType[index] === 'Event Store Group') {
                (await customStoreGroupPage.storeMatchMessage).waitForExist({timeout: 15000})
                const selectedStoresMatchedMessage = await customStoreGroupPage.storeMatchMessage.getText();
                await expect(selectedStoresMatchedMessage).toEqual(emData.store_matched_message);
                await customStoreGroupPage.waitBrowserToLoadPage(1000);
                await customStoreGroupPage.randomClickStoreCheckBox();
                selectedStore = await customStoreGroupPage.returnNumOfSelectedCheckBox(customStoreGroupPage.storeMatchMessage, customStoreGroupPage.storeNumberCheckBoxHighLightList);
                const selectedStoresUnMatchedMessage = await customStoreGroupPage.storeMatchMessage.getText();
                await customStoreGroupPage.waitBrowserToLoadPage(1000);
                await expect(selectedStoresUnMatchedMessage).toEqual(emData.store_unmatched_message);
            } else {
                await customStoreGroupPage.randomClickStoreCheckBox();
            }
            await customStoreGroupPage.waitBrowserToLoadPage(3000);
            await customStoreGroupPage.clickSingleButtonFromList(customStoreGroupPage.submitCloseButtonList, 'Submit');
            await customStoreGroupPage.waitBrowserToLoadPage(1000);
            successMessage = await customStoreGroupPage.customGroupCreatedMessage.getText();
            if (successMessage.includes(emData.storeGroup_successfully_created_message)) {
                await customStoreGroupPage.waitBrowserToLoadPage(2000);
                await customStoreGroupPage.enterSearchInput('Group Name', dynamicStoreGroupName);
                const groupNameColumnList = await customStoreGroupPage.getCustomGroupColumnList('Group Name');
                groupNameColumnListUnique = await customStoreGroupPage.getUniqueArrayValue(groupNameColumnList);
                await expect(groupNameColumnListUnique.toString()).toEqual(dynamicStoreGroupName);
                storeValueFromTableUi = await customStoreGroupPage.returnTopRowStoreValue();
                if (groupCategoryType[index] === 'Event Store Group') {
                    await expect((await storeValueFromTableUi.split('\n')).length).toEqual(selectedStore);
                }
                let createCustomStoreDbData = await result.getResult(qrString.singleCustomGroupName.replace('@customGroupName', dynamicStoreGroupName));
                await expect((createCustomStoreDbData.flat()).toString()).toEqual(dynamicStoreGroupName);
            } else {
                await assert.fail(0, 1, "Store is not selected or success message did not appear!");
            }
             // Delete newly created CSG AND SSE
            await customStoreGroupPage.selectCategoryColumnDropdownValue(await groupCategoryType[index]);
            topSingleGroupNameColumnBeforeDelete = await (await customStoreGroupPage.getCustomGroupColumnList('Group Name'))[0];
            await customStoreGroupPage.clickDeleteCustomStore();
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            await customStoreGroupPage.clickSingleButtonFromList(customStoreGroupPage.deleteStoreModalFooterButtonList, emData.yes_delete_it);
            const topSingleGroupNameColumnAfterDelete = (await customStoreGroupPage.getCustomGroupColumnList('Group Name'))[0];
            let deletedDbData = await result.getResult(qrString.singleCustomGroupName.replace('@customGroupName', topSingleGroupNameColumnBeforeDelete));
            if (deletedDbData.length === 0) {
                await assert.notEqual(topSingleGroupNameColumnBeforeDelete, topSingleGroupNameColumnAfterDelete, 'Error: selected store is not deleted!');
            } else {
                await assert.fail(0, 1, "Top custom store group is not deleted!");
            }
        }
        })
           })
        
        
