const customStoreGroupPage = require('../../EventManagerPO/customStoreGroup.page')
const {assert} = require("chai");
let emData = require('../../../../TestData/EventManagerData/eventManagerUIData.json')
const result = require("../../../../SQLConnection/server.js.ts");
const qrString = require("../../../../SQLConnection/queryStringEM");
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
// const ENV = process.env.ENV
// const envVariables = require("../../UserRoles/roles.js");
const eventMaintanencePage = require('../../EventManagerPO/eventMaintanence.page');

let successMessage, selectedStore, dynamicStoreGroupName, storeValueFromTableUi, groupNameColumnListUnique,groupCategoryType;

        describe('createdAndEditNewCustomStoreGroup: Validate create and edit new custom store group for : ' , async () => { 
     
        // before(async () => {
        //     await testSetup(await envVariables.CorpUserEM[ENV], await envVariables.CorpPasswordEM[ENV]);
        // })

        after(async () => {
            await browser.deleteSession();
        });

        it('Validate: 1)create custom store group; 2) validate newly created customStoreGroup on the Custom Management table;' +
            '3) validate store values under new storeGroup expendable feature; 4) edit group Name; 5)edit store number' , async () => {  

                groupCategoryType =  ['Event Store Group', 'Shelf Strip Exclusion'];
                for(let index=0; index<groupCategoryType.length;index++) {

                    await eventMaintanencePage.waitBrowserToLoadPage(6000)
                    await eventMaintanencePage.open('EventMaintenance/storeGroupManagement');
                    await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000, timeoutMsg: "Error: Table contents for event maintenance page did not load before timeout" });
                    await customStoreGroupPage.waitForPageLoad(await customStoreGroupPage.storeGroupManagementText, 'Store Group Management');
                    await customStoreGroupPage.waitBrowserToLoadPage(2000);
                    if(await groupCategoryType[index] === 'Shelf Strip Exclusion'){
                        await customStoreGroupPage.selectCategoryColumnDropdownValue('Shelf Strip Exclusion');
                        await customStoreGroupPage.waitBrowserToLoadPage(2000);
                        const tableDivisionList = await customStoreGroupPage.getCustomGroupColumnList('Division');
                        const tableDivisionListUnique = await customStoreGroupPage.getUniqueArrayValue(await tableDivisionList);
                        await customStoreGroupPage.waitBrowserToLoadPage(1000);
                        await customStoreGroupPage.clickCreateNewGroup();
                        await customStoreGroupPage.waitBrowserToLoadPage(2000);
                        let createDivisionDropdownList = await customStoreGroupPage.getAllDropdownValues(await customStoreGroupPage.selectDivisionDropdown, await customStoreGroupPage.divisionDropdownList)
                        await customStoreGroupPage.waitBrowserToLoadPage(2000);
                        let unselectedRandomDivisionValue = await customStoreGroupPage.getRandomUniqueValue(await tableDivisionListUnique, await createDivisionDropdownList);
                        await customStoreGroupPage.waitBrowserToLoadPage(2000);
                        await customStoreGroupPage.selectCustomStoreDivisionValue(await unselectedRandomDivisionValue);
                    } else{
                        await customStoreGroupPage.clickCreateNewGroup();
                        await customStoreGroupPage.waitBrowserToLoadPage(2000);
                        await customStoreGroupPage.selectCustomStoreDivisionValue(emData.division_Cincinnati_Operating_Division_014);
                    }
                    await customStoreGroupPage.waitBrowserToLoadPage(1000);
                    dynamicStoreGroupName = await customStoreGroupPage.stringGenerator('Create - CustomGroup - 1234567890');
                    await customStoreGroupPage.genericFillGroupNameInputBox('createdInputBox', dynamicStoreGroupName);
                    await customStoreGroupPage.waitBrowserToLoadPage(2000);
                    await customStoreGroupPage.selectGroupCategory(await groupCategoryType[index]);
                    await customStoreGroupPage.waitBrowserToLoadPage(2000);
                    selectedStore = await customStoreGroupPage.randomClickStoreCheckBox();
                    await customStoreGroupPage.waitBrowserToLoadPage(3000);
                    await customStoreGroupPage.clickSingleButtonFromList(await customStoreGroupPage.submitCloseButtonList, 'Submit');
                    (await customStoreGroupPage.customGroupCreatedMessage).waitForExist({timeout: 15000})
                    successMessage = await customStoreGroupPage.customGroupCreatedMessage.getText();
                    if (await successMessage.includes(emData.storeGroup_successfully_created_message)) {
                        await customStoreGroupPage.waitBrowserToLoadPage(2000);
                        await customStoreGroupPage.enterSearchInput('Group Name', dynamicStoreGroupName);
                        const groupNameColumnList = await customStoreGroupPage.getCustomGroupColumnList('Group Name');
                        groupNameColumnListUnique = await customStoreGroupPage.getUniqueArrayValue(groupNameColumnList);
                        await expect(groupNameColumnListUnique.toString()).toEqual(dynamicStoreGroupName);
                        storeValueFromTableUi = await customStoreGroupPage.returnTopRowStoreValue();
                        await expect(storeValueFromTableUi.toString()).toEqual(selectedStore.toString()); 
                        let createCustomStoreDbData = await (await result.getResult(qrString.singleCustomGroupName.replace('@customGroupName', await dynamicStoreGroupName)));
                        await expect(await (createCustomStoreDbData.flat()).toString()).toEqual(await dynamicStoreGroupName);
                    } else {
                        await assert.fail(0, 1, "Store is not selected or success message did not appear !");
                    }
            
            await customStoreGroupPage.enterSearchInput('Number of Stores', storeValueFromTableUi);
            await customStoreGroupPage.customGroupTableTopExpandButton.click();
            await customStoreGroupPage.waitBrowserToLoadPage(3000);
            await customStoreGroupPage.selectCategoryColumnDropdownValue(await groupCategoryType[index]);
            const storeValueFromTableUiFinally = await customStoreGroupPage.returnTopRowStoreValue();            
            await expect(storeValueFromTableUi).toEqual(storeValueFromTableUiFinally);

            // edit newly created CSG 
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            await customStoreGroupPage.clickEditNewGroup();
            await expect(await customStoreGroupPage.locatorDisabled(await customStoreGroupPage.defaultDivisionSelectValue)).toEqual(true);
            const generateStringForUpdate = await customStoreGroupPage.stringGenerator("Update - 1234567890");
            await customStoreGroupPage.updateGroupNameValue( await generateStringForUpdate);
            await customStoreGroupPage.randomClickStoreCheckBox();
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            await customStoreGroupPage.clickSingleButtonFromList(await customStoreGroupPage.submitCloseButtonList, 'Save Changes');
            await customStoreGroupPage.waitBrowserToLoadPage(1000);
            let groupNameColumnListUpdated = await customStoreGroupPage.getCustomGroupColumnList('Group Name');
            let groupNameColumnListUpdatedRemoveEmptyString = await customStoreGroupPage.removeEmptyStringFromArray(groupNameColumnListUpdated);
            await assert.notEqual(await groupNameColumnListUnique, await groupNameColumnListUpdated, 'Error: Two values should not be the same !')
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            const clearInputValue = await customStoreGroupPage.clearValueFromInputBox('Group Name');
            await expect(clearInputValue).toEqual(true);
            await customStoreGroupPage.enterSearchInput('Group Name', await generateStringForUpdate);
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            await customStoreGroupPage.customGroupTableTopExpandButton.click();
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            const storeValueFromTableUiUpdated = await customStoreGroupPage.returnTopRowStoreValue();
            await customStoreGroupPage.waitBrowserToLoadPage(1000);
            let customStoreNameUpdatedDbData = await (await result.getResult(qrString.singleCustomGroupName.replace('@customGroupName', await groupNameColumnListUpdatedRemoveEmptyString.toString())));
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            if (await (await customStoreNameUpdatedDbData.flat()).toString() === groupNameColumnListUpdatedRemoveEmptyString.toString()) {
                await customStoreGroupPage.waitBrowserToLoadPage(3000);
                await assert.notEqual(await storeValueFromTableUi, storeValueFromTableUiUpdated, 'Error:Two values should not be the same !')
            } else {
                await assert.fail(0, 1, "Group Name is not updated from backend !");
            }

            // delete newly updated groups
            await customStoreGroupPage.clickDeleteCustomStore();
                        await customStoreGroupPage.waitBrowserToLoadPage(2000);
                        await customStoreGroupPage.clickSingleButtonFromList(await customStoreGroupPage.deleteStoreModalFooterButtonList, emData.yes_delete_it);
                        let deleteConfirmMessage = await (await customStoreGroupPage.customGroupDeletedMessage).getText();
                        if (deleteConfirmMessage === 'Store Group Deleted' ) {
                            assert.strictEqual(true, true, 'Store Group Deleted; Test passed !');
                        } else {
                            await assert.fail(0, 1, "Top custom store group did not get deleted !");
                        }
        }
    })
})

  