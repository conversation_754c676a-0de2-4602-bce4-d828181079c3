const { assert } = require("chai");
let emData = require('../../../../TestData/EventManagerData/eventManagerUIData.json')
const customStoreGroupPage = require("../../EventManagerPO/customStoreGroup.page");
const eventMaintenancePage = require("../../EventManagerPO/eventMaintanence.page");
const createSFPEventPage = require("../../EventManagerPO/createSFPEvent.page");
const storeSelectionPage = require("../../EventManagerPO/storeSelection.page.js");
const createSfpEventPage = require("../../EventManagerPO/createSFPEvent.page.js");
const eventMaintanencePage = require('../../EventManagerPO/eventMaintanence.page');

let topDivisionValue;
describe('e2eValidateCustomStoresMatchOnStoreSelectionPage: Validate custom store values matching  ', () => {

    it('Validate: 1) storeList values from top StoreGroupManagement table matches with stores selected on 3rd step of event creation' +
        'after clicking the same GroupName value; 2) filtered table groupNameList matches with SelectCustomGroup dropdown list on 3rd step of' +
        'event creation', async () => {

           
            await eventMaintanencePage.waitBrowserToLoadPage(6000)
            await eventMaintanencePage.open('EventMaintenance/storeGroupManagement');
            await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000, timeoutMsg: "Error: Table contents for event maintenance page did not load before timeout" });
            await customStoreGroupPage.waitForPageLoad(await customStoreGroupPage.storeGroupManagementText, 'Store Group Management');
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            await customStoreGroupPage.selectCategoryColumnDropdownValue('Event Store Group');
            const topGroupNameValue = await (await customStoreGroupPage.getCustomGroupColumnList('Group Name'))[0];
            topDivisionValue = await customStoreGroupPage.extractDigits(await (await customStoreGroupPage.getCustomGroupColumnList('Division'))[0]);
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            const storeValueFromTopCustomGroup = await customStoreGroupPage.returnTopRowStoreValue();
            await customStoreGroupPage.enterSearchInputForDivisionDropdown(await topDivisionValue);
            await customStoreGroupPage.waitBrowserToLoadPage(3000);
            const groupNameColumnList = await customStoreGroupPage.getCustomGroupColumnList('Group Name');
            const groupNameColumnListDeleteEmptyString = await customStoreGroupPage.removeEmptyStringFromArray(await groupNameColumnList);
            groupNameColumnListDeleteEmptyString.toString();        
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            await createSfpEventPage.openEventMaintenancePage();
            await createSfpEventPage.addEventDetails1('', 'SFP Event', await topDivisionValue, emData.eventType_KOMPASS, "Maintenance", 'e2eTest' + " -12345", emData.vendor_AZB)
            await createSFPEventPage.submitEventDetails();
            await expect(await eventMaintenancePage.linkSubModule.getText()).toEqual(emData.Event_Schedule);
            await createSfpEventPage.addEventScheduleStep2();
            await expect(await storeSelectionPage.linkSubModule.getText()).toEqual(emData.Store_Selection);
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            let customGroupDropdownList = await customStoreGroupPage.getSelectCustomStoreGroupDropdownList();
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            // Sort and compare if order doesn't matter
            groupNameColumnListDeleteEmptyString.sort();
            customGroupDropdownList.sort();
            // Find missing or extra values if a mismatch
            const missingFromDropdown = groupNameColumnListDeleteEmptyString.filter(item => !customGroupDropdownList.includes(item));
            const extraInDropdown = customGroupDropdownList.filter(item => !groupNameColumnListDeleteEmptyString.includes(item));
            if (await missingFromDropdown.length > 0 || await extraInDropdown.length > 0) {
                assert.fail(
                    `Error: Arrays do not match. Missing from dropdown: ${missingFromDropdown}, Extra in dropdown: ${extraInDropdown}`
                );
            }
            await expect(await groupNameColumnListDeleteEmptyString).toEqual(await customGroupDropdownList);
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            await customStoreGroupPage.selectCustomStoreGroupDropdown(await topGroupNameValue);
            await eventMaintenancePage.waitBrowserToLoadPage(1000);
            await customStoreGroupPage.selectScheduleDropDownValue();
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            await customStoreGroupPage.storeSelectionSelectButton.click();
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            let selectedStores = await storeSelectionPage.returnSelectedStores();
            if (selectedStores.length >= 1) {
                const selectedValuesExist = await customStoreGroupPage.stringIncludesArrayValues(storeValueFromTopCustomGroup, selectedStores);
                await expect(selectedValuesExist).toEqual(true);
            } else {
                assert.fail(0, 1, 'Error: Stores are not selected after applying custom store group. ' +
                    'Might be because store checkboxes are not clickable. Re-run the test !!!');
            }
        })

})
