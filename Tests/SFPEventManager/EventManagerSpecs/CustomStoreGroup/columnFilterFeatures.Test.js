const customStoreGroupPage = require('../../EventManagerPO/customStoreGroup.page');
const {assert} = require('chai');
const eventMaintanencePage = require('../../EventManagerPO/eventMaintanence.page');

let groupCategoryType;
groupCategoryType =  ['Event Store Group', 'Shelf Strip Exclusion', 'Kompass Exclusions'];

describe('columnFilterFeatures', () =>{
    groupCategoryType.forEach((category) =>{

 
    describe('columnFilterFeatures: Validate filtering customStoreManagement table with CategoryGroup and Stores column filters '+ ' ' + `${category}` + `${groupCategoryType}` , () => {
    beforeEach( async () =>{
       
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintenance/storeGroupManagement');
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000, timeoutMsg: "Error: Table contents for event maintenance page did not load before timeout" });
        await customStoreGroupPage.waitForPageLoad(await customStoreGroupPage.storeGroupManagementText,'Store Group Management');
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        await customStoreGroupPage.selectCategoryColumnDropdownValue(await category);

    })

    it('Validate Division column filter "Select All" checkBox selects all dropdown values ' + ' ' + `${category}`, async () => {

        const divisionDropdownTextBefore = await customStoreGroupPage.divisionFilterDropdown.getText();
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        await customStoreGroupPage.selectAllDivisionDropdownValues();
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        let countSelectedCheckBoxAfter = await customStoreGroupPage.returnNumOfSelectedCheckBox(await customStoreGroupPage.divisionCloseDropdownButton, await customStoreGroupPage.customGroupDivisionDropdownList);
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        const divisionDropdownTextAfter = await customStoreGroupPage.divisionFilterDropdown.getText();
        await assert.notEqual(divisionDropdownTextBefore, divisionDropdownTextAfter, 'Error: Division dropdowns did not get to select !');
        const divisionDropdownTextAfterDigitOnly = await customStoreGroupPage.extractDigits(divisionDropdownTextAfter);
        await expect(countSelectedCheckBoxAfter.toString()).toEqual(divisionDropdownTextAfterDigitOnly);
        await customStoreGroupPage.divisionDropdownClearButton;
    })

    it('Validate Filtering Division column  ' + ' ' + `${category}`, async () => {

        const topRowDivisionValue = await customStoreGroupPage.getFirstColumnValueFromTopRow('Division');
        const divisionDropdownTextBefore = await customStoreGroupPage.divisionFilterDropdown.getText();
        await customStoreGroupPage.enterSearchInputForDivisionDropdown(await topRowDivisionValue);
        await customStoreGroupPage.waitBrowserToLoadPage(3000);
        const divisionDropdownTextAfter = await customStoreGroupPage.divisionFilterDropdown.getText();
        await assert.notEqual(divisionDropdownTextBefore, divisionDropdownTextAfter, 'Error: Division dropdowns did not get to select !')
        const divisionColumnList = await customStoreGroupPage.getCustomGroupColumnList('Division');
        const divisionColumnListUnique = await customStoreGroupPage.getUniqueArrayValue(await divisionColumnList);
        await expect(await divisionColumnListUnique.length).toEqual(1);
    })

    it('Validate clicking header names sorts the column data: GroupName, Division, Stores and RevBy columns' + ' ' + `${category}`,  async () => {

        await customStoreGroupPage.waitBrowserToLoadPage(3000);
        const tableHeaderList = await customStoreGroupPage.getArrayElementsText(await customStoreGroupPage.storeGroupHeaderList);
        if(tableHeaderList.length > 1){
            const columnNameList = ['Group Name','Division','Number of Stores','Last Modified By'];
            const sortColumnIdList = ['name','division','stores','revBy'];
            for(let index=0;index<columnNameList.length; index++){
                await customStoreGroupPage.waitBrowserToLoadPage(2000);
                const columnListBefore = await customStoreGroupPage.getCustomGroupColumnList(columnNameList[index]);
                await customStoreGroupPage.clickCustomGroupHeader(sortColumnIdList[index]);
                const columnListAfter = await customStoreGroupPage.getCustomGroupColumnList(columnNameList[index]);
                await assert.notEqual(columnListBefore, columnListAfter, 'Error: column sort buttons did not work !');
                await customStoreGroupPage.waitBrowserToLoadPage(2000);
            }
        } else{
            await assert.fail(0, 1, "Table headers did not display !");
        }
    })

    it('Validate search by inputs for GroupName,Division and LastModifiedBy ' + ' ' + `${category}`, async () => {

        const columnNameList = ['Group Name','Division','Last Modified By'];
        await customStoreGroupPage.waitBrowserToLoadPage(3000);
        for(let index=0; index<columnNameList.length; index++){
            const topRowValue = await customStoreGroupPage.getColumnTopElement(columnNameList[index]);
            if(columnNameList[index] === 'Division'){
             await customStoreGroupPage.enterSearchInputForDivisionDropdown(topRowValue);
            } else{
                await customStoreGroupPage.enterSearchInput(columnNameList[index], await topRowValue);
            }
            await customStoreGroupPage.waitBrowserToLoadPage(5000);
            const tableRowCount = await customStoreGroupPage.customGroupRowList.length;
            if(await tableRowCount > 0){
                const columnList = await customStoreGroupPage.getCustomGroupColumnList(columnNameList[index]);
                const columnListContainsFilterValue = await customStoreGroupPage.arrayIncludesStringValue(columnList, topRowValue);
                await expect(columnListContainsFilterValue).toEqual(true);
            } else{
                await assert.fail(0, 1, "Error: Table is empty !");
            }
        }
    })
})
})
})
