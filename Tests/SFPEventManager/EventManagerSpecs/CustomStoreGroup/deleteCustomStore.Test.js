const customStoreGroupPage = require('../../EventManagerPO/customStoreGroup.page')
const {assert} = require("chai");
const result = require("../../../../SQLConnection/server.js.ts");
const qrString = require("../../../../SQLConnection/queryStringEM");
let emData = require('../../../../TestData/EventManagerData/eventManagerUIData.json');
const eventMaintanencePage = require('../../EventManagerPO/eventMaintanence.page');

let topSingleGroupNameColumnBeforeDelete,topSingleGroupNameColumnAfterDelete,groupCategoryType;
groupCategoryType = ['Event Store Group', 'Shelf Strip Exclusion', 'Kompass Exclusions'];

after(async () => {

    await browser.deleteSession();
})

for(let index=0; index<groupCategoryType.length;index++) {
    
    describe('deleteCustomStore: Validate deleting custom store from ui and db ' + `${index}` + ' ' +  `${groupCategoryType[index]}`, () => {

        before(async () => {

            await eventMaintanencePage.open('EventMaintenance/storeGroupManagement');
            await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000, timeoutMsg: "Error: Table contents for event maintenance page did not load before timeout" });
            await customStoreGroupPage.waitForPageLoad(await customStoreGroupPage.storeGroupManagementText, 'Store Group Management');
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            await customStoreGroupPage.selectCategoryColumnDropdownValue(await groupCategoryType[index]);
            topSingleGroupNameColumnBeforeDelete = await (await customStoreGroupPage.getCustomGroupColumnList('Group Name'))[0];
            await customStoreGroupPage.clickDeleteCustomStore();
        })

        it('Validate clicking "No,keep it" button does not delete the custom store ', async () => {

            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            await customStoreGroupPage.clickSingleButtonFromList(await customStoreGroupPage.deleteStoreModalFooterButtonList, emData.no_keep_it);
            const topSingleGroupNameColumnAfter = await (await customStoreGroupPage.getCustomGroupColumnList('Group Name'))[0];
            await expect(await topSingleGroupNameColumnBeforeDelete).toEqual(await topSingleGroupNameColumnAfter);
        })

        it('Validate clicking "Yes,delete it" button deletes the custom store ', async () => {

            let rowCount = await (customStoreGroupPage.customGroupRowList.length);
            if(rowCount > 0){
                await customStoreGroupPage.clickDeleteCustomStore();
                await customStoreGroupPage.waitBrowserToLoadPage(2000);
                await customStoreGroupPage.clickSingleButtonFromList(await customStoreGroupPage.deleteStoreModalFooterButtonList, emData.yes_delete_it);
                topSingleGroupNameColumnAfterDelete = await (await customStoreGroupPage.getCustomGroupColumnList('Group Name'))[0];
                await assert.notEqual(await topSingleGroupNameColumnBeforeDelete, await topSingleGroupNameColumnAfterDelete, 'Error: selected store is not deleted !');
                let deletedDbData = await (await result.getResult(qrString.singleCustomGroupName.replace('@customGroupName', await topSingleGroupNameColumnBeforeDelete)));
                if (await deletedDbData.length === 0) {
                    await assert.notEqual(await topSingleGroupNameColumnBeforeDelete, await topSingleGroupNameColumnAfterDelete, 'Error: selected store is not deleted !');
                } else {
                    await assert.fail(0, 1, "Top custom store group is not deleted !");
                }
            }else{
                await assert.fail(0, 1, "Custom store group does not have data !");
            }
        })
    })
}
