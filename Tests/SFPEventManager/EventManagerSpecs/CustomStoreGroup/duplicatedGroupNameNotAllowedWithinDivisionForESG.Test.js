const customStoreGroupPage = require('../../EventManagerPO/customStoreGroup.page');
const {assert} = require("chai");
let emData = require('../../../../TestData/EventManagerData/eventManagerUIData.json');
const eventMaintanencePage = require('../../EventManagerPO/eventMaintanence.page');
const {testSetup} = require("../../../../helpers/testSetupTeardown.js");

let dynamicStoreGroupName, topSingleDivisionValue, topSingleGroupNameValue,originalRowLength,newRowLength;

describe('duplicatedGroupNameNotAllowedWithinDivisionForESG: Validate duplicated group name cannot be created under the same division ', () => {

    beforeEach(async () => {
        await eventMaintanencePage.open('EventMaintenance/storeGroupManagement');
        await eventMaintanencePage.tableFirstRow.waitForExist({
            timeout: 90000,
            timeoutMsg: "Error: Table contents for event maintenance page did not load before timeout"
        });
        await eventMaintanencePage.waitBrowserToLoadPage(6000);
        await customStoreGroupPage.waitForPageLoad(await customStoreGroupPage.storeGroupManagementText, 'Store Group Management');
        await customStoreGroupPage.selectCategoryColumnDropdownValue('Event Store Group');
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        topSingleGroupNameValue = await (await customStoreGroupPage.getCustomGroupColumnList('Group Name'))[0];
        topSingleDivisionValue = await (await customStoreGroupPage.getCustomGroupColumnList('Division'))[0];
        await customStoreGroupPage.enterSearchInput('Group Name', await topSingleGroupNameValue);
        originalRowLength = await customStoreGroupPage.customGroupRowList.length;
        await customStoreGroupPage.clickCreateNewGroup();
        await customStoreGroupPage.genericFillGroupNameInputBox('createdInputBox', topSingleGroupNameValue);
        dynamicStoreGroupName = await customStoreGroupPage.stringGenerator('Custom Store ');
    })

    it('Validate customStore cannot be created with duplicated GroupName within the same division', async () => {

           await customStoreGroupPage.selectCustomStoreDivisionValue(await topSingleDivisionValue);
           await customStoreGroupPage.randomClickStoreCheckBox();
           await customStoreGroupPage.waitBrowserToLoadPage(1000);
           await customStoreGroupPage.selectGroupCategory('Event Store Group');
           await customStoreGroupPage.waitBrowserToLoadPage(1000);
           await customStoreGroupPage.randomClickStoreCheckBox();
           await customStoreGroupPage.clickSingleButtonFromList(await customStoreGroupPage.submitCloseButtonList, 'Submit');
           await customStoreGroupPage.waitBrowserToLoadPage(1000);
           const groupNameExistMessage = await customStoreGroupPage.groupNameExistedMessage.getText();
           await customStoreGroupPage.waitBrowserToLoadPage(1000);
           if (await groupNameExistMessage.includes(emData.groupName_exists_message)) {
               await customStoreGroupPage.updateGroupNameValue(dynamicStoreGroupName);
               await customStoreGroupPage.clickSingleButtonFromList(await customStoreGroupPage.submitCloseButtonList, 'Submit');
               await customStoreGroupPage.waitBrowserToLoadPage(1000);
               let successMessage = await customStoreGroupPage.getTextOfElement(await customStoreGroupPage.customGroupCreatedMessage);
               await customStoreGroupPage.waitBrowserToLoadPage(1000);
               await expect(await successMessage).toEqual(emData.storeGroup_successfully_created_message);
               await customStoreGroupPage.enterSearchInput('Group Name', dynamicStoreGroupName);
               const topGroupNameColumnValue = await (await customStoreGroupPage.getCustomGroupColumnList('Group Name'))[0];
               await expect(await dynamicStoreGroupName).toEqual(await topGroupNameColumnValue);
           }else{
               await assert.fail(0, 1, "Error: groupNameExistMessage did not print !");
       }
    })

    it('Validate customStore can be created with duplicated GroupName when division is different than existing division', async () => {

        const selectDifferentDivNumber = await customStoreGroupPage.getRandomDivisionValueExceptExistingOne(await topSingleDivisionValue);
        await customStoreGroupPage.randomClickStoreCheckBox();
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        await customStoreGroupPage.selectGroupCategory('Event Store Group');
        await customStoreGroupPage.clickSingleButtonFromList(await customStoreGroupPage.submitCloseButtonList, 'Submit');
        let successMessage = await customStoreGroupPage.getTextOfElement(await customStoreGroupPage.customGroupCreatedMessage);
        await customStoreGroupPage.waitBrowserToLoadPage(1000);
        await expect(await successMessage).toEqual(emData.storeGroup_successfully_created_message);
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        const groupNameColumnList = await (await customStoreGroupPage.getCustomGroupColumnList('Group Name'));
        newRowLength = await customStoreGroupPage.customGroupRowList.length;
        const groupNameColumnListUnique = await customStoreGroupPage.getUniqueArrayValue(groupNameColumnList);
        if(await groupNameColumnListUnique.length === 1){
            const divisionColumnListAfterUpdate = await (await customStoreGroupPage.getCustomGroupColumnList('Division'));
            const divColumnListContainsNewDivision = await customStoreGroupPage.arrayIncludesStringValue(divisionColumnListAfterUpdate, selectDifferentDivNumber);
            await expect(divColumnListContainsNewDivision).toEqual(true);
            await expect(parseInt(originalRowLength)).toEqual(parseInt(newRowLength) - 1);
        } else{
            await assert.fail(0, 1, "Error: Table is empty !");
        }
    })

})
