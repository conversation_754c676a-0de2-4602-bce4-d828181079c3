const { assert } = require("chai");
let emData = require('../../../../TestData/EventManagerData/eventManagerUIData.json')
const customStoreGroupPage = require("../../EventManagerPO/customStoreGroup.page");
const eventMaintenancePage = require("../../EventManagerPO/eventMaintanence.page");
const createSFPEventPage = require("../../EventManagerPO/createSFPEvent.page");
const storeSelectionPage = require("../../EventManagerPO/storeSelection.page.js");
const createSfpEventPage = require("../../EventManagerPO/createSFPEvent.page.js");
const eventMaintanencePage = require('../../EventManagerPO/eventMaintanence.page');


describe('kompassStoresMatchOnStoresSelectionPage: Validate Kompass Exclusion stores displays on StoreSelection  ', () => {

    it('Validate Kompass Exclusion stores displays on StoreSelection of Event Creation and store values should match ', async () => {

        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintenance/storeGroupManagement');
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000, timeoutMsg: "Error: Table contents for event maintenance page did not load before timeout" });
        await customStoreGroupPage.waitForPageLoad(await customStoreGroupPage.storeGroupManagementText, 'Store Group Management');
        await customStoreGroupPage.waitBrowserToLoadPage(2000);
        const rowCount = await customStoreGroupPage.customGroupRowList.length;
        if(rowCount > 0){
            await customStoreGroupPage.selectCategoryColumnDropdownValue('Kompass Exclusions');
            let topDivisionValue = await customStoreGroupPage.extractDigits(await (await customStoreGroupPage.getCustomGroupColumnList('Division'))[0]);
            await customStoreGroupPage.waitBrowserToLoadPage(2000);
            const storeValueFromTopCustomGroup = await customStoreGroupPage.returnTopRowStoreValue();
            await customStoreGroupPage.enterSearchInputForDivisionDropdown(await topDivisionValue);
            await customStoreGroupPage.waitBrowserToLoadPage(3000);
            const groupNameColumnList = await customStoreGroupPage.getCustomGroupColumnList('Group Name');
            const groupNameColumnListDeleteEmptyString = await customStoreGroupPage.removeEmptyStringFromArray(await groupNameColumnList);
            groupNameColumnListDeleteEmptyString.toString();
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            await createSfpEventPage.addEventDetails1('', 'SFP Event', await topDivisionValue, emData.eventType_Remodel, 'Remodel', 'KompassRemodel' + " -12345", emData.vendor_AZB)
            await createSFPEventPage.submitEventDetails();
            await expect(await eventMaintenancePage.linkSubModule.getText()).toEqual(emData.Event_Schedule);
            await createSfpEventPage.addEventScheduleStep2();
            await expect(await storeSelectionPage.linkSubModule.getText()).toEqual(emData.Store_Selection);
            await eventMaintenancePage.waitBrowserToLoadPage(3000);
            let remodelStoreList = await customStoreGroupPage.getCheckedBoxesWithClass();
            let remodelStoreListFormatted = await customStoreGroupPage.convertArrayToNewLines(await remodelStoreList);
            await expect(await storeValueFromTopCustomGroup).toEqual(await remodelStoreListFormatted);
        } else{
            assert.fail(0,1,'Error: Kompass Exclusion table does not have data !!!')
        }

    })

})

