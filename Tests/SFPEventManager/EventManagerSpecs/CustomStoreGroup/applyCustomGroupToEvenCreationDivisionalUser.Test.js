const customStoreGroupPage = require('../../EventManagerPO/customStoreGroup.page')
const emData = require("../../../../TestData/EventManagerData/eventManagerUIData.json");
const createSFPEventPage = require("../../EventManagerPO/createSFPEvent.page");
const storeSelectionPage = require("../../EventManagerPO/storeSelection.page.js");
const {clickOn} = require("../../EventManagerPO/createSFPEvent.page");
const eventMaintenancePage = require("../../EventManagerPO/eventMaintanence.page");
const selectPOG = require("../../EventManagerPO/selectingPogCommodities.page");
const {assert} = require("chai");
const {testSetup} = require("../../../../helpers/testSetupTeardown.js");
const envVariables = require("../../UserRoles/roles.js");
const ENV = process.env.ENV

let preSelectedStores, divisionDefaultSelectedValue, description;


describe('applyCustomGroupToEvenCreationDivisionalUser: Validate creating customStore group ',  () => {

    before(async () => {
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await testSetup(await envVariables.divisionalUserEM[ENV], await envVariables.divisionalPasswordEM[ENV]);
        divisionDefaultSelectedValue =  emData.div_Columbus_Operating_Division_016;
    })

    it('Validate: 1)create custom store group and validate on the Custom Management table ', async () => {
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await eventMaintenancePage.open('EventMaintenance');
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await eventMaintenancePage.tableSecondPageBtn.waitForExist({
            timeout: 90000,
            timeoutMsg: 'Error: Table contents for event maintenance page did not load before timeout'
        })
        await eventMaintenancePage.selectEventType('SFP Event');
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await expect((await (await eventMaintenancePage.linkCreateSFPEvent).getText()).toString()).toEqual(emData.Create_SFP_Event);
        const divisionDropdownDefaultSelected = await createSFPEventPage.locatorDisabled(await createSFPEventPage.divisionDefaultSelectedValueForDivUser);
        await expect(divisionDropdownDefaultSelected).toEqual(true);
        const divisionDropdownDefaultValue = await createSFPEventPage.getTextOfElement(await createSFPEventPage.divisionDefaultSelectedValueForDivUser);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        if(await divisionDropdownDefaultValue === await divisionDefaultSelectedValue){
            await createSFPEventPage.selectDropdown('scheduleType', 'KOMPASS');
            await createSFPEventPage.selectDropdown('selectType', 'Update');
            description = await selectPOG.stringGenerator('Divisional -'+ 'CustomGroup' + "-98765");
            await createSFPEventPage.enterEventResetDescription(await description);
            await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB);
            await createSFPEventPage.submitEventDetails();
        } else{
            assert.fail(0,1,"Error: Default selected division value does not match with Columbus Operating Division - 016")
        }
        let nextPage = await eventMaintenancePage.linkSubModule.getText();
        await expect(nextPage).toEqual(emData.Event_Schedule);
        if(await storeSelectionPage.saveSchedule.waitForClickable()){
            await clickOn(await storeSelectionPage.saveSchedule);
        }
        await eventMaintenancePage.tableFirstRow.waitForExist({
            timeout: 9000,
            timeoutMsg: 'Error: Table contents for event maintenance page did not load before timeout'
        })
        await storeSelectionPage.saveGoToStoreSelection();
        let storePage = await storeSelectionPage.linkSubModule.getText();
        await expect(storePage).toEqual(emData.Store_Selection);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await storeSelectionPage.clickRandomEnabledStoreCheckboxes(1, 10);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        preSelectedStores = await storeSelectionPage.returnSelectedStores();
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await customStoreGroupPage.clickAndRandomlySelectCustomStoreGroup();
        await eventMaintenancePage.waitBrowserToLoadPage(1000);
        await customStoreGroupPage.selectScheduleDropDownValue();
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await customStoreGroupPage.storeSelectionSelectButton.click();
        const storeSelectionOverrideMessage = await customStoreGroupPage.getTextOfElement(await customStoreGroupPage.storeSelectionOverrideWarningMessage)
        if(storeSelectionOverrideMessage === 'Store Selection Override Warning'){
            await customStoreGroupPage.clickSingleButtonFromList(await customStoreGroupPage.storeSelectionModalFooterButtonList, 'Continue');
        }else{
            assert.fail(0,1,'Custom Store modal did not display !')
        }
        const customSelectedStores = await storeSelectionPage.returnSelectedStores();
        if(customSelectedStores !== await preSelectedStores){
            await eventMaintenancePage.waitBrowserToLoadPage(3000);
            await clickOn(storeSelectionPage.saveStoreSchedule);
        }else{
            assert.fail(0,1,'Custom Store did not get to selected on Store Selection page !')
        }
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Select Pogs");
        let pogPage = await storeSelectionPage.linkSubModule.getText();
        await expect(pogPage).toEqual(emData.Select_Pogs);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        let intParams = await storeSelectionPage.generateTwoIntParams();
        await eventMaintenancePage.selectCommodityDropDownValuesByIndex(intParams[0], intParams[1]);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await clickOn(selectPOG.selectChoosePogByCommodityBtn);
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await clickOn(selectPOG.gotoEventListButton);
        await expect(browser).toHaveUrlContaining("EventMaintenance");
        await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.filtersTxtBtn, emData.Filters);
        await eventMaintenancePage.clickFiltersWithDiscp(description.toString());
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        let storeListLengthEmMainPage = await eventMaintenancePage.getLinkText("S-Str");
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        let storeListUi = await eventMaintenancePage.getArrayElementsText(await eventMaintenancePage.scheduledStoreModalList);
        await eventMaintenancePage.clickCloseButton("Close");
        if(await storeListUi.length > 0){
            await expect(customSelectedStores).toEqual(await storeListUi.sort());
            await expect(storeListLengthEmMainPage).toEqual((customSelectedStores.length).toString())
            await eventMaintenancePage.clickOn(await eventMaintenancePage.slideOutClose);
            await eventMaintenancePage.waitBrowserToLoadPage(2000);
            let releaseEventEnabled = await eventMaintenancePage.selectReleaseEvents(emData.ReleaseEvent_Corporate, emData.Header_Action, description);
            await expect(releaseEventEnabled).toEqual(true);
        } else{
            await assert.fail(0, 1, "Could not read store values from EM main page !");
        }
    })
})
