const eventMaintenancePage = require('../EventManagerPO/eventMaintanence.page.js');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const result = require("../../../SQLConnection/server.js.ts");
const qrString = require("../../../SQLConnection/queryStringEM");
const createSfpEventPage = require("../EventManagerPO/createSFPEvent.page");

let description, storeShelfStripTypeList, storeEventShelfStripTypeListOnTable, storeOfficeList, officeEventShelfStripTypeListOnTable;

storeOfficeList = ['ne_shelfstrips','ne_event_strip_typ'];
officeEventShelfStripTypeListOnTable = ['O - V', 'O - I', 'O - C', 'O - F'];
for(let storeOfficeIndex=0;storeOfficeIndex < storeOfficeList.length;storeOfficeIndex++) {
    storeShelfStripTypeList = ['V', 'I', 'C', 'F'];
    storeEventShelfStripTypeListOnTable = ['S - V', 'S - I', 'S - C', 'S - F'];

    for (let index = 0; index < storeShelfStripTypeList.length; index++) {

        describe('validateSFPEventShelfStripTypes:validate Sfp Event Shelf-Strip Types ' + `${index}` +  + ' ' + `${storeOfficeList[storeOfficeIndex]}` + ' ' + `${officeEventShelfStripTypeListOnTable[index]}`, () => {

            beforeEach(`${officeEventShelfStripTypeListOnTable[index]}`,async () => {
                description = await createSfpEventPage.addEventDetails1('', 'SFP Event', emData.div_Cincinnati_Operating_Division_014, emData.eventType_KOMPASS, "Update", "ShelfStripValidation12345", emData.vendor_AZB);
                await createSFPEventPage.clickCheckBox(emData.checkBox_RequestShelfStrips);
                await eventMaintenancePage.waitBrowserToLoadPage(2000);
                if (await storeOfficeList[storeOfficeIndex] === 'ne_event_strip_typ') {
                    await createSFPEventPage.clickOn(await createSFPEventPage.shelfStripOfficeRadio);
                }
            })

            it('Store: Validate Sfp Event STORE_Shelf_Strip and OFFICE_Shelf_Strip NewItemIntroduction' + `${index}` + ' ' + `${storeShelfStripTypeList[index]}` + ' ' + `${storeEventShelfStripTypeListOnTable[index]}` + ' ' + `${officeEventShelfStripTypeListOnTable[index]}`, async () => {

                officeEventShelfStripTypeListOnTable = ['O - V', 'O - I'];
                await createSFPEventPage.clickShelfStripCheckBox(await storeShelfStripTypeList[index]);
                await eventMaintenancePage.waitBrowserToLoadPage(2000);
                await createSFPEventPage.submitEventDetails();
                let nextPage = await eventMaintenancePage.linkSubModule.getText();
                await expect(await nextPage).toEqual(emData.Event_Schedule);
                await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout');
                await eventMaintenancePage.waitBrowserToLoadPage(2000);
                await eventMaintenancePage.clickOn(await storeSelectionPage.saveSchedule);
                await eventMaintenancePage.waitBrowserToLoadPage(2000);
                let weekText = await storeSelectionPage.storeWeekSchedule.getText();
                await eventMaintenancePage.waitBrowserToLoadPage(2000);
                await eventMaintenancePage.clickOn(storeSelectionPage.saveGoToStore);
                await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
                let storePage = await storeSelectionPage.linkSubModule.getText();
                await expect(await storePage).toEqual(emData.Store_Selection);
                await eventMaintenancePage.open('EventMaintenance');
                await eventMaintenancePage.waitBrowserToLoadPage(2000);
                await expect(browser).toHaveUrlContaining("EventMaintenance");
                await eventMaintenancePage.waitBrowserToLoadPage(2000);
                await eventMaintenancePage.clickFiltersWithDiscp(description.toString())
                let eventDescription = await selectPOG.getEventDescText(description, await weekText.replace(', ', '/'));
                await expect(eventDescription).toEqual(true);
                let shelfStripType = await eventMaintenancePage.getHeaderValue(emData.Header_Strips, await description);
                if (await storeOfficeList[storeOfficeIndex] === 'ne_shelfstrips') {
                    await expect(await shelfStripType).toEqual(await storeEventShelfStripTypeListOnTable[index]);
                }
                if (await storeOfficeList[storeOfficeIndex] === 'ne_event_strip_typ') {
                    await expect(await shelfStripType).toEqual(await officeEventShelfStripTypeListOnTable[index]);
                }
                let initialEventId = await eventMaintenancePage.getHeaderValue(emData.Event_ID, description);
                await eventMaintenancePage.waitBrowserToLoadPage(3000);
                await eventMaintenancePage.clickOn(await eventMaintenancePage.slideOutClose)
                let eventSelfStripDB = await (await result.getResult(qrString.SfpEventRequestShelfStripTypeQuery.replace('@eventID', initialEventId))).flat();
                let eventSelfStripDbTreated = await eventMaintenancePage.concatArrayElements(eventSelfStripDB);
                await expect(eventSelfStripDbTreated).toEqual(await shelfStripType);
                await eventMaintenancePage.waitBrowserToLoadPage(2000);
            })
        })
    }
}
