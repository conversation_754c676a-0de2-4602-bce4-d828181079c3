const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let data = require('../../../Testdata/data.json');
let emData = require('../../../Testdata/EventManagerdata/eventManagerUIdata.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const dBResult = require('../../../SQLConnection/server.js.ts');
const { assert } = require('chai');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');

describe('validateDisplayAndWhiteSpaceInPOG: validate Display And White Space do not display in UI and DB Dsc6', () => {

    it('Should validate Display And White Space do not display in UI and DB Dsc6', async () => {
        await expect(browser).toHaveTitle(data.homeTitle);
        await eventMaintanencePage.waitBrowserToLoadPage(6000);                                                                     
        await eventMaintanencePage.open('EventMaintenance');     
        await eventMaintanencePage.waitBrowserToLoadPage(6000);
        await eventMaintanencePage.selectEventType('SFP Event')
        let sfpBreadCrumbText = await eventMaintanencePage.linkCreateSFPEvent.getText();
        expect(sfpBreadCrumbText).toEqual(emData.Create_SFP_Event);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await createSFPEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014);
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS);
        await createSFPEventPage.selectDropdown('selectType', "Update");
        let description = await selectPOG.stringGenerator("AutomationEventPogStatus");
        await createSFPEventPage.enterEventResetDescription(description);
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB);
        await createSFPEventPage.submitEventDetails();
        let nextPage = await eventMaintanencePage.linkSubModule.getText();
        await expect(nextPage).toEqual(emData.Event_Schedule);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Event Schedule");
        await createEventSchedulePage.genericClickDropdown('Fiscal Week');
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout');
        await clickOn(storeSelectionPage.saveSchedule);
        let weekText = await storeSelectionPage.storeWeekSchedule.getText();
        await clickOn(storeSelectionPage.saveGoToStore);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Store Selection");
        let storePage = await storeSelectionPage.linkSubModule.getText();
        await expect(storePage).toEqual(emData.Store_Selection);
        await storeSelectionPage.clickCheckBoxText(emData.Store_351);
        await clickOn(storeSelectionPage.saveStoreSchedule);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Select Pogs");
        let pogPage = await storeSelectionPage.linkSubModule.getText();
        await expect(pogPage).toEqual(emData.Select_Pogs);
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Department', emData.dept_01_GROCERY)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER);
        await selectPOG.waitForPageLoad(selectPOG.selectText,emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Commodity *', emData.ResetComplete_All);
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await clickOn(selectPOG.selectChoosePogByCommodityBtn);
        await eventMaintanencePage.waitBrowserToLoadPage(9000);
        // pogKeys is string array
        let pogKeys = await selectPOG.getHeaderValues(emData.Pog_Key_Header);
        // pogDisplay is string array of arrays
        let pogDisplay = await dBResult.getResult(qrString.displayPogType.replace('@POGKEYS', pogKeys));
        let uniqueDbKeys  = await selectPOG.getUniqueArrayValue(pogDisplay.flat());
        for(let i =0; i<uniqueDbKeys.length;i++){
        await assert.notEqual( uniqueDbKeys[i],(emData.Display_Space),"Display space found");
        await assert.notEqual( uniqueDbKeys[i],(emData.White_Space),"White space found");
        }
    })
})
