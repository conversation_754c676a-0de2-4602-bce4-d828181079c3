let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const {clickOn} = require("../EventManagerPO/createSFPEvent.page");
const eventMaintanencePage = require("../EventManagerPO/eventMaintanence.page");
const {assert} = require("chai");

describe('validateEventMaintenance: Validate Event Manager page objects', () => {

    before(async () => {
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.waitBrowserToLoadPage(1000)
        await eventMaintanencePage.tableSecondPageBtn.waitForExist({
            timeout: 90000,
            timeoutMsg: 'Error: Table contents for event maintenance page didn`t load before timeout'
        })
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
    })

    it('Validate 1) EventId column sort feature: descending and ascending order; 2) EM main page paginates', async () => {

        await clickOn(await eventMaintanencePage.refreshEventBtn);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters_text);
        let eventIdListBeforeSort = await eventMaintanencePage.eventIdColumnList('3');
        let eventIdDescendingOrder = await eventMaintanencePage.isOrdered(eventIdListBeforeSort, 'descending')
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await expect(eventIdDescendingOrder).toEqual(true)
        await clickOn(await eventMaintanencePage.eventIdSortBtn)
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        let eventIdListAfterSort = await eventMaintanencePage.eventIdColumnList('3');
        assert.notDeepEqual(eventIdListBeforeSort, eventIdListAfterSort, 'Error: Two arrays are the equal but they should not be !')
        let eventIdAscendingOrder = await eventMaintanencePage.isOrdered(eventIdListAfterSort, 'ascending')
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await expect(eventIdAscendingOrder).toEqual(true);
    })

    it('Validate EM Main page dropdowns: 1) Can select single values for each dropdown; 2) Selected dropdown values can be cleared by clicking "Close" button ', async () => {

        await eventMaintanencePage.clickFilters();
        let filterDropdownListIds = ['selectedEventDropdown', 'divisionSingleSelect', 'selectedDepartmentDropdown', 'subDepartmentDropdown', 'selectedCommodityDropdown', 'selectedStatusDropdown']
        for (let index = 0; index < await filterDropdownListIds.length; index++) {
            await eventMaintanencePage.waitBrowserToLoadPage(2000)
            let selectedDropdownTextBefore = await eventMaintanencePage.getTextOfSelectedDropdown(filterDropdownListIds[index]);
            await eventMaintanencePage.selectDropDownValuesByIndex(filterDropdownListIds[index], 1, 2);
            let selectedDropdownTextAfter = await eventMaintanencePage.getTextOfSelectedDropdown(filterDropdownListIds[index]);
            assert.notDeepEqual(selectedDropdownTextBefore, selectedDropdownTextAfter, 'Two values should not be same !')
            await eventMaintanencePage.clickEmMainPageDropdownCloseBtn(filterDropdownListIds[index]);
            await eventMaintanencePage.waitBrowserToLoadPage(2000)
            let selectedDropdownTextFinal = await eventMaintanencePage.getTextOfSelectedDropdown(filterDropdownListIds[index]);
            await expect(selectedDropdownTextBefore).toEqual(selectedDropdownTextFinal);
        }
    })
})
