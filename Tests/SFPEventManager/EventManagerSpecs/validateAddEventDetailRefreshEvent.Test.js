const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const selectPOG = require("../EventManagerPO/selectingPogCommodities.page");
const { assert, expect } = require('chai')


describe('validateAddEventDetailRefreshEvent: validate the dropdown in RefreshEvent', () => {

    it('validate Add Event Detail for RefreshEvent', async () => {
        let eventDescription = await createSfpEventPage.addEventDetails1('','SFP Refresh', emData.div_Cincinnati_Operating_Division_014, emData.eventType_KOMPASS, "Update","RefreshSlideOut", emData.vendor_AZB, ["digitalRefresh","allocationRefresh","appRefresh"])
        await createSfpEventPage.submitEventRefreshDetails();
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await eventMaintanencePage.clickOn(createSfpEventPage.sfpAppModalButton)
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await createEventSchedulePage.scheduling()
        await storeSelectionPage.saveGoToStoreSelection()
        let div = (emData.div_Cincinnati_Operating_Division_014.match(/\d/g).join("")).toString()
        let dbScheduledStores = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, div).replace('@num', '30'))).flat();
        await storeSelectionPage.storeSelection(dbScheduledStores, 5, 1)
        await storeSelectionPage.saveStoreScheduleButton()
        await eventMaintanencePage.waitBrowserToLoadPage(5000);
        await selectPOG.selectingPogCommodities('Department', emData.dept_01_GROCERY, 'Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER, 'Commodity *', emData.commodity_003_CAN_BEANS_01_GROC_ALL_OTHER_01_GROCERY, '')
        await eventMaintanencePage.clickOn(selectPOG.selectChoosePogByCommodityBtn);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000, timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await eventMaintanencePage.clickOn(selectPOG.gotoEventListButton);
        await eventMaintanencePage.waitBrowserToLoadPage(4000)
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000, timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await expect(await eventMaintanencePage.eventCreationText.getText()).to.equal(emData.Event_Creation);
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters);
        await eventMaintanencePage.clickFiltersWithDiscp(eventDescription.toString())
        let eventDisp = await eventMaintanencePage.getHeaderValue(emData.Filters_Event_Desc, eventDescription)
        let scheduleType = await eventMaintanencePage.getHeaderValue(emData.Header_scheduleType, eventDisp)
        await expect(scheduleType).to.equal(emData.eventType_KOMPASS)
        let pogType = await eventMaintanencePage.getHeaderValue(emData.Header_Pog, eventDisp)
        let pogStringStoreNum = await eventMaintanencePage.extractingDigit(emData.PogString, emData.PogString, 1);
        await expect(pogType).to.equal(pogStringStoreNum)
        await eventMaintanencePage.clickCloseButton('Close');
        let refreshType = await eventMaintanencePage.getHeaderValue(emData.Header_Refresh, eventDescription)
        await expect(refreshType).to.equal(emData.Event_refreshType_B_D_APP)
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 70000, timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, eventDescription)
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        let eventId = await eventMaintanencePage.getEventId(initialEventId)
        await expect(eventId).to.equal(initialEventId)
        let dataFromDb = await result.getResult(qrString.SfpEvent_Eventid.replace('eventID', initialEventId))
        await assert.equal(dataFromDb.length, 1)
    })

})