import { getTodayDate } from '../../../util/date';
const eventMaintenancePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page.js');
const { clickOn } = require("../EventManagerPO/createSFPEvent.page");
const selectPOG = require("../EventManagerPO/selectingPogCommodities.page");
const { setValue } = require("../../../GlobalObjectRepository/login.page");
const assertValue = require('chai').expect;
const { assert } = require('chai');
const UPLOrderPage = require('../EventManagerPO/UPLOrderFlow.page');

describe('validateFiltersOnEventMaintenanceByEventType: Validate user is able to filter EM page results by EventType dropdown options', () => {

    it('Validate filtering with SFPEvent option by newly created event is included in eventIdList column ', async () => {
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await eventMaintenancePage.open('EventMaintenance');
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents didn`t load before timeout');
        await eventMaintenancePage.selectEventType('SFP Event');
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        expect(await eventMaintenancePage.linkCreateSFPEvent.getText()).toHaveTextContaining(emData.Create_SFP_Event);
        await createSFPEventPage.selectDropdown('division', emData.div_Columbus_Operating_Division_016);
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_Remodel);
        let description = await selectPOG.stringGenerator("sfpEventfilter");
        await createSFPEventPage.enterEventResetDescription(description);
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB);
        await createSFPEventPage.submitEventDetails();
        let nextPage = await eventMaintenancePage.linkSubModule.getText();
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await expect(nextPage).toEqual(emData.Event_Schedule);
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        await clickOn(storeSelectionPage.saveSchedule);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await clickOn(storeSelectionPage.saveGoToStore);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
        let storePage = await storeSelectionPage.linkSubModule.getText();
        await expect(storePage).toEqual(emData.Store_Selection);
        await storeSelectionPage.clickCheckBoxText(emData.store_Selection_350);
        await storeSelectionPage.clickCheckBoxText(emData.store_Selection_531);
        await storeSelectionPage.clickCheckBoxText(emData.store_Selection_581);
        await clickOn(await storeSelectionPage.saveStoreSchedule);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Select_Pogs);
        let pogPage = await storeSelectionPage.linkSubModule.getText();
        await expect(pogPage).toEqual(emData.Select_Pogs);
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await selectPOG.selectDropdown('Department', emData.selecting_POG_Department_15_DELI);
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await selectPOG.selectDropdown('Sub-Department', emData.selecting_POG_SubDepartment_15_DELI_49_SPECIALTY_CHSE);
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await selectPOG.selectDropdown('Commodity *', emData.selecting_POG_Commodity_462_SPECIALTY_CHEESE_PRE_PACK_49_SPECIALTY_CHSE_15_DELI);
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await selectPOG.selectDropdown('Commodity Group', emData.selecting_POG_CommodityGroup_462_SPECIALTY_CHEESE_PRE_PACK_462_SPECIALTY_CHEESE_PRE_PACK);
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await clickOn(await selectPOG.selectChoosePogByCommodityBtn);
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await clickOn(await selectPOG.gotoEventListButton);
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl);
        await clickOn(await eventMaintenancePage.refreshEventBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await eventMaintenancePage.waitForPageLoad(await eventMaintenancePage.filtersTxtBtn, emData.Filters_text);
        await clickOn(await eventMaintenancePage.refreshEventBtn);
        await eventMaintenancePage.clickFiltersWithDiscp(description.toString())
        await eventMaintenancePage.selectFiltersDropdown(emData.select_EventType_selectedEventType, emData.SFP_Event,"selectedEventDropdown");
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await clickOn(await eventMaintenancePage.searchBtn);
        let initialEventId = await eventMaintenancePage.getHeaderValue(emData.Event_ID, await description);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await eventMaintenancePage.clickOn(await eventMaintenancePage.slideOutClose)
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        let eventId = await eventMaintenancePage.getEventId(initialEventId);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await expect(eventId).toEqual(initialEventId);
        await eventMaintenancePage.waitForPageLoad(await eventMaintenancePage.filtersTxtBtn, emData.Filters_text);
        let eventIdList = await eventMaintenancePage.getColumnList(emData.header_Event_Id);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await assertValue(eventIdList).to.include(eventId, 'Event is not included in SFP Event list');
    })

    it('Validate filtering with SFPEActivation option by newly created event is included in eventIdList column', async () => {
        await eventMaintenancePage.open('EventMaintenance');
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents didn`t load before timeout');
        await eventMaintenancePage.selectEventType('SFP Activation');
        expect(await eventMaintenancePage.linkCreateSFPEvent.getText()).toHaveTextContaining(emData.Create_SFP_Activation_Event);
        await createSFPEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014);
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS);
        await createSFPEventPage.selectDropdown('selectType', "Activation");
        let description = await selectPOG.stringGenerator("AutomationEventActivation");
        await createSFPEventPage.enterEventResetDescription(description);
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB);
        await setValue(createSFPEventPage.defaultMin, 3);
        await setValue(createSFPEventPage.defaultAllocation, 1);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await createSFPEventPage.submitEventDetails();
        let nextPage = await eventMaintenancePage.linkSubModule.getText();
        await expect(nextPage).toEqual(emData.Event_Schedule);
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Page did not appear before timeout');
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await clickOn(await storeSelectionPage.saveSchedule);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        let weekText = await storeSelectionPage.storeWeekSchedule.getText();
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await clickOn(storeSelectionPage.saveGoToStore);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
        let storePage = await storeSelectionPage.linkSubModule.getText();
        await expect(storePage).toEqual(emData.Store_Selection);
        await storeSelectionPage.clickCheckBoxText(emData.Store_351);
        await clickOn(await storeSelectionPage.saveStoreSchedule);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Upload_UPC);
        let pogPage = await storeSelectionPage.linkSubModule.getText();
        await expect(pogPage).toEqual(emData.Upload_UPC);
        await createSFPEventPage.inputUPC.setValue(61076426171);
        await clickOn(createSFPEventPage.addUPC);
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 70000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')  
        await clickOn(createSFPEventPage.saveUPC)
        await expect(browser).toHaveUrlContaining("EventMaintenance");
        await eventMaintenancePage.waitBrowserToLoadPage(9000);
        let eventDescription = await selectPOG.getEventDescText(description, weekText.replace(', ', '/'));
        await expect(eventDescription).toEqual(true)
        await eventMaintenancePage.table.waitForExist({ timeout: 70000 }, 'not displaying in given time ');
        let initialEventId = await eventMaintenancePage.getHeaderValue(emData.header_Event_Id, description);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await eventMaintenancePage.clickOn(await eventMaintenancePage.slideOutClose)
        let eventId = await eventMaintenancePage.getEventId(initialEventId);
        await expect(eventId).toEqual(initialEventId);
        await eventMaintenancePage.clickOn(await eventMaintenancePage.slideOutClose)
        await eventMaintenancePage.waitForPageLoad(await eventMaintenancePage.filtersTxtBtn, emData.Filters_text);
        await clickOn(await eventMaintenancePage.refreshEventBtn);
        await eventMaintenancePage.clickFiltersWithDiscp(description.toString())
        await eventMaintenancePage.selectFiltersDropdown(emData.select_EventType_selectedEventType, emData.SFP_Activation,"selectedEventDropdown");
        await clickOn(await eventMaintenancePage.searchBtn);
        await eventMaintenancePage.waitForPageLoad(await eventMaintenancePage.filtersTxtBtn, emData.Filters_text);
        let eventIdList = await eventMaintenancePage.getColumnList(emData.header_Event_Id);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await assertValue(eventIdList).to.include(eventId, 'Event is not included in SFP Event list');
    })

    it('Validate filtering with SFPEventRefresh option by newly created event is included in eventIdList column', async () => {
        await eventMaintenancePage.open('EventMaintenance');
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents didn`t load before timeout');
        await eventMaintenancePage.selectEventType('SFP Refresh');
        expect(await eventMaintenancePage.linkCreateSFPEvent.getText()).toHaveTextContaining(emData.Create_SFP_Event);
        await createSFPEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014);
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS);
        let description = await selectPOG.stringGenerator("EventRefreshFilter");
        await createSFPEventPage.enterEventResetDescription(description);
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB);
        await createSFPEventPage.clickCheckBox(emData.checkBox_Digital_Refresh_ClickList);
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        await createSFPEventPage.submitEventRefreshDetails()
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        await clickOn(createSFPEventPage.sfpAppModalButton)
        await eventMaintenancePage.waitBrowserToLoadPage(4000)
        let nextPage = await createSFPEventPage.linkCreateSfpRefreshEvent.getText();
        await expect(nextPage).toEqual(emData.Event_Refresh);
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout');
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await clickOn(storeSelectionPage.saveSchedule);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await storeSelectionPage.storeWeekSchedule.getText();
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await clickOn(storeSelectionPage.saveGoToStore);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkCreatSfpRefreshEvent, emData.Event_Refresh);
        let storePage = await storeSelectionPage.linkCreatSfpRefreshEvent.getText();
        await expect(storePage).toEqual(emData.Event_Refresh);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await storeSelectionPage.clickCheckBoxText(emData.Store_351);
        await clickOn(storeSelectionPage.saveStoreSchedule);
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkCreatSfpRefreshEvent, emData.Event_Refresh);
        let pogPage = await storeSelectionPage.linkCreatSfpRefreshEvent.getText();
        await expect(pogPage).toEqual(emData.Event_Refresh);
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await selectPOG.selectDropdown('Department', emData.department_03_HBC);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await selectPOG.selectDropdown('Sub-Department', emData.subdept_03_HBC_03_HEALTH);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await selectPOG.selectDropdown('Commodity *', emData.commodity_172_ANALGESICS_03_HEALTH_03_HBC);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await selectPOG.selectDropdown('Commodity Group', emData.commodityGroup_172_ANALGESICS_172_ANALGESICS);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await clickOn(await selectPOG.selectChoosePogByCommodityBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await clickOn(await selectPOG.gotoEventListButton);
        await createSFPEventPage.submitEventRefreshDetails();
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl);
        await clickOn(await eventMaintenancePage.refreshEventBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(5000);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        let initialEventId = await eventMaintenancePage.getHeaderValue(emData.header_Event_Id, description);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await eventMaintenancePage.clickOn(await eventMaintenancePage.slideOutClose)
        let eventId = await eventMaintenancePage.getEventId(initialEventId);
        await eventMaintenancePage.waitBrowserToLoadPage(5000);
        await expect(eventId).toEqual(initialEventId);
        await clickOn(await eventMaintenancePage.refreshEventBtn);
        await eventMaintenancePage.clickFiltersWithDiscp(description.toString())
        await eventMaintenancePage.selectFiltersDropdown(emData.select_EventType_selectedEventType, emData.SFP_Refresh,"selectedEventDropdown");
        await clickOn(await eventMaintenancePage.searchBtn);
        await eventMaintenancePage.waitForPageLoad(await eventMaintenancePage.filtersTxtBtn, emData.Filters_text);
        let eventIdList = await eventMaintenancePage.getColumnList(emData.header_Event_Id);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await assertValue(eventIdList).to.include(eventId, 'Event is not included in SFP Event list');
    })

    it.skip('Validate filtering with UPL Order option by newly created UPL Order in included in eventIdList column ', async () => {
        await eventMaintenancePage.open('EventMaintenance');
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents didn`t load before timeout');
        await clickOn(await eventMaintenancePage.refreshEventBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        await clickOn(await eventMaintenancePage.filtersBtn);
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        await eventMaintenancePage.selectFiltersDropdown(emData.select_EventType_selectedEventType, emData.UPL_Orders);
        await clickOn(await eventMaintenancePage.searchBtn);
        await eventMaintenancePage.waitForPageLoad(await eventMaintenancePage.filtersTxtBtn, emData.Filters_text);
        let eventIdListBefore = await eventMaintenancePage.getColumnList(emData.header_Event_Id);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await eventMaintenancePage.open('EventMaintenance');
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await eventMaintenancePage.selectEventType(emData.UPL_Orders);
        expect(await UPLOrderPage.linkSubModule.getText()).toHaveTextContaining(emData.Create_UPL_Orders);
        await UPLOrderPage.selectSingleValue(emData.Location, emData.Locations, emData.Loc00005_Oab_Albany_Fred_Meyer_Stores);
        await UPLOrderPage.selectSingleValue(emData.Department, emData.Departments, emData.Depart_96_GM_MISC);
        await UPLOrderPage.selectRadioButton(emData.Category_Name, emData.Charcoal);
        await UPLOrderPage.selectRadioButton(emData.Product_Group, emData.FLG);
        await UPLOrderPage.setValue(UPLOrderPage.selectATTNToInput, await UPLOrderPage.stringGenerator('12344'));
        await UPLOrderPage.inputSendDate(getTodayDate());
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await UPLOrderPage.setValue(UPLOrderPage.getMailLocationInput, await UPLOrderPage.stringGenerator('ML123'));
        await UPLOrderPage.setValue(UPLOrderPage.getMailStopInput, await UPLOrderPage.stringGenerator('MS2'));
        await UPLOrderPage.setValue(UPLOrderPage.getRoutingInitialsInput, await UPLOrderPage.stringGenerator('RI3'));
        await storeSelectionPage.clickOn(await UPLOrderPage.getUpdateRequestTable);
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await clickOn(await UPLOrderPage.proceedBtn);
        await eventMaintenancePage.open('EventMaintenance');
        await eventMaintenancePage.waitBrowserToLoadPage(2000);
        await eventMaintenancePage.waitForPageLoad(eventMaintenancePage.filtersTxtBtn, emData.Filters)
        await expect(eventMaintenancePage.clickFilters());
        await eventMaintenancePage.selectFiltersDropdown(emData.select_EventType_selectedEventType, emData.UPL_Orders,"selectedEventDropdown");
        await clickOn(await eventMaintenancePage.searchBtn);
        await eventMaintenancePage.waitForPageLoad(await eventMaintenancePage.filtersTxtBtn, emData.Filters_text);
        let eventIdList = await eventMaintenancePage.getColumnList(emData.header_Event_Id);
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await assert.notEqual(eventIdListBefore, eventIdList, 'New UPL Order event is not created !')
    })

    it('Validate EventType dropdown values by comparing EventId column list after selecting each of EventType dropdown values', async () => {
        await eventMaintenancePage.open('EventMaintenance');
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await clickOn(await eventMaintenancePage.refreshEventBtn);
        await eventMaintenancePage.waitForPageLoad(await eventMaintenancePage.filtersTxtBtn, emData.Filters_text);
        let eventIdListInitial = await eventMaintenancePage.getColumnList(emData.header_Event_Id);
        await clickOn(await eventMaintenancePage.filtersBtn);
        await eventMaintenancePage.selectFiltersDropdown(emData.select_EventType_selectedEventType, emData.SFP_Event);
        await clickOn(await eventMaintenancePage.searchBtn);
        let eventIdListSfpEvent = await eventMaintenancePage.getColumnList(emData.header_Event_Id);
        //comparing default EventID list before and after clicking <SFP Event> dropdown value
        await assert.notEqual(eventIdListInitial, eventIdListSfpEvent, 'SFP Event option did not filter the Event ID column list !');
        await eventMaintenancePage.waitForPageLoad(await eventMaintenancePage.filtersTxtBtn, emData.Filters_text);
        await eventMaintenancePage.selectFiltersDropdown(emData.select_EventType_selectedEventType, emData.SFP_Activation,"selectedEventDropdown");
        await clickOn(await eventMaintenancePage.searchBtn);
        let eventIdListSfpActivation = await eventMaintenancePage.getColumnList(emData.header_Event_Id);
        //comparing EventID list before and after clicking <SFP Activation>  dropdown value
        await assert.notEqual(eventIdListInitial, eventIdListSfpActivation, 'SFP Activation option did not filter the Event ID column list !');
        await eventMaintenancePage.waitForPageLoad(await eventMaintenancePage.filtersTxtBtn, emData.Filters_text);
        await eventMaintenancePage.selectFiltersDropdown(emData.select_EventType_selectedEventType, emData.SFP_Refresh,"selectedEventDropdown");
        await clickOn(await eventMaintenancePage.searchBtn);
        let eventIdListSfpRefresh = await eventMaintenancePage.getColumnList(emData.header_Event_Id);
        //comparing EventID list before and after clicking <SFP Refresh>  dropdown value
        await assert.notEqual(eventIdListInitial, eventIdListSfpRefresh, 'SFP Refresh option did not filter the Event ID column list !');
        await eventMaintenancePage.waitForPageLoad(await eventMaintenancePage.filtersTxtBtn, emData.Filters_text);
        await eventMaintenancePage.selectFiltersDropdown(emData.select_EventType_selectedEventType, emData.UPL_Orders,"selectedEventDropdown");
        await clickOn(await eventMaintenancePage.searchBtn);
        let eventIdListUplOrder = await eventMaintenancePage.getColumnList(emData.header_Event_Id);
        //comparing EventID list before and after clicking <UPL Orders>  dropdown value
        await assert.notEqual(eventIdListInitial, eventIdListUplOrder, 'UPL Order option did not filter the Event ID column list !');
    })
});
