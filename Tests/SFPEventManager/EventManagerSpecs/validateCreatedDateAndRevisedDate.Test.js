const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const eventSchedulingPage = require('../EventManagerPO/eventScheduling.page')
const selectPOGPage = require("../EventManagerPO/selectingPogCommodities.page");
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const underscorelib = require('underscore')

describe('validateCreatedDateAndRevicedDate: validate CreatedDateAndRevicedDate on UI and DB', () => {

    it('Should validate corporate event CreatedDateAndRevicedDate on UI and DB', async () => {
       await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.waitBrowserToLoadPage(6000);
        await eventMaintanencePage.tableSecondPageBtn.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.selectEventType('SFP Event')
        expect(await eventMaintanencePage.linkCreateSFPEvent.getText()).toHaveTextContaining(emData.Create_SFP_Event)
        await createSfpEventPage.selectDropdown('division', emData.div_Corporate_060)
        await createSfpEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        await createSfpEventPage.selectDropdown('selectType', "Update");
        let description = await selectPOG.stringGenerator("CorporateEventSplit")
        await createSfpEventPage.enterEventResetDescription(description)
        await createSfpEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSfpEventPage.submitEventDetails()
        let nextPage = await eventMaintanencePage.linkSubModule.getText();
        await expect(nextPage).toEqual(emData.Event_Schedule);
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        for (let index = 0; index <= 1; index++) {
            await eventMaintanencePage.waitBrowserToLoadPage(3000);
            await eventMaintanencePage.waitForPageLoad(eventSchedulingPage.saveScheduleBtn, emData.Save_Schedule);
            await clickOn(await storeSelectionPage.saveSchedule);
        }
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        let week1Text = await (await storeSelectionPage.storeWeekSchedule.getText())
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        let week2Text = await (await storeSelectionPage.storeWeekSchedule2.getText())
        let weekText = await (await storeSelectionPage.storeWeekSchedule2.getText()).replace('Week', '').replace(', ', '/')
        const Week = week1Text.concat(' -', weekText).replace(', ', '/')
        await storeSelectionPage.saveGoToStoreSelection()
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, emData.Store_Selection);
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(storePage).toEqual(emData.Store_Selection)
        let divNum14 = (emData.div_Cincinnati_Operating_Division_014.match(/\d/g).join("")).toString();
        let corpNum06 = (emData.div_Corporate_060.match(/\d/g).join("")).toString();
        let dbScheduledStores = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, divNum14).replace('@num', '30'))).flat();
        let div14 = emData.div_Cincinnati_Operating_Division_014.replace('Cincinnati Operating', '').replace('- ', '').toString().trim()
        await eventMaintanencePage.waitBrowserToLoadPage(6000);
        await storeSelectionPage.clickPlusText(week1Text)
        await storeSelectionPage.clickPlusText(div14)
        await storeSelectionPage.clickCheckBoxText(await dbScheduledStores[5])
        await storeSelectionPage.clickPlusText(week1Text)
        await storeSelectionPage.clickPlusText(week2Text)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.clickPlusText(div14)
        await storeSelectionPage.clickSecondScheduleStoreCheckbox(await dbScheduledStores[12]);
        await storeSelectionPage.clickPlusText(await week2Text)
        await clickOn(storeSelectionPage.saveStoreSchedule)
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, emData.Select_Pogs)
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(pogPage).toEqual(emData.Select_Pogs)
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await selectPOGPage.selectDropdown('Department', emData.dept_01_GROCERY);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOGPage.selectDropdown('Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await selectPOGPage.selectDropdown('Commodity *', emData.ResetComplete_All);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await clickOn(selectPOGPage.selectChoosePogByCommodityBtn);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(8000)
        await clickOn(selectPOGPage.gotoEventListButton);
        await eventMaintanencePage.waitBrowserToLoadPage(6000);
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters);
        await expect(await eventMaintanencePage.eventCreationText.getText()).toEqual(emData.Event_Creation);
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        let dbCorporateEventIdcreated = await (await result.getResult(qrString.corporateSplitEvent.replace('@discrp', description))).flat();
        let initialEventId = await eventMaintanencePage.getHeaderValue(emData.Event_ID, description)
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        await expect(initialEventId).toEqual(dbCorporateEventIdcreated[0].toString());
        let corpDivision = await eventMaintanencePage.getHeaderValue("Division", description)
        await expect(corpDivision).toEqual(corpNum06);
        let eventId = await eventMaintanencePage.getEventId(initialEventId)
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.expandEvent(eventId)
        // use this event id for updating the rev_date to backdate
        let divEventId = await eventMaintanencePage.getHeaderValueExpand(emData.Event_ID, description)
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        await expect(divEventId).toEqual(dbCorporateEventIdcreated[3].toString());
        let divNO = await eventMaintanencePage.getHeaderValueExpand("Division", description)
        await expect(divNO).toEqual(divNum14);
        // update event to  2 days back using event id divisional and run Update querry
        await (await result.executeQuery(qrString.updateRevisedDate.replace('@evID', divEventId)))
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await eventMaintanencePage.clickOn(eventMaintanencePage.searchBtn)
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await eventMaintanencePage.expandEvent(eventId)
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await eventMaintanencePage.expandEventPlus(divEventId);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        let createdDate = await eventMaintanencePage.getHeaderValuesExpanded(emData.plus_Stores_header_CreatedDate);
        let createdDateList = await createdDate.flat()
        let revisedDate = await eventMaintanencePage.getHeaderValuesExpanded(emData.plus_Stores_header_RevisedDate);
        let revisedDateList = await revisedDate.flat()
        // start editing the event
        await eventMaintanencePage.expandEvent(eventId);
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        let releaseEventEdit = await eventMaintanencePage.selectReleaseEvents(emData.ReleaseEvent_Edit, emData.Header_Action, description)
        await expect(releaseEventEdit).toEqual(true)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await createSfpEventPage.submitEventDetails()
        await createEventSchedulePage.selectDropdown('Fiscal Week');
        await storeSelectionPage.saveGoToStoreSelection()
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await storeSelectionPage.clickPlusText(week1Text)
        await clickOn(storeSelectionPage.saveStoreSchedule)
     await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, emData.Select_Pogs)
        await expect(pogPage).toEqual(emData.Select_Pogs)
        await clickOn(selectPOGPage.selectChoosePogByCommodityBtn);
        await selectPOGPage.waitForPageLoad(selectPOGPage.selectText, emData.Department);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await (await selectPOG.gotoEventListButton).waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await clickOn(selectPOGPage.gotoEventListButton);
        await eventMaintanencePage.waitBrowserToLoadPage(8000)

        await eventMaintanencePage.waitForPageLoad(await eventMaintanencePage.filtersTxtBtn, emData.Filters);
        await expect(browser).toHaveUrlContaining(emData.EventMaintanceUrl)
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await eventMaintanencePage.clickFiltersWithDiscp(description.toString())
        await eventMaintanencePage.waitBrowserToLoadPage(2000)
        await eventMaintanencePage.expandEvent(eventId);
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        let newDivEventId = await eventMaintanencePage.getHeaderValueExpand(emData.Event_ID, description)
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await eventMaintanencePage.clickOn(await eventMaintanencePage.slideOutClose)
        await eventMaintanencePage.expandEventPlus(newDivEventId)
        // final created list
        let finalcreatedDate = await eventMaintanencePage.getHeaderValuesExpanded(emData.plus_Stores_header_CreatedDate)
        let finalcreatedDateList = await finalcreatedDate.flat()
        await expect(underscorelib.isEqual(finalcreatedDateList, createdDateList)).toBe(true)
        // final revised list
        let finalrevisedDate = await eventMaintanencePage.getHeaderValuesExpanded(emData.plus_Stores_header_RevisedDate)
        let finalrevisedDateList = await finalrevisedDate.flat()
        await expect(underscorelib.isEqual(finalrevisedDateList, revisedDateList)).toBe(false)
        // validate the create_Date and Rev_Date with DB
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        let dbCreatedDate = await (await result.getResult(qrString.splitCreatedDate.replace('@discrp', description))).flat()
        await expect(finalcreatedDateList).toEqual(dbCreatedDate)
        let dbRevisedDate = await (await result.getResult(qrString.splitRevisedDate.replace('@discrp', description))).flat()
        await expect(finalrevisedDateList).toEqual(dbRevisedDate)
    })
})
