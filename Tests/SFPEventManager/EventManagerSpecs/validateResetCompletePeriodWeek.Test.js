const resetCompletionPage = require('../EventManagerPO/resetCompletion.page.js')
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page.js');
const autoActivationReport = require('../EventManagerPO/autoActivationReport.page.js');
const ResetCompletionPage = require("../EventManagerPO/resetCompletion.page.js");

describe('validateResetCompletion YearPeriodWeek', () => {

    beforeEach(async () => {
        await resetCompletionPage.waitBrowserToLoadPage(6000)
        await resetCompletionPage.open('EventMaintanence/resetCompletion');
        await ResetCompletionPage.waitForPageLoad(await ResetCompletionPage.linkSubModule, emData.Admin_reset_completion)
        await resetCompletionPage.selectSingleValue(emData.selectedCompletionType_ResetCompletion, emData.ResetComplete_All)
        await resetCompletionPage.waitForPageLoad(resetCompletionPage.clearFiltersBtn, emData.ResetComplete_Clear_Filters)
        await resetCompletionPage.selectSingleValue(await emData.selectedDivision_ResetCompletion, emData.division_Cincinnati_Operating_Division_014)
        await expect(await resetCompletionPage.selectDivision.getText()).toEqual(emData.division_Cincinnati_Operating_Division_014_Txt)
        await resetCompletionPage.setSingleValue(emData.selectedLocation_ResetCompletion, emData.selectedLocation_014_00466)
        expect(await resetCompletionPage.selectLocation.getText()).toEqual(await emData.selectedLocation_014_00466_Txt);
        //await autoActivationReport.selectYPW(emData.Year_Input, emData.Year_2022);
        await expect(await resetCompletionPage.selectYear.getText()).toEqual(emData.Year_2022)
        await autoActivationReport.selectYPW(emData.Period_Input, emData.Period_8)
        await expect(await resetCompletionPage.selectPeriod.getText()).toEqual(emData.Period_8)
        await autoActivationReport.selectYPW(emData.Week_Input, emData.Week_4_32)
        await expect(await resetCompletionPage.selectWeek.getText()).toEqual(emData.Week_4_32)
        await storeSelectionPage.clickOn(resetCompletionPage.searchBtn)
        await storeSelectionPage.clickOn(resetCompletionPage.storeReview)
        await resetCompletionPage.waitBrowserToLoadPage(8000)
    })

    it('should validate  Year from the filter and in table UI', async () => {
        let year = await (await resetCompletionPage.getStoreReviewColumnArrayList("Y","4"))
        let yearString = await resetCompletionPage.getUniqueArrayValue(year)
        expect(await resetCompletionPage.getTextOfElement(resetCompletionPage.selectYear)).toEqual(yearString.join())
    })

    it('should validate  Week from the filter and in table UI', async () => {
        let week = await (await resetCompletionPage.getStoreReviewColumnArrayList("W","4"))
        let weekString = await resetCompletionPage.getUniqueArrayValue(week)
        expect(await resetCompletionPage.getTextOfElement(resetCompletionPage.selectWeek)).toEqual(weekString.join())

    })

    it('should validate  Period from the filter and in table UI', async () => {
        let period = await (await resetCompletionPage.getStoreReviewColumnArrayList("P","4"))
        let periodString = await resetCompletionPage.getUniqueArrayValue(period)
        expect(await resetCompletionPage.getTextOfElement(resetCompletionPage.selectPeriod)).toEqual(periodString.join())
    })
})
