const eventMaintenancePage = require('../EventManagerPO/eventMaintanence.page');
let EMData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const {clickOn} = require("../EventManagerPO/createSFPEvent.page");



describe('validateFiltersClearDropdowns: validateFiltersClearDropdowns: validate individual clearDropdown buttons when dropdowns are selected separately and validate ClearAllFilters button', () => {
    
    before(async ()=>{
           await eventMaintanencePage.waitBrowserToLoadPage(6000)
            await eventMaintenancePage.open('EventMaintenance');  
               await eventMaintenancePage.waitBrowserToLoadPage(5000)   
               await expect(await eventMaintenancePage.GetText(await eventMaintenancePage.emHomePageText)).toEqual('SFP Event Manager')
               await eventMaintenancePage.tableSecondPageBtn.waitForExist({timeout: 90000, timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await (eventMaintenancePage.filtersBtn).click();
        await eventMaintenancePage.clickOn(eventMaintenancePage.refreshEventBtn);
    });

    it('Validate ClearEventType dropdown button for EventType dropdown', async () => {
        await eventMaintenancePage.selectFiltersDropdown(EMData.select_EventType_selectedEventType, EMData.SFP_Refresh);
        await clickOn(await eventMaintenancePage.searchBtn);
        let resultEventTypeText = await eventMaintenancePage.getFiltersDropdownText(EMData.Event_Type);
        await expect(resultEventTypeText).toEqual(EMData.SFP_Refresh);
        await eventMaintenancePage.clickClearFiltersDropdown(EMData.clearEventType);
        let resultEventTypeClearText = await eventMaintenancePage.getFiltersDropdownText(EMData.Event_Type);
        await expect(resultEventTypeClearText).toEqual(EMData.Select_Event_Type);
    });

    it('Validate ClearDivision button for Division dropdown', async () => {
        await eventMaintenancePage.selectFiltersDropdown(EMData.selectedDivision_text, EMData.div_Columbus_Operating_Division_016);
        let resultDivisionText = await eventMaintenancePage.getFiltersDropdownText(EMData.Division_text);
        await expect(resultDivisionText).toEqual(EMData.div_Columbus_Operating_Division_016);
        await eventMaintenancePage.clickClearFiltersDropdown(EMData.clearDivision_text);
        let resultDivisionClearText = await eventMaintenancePage.getFiltersDropdownText(EMData.Division_text);
        await expect(resultDivisionClearText).toEqual(EMData.Select_Division_text);
    });

    it('Validate ClearDepartment button for Department dropdown', async () => {
        await eventMaintenancePage.selectFiltersDropdown(EMData.selectedDepartment_eventMaintenance, EMData.Department_15_DELI);
        let resultDepartText = await eventMaintenancePage.getFiltersDropdownText(EMData.Department);
        await expect(resultDepartText).toEqual(EMData.Department_15_DELI);
        await eventMaintenancePage.clickClearFiltersDropdown(EMData.clearDepartment_text);
        let resultDepartClearText = await eventMaintenancePage.getFiltersDropdownText(EMData.Department);
        await expect(resultDepartClearText).toEqual(EMData.Select_Department_text);
    });

    it('Validate ClearSubDepartment button for SubDepartment dropdown', async () => {
        await eventMaintenancePage.selectFiltersDropdown(EMData.selectedSubDept_eventMaintenance, EMData.Sub_Depart_15_DELI_49_SPECIALTY_CHSE);
        let resultSubDepText = await eventMaintenancePage.getFiltersDropdownText(EMData.Sub_Department_text);
        await expect(resultSubDepText).toEqual(EMData.Sub_Depart_15_DELI_49_SPECIALTY_CHSE);
        await eventMaintenancePage.clickClearFiltersDropdown(EMData.clearSubDepartment_text);
        let resultSubDepClearText = await eventMaintenancePage.getFiltersDropdownText(EMData.Sub_Department_text);
        await expect(resultSubDepClearText).toEqual(EMData.Select_Sub_Department_text);
    });

    it('Validate ClearCommodity button for Commodity dropdown', async () => {
        await eventMaintenancePage.selectFiltersDropdown(EMData.selectedCommodity_eventMaintenance, EMData.Commodity_15_DELI_462_SPECIALTY_CHEESE_PRE_PACK);
        let resultCommodityText = await eventMaintenancePage.getFiltersDropdownText(EMData.Commodity_text);
        await expect(resultCommodityText).toEqual(EMData.Commodity_15_DELI_462_SPECIALTY_CHEESE_PRE_PACK);
        await eventMaintenancePage.clickClearFiltersDropdown(EMData.clearCommodity_text);
        let resultCommodityClearText = await eventMaintenancePage.getFiltersDropdownText(EMData.Commodity_text);
        await expect(resultCommodityClearText).toEqual(EMData.Select_Commodity_text);
    });

    it('Validate ClearPeriodWeek button for PeriodWeek dropdown', async () => {
        await eventMaintenancePage.selectFiltersDropdown(EMData.selectedWeek_eventMaintenance, EMData.PeriodWeek_2022_Week_4);
        let resultWeekText = await eventMaintenancePage.getFiltersDropdownText(EMData.Period_Week_text);
        await expect(resultWeekText).toEqual(EMData.PeriodWeek_2022_Week_4);
        await eventMaintenancePage.clickClearFiltersDropdown(EMData.clearPeriodWeek_text);
        let resultWeekClearText = await eventMaintenancePage.getFiltersDropdownText(EMData.Period_Week_text);
        await expect(resultWeekClearText).toEqual(EMData.Select_Period_Week_text);
    });

    it('Validate ClearAllFilters button for ALL dropdowns and input box', async () => {
        await eventMaintenancePage.selectFiltersDropdown(EMData.select_EventType_selectedEventType, EMData.eventType_SFP_Event);
        await eventMaintenancePage.selectFiltersDropdown(EMData.selectedDivision_eventMaintenance, EMData.div_Columbus_Operating_Division_016);
        await eventMaintenancePage.selectFiltersDropdown(EMData.selectedDepartment_eventMaintenance, EMData.Department_15_DELI);
        await eventMaintenancePage.selectFiltersDropdown(EMData.selectedSubDept_eventMaintenance, EMData.Sub_Depart_15_DELI_49_SPECIALTY_CHSE);
        await eventMaintenancePage.selectFiltersDropdown(EMData.selectedCommodity_eventMaintenance, EMData.Commodity_15_DELI_462_SPECIALTY_CHEESE_PRE_PACK);
        await eventMaintenancePage.selectFiltersDropdown(EMData.selectedWeek_eventMaintenance, EMData.PeriodWeek_2022_Week_4);
        await eventMaintenancePage.setValue(await eventMaintenancePage.eventDescInput,  EMData.inputString_sfpEvent1234560be0b01506);
        let inputIsNotEmptyBoolean = await eventMaintenancePage.inputBoxIsNotEmpty(eventMaintenancePage.eventDescInput);
        await clickOn(await eventMaintenancePage.searchBtn);
        await expect(inputIsNotEmptyBoolean).toEqual(true);
        await clickOn(await eventMaintenancePage.clearAllFiltersBtn);
        let resultEventTypeClearText = await eventMaintenancePage.getFiltersDropdownText(EMData.Event_Type);
        await expect(resultEventTypeClearText).toEqual(EMData.Select_Event_Type);
        let resultDivisionClearText = await eventMaintenancePage.getFiltersDropdownText(EMData.Division_text);
        await expect(resultDivisionClearText).toEqual(EMData.Select_Division_text);
        let resultDepartClearText = await eventMaintenancePage.getFiltersDropdownText(EMData.Department);
        await expect(resultDepartClearText).toEqual(EMData.Select_Department_text);
        let resultSubDepClearText = await eventMaintenancePage.getFiltersDropdownText(EMData.Sub_Department_text);
        await expect(resultSubDepClearText).toEqual(EMData.Select_Sub_Department_text);
        let resultCommodityClearText = await eventMaintenancePage.getFiltersDropdownText(EMData.Commodity_text);
        await expect(resultCommodityClearText).toEqual(EMData.Select_Commodity_text);
        let resultWeekClearText = await eventMaintenancePage.getFiltersDropdownText(EMData.Period_Week_text);
        await expect(resultWeekClearText).toEqual(EMData.Select_Period_Week_text);
        let inputIsEmptyBoolean = await eventMaintenancePage.inputBoxIsEmpty(eventMaintenancePage.getEventDescInputText, EMData.attribute_ng_reflect_model);
        await expect(inputIsEmptyBoolean).toEqual(true);
    });

})
