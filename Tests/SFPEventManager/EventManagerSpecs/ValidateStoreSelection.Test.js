const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');

describe('ValidateStoreSelection: validate Store selection page', () => {

    it('Should validate Store selection page', async () => {

        await createSFPEventPage.addEventDetails1('','SFP Event', emData.div_Cincinnati_Operating_Division_014, emData.eventType_KOMPASS, "Update", "Event or Reset Description", emData.vendor_AZB);
        await createSFPEventPage.submitEventDetails()
        let nextPage = await eventMaintanencePage.linkSubModule.getText()
        await expect(await nextPage).toEqual(emData.Event_Schedule)
        await createSFPEventPage.addEventScheduleStep2();
        let storePage = await storeSelectionPage.linkSubModule.getText()
         await expect(await storePage).toEqual(emData.Store_Selection);
        let storeList = ['351','317','395'];
        for(let index=0;index<storeList.length;index++){
            await storeSelectionPage.clickCheckBoxText(storeList[index]);
        }
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await clickOn(await storeSelectionPage.saveStoreSchedule)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Select Pogs")
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(await pogPage).toEqual(emData.Select_Pogs)
    })
})
