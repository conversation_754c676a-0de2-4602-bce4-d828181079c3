const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const createSfpEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');
const {expect } = require('chai')

describe('validateAddEventDetailActivation: validate the add event detail dropdown for Activation', () => {

    it('validate the Add detail dropdown for Activation events', async () => {
        let eventDescription = await createSfpEventPage.addEventDetails1('','SFP Activation', emData.div_Cincinnati_Operating_Division_014, emData.eventType_KOMPASS, "Activation", "ActivationEvent", emData.vendor_AZB)
        await createSfpEventPage.submitEventDetails()
        await createEventSchedulePage.scheduling(1)
        await storeSelectionPage.saveGoToStoreSelection()
        let div = (emData.div_Cincinnati_Operating_Division_014.match(/\d/g).join("")).toString()
        let dbScheduledStores = await (await result.getResult(qrString.scheduledStores.replace(/@div/g, div).replace('@num', '30'))).flat();
        await storeSelectionPage.storeSelection(dbScheduledStores, 5, 1)
        await storeSelectionPage.storeSelection(dbScheduledStores, 12, 2)
        await storeSelectionPage.saveStoreScheduleButton()
        await eventMaintanencePage.waitBrowserToLoadPage(4000);
        await createSfpEventPage.inputUPC.setValue(emData.UPC_Value_61076426171)
        await eventMaintanencePage.clickOn(createSfpEventPage.addUPC)
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000, timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await eventMaintanencePage.clickOn(createSfpEventPage.saveUPC)
        await expect(await eventMaintanencePage.eventCreationText.getText()).to.equal(emData.Event_Creation);
        await eventMaintanencePage.waitForPageLoad(eventMaintanencePage.filtersTxtBtn, emData.Filters);
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000, timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await (await eventMaintanencePage.tableSecondPageBtn).waitForClickable({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.clickFiltersWithDiscp(eventDescription.toString())
        let scheduledStrCount = await eventMaintanencePage.getHeaderValue(emData.Header_ScheduledStr, eventDescription)
        let scheduleType = await eventMaintanencePage.getHeaderValue(emData.Header_scheduleType, eventDescription)
        await expect(scheduleType).to.equal(emData.eventType_KOMPASS)
        let type = await eventMaintanencePage.getHeaderValue(emData.Header_Type, eventDescription)
        await expect(type).to.equal(emData.type_Activation)
        let popUpSchedule = (await eventMaintanencePage.getColumnValues(emData.event_Schedule_Popup_Schedule_Table, emData.event_Schedule_Popup_Schedule_List)).sort();
        await expect(popUpSchedule.length.toString()).to.equal(scheduledStrCount)
        await eventMaintanencePage.clickCloseButton('Close');
    })

})