const eventMaintenancePage = require('../EventManagerPO/eventMaintanence.page');
const createSFPEventPage = require('../EventManagerPO/createSFPEvent.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
const { clickOn } = require('../EventManagerPO/createSFPEvent.page');
const { GetText } = require('../../../GlobalObjectRepository/login.page');
const { assert } = require('chai');
const qrString = require('../../../SQLConnection/queryStringEM');
const result = require('../../../SQLConnection/server.js.ts');
const createEventSchedulePage = require('../EventManagerPO/eventScheduling.page');

describe('validateDeletedEvents: validate the Event is deleted in EventMaintenance page UI and DB', () => {

    it('Should validate the Event is deleted in EventMaintaince page UI and DB', async () => {
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await eventMaintenancePage.open('EventMaintenance');
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'});
        await eventMaintenancePage.selectEventType('SFP Event')
        await createSFPEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014)
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        await createSFPEventPage.selectDropdown('selectType', "Update");
        let description = await selectPOG.stringGenerator("DeleteEvent")
        await createSFPEventPage.enterEventResetDescription(description)
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSFPEventPage.submitEventDetails()
        let nextPage = await eventMaintenancePage.linkSubModule.getText()
        await expect(nextPage).toEqual(emData.Event_Schedule)
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 , timeoutMsg:'Error: Save Schedule button did not appear before timeout'})
        await createEventSchedulePage.selectDropdown('Fiscal Week');
        await clickOn(storeSelectionPage.saveSchedule)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        let weekText = await storeSelectionPage.storeWeekSchedule.getText();
        console.log('$$$ weekText', weekText)

        await storeSelectionPage.saveGoToStoreSelection()
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(storePage).toEqual(emData.Store_Selection)
        await storeSelectionPage.clickCheckBoxText(emData.Store_353); 
        await clickOn(await storeSelectionPage.saveStoreSchedule);
        await eventMaintenancePage.waitBrowserToLoadPage(3000)
        await storeSelectionPage.waitForPageLoad(await storeSelectionPage.linkSubModule, "Select Pogs")
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(pogPage).toEqual(emData.Select_Pogs)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await selectPOG.selectDropdown('Department', emData.dept_01_GROCERY)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Commodity *', emData.commodity_003_CAN_BEANS_01_GROC_ALL_OTHER_01_GROCERY)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Commodity Group', emData.commodity_003_CAN_BEANS_01_GROC_ALL_OTHER_01_GROCERY)
        await clickOn(selectPOG.selectChoosePogByCommodityBtn)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        let text = await selectPOG.getPogRowText(emData.DBkey_7239932); 
        await expect(text).toEqual(true)
        await clickOn(selectPOG.gotoEventListButton)
        await expect(browser).toHaveUrlContaining("EventMaintenance")
        await eventMaintenancePage.waitBrowserToLoadPage(4000);
        await eventMaintenancePage.clickFiltersWithDiscp(description.toString())
        let eventDescription = await selectPOG.getEventDescText(await description, await weekText.replace(', ', '/'));
        await expect(await eventDescription).toEqual(true);
        await (await eventMaintenancePage.tableFirstRow).waitForExist(({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'}))
        let initialEventId = await eventMaintenancePage.getHeaderValue(emData.Event_ID, description)
        await eventMaintenancePage.waitBrowserToLoadPage(3000);
        await eventMaintenancePage.clickOn(await eventMaintenancePage.slideOutClose)
        let eventId = await eventMaintenancePage.getEventId(initialEventId)
        await expect(eventId).toEqual(initialEventId)
        await (await eventMaintenancePage.tableFirstRow).waitForExist(({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'}))
        await eventMaintenancePage.deleteActionBtn('OK')
        await (await eventMaintenancePage.tableFirstRow).waitForExist(({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'}))
        let finalEventId = await eventMaintenancePage.getEventId(initialEventId)
        await assert.notEqual(finalEventId, initialEventId)
        let dataFromDb = await result.getResult(qrString.SfpEvent_Eventid.replace('eventID', initialEventId))
        await assert.equal(dataFromDb.length, 0)
    })

    it('Should validate the Pog is deleted in pogCommodities page', async () => {
        await eventMaintenancePage.open('EventMaintenance');
        await eventMaintenancePage.waitBrowserToLoadPage(6000)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await eventMaintenancePage.selectEventType('SFP Event')
        await createSFPEventPage.selectDropdown('division', emData.div_Cincinnati_Operating_Division_014)
        await createSFPEventPage.selectDropdown('scheduleType', emData.eventType_KOMPASS)
        await createSFPEventPage.selectDropdown('selectType', "Update");
        let description = await selectPOG.stringGenerator("DeletePog")
        await createSFPEventPage.enterEventResetDescription(description)
        await createSFPEventPage.selectDropdown('vendor', emData.vendor_AZB)
        await createSFPEventPage.submitEventDetails()
        let nextPage = await eventMaintenancePage.linkSubModule.getText()
        await expect(nextPage).toEqual(emData.Event_Schedule)
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 , timeoutMsg:'Error: Save Schedule button did not appear before timeout'})
        await createEventSchedulePage.selectDropdown('Fiscal Week');
        await clickOn(storeSelectionPage.saveSchedule)
        await eventMaintenancePage.tableFirstRow.waitForExist({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'})
        await storeSelectionPage.saveGoToStoreSelection()
        let storePage = await storeSelectionPage.linkSubModule.getText()
        await expect(storePage).toEqual(emData.Store_Selection)
        await storeSelectionPage.clickCheckBoxText(emData.Store_353); 
        await clickOn(storeSelectionPage.saveStoreSchedule)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Select Pogs")
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(pogPage).toEqual(emData.Select_Pogs)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await selectPOG.selectDropdown('Department', emData.dept_01_GROCERY)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Sub-Department', emData.subdept_01_GROCERY_01_GROC_ALL_OTHER)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Commodity *', emData.commodity_003_CAN_BEANS_01_GROC_ALL_OTHER_01_GROCERY)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await selectPOG.selectDropdown('Commodity Group', emData.CommodityGroup_013_SPICES_EXTRACTS_013_SPICES_EXTRACTS)
        await clickOn(selectPOG.selectChoosePogByCommodityBtn)
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department)
        let text = await selectPOG.getPogRowText(emData.DBkey_7239932);
        await eventMaintenancePage.waitBrowserToLoadPage(2000)
        await expect(text).toEqual(true)
        await clickOn(selectPOG.checkBox)
        await (await selectPOG.selectRemoveSelectedPogButton).isClickable(({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'}))
        await clickOn(selectPOG.selectRemoveSelectedPogButton)
        await (await selectPOG.pogDeleteMsg).waitForExist(({ timeout: 90000 , timeoutMsg:'Error: Table contents for event maintenance page didn`t load before timeout'}))
        let pogDelete = await GetText(selectPOG.pogDeleteMsg);
        await expect(pogDelete).toEqual(emData.pogDeleteMsg);
    })
})
