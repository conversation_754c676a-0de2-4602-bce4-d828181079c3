const { default: clearValue } = require('webdriverio/build/commands/element/clearValue');
const Page = require('../../../GlobalObjectRepository/page');
const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
/**
 * sub page containing specific selectors and methods for Event Scheduling page
 */
let no,previousIndex,indexvalue;
class EventSchedulingPage extends Page {
    get linkCreateEventSchedule()                  { return $('#page > app-breadcrumb > div > ol > li:nth-child(4) > a')}
    get eventSchedulingTxt()                       { return $('fieldset[id="ui-fieldset-2"]>legend>span+p-header')}
    get scheduledListTxt()                         { return $('fieldset[id="ui-fieldset-3"]>legend>span+p-header')}
    get goToStoreSelectionBtn()                    { return $('kds-button[aria-label="Done, go to Store Selection"]')}
    get step2Txt()                                 { return $('ul[class="nav nav-tabs"]>li:nth-child(2)')}
    get attentionDate()                            { return $('div[id="attention"]>kds-message>kds-text>span>kds-text:nth-child(2)>span>span')}
    get attentionMessage()                         { return $('div[id="attention"]>kds-message')}
    get saveScheduleBtn()                           { return $('kds-button[aria-label="Save Schedule"]') }
    get fiscalWeekSelect()                         { return $('div[aria-label="Fiscal Week"]') }
    get fiscalYearSelect()                         { return $('div[aria-label="Fiscal Year"]') }
    get table()                                    { return $('[id^="pn_id_"][id$="-table"]') }
    get processWeekdaySelect()                     { return $('div[aria-label="Process Weekday"]') }
    get retailEffectiveDateSelect()                { return $('#event-schedule-content > div.event-schedule--form > div > div.fulltags.mt-16.ng-star-inserted > div > div > p-calendar > span > input') }
    get dateIcon()                                 { return $('#event-schedule-content > div.event-schedule--form > div > div.fulltags.mt-16.ng-star-inserted > div > div > p-calendar > span > button > calendaricon')}
    get firstTableRow()                            { return $('tbody[class="ui-table-tbody"]>tr:nth-child(1)') }
    get firstRowScheduledValue()                   { return $('tbody[class="ui-table-tbody"]>tr:nth-child(1)>td:nth-child(1)') }
    get firstRowStoresValue()                      { return $('tbody[class="ui-table-tbody"]>tr:nth-child(1)>td:nth-child(2)') }
    get firstRowDayOfWeekValue()                   { return $('tbody[class="ui-table-tbody"]>tr:nth-child(1)>td:nth-child(3)') }
    get firstRowEffectiveDateValue()               { return $('tbody[class="ui-table-tbody"]>tr:nth-child(1)>td:nth-child(4)') }
    get linkSubModule()                             { return $('#page > app-breadcrumb > div > ol > li:nth-child(4) > a')}
    get yesDeleteBtn()                             { return $('button[aria-label="Yes, Delete"]')}
    get deleteTxt()                                { return $('div[id="delete-modal"]>div>div>span+div')}
    get cancelBtn()                                { return $('div[class="ui-dialog-footer ui-widget-content"]>ers-button:nth-child(2)')}
    get cancelTxt()                                { return $('div[class="ui-dialog-footer ui-widget-content"]>ers-button:nth-child(2)>button>span')}
    get calenderTitle()                            { return $('div[class="ui-datepicker-title"]')}
    get calenderTable()                            { return $('tbody[class="ng-tns-c13-48"]')}
    get monthCalender()                            { return $('div[class="ui-datepicker-title"]>span:nth-child(1)')}
    get yearCalender()                             { return $('div[class="ui-datepicker-title"]>span:nth-child(2)')}
    get nextCalender()                             { return $('span[class="ui-datepicker-next-icon pi pi-chevron-right"]')}
    get fiscalYearDropdownList()                   { return $$('ers-single-select-list-item[class="ng-star-inserted"]>li')}
    get fiscalWeekDropdownList()                   { return $$('#scheduling-dropdowns > mx-single-select[name="selectedWeek"]>div mx-single-select-list >ul >div> mx-single-select-list-item')}
    get fiscalWeekDropdownListInModal()            { return $$('body > div.mx-single-select-container.mx-dropdown-list.is-compact > mx-single-select-list > ul > div > mx-single-select-list-item')}
    get processWeekdayDropdownList()               { return $$('#event-schedule-content > div.event-schedule--form > div > div.fulltags.mt-16.ng-star-inserted > div > mx-single-select > div > div.mx-single-select-container.mx-dropdown-list.is-compact > mx-single-select-list > ul > div > mx-single-select-list-item> div')}
    get columnNameList()                           { return $$('thead[class="ui-table-thead"]>tr>th')}
    get tableValues()                              { return $$('tr[class="ng-star-inserted"]>td')}
    get rowList()                                  { return $$('tbody[class="ui-table-tbody"]>tr')}
    get editBtnList()                              { return $$('button[aria-label="Edit Event Schedule"]')}
    get deleteBtnList()                            { return $$('app-delete-modal[id="delete-event-modal-btn"]')}
    get scheduledColumnList()                      { return $$('tr[class="ng-star-inserted"] td:nth-child(1)')}
    get processDayOfWeekList()                     { return $$('tr[class="ng-star-inserted"] td:nth-child(3)')}
    get retailEffectiveDataList()                  { return $$('tr[class="ng-star-inserted"] td:nth-child(4)')}
    get actionList()                               { return $$('tr[class="ng-star-inserted"] td:nth-child(5)')}
    get calenderValuesList()                       { return $$('div.p-datepicker-calendar-container> table > tbody > tr > td')}
    get processDayOfWeek()                         { return $('tbody[class="p-element p-datatable-tbody"] tr:nth-child(1) td:nth-child(3)')}
    get deleteBtn()                                { return $('[id^="pn_id_"][id$="-table"]> tbody > tr:nth-child(1) > td:nth-child(5) > div > kds-tooltippable:nth-child(2)')}
    get tableRows()                                { return $$('tbody[class="p-element p-datatable-tbody"]>tr') }
    get linkCreateRefreshEventSchedule()           { return $('#page>app-breadcrumb>div>ol>li:nth-child(3)>a')}
    get eventScheduleHeader()                        { return $('div>div>div>ul>li:nth-child(2)>a')}
    get retailEffectiveDate()                      { return $('tbody[class="p-element p-datatable-tbody"] tr>td:nth-child(4)')}
    get saveScheduleFailedTxt()                     { return $('#kds-Portal-toast>div>kds-toast>section>header>h1')}
    get sfpAppModalButton()                          { return $('button[aria-label="OK"]')}
    get editScheduleList()                          {return $(`tbody > tr > td:nth-child(5) > div > kds-tooltippable:nth-child(1) > kds-button button kds-icon-edit `)}
    get fiscalWeekSelectInModal()                    {return $(`div>mx-single-select#fiscalWeekDropdown`)}
    get updateButtonInModal()                       {return $(`ers-button >button[aria-label="Update"]`)}
    get viewActivityLog()                           {return $(`button[aria-label='Activity Log']`)}
    get tableHeaders()                              {return $$(`thead[class='p-datatable-thead']>tr th`) }
    get tableValueList()                           { return $$(`div table > tbody > tr > td:nth-child(${no})`) }
    get activityVisibleText()                      { return $('body > ers-modal:nth-child(9) > div > div.ers-modal > div > div.ers-dialog-titlebar > div.header-tile > div') }
    get fiscalWeekAlert()                          { return $('#scheduling-dropdowns > div> kds-message > kds-text span[class="kds-Text--s"]') }
    get previousIndex()                            { return $(`mx-single-select-list > ul > div > mx-single-select-list-item:nth-child(${previousIndex})>div>li`)} 
    get checkbox()                                 { return $(`#scheduling-dropdowns > mx-single-select[name="selectedWeek"]>div mx-single-select-list >ul >div> mx-single-select-list-item:nth-child(${indexvalue}) > div > li`)}
    get checkboxInEdit()                           { return $(`body > div.mx-single-select-container.mx-dropdown-list.is-compact > mx-single-select-list > ul > div > mx-single-select-list-item:nth-child(${indexvalue}) > div > li`)}
    get weekDayNretailEffectiveInModal()           { return $(`#edit-event-scheduling div.ml-4.mt-4 > ers-dropdown > div > div`)}
    get weekDayNretailEffectiveInModalList()       { return $$(`#edit-event-scheduling  mx-single-select`)}
    get activityTableHeaders()                     {return $$(`thead[class="ui-table-thead"]>th`) }
    get activityTableValueList()                   { return $$(`div.ers-dialog-content  table > tbody > tr > td:nth-child(${no})`) }
    get closeButtonInModalActivitylog()            {return $(`ers-button >button[aria-label="Close"]`)}
    get stripScheduleEditErrorText()               {return $(`#scheduling-dropdowns > div:nth-child(3) > kds-message > kds-text > span > kds-text > span`)}
    get schedulingAttentionMessage()               {return $(`#attention-msg > kds-text > span > kds-text.mb-0.block.ng-star-inserted.hydrated`)}


    async genericClickDropdown(item) {
        let locator;
        switch (item) {
            case 'Fiscal Year':
                locator = await this.fiscalYearSelect;
                break;
            case 'Fiscal Week':
                locator = await this.fiscalWeekSelect;
                break;
            case 'Process Weekday':
                locator = await this.processWeekdaySelect;
                break;
            case 'Fiscal Week In Modal':
                await browser.pause(2000)
                locator = await this.fiscalWeekSelectInModal;
                break;
            case 'Process Weekday And ReailEffective In Modal':
                locator = await this.weekDayNretailEffectiveInModal;
                break;    
        }
        if (await locator.isClickable()) {
            await super.clickOn(await locator);
            await super.waitBrowserToLoadPage(2000)
        }
    }

    async selectDropdown(item, value = null) {
        // Define locator and dropdown mappings
        const locators = {
            'Fiscal Year': await this.fiscalYearSelect,
            'Fiscal Week': await this.fiscalWeekSelect,
            'Process Weekday': await this.processWeekdaySelect,
            'Process Weekday And RetailEffective In Modal': await this.retailEffectiveDateSelect,
        };
        const dropdowns = {
            'Fiscal Year': await this.fiscalYearDropdownList,
            'Fiscal Week': await this.fiscalWeekDropdownList,
            'Process Weekday': await this.processWeekdayDropdownList,
            'Process Weekday And RetailEffective In Modal': await this.weekDayNretailEffectiveInModalList,
        };
        // Check if the provided item is valid
        if (!locators[item] || !dropdowns[item]) {
            throw new Error(`Invalid dropdown item: ${item}`);
        }
        await super.waitBrowserToLoadPage(2000);
        await super.clickOn(await locators[item]);
        const dropdown = await dropdowns[item];
        // If no value is provided, select the first item
        if (value === null) {
            const firstItem = await dropdown[0];
            await firstItem.waitForDisplayed();
            await firstItem.waitForClickable();
            await firstItem.click();
            return;
        }
        for (let i = 0; i < await dropdown.length; i++) {
            const dropdownItem = await dropdown[i];
            await dropdownItem.waitForDisplayed();
            const text = await dropdownItem.getText();
            if (text === value) {
                await dropdownItem.scrollIntoView();
                await dropdownItem.waitForClickable();
                await dropdownItem.click();
                // Exit once the matching item is clicked
                break;
            }
        }
    }

    async clickAndValidateSingleDropdownValue(dropdownLocator) {
        try {
            await super.waitBrowserToLoadPage(2000);
            await super.clickOn(dropdownLocator);
            const dropdownItems = await this.fiscalWeekDropdownList;
            if (dropdownItems.length === 1) {
                const text = await this.getArrayElementsText(dropdownItems)
                await super.waitBrowserToLoadPage(1000);
                return text;
            } else {
                throw new Error(`Dropdown contains ${dropdownItems.length} values, expected 1.`);
            }
        } catch (error) {
            console.error("Error in clickAndValidateSingleDropdownValue:", error);
            throw error;
        }
    }

    async verifySchedulingPageLoaded() {
        await this.saveScheduleBtn.waitForDisplayed({timeout:4000}, 'Error: Scheduling page could not load within timeout')

    }

    async clickSaveScheduleBtn() {
        let saveScheduleBtn = await this.saveScheduleBtn
        await saveScheduleBtn.waitForClickable({timeout: 3000}, 'Error: saveSceduleBtn did not appear before timeout')
        await expect(saveScheduleBtn).toBeClickable()
        await super.clickOn(saveScheduleBtn)
    }

    async clickDoneBtn() {
        let done = await this.goToStoreSelectionBtn;
        await done.waitForClickable({timeout: 5000}, 'Error: DoneBtn did not appear before timeout')
        await expect(done).toBeClickable()
        await super.clickOn(done)
    }

    async processWeekday(day) {
        let weekDay = await this.processWeekdaySelect
        await weekDay.waitForClickable({timeout: 5000}, 'Error: processWeekday dropdown did not appear before timeout')
        await expect(weekDay).toBeClickable()
        await super.clickOn(weekDay)
        await super.waitBrowserToLoadPage(2000)
        let weekDayList = await this.processDayOfWeekList
        for (let i = 0; i < weekDayList.length; i++) {
            let Text = weekDayList[i]
            if (await weekDayList[i].getText() === day) {
                await weekDayList[i].isDisplayed()
                await Text.click();
                break;
            }
        }
    }

    // pass only day of current month
    async datePicker(date) {
        let effectiveDate = await this.retailEffectiveDateSelect
        let dateCalender = await this.dateIcon
        await dateCalender.waitForExist({timeout: 2000}, 'Error: dateCalender did not appear before timeout')
        await super.clickOn(dateCalender)
        await super.waitBrowserToLoadPage(2000)
        await effectiveDate.waitForExist({timeout: 2000}, 'Error: effectiveDateInputField did not appear before timeout')
        await expect(effectiveDate).toBeClickable()
        await browser.execute(s => {
            s.value = null;
        }, effectiveDate);
        let monthDates = await this.calenderValuesList      
        for(let i=0;i<monthDates.length;i++){              
            let currentDate = await monthDates[i]
            if( await monthDates[i].getText() === date.toString()){
                await expect(currentDate).toBeClickable()
                await currentDate.click()
                break;
            }
        }
    }

    async getFiscalWeekList() {
        let fiscalWeekValue = await this.fiscalWeekSelect;
        await fiscalWeekValue.waitForExist({ timeout: 2000 }, 'Error: fiscalWeek dropdown did not appear before timeout');
        await expect(fiscalWeekValue).toBeDisplayedInViewport();
        await super.clickOn(fiscalWeekValue);
        await super.waitBrowserToLoadPage(2000);
        let text = await this.fiscalWeekDropdownList;
        let weekListPromises = [];
        for (let i = 0; i < text.length; i++) {
            weekListPromises.push(text[i].getText());
        }
        // Wait for all promises to resolve using Promise.all()
        let weekList = await Promise.all(weekListPromises);
        await super.waitBrowserToLoadPage(2000);
        await browser.keys('Escape');
        return weekList;
    }
    

    async clickFiscalWeekList() {
        let fiscalWeekValue = await this.fiscalWeekSelect;
        await fiscalWeekValue.waitForExist({timeout: 2000}, 'Eoor: fiscalWeek dropdown did not appear before timeout')
        await expect(fiscalWeekValue).toBeDisplayedInViewport()
        await super.clickOn(await fiscalWeekValue);
        await super.waitBrowserToLoadPage(2000)
        let saveScheduleBtn = await this.saveScheduleBtn;
        let text = await this.fiscalWeekDropdownList;
        let weekList = []
        for (let i = 0; text.length - 3; i++) {
           let list =  text[i]
            weekList.push(text[i].getText())
            await super.waitBrowserToLoadPage(1000)
            await list.click()
            //await super.clickOn(await saveScheduleBtn)
           // break;
        }
        await super.clickOn(saveScheduleBtn)
        await super.waitBrowserToLoadPage(3000)
        await browser.keys('Escape')
        return weekList
    }

    async getProcessDayOfWeek() {
        let tableValues = await this.table;
        await tableValues.waitForExist({timeout: 2000}, 'Eoor: tableValues did not appear before timeout')
        await expect(tableValues).toBeDisplayedInViewport()
        await super.waitBrowserToLoadPage(2000)
        let text = await this.processDayOfWeekList;
        let weekList = []
        for (let i = 0; i < text.length; i++) {
            weekList.push(text[i].getText())
        }
        await super.waitBrowserToLoadPage(3000)
        return weekList
    }
    // return week numbers from Scheduled List
    async getScheduledWeek() {
        let tableValues = await this.table;
        await tableValues.waitForExist({timeout: 2000}, 'Error: tableValues did not appear before timeout')
        await expect(tableValues).toBeDisplayedInViewport()
        await super.waitBrowserToLoadPage(2000)
        let text = await this.scheduledColumnList;
        let weekList = [];
        for (let i = 0; i < text.length; i++) {
            weekList.push(await text[i].getText())
        }
        await super.waitBrowserToLoadPage(3000)
        return weekList
    }

    async clickDeleteEventBtn() {
        let tableValues = await this.table
        await tableValues.waitForExist({timeout: 5000}, 'Error: tableValues did not appear before timeout')
        await expect(tableValues).toBeDisplayedInViewport()
        await super.waitBrowserToLoadPage(2000)
        await super.clickOn(await this.deleteBtn);
       //  let deleting = await this.yesDeleteBtn;
        // await deleting.waitForClickable({timeout: 5000}, 'Error: deleteBtn did not appear before timeout');
        // await expect(deleting).toBeClickable();
       // await super.clickOn(deleting);
        await super.waitBrowserToLoadPage(3000)
        for(let i=0;i<2;i++){
            await super.waitBrowserToLoadPage(2000)
            await eventMaintanencePage.clickElementWithText(await (eventMaintanencePage.okBtn),"OK")
        }
    }

    async clickCancelBtn() {
        let cancelling = await this.cancelBtn()
        await cancelling.waitForClickable({timeout: 2000}, 'Error: cancelBtn did not appear before timeout')
        await expect(cancelling).toBeClickable()
        await super.clickOn(cancelling)
    }

    //select dropdown by index
    async selectCheckbox(index,edit) {
        indexvalue =index;
        let checkboxElement
        if(await edit === "scheduleEdit"){
               checkboxElement = await this.checkboxInEdit
        }else{
               checkboxElement = await this.checkbox;
        }
        await checkboxElement.scrollIntoView({ behavior: 'smooth' });
        // Check if the checkbox is already selected
        const isSelected = await checkboxElement.getAttribute("class");
        if (!isSelected.includes("active")) {
            // If the checkbox is not selected, select it
            await super.waitBrowserToLoadPage(3000)
            await checkboxElement.click();
        } else {
            // If the checkbox was already selected, select the previous one
            await super.waitBrowserToLoadPage(3000)
            await checkboxElement.click();
            let previousIndex = index - 1;
            if (previousIndex >= 1) {                                                                                                                                                       
                // Define the selector for the previous checkbox
                let previousCheckboxElement = await this.previousIndex;
                await previousCheckboxElement.scrollIntoView({ behavior: 'smooth' });
                await previousCheckboxElement.click();
            }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           
        }
        return checkboxElement;
    }

    async selectFiscalWeekDropdown(Locator,edit) {
        await this.genericClickDropdown(Locator);
        await super.waitBrowserToLoadPage(2000);
        let text;
        if(await edit ==="scheduleEdit"){
        text = await this.fiscalWeekDropdownListInModal;
        }else {
        text = await this.fiscalWeekDropdownList;
        }
        const appList = [];
        for (let i = 0; i < text.length; i++) {
            appList.push(await text[i].getText());
        }
        const finalValue = appList[appList.length - 1];
        expect(text).toBeElementsArrayOfSize(appList.length);
        // Get the checkbox element for the last item
        await browser.pause(2000)
        await this.selectCheckbox(appList.length - 1,edit);
        return finalValue;
    }
    
    async validateNewTab(parentGUID) {
        let allGUID = await browser.getWindowHandles()
        for (let i = 0; i < allGUID.length; i++) {
            if (allGUID[i] !== parentGUID) {
                await browser.switchToWindow(allGUID[i])
                break;
            }
        }
    }

    async waitScheduledDay(list) {
        await browser.waitUntil(async () => await this.processDayOfWeek.getText() === list,
            {
                timeout: 9000,
                timeoutMsg: ' process day of week did not appear before timeout'
            })
    }

    async getArrayValueByIndex(arr, num){
        let arrayValue = [];
        for(let i = 0; i< await arr.length; i++){
            if( i + 1 === num ){
                arrayValue.push(arr[i])
                return arrayValue
            }
        }
        return -1;
    }

    async scheduling(schedule){
        let nextPage = await eventMaintanencePage.linkSubModule.getText();
        await expect(nextPage).toEqual(emData.Event_Schedule);
        await storeSelectionPage.saveSchedule.waitForClickable({ timeout: 59000 }, 'Error: Save Schedule button did not appear before timeout')
        
        if(schedule===undefined){
            await eventMaintanencePage.waitForPageLoad(this.saveScheduleBtn, emData.Save_Schedule);
            await this.selectDropdown('Fiscal Week')
            await super.clickOn(await storeSelectionPage.saveSchedule);
            await super.waitBrowserToLoadPage(2000)
                  
    }else{
        for (let index = 0; index <= schedule; index++) {
            await super.waitBrowserToLoadPage(3000);
            await eventMaintanencePage.waitForPageLoad(this.saveScheduleBtn, emData.Save_Schedule);
            await super.clickOn(await storeSelectionPage.saveSchedule);
        }
        }
    }

    async getHeaderValue(headerKey) { 
        let headerData = await this.tableHeaders;
        // let eventData = await this.rows;
        let headerValues = [];
        for (let j = 0; j < headerData.length; j++) {
            if ((await headerData[j].getText()) === headerKey) {
                no = j + 1;
                let valueElement = await this.tableValueList;
                await super.waitBrowserToLoadPage(2000);
                for (let i = 0; i < valueElement.length; i++) {
                    await super.waitBrowserToLoadPage(2000);
                    let headerValue = await valueElement[i].getText();
                    headerValues.push(headerValue);
                }
                break;
            }
        }
        return headerValues;
    }
    
    async getHeaderValuesForRow(headerKey) {
        let headerData = await this.activityTableHeaders;
        let headerValues = [];
        for (let j = 0; j < headerData.length; j++) {
            if ((await headerData[j].getText()) === headerKey) {
                no = j + 1;
                let valueElements = await this.activityTableValueList;
                await super.waitBrowserToLoadPage(2000);
                for (let i = 0; i < valueElements.length; i++) {
                    await super.waitBrowserToLoadPage(2000);
                    let headerValue = await valueElements[i].getText();
                    headerValues.push(headerValue);
                }
                break;
            }
        }
        return headerValues;
    }
    
}
module.exports = new EventSchedulingPage();
