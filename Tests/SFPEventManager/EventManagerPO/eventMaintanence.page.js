const Page = require('../../../GlobalObjectRepository/page');
let EMData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
let n = 0;
let r;
let columnIndex,rowIndex,headerIndex;
/**
 * sub page containing specific selectors and methods for Event Maintenance page
 */
class EventMaintanencePage extends Page {

    get selectEventTypeDropDown()       { return $('div>[name="selectedEvent"]>div>div>span') }
    get linkCreateSFPEvent()            { return $('#page > app-breadcrumb > div > ol > li:nth-child(3) > a') }
    get selectEventTypeDropDownList()   { return $$('label[class*="list-item flex items-center overlay-nc"]') }
    get linkSubModule()                 { return $('#page > app-breadcrumb > div > ol > li:nth-child(4) > a') }
    get tabHeaders()                    { return $$('div.collapse.navbar-collapse > ul>li >a') }
    get tableHeaderTitles()             { return $$('tr[class="ng-star-inserted"]>th>p') }
    get tableHeaders()                  { return $$('thead[class="p-datatable-thead"]>tr>th') }
    get table()                         { return $('[id^="pn_id_"][id$="-table"]') }
    get tableFirstRow()                 { return $('tbody[class="p-element p-datatable-tbody"]>tr:nth-child(1)') }
    get tableSecondPageBtn()            { return $('div > p-table > div > p-paginator > div > span button:nth-child(2)') }
    get tableValueList()                { return $(`tr[class="ng-star-inserted"]:nth-child(${r})>td:nth-child(${n})>b`) }
    get tableValueListOnlyValue()       { return $(`tr[class="ng-star-inserted"]:nth-child(${r})>td:nth-child(${n})>p`) }
    get headerIdList()                  { return $(`tr[class="ng-star-inserted"]:nth-child(${r})>td:nth-child(${n}) a`) }
    get headerList()                    { return $(`tr[class="ng-star-inserted"]:nth-child(${r})>td:nth-child(${n})`) }
    get addEventDropdown()              { return $(`mx-single-select[name='selectedEvent']> div > div> span`) }
    get addEventDropdownList()          { return $$('li[class="ers-list-item items overlay-nc"]') }
    get rowExpandIcons()                { return $$('tr[class="ng-star-inserted"]>td>i') }
    get firstRowExpandIcon()            { return $('tr[class="ng-star-inserted"]>td>i') }
    get tableRowList()                  { return $$('tr[class="ng-star-inserted"]') }
    get refreshEventListSpin()          { return $('button[aria-label="Refresh Event List"]>span:nth-child(1)') }
    get refreshEventBtn()               { return $('#refreshButton') }
    get filtersTxtBtn()                 { return $('kds-button[id="filterButton"] > button') }
    get filtersIconBtn()                { return $('button[class="kroger-btn icon-left favorable default"]>span>kds-icon-filters') }
    get filtersBtn()                    { return $('kds-button#filterButton') }
    get eventIdTitle()                  { return $('tr[class="ng-star-inserted"]>th:nth-child(2)') }
    get eventID()                       { return $$('tr[class="ng-star-inserted"] td:nth-child(2)') }
    get filtersList()                   { return $('button[class="kroger-btn icon-left favorable default"]>span') }
    get tableValue()                    { return $$('div#page tbody tr:nth-child(1) td') }
    get tableRows()                     { return $$('tbody[class="ui-table-tbody"]>tr') }
    get tablePlusArrowIcons()           { return $$('tbody[class="ui-table-tbody"]>tr>td>i') }
    get eventManagerDropdowns()         { return $$('div[class="ers-dropdown-wrapper"]>label+div>span') }
    get dropdownValueList()             { return $$('div[class="cdk-virtual-scroll-content-wrapper"]>ers-single-select-list-item>li>label') }
    get filtersDropdownSelect()         { return ('mx-single-select[name="value"]>div>kds-label+div') }
    get selectFiltersEventType()        { return $('mx-single-select[name="selectedEventType"]') }
    get selectCommodity()               { return $('mx-single-select[name="selectedCommodity"]>div>label+div>span') }
    get selectDivision()                { return $('mx-single-select[name="selectedDivision"]') }
    get selectPeriodWeek()              { return $('mx-single-select[name="selectedWeek"]>div>label+div>span') }
    get selectDepartment()              { return $('mx-single-select[name="selectedDepartment"]>div>label+div>span') }
    get selectSubDepartment()           { return $('mx-single-select[name="selectedSubDept"]>div>label+div>span') }
    get selectEventDesc()               { return $('mx-single-select[name="selectedEventType"]>div>label+div>span') }
    get eventTypeDropdownList()          { return $$('label[class*="list-item overlay-nc"]') }
    get commodityDropdwnList()          { return $$('li[class="ers-list-item items overlay-nc"]>label') }
    get divisionDropdwnList()           { return $$('#divisionSingleSelect > div > div> mx-single-select-list > ul > div > mx-single-select-list-item> div > li') }
    get periodWeekDropdwnList()         { return $$('li[class="ers-list-item items overlay-nc"]') }
    get departmentDropdwnList()         { return $$('li[class="ers-list-item items overlay-nc"]') }
    get subDepartmentDropdwnList()      { return $$('li[class="ers-list-item items overlay-nc"]') }
    get textEventDesc()                 { return $('input[placeholder="Event Desc"]') }
    get eventDescColumnRowValues()      { return $$('tr[class="ng-star-inserted"]>td:nth-child(4)') }
    get searchBtn()                     { return $('kds-button[id="searchButton"]>button') }
    get editIconList()                  { return $$('button[aria-label="Edit Event"]') }
    get deleteIconList()                { return $$('button[aria-label="Delete Event"]') }
    get releaseEventList()              { return $$('button[aria-label="Corporate Event"]') }
    get deleteTxt()                     { return $$('#mx-modal-id > div') }
    get okBtn()                         { return $$('#mx-Modal > mx-modal-footer > div > div.mx-modal-footer-container-right > kds-button:nth-child(2) > button') }
    get releasedTxt()                   { return $('div[class="ui-toast-summary"]') }
    get releasedPositiveList()          { return $$('button[class="kds-Button kds-Button--primaryPositive kds-Button--hasIconOnly"]') }
    get releasedNegativeList()          { return $$('button[class="kds-Button kds-Button--primaryNegative kds-Button--hasIconOnly"]>kds-icon-account') }
    get paginationLine()                { return $('p-paginator[styleclass="ui-paginator-bottom"]>div') }
    get pageNumberOne()                 { return $('span[class="ui-paginator-pages"]') }
    get pageNumbers()                   { return $$('span[class="ui-paginator-pages"]>a:nth-child("+i+")') }
    get forwardBackIconList()           { return $$('p-paginator[styleclass="ui-paginator-bottom"]>div>a') }
    // capture first row of 15 columns
    get firstColumnValues()             { return $$('tr[class="ng-star-inserted"]>td:nth-child(2)') }
    get chevDropdown()                  { return $('span[class="ui-dropdown-trigger-icon ui-clickable pi pi-chevron-down"]') }
    get pageNumberDropdownList()         { return $$('li[role="option"]>span') }
    get backButtonPagination()          { return $('p-paginator[styleclass="ui-paginator-bottom"]>div>a:nth-child(1)') }
    get nextButtonPagination()          { return $('p-table[sortfield="eventId"] p-paginator > div > button.p-ripple.p-element.p-paginator-next.p-paginator-element') }
    get spinner()                       { return $('label[class="kds-LoadingSpinner kds-LoadingSpinner--l kds-Text--l"]>progress') }
    get pogHeader()                     { return $$(`table[class='p-datatable-table ng-star-inserted'] thead tr>th`) }
    get pogRows()                       { return $(`div.ers-dialog-content> div div > div > table > tbody > tr > td:nth-child(${n})`) }
    get pogCancelButton()               { return $$('#mx-Modal > mx-modal-footer > div > div.mx-modal-footer-container-right > kds-button:nth-child(2) > button') }
    get updateModalAction()             { return $$('#mx-Modal > mx-modal-footer > div > div.mx-modal-footer-container-right > kds-button > button') }
    get rows()                          { return $$('div#page tbody tr td:nth-child(5)>span') }
    get pogValues()                    { return $$(`table[class='p-datatable-table ng-star-inserted'] > tbody >tr>td:nth-child(${columnIndex})`) }
    get scheduledStore()                { return $("div.gridboxes >span") }
    get closeScheduleStoreDialogBox()   { return $('a[role="button"]') }
    get corporateEvent()                { return  (`tr[class="ng-star-inserted"]:nth-child(${r})>td:nth-child(${n}) kds-button>button[aria-label="name Event"]`) }
    get eventReleaseTextAlert()         { return $('#kds-Portal-toast > div >kds-toast >section>p') }
    get loading()                       { return $('div > kds-loading-spinner > label > progress') }
    get pogFirstRowValue()              { return $('tbody>tr:nth-child(1)>td:nth-child(' + n + ')') }
    get scheduledStoreXBtn()            { return $('div[role="dialog"]>div>span+a') }
    get refreshBtn()                    { return $('div[class="ui-table ui-widget"]>div>div>div>ers-button:nth-child(1)>button>span:nth-child(2)') }
    get firstPlusBtn()                  { return $('table > tbody > tr:nth-child(1)>td>i') }
    get scheduledUndefinedStore()       { return $('div[role="region"]>div>div[class="twopanel ng-star-inserted"]>div:nth-child(2)>span') }
    get scheduleUndefinedDiv()          { return $('div[role="region"]>div>div[class="twopanel ng-star-inserted"]>div:nth-child(1)') }
    get upcNO()                         { return $('#pn_id_20-table > tbody > tr > td:nth-child(1)') }
    get popUpPogTitleTxt()              { return $('#content-overlay >div>div>div>div>div>div') }
    get popUpCancelBtn()                { return $('#content-overlay > div div > ers-button[ng-reflect-btn-display-type="cancel"]:nth-child(1) > button') }
    get pogTable()                      { return $('[id^="pn_id_"][id$="-table"] > tbody') }
    get popUpPogTable()                 { return $('table[class="p-datatable-table ng-star-inserted"]> tbody') }
    get schedulePopUpTable()            { return $('div[aria-labelledby="ui-dialog-1-label"]>div+div>p-fieldset>fieldset>div>div') }
    get resetScheduleTable()            { return $('tbody[class="ui-table-tbody"]') }
    get pogDBKeyRowValue()              { return $('div#page tbody>tr:nth-child(1)>td:nth-child(' + n + ')') }
    get pogDBKeyFirstRowValues()        { return $$('div#page tbody>tr:nth-child(1)>td') }
    get popUpScheduleDivValue()         { return $('div[role="region"]>div>div[class="twopanel ng-star-inserted"]>div:nth-child(1)') }
    get eventListFirstRowPog()          { return $('table>tbody>tr:nth-child(1)>td:nth-child(18)>b') }
    get eventListFirstRowSchedStr()     { return $('table>tbody>tr:nth-child(1)>td:nth-child(20)>b') }
    get popUpPogString()                { return $('mx-modal#planogramModal mx-modal-header div>span') }
    get selectedHeaders()               { return $$('table>thead>tr.ng-star-inserted>th') }
    get scheduledUndefinedValues()      { return $$('div[role="region"]>div>div[class="twopanel ng-star-inserted"]>div') }
    get pogDBKeyList()                  { return $$('div>table>tbody>tr>td:nth-child(7)') }
    get rowValuesWithinFrame()          { return $$('table > tbody > tr.ng-star-inserted.bluecoloredrow > td') }
    get tableTitles()                   { return $$('table>thead>tr>th>p') }
    get popUpFirstRowValues()           { return $$('#content-overlay tbody[class="ui-table-tbody"]>tr:nth-child(1)>td') }
    get popUpTableValues()              { return $$('#content-overlay tbody[class="ui-table-tbody"]>tr>td') }
    get popUpDbKeyValueList()           { return $$('div>table[class="p-datatable-table ng-star-inserted"] > tbody[class="p-element p-datatable-tbody"] > tr> td:nth-child(3)') }
    get popUpTableTitleValues()         { return $$('#content-overlay thead[class="ui-table-thead"]>tr:nth-child(1)>th') }
    get popUpScheduleStoreList()        { return $$('div[role="region"]>div>div[class="twopanel ng-star-inserted"]>div>span') }
    get popUpScheduleDivList()          { return $$('div[role="region"]>div>div[class="twopanel ng-star-inserted"]>div:nth-child(1)')}    
    get columnLink()                    { return $(`tr[class="ng-star-inserted"]:nth-child(1)>td:nth-child(${n})>a`) }
    get columnText()                    { return $(`tr[class="ng-star-inserted"]:nth-child(1)>td:nth-child(${n})`) }
    get plusColumnHeaders()             { return $$('tr:nth-child(2)>td>div>p-table>div>div>table>thead>th') }
    get plusNthColumnList()             { return $('table>tbody>tr:nth-child('+n+')>td:nth-child(1)') }
    get eventIDList()                   { return $$('table>tbody>tr>td:nth-child(2)') }
    get columnTextPlus()                { return $(`tr[class="ng-star-inserted"]:nth-child(1)>td:nth-child(${n})`) }
    get columnLinkPlus()                { return $(`tr[class="ng-star-inserted"]:nth-child(1)>td:nth-child(${n})>b`) }
    get eventCreationText()             { return $('[id^="kds-toast-"]>section>header>h1') }
    get plusStoreList()                 { return $$('#mx-modal-id > div > div > div > span') }
    get plusTable()                     { return $('table>tbody>tr>td>div>p-table>div>div>table') }
    get nthPlusColumnList()             { return $$(`table>tbody>tr>td:nth-child(${n})>b`) }
    get clickFirstPlus()                { return $('table > tbody > tr:nth-child(1) > td > i') }
    get clickableColumnList()           { return $('table>tbody>tr>td:nth-child(4)>b') }
    get columnValueList()               { return $$(`tr[class="ng-star-inserted"]>td:nth-child(${n})>b)`) }
    get plusStoreSecondRowValue()       { return $('table>tbody>tr:nth-child(2)>td:nth-child(4)>b') }
    get eventPlus()                     {return $(`table>tbody>tr:nth-child(${n})>td>i`)}
    get nthColumnList()                 { return $$(`td>div>p-table>div>div>table>tbody>tr>td:nth-child(${n})`) }
    get paginationPageThree()           { return $('#ui-fieldset-0-content>div>div>div>p-table>div>p-paginator>div>span>a:nth-child(3)') }
    get columnList()                    { return $$(`table>tbody>tr>td:nth-child(${n})`) }
    get dropdownList()                  { return $$('div>ers-single-select-list-item>li') }
    get filterDropDownList()            { return ('#name> div > div.mx-single-select-container.mx-dropdown-list.has-search > mx-single-select-list > ul > div > mx-single-select-list-item > div > li>label')}
    get eventIDColumn()                 { return $$('table > tbody > tr > td:nth-child(2)') }
    get eventDescInput()                { return $('#eventDescriptionText div mx-none-masked-input div kds-input>input') }
    get filtersDropdownText()           { return ('div[aria-label="value"]>span') }
    get clearFiltersDropdowns()         { return ('#value') }
    get getEventDescInputText()         { return $('div>div:nth-child(2)>div:nth-child(3)>ers-input-text-box>div>div')}
    get clearAllFiltersBtn()            { return $('#clearAllFiltersButton>button') }
    get pogModal()                      { return $(`table[class='p-datatable-table ng-star-inserted']`)}
    get pogModalNextButtonPagination()  { return $('[class="p-datatable-striped p-datatable p-component"] > p-paginator > div > button.p-ripple.p-element.p-paginator-next.p-paginator-element.p-link') }
    get eventIDColumnExpand()           { return $$('#ui-fieldset-12-content > div > div > div > p-table > div > div > table > tbody > tr:nth-child(2) > td > div > p-table > div > div > table > tbody > tr> td:nth-child(2)') }
    get tableHeadersExpand()            { return $$('table[class="p-datatable-table ng-star-inserted"]> thead > th') }
    get rowsExpand()                    { return $$('div > p-table > div > div > table > tbody > tr:nth-child(2) > td > div > p-table > div > div > table > tbody > tr > td.text-left') }
    get tableValueListExpand()          { return $(`tr[class="ng-star-inserted"]:nth-child(2)>td:nth-child(1) tr:nth-child(${rowIndex})>td:nth-child(${columnIndex})`)}
    get eventPlusExpand()                {return $(`table > tbody > tr:nth-child(${rowIndex}) > td > div > p-table > div > div > table > tbody > tr > td > i`)}
    get nthColumnListExpanded()          { return $$(`table > tbody > tr:nth-child(2) > td > div > p-table > div > div > table > tbody > tr> td > div > p-table > div > div > table > tbody > tr > td:nth-child(${columnIndex})`) }
    get plusColumnHeadersExpand()        { return $$('table[class="p-datatable-table ng-star-inserted"] > tbody > tr:nth-child(2) > td > div > p-table>div>div>table>thead[class="p-datatable-thead"]>th') }
    get popUpModal()                     { return $('p-dialog> div > div> div > span') }
    get storeReviewTableHeaders()        { return $$('p-table#storeReviewTable thead>tr>th') }
    get loadingSpinnerEventmaintenance() { return $('#page > content > app-eventmaintenance > kds-loading-spinner > label') }
    get slidePaneleventId()              { return $('#page > content > div > app-eventmaintenance > p-sidebar > div > div> app-event-information > div > div > div > div.row.header-row > div.float-left > div > h3') }
    get slidePanelDiscription()          { return $('div > app-event-information > div > div > div > div.row.header-row > div.float-left > p') }
    get slidePanelKeys()                 { return ('div.col-sm-value.font-bold')}
    get slidePanelValues()               { return ('div.col-sm-value.font-bold + div')}
    get slidePanelTabs()                 { return ('#Tab')}  
    get slidePanelDropdownHeader()       { return ('#scheduleTab div > table >thead>tr>th')}
    get slidePanelDropdownColumn()       { return ('#scheduleTab div > table > tbody>tr>td:nth-child(1)')} 
    get slidePanelCloseButton()          { return $('button[aria-label="undefined  "]')}
    get slideoutPanelEventId()           { return $('p-table tr:nth-of-type(1) td:nth-of-type(2) a')}
    get slideoutPanelCard()              { return $('#page > content > div > app-eventmaintenance > p-sidebar > div')}
    get createdByColumnText()            { return $('.col-sm-5:contains(Created By)')}
    get lastModifiedByUsername()         { return $('tbody tr:nth-of-type(1) td:nth-of-type(7)')}
    get slideoutPanelHeaderCloseButton() { return $('.kds-SlidingPanel-header kds-icon-close')}
    get slideoutPanelFiltersButton()     { return $('#filterButton button')}
    get modifiedByUsernameInputText()    { return $('#modifiedByText input')}
    get createdByUsernameText()          { return $('#createdByText input')}
    get schedulesExpandPanel()           { return $('#scheduleTab a')}
    get slideoutPanelRefreshEventBtn()   { return $('#refreshButton button')}
    get eventIDheaderText()              { return $('.event-id-header h3')}
    get statusColumnText()               { return $('#ui-fieldset-0-content th:nth-child(5)')}
    get lastModifiedByColumnText()       { return $('#ui-fieldset-0-content th:nth-child(7)')}
    get pogExpandPanel()                 { return $('#planogramTab a')}
    get storesExpandPanel()              { return $('#storeTab a')}
    get pogSearchBox()                   { return $('#planogramTab input[placeholder="Search"]')}
    get pogNameTable()                   { return $('#planogramTab p-table tr > td:nth-child(3)')}
    get storeNameTable()                 { return $('#storeTab p-table tr >td:nth-child(3)')}
    get storesSearchBox()                { return $('#storeTab input[placeholder="Search"]')}
    get slideOutClose()                  { return $('#page > content > div > app-eventmaintenance > p-sidebar > div > div> button>timesicon')}
    get slideOutExpand()                 { return $('table>tbody>tr>td>i[class="pi icon_pi ng-star-inserted pi-chevron-right"]')}
    get popUpScheduleDivCount()          { return $$('div[class="twopanel ng-star-inserted"]') }
    get popUpScheduleDivClose()          { return $('div a[role="button"]')} 
    get childEventID()                   { return $$('[id^=pn_id ][id$="table"]  tr >td:nth-child(1) a:nth-child(1)')}
    get secondChildPlusBtn()             { return $('table > tbody > tr:nth-child(2)>td>i[class="pi pi-plus ng-star-inserted"]')}  
    get secondChildMinusBtn()            { return $('table > tbody > tr:nth-child(2)>td>i[class="pi ng-star-inserted pi-minus"]')}
    get firstChildPlusBtn()              { return $('table > tbody > tr:nth-child(1)>td>i[class="pi icon_pi pi-plus ng-star-inserted"]')} 
    get firstChildMinusBtn()             { return $('table > tbody > tr:nth-child(1)>td>i[class="pi ng-star-inserted pi-minus"]')}
    get childStore()                     { return $('table > tbody > tr [class="ng-star-inserted"]> td:nth-child(5)>b')}
    get childStoreValueFromModal()       { return $('p-accordiontab[id="storeTab"] table > tbody > tr td:nth-child(3)')}
    get divStoreValue()                  { return $('div[class="griditems"] > span')}
    get modalStore()                     { return $('div[class="ng-star-inserted"] > p-accordion > div > p-accordiontab[id="storeTab"] a')}
    get childEventPopUpClose()           { return $('div[role="dialog"] > div > a')}
    get fiscalWeek()                     { return $$('#attention > kds-text > span > table > tbody > tr> td.text-left.pl-8')}
    get fiscalWeekValue()                { return $$('#attention > kds-text > span > table > tbody > tr> td.text-left.pl-8 + td:nth-child(2')}
    get emHomePageText()                 { return $('#page > app-breadcrumb > div > ol > li.ng-star-inserted > a')}
    get divisionDropDown()               { return $('#divisionSingleSelect > * span') }
    get storeDropDown()                  { return $('#selectedStoreDropdown > div > div > span')}
    get paginationLabel()                { return $('p-paginator > div > span.p-paginator-current.ng-star-inserted')}
    get columnByIndex()                  { return $$(`table > tbody > tr > td:nth-child(${n})`)}
    get addEventDropDownList()           { return $$('mx-single-select[name="selectedEvent"] > * mx-single-select-list-item >  div > li >label')}
    get storeDropDownList()               { return $$('#selectedStoreDropdown > * label > div')}
    get divisionDropdownEventManList()   { return $$('#divisionSingleSelect > * li > label') }
    get stripErrorToolTipTrigger()       { return $('#form-content-extra > div > div > div.form-group.content-evenly > div.inline-block.ng-star-inserted > kds-tooltippable > kds-icon-error-solid > kds-icon > svg') }
    get tooltipElement()                 { return $('.kds-Tooltip-content')}
    get stripErrorElement()              { return $('div > app-eventschedule > div:nth-child(2) > kds-message > kds-text > span > kds-text > span')}  
    get tooltipElementScheduling()       { return $('table > tbody > tr > td:nth-child(1) > div > div.p-element.inline-block.error-indicator.ng-star-inserted > kds-icon-error-solid > kds-icon > svg')}
    get tooltipElementEventID()          { return $('table > tbody > tr:nth-child(1) > td:nth-child(2) > div > div.p-element.inline-block.error-indicator.ng-star-inserted > kds-icon-error-solid > kds-icon > svg')}
    get slideOutTooltipText()            { return $('div[class="kds-Tooltip-content"]')}
    get slideOutTooltip()                { return $('#page > content > div > app-eventmaintenance > p-sidebar > div > div.p-sidebar-content> app-event-information > div > div > div > div:nth-child(2) > div:nth-child(1) > div:nth-child(4) > div.col-sm-6.content-evenly > div.inline-block.ng-star-inserted > kds-tooltippable > kds-icon-error-solid > kds-icon > svg')}
    get tooltipChildElementEventID()     { return $$('table > tbody > tr > td:nth-child(2) > div > kds-icon-error-solid > kds-icon > svg')}
    get slideOutInlineErrorText()        { return $('#page > content > div > app-eventmaintenance > p-sidebar > div > div.p-sidebar-content > app-event-information > div > div > kds-message > kds-text > span > div:nth-child(1) span')}
    get slideOutReleaseButton()          { return $('button[name="releaseBtn"]')}
    get releaseEventWarning()            { return $('#mx-modal-id > div > p')}
    get eventIdSortBtn()                  { return $('p-table[sortfield="eventId"] thead > tr > th.p-element.bold.p-sortable-column.p-highlight')}
    get emActivePaginationNum()           {return $('p-paginator > div > span.p-paginator-pages.ng-star-inserted > button.p-ripple.p-element.p-paginator-page.p-paginator-element.p-link.ng-star-inserted.p-highlight')}
    get emDropdownCloseBtn()              {return ('#?Dropdown>div>div>kds-icon-close>kds-icon>svg')}
    get emMainPageDropdown()              { return ('#?Dropdown>div>div>span')}
    get emMainPageDropdownList()          { return ('#?Dropdown>div>div>mx-single-select-list>ul>div>mx-single-select-list-item>div>li>label')}
    get emMainPageStatusesDropdownList()  { return $$('#selectedStatusDropdown>div>div>div>div>mx-multi-select-list > ul > div > mx-menu-item> li > div > div.w-full > kds-label > label')}
    get step4CommodityDropdownList()      { return $$('#commList > div > div:nth-child(3) > div > div > mx-multi-select-list > ul > div > mx-menu-item> li > div > div.w-full')}
    get step4CommodityDropdown()          { return $('#commList > div > div.mx-dropdown-input-wrapper.compact > span')}
    get storeSelectionAlert()             { return $('div[aria-label="NO STORES SELECTED FOR SCHEDULED WEEK"] div>span')}
    get scheduledStoreListString()        { return $('mx-modal-body[id="mx-modal-id"] fieldset>div>div>div>div+div')}
    get scheduledStoreModalList()        { return $$('div[data-pc-section="toggleablecontent"] > div > div > div.gridboxes > span')}
    get aisleRefreshEventAisleList()        { return $$('app-aisleoptions>div>div>div>div>kds-label>label')}
    get eventModelEventIdStringList()        { return $$('#mx-Modal > mx-modal-header > header > div > span')}


    async genericClickTable(TableLocator) {
        let values;
        switch (TableLocator) {
            case 'PogDBKey Table':
                values = await this.pogTable;
                await (values).waitForExist({ timeout: 60000 }, 'Error PogDBKey Table did not appear before timeout')
                break;
            case 'PopUp DBKey Table':
                values = await this.popUpPogTable;
                await (values).waitForExist({ timeout: 60000 }, 'Error: PopUp DBKey Table did not appear before timeout')
                break;
            case 'Popup Schedule Table':
                //schedulePopUpTable
                values = await this.scheduleUndefinedDiv;
                await (values).waitForExist({ timeout: 60000 }, 'Error: Schedule Popup Table did not appear before timeout')
                break;
                case 'Popup Upc Table':
                //UPCPopUpTable
                values = await this.upcNO;
                await (values).waitForExist({ timeout: 60000 }, 'Error: UPC Popup Table value did not appear before timeout')
                break;    
        }
        return values;
    }

    async genericColumnList(ListLocator) {
        let header;
        switch (ListLocator) {
            case 'PogDBKey List':
                header = await this.pogDBKeyList;
                break;
            case 'PopUp DBKey List':
                header = await this.popUpDbKeyValueList;
                break;
            case 'Popup Schedule List':
                header = await this.popUpScheduleStoreList;
                break;
            case 'Popup div List':
                header = await this.popUpScheduleDivList;
                break;
        }
        return header;
    }

    async genericStringLocator(locator) {
        let values;
        switch (locator) {
            case 'PogString':
                values = await this.popUpPogString;
                await (values).waitForExist({ timeout: 30000 }, 'Error PopUp DBKey Table did not appear before timeout')
                break;
            case 'Schedule Div':
                values = await this.scheduleUndefinedDiv;
                await (values).waitForExist({ timeout: 30000 }, 'Error: Schedule Popup Table did not appear before timeout')
                break;
            case 'Sched':
                values = await this.eventListFirstRowPog;
                await (values).waitForExist({ timeout: 30000 }, 'EventList Table did not appear before timeout')
                break;
            case 'SchedStore':
                values = await this.eventListFirstRowSchedStr;
                await (values).waitForExist({ timeout: 30000 }, 'EventList Table did not appear before timeout')
                break;
        }
        return values;
    }

    // Function to find the row index based on the value
async findRowIndexByValue(value) {
    const eventData = await this.rows;
    for (let i = 0; i < eventData.length; i++) {
        await browser.pause(2000)
        if ((await eventData[i].getText()).includes(value.slice(0,17))) {
            return i + 1;
        }
    }
    // Return -1 if not found
    return -1;
}

 // Function to find the header index based on the key
async findHeaderIndexByKey(key) {
    const headerData = await this.tableHeaders;
    for (let j = 0; j < headerData.length; j++) {
        if ((await headerData[j].getText()) === key) {
            return j + 1;
        }
    }
    // Return -1 if not found
    return -1;
}

 // Function to get the correct element based on the key
async getElementByKey(key) {
    if (["Refresh", "Type", "Tags"].includes(key)) {
        return await this.tableValueListOnlyValue;
    } else if (["Act", "2wk", "Strips", "Event Desc", "Sched", "Schedule Type", "Division"].includes(key)) {
        return await this.headerList;
    } else if (key === "Event Id") {
        return await this.headerIdList;
    } else {
        return await this.tableValueList;
    }
}

// Main function
async getHeaderValue(key, value) {
    // Find the row index based on the value
    r = await this.findRowIndexByValue(value);
    if (r === -1) {
        throw new Error(`Row with value "${value}" not found`);
    }
    // Find the header index based on the key
    n = await this.findHeaderIndexByKey(key);
    if (n === -1) {
        throw new Error(`Header with key "${key}" not found`);
    }
    // Get the appropriate element based on the key
    const valueElement = await this.getElementByKey(key);
    if (!valueElement) {
        throw new Error(`Element for key "${key}" not found`);
    }
    // Wait for the page to load
    await super.waitBrowserToLoadPage(2000);
    // Get the text of the element
    let rowValueText;
    try {
        rowValueText = await valueElement.getText();
    } catch (err) {
        throw new Error(`Failed to get text for key "${key}" at row ${rowIndex} and header ${headerIndex}`);
    }

    // Check if the value is clickable and click it
    if (await valueElement.isClickable()) {
        await super.waitBrowserToLoadPage(5000);
        await valueElement.click();
    }

    return rowValueText;
}

    async getHeaderValueExpand(key, value) {
        await super.waitBrowserToLoadPage(4000);
        let headerData = await this.tableHeadersExpand;
        let eventData = await this.rowsExpand;
        let flag;
         flag = false;
        for (let index = 0; index < eventData.length; index++) {
            if ((await eventData[index].getText()).includes(value)) {
                rowIndex = index + 1;
                break;
            }
        }
        if (rowIndex === -1) {
            console.error('Error: Row with the specified value not found.');
            return flag;
        }
        for (let column = 0; column < headerData.length; column++) {
            if ((await headerData[column].getText()).includes(key)) {
                columnIndex = column + 1;
                break;
            }
        }
        if (columnIndex === -1) {
            console.error('Error: Column with the specified key not found.');
            return flag;
        }
        let valueElement = await this.tableValueListExpand;
        await valueElement.waitForExist({ timeout: 60000 }, 'Error: table not expanded');
        await super.waitBrowserToLoadPage(2000);
        let rowValueText = await valueElement.getText();
        if (await valueElement.isClickable()) {
            await super.waitBrowserToLoadPage(2000);
            await super.clickOn(valueElement);
        } else {
            console.error('Error: value is not clickable!');
            return flag;
        }
        return rowValueText;
    }

    // This function checks for the headerName(key) and return the value as Text string
    async getHeaderTextOnly(key) {
        let headerData
        if(await key === "CAO Event ID"){
            headerData= await $$('thead[class="p-datatable-thead"]>th')
            await super.waitBrowserToLoadPage(4000)
            await expect(headerData.length.toString()).toEqual("15")
        }else{
            headerData = await this.tableHeaders;
            await super.waitBrowserToLoadPage(2000)
            await expect(headerData.length.toString()).toEqual("26")
        }
        let headerText
        for (let j = 0; j < headerData.length; j++) {
            if ((await headerData[j].getText()) === (key)) {
                headerText = await headerData[j].getText()
                break
            }
        }
        return headerText
    }

    async selectReleaseEvents(Locator, key, value) {
        let headerData = await this.tableHeaders
        let eventData = await this.rows
        let flag
        for (let i = 0; i < eventData.length; i++) {
            let eventText = await eventData[i].getText();
            if (eventText && eventText.includes(value)) {
                r = i + 1;
            }
        }
        for (let j = 0; j < headerData.length; j++) {
            let headerText = await headerData[j].getText();
            if (headerText && headerText.includes(key)) {
                n = j + 1;
                let eventValue = $((this.corporateEvent).replace('name', Locator))
                await super.waitBrowserToLoadPage(1000)
                if (await eventValue.isClickable() === true) {
                    await super.clickOn(eventValue)
                    flag = true
                    break;
                } else {
                    flag = false
                }
            }
        }
        return flag
    }

    async getPogHeaderValue(key) {
        await (await this.pogModal).waitForExist({timeout:5000})
        await super.waitBrowserToLoadPage(4000)
        let headerData = await this.pogHeader
        let columnArray = []
        for (let i = 0; i < headerData.length; i++) {
            if ((await headerData[i].getText()).includes(key)) {
                columnIndex = i + 1;
                let value = await this.pogValues
                for (let j = 0; j < value.length; j++) {
                    columnArray.push(await value[j].getText())
                }
                return columnArray
            }
        }
        return columnArray
    }

    // get all Pog Modal column values in EM page , all pagination
    async getPogHeaderValues(key) {
        await (await this.pogModal).waitForExist({timeout:5000})
        let columnValues = [];
        let nextButton = await this.pogModalNextButtonPagination
        let headerData = await this.pogHeader
        await super.waitBrowserToLoadPage(2000)
        for (let index = 0; index < headerData.length; index++) {
            if ((await headerData[index].getText()) === (key)) {
                columnIndex = index + 1;
                break
            }
        }
        while (nextButton.isEnabled) {
            (await this.pogModalNextButtonPagination).scrollIntoView()
            let nextBtnClassName = await (await this.pogModalNextButtonPagination).getAttribute("class")
            let value = await this.pogValues;
            for (let index = 0; index < value.length; index++) {
                columnValues.push(await value[index].getText())
            }
            await super.clickOn(nextButton)
            await super.waitBrowserToLoadPage(1000)
            // either of "disable" or "0" UI value exists the function
            if (nextBtnClassName && nextBtnClassName.includes("disabled") || value.length === 0) {
                break;
            }
        }
        return columnValues
    }

    async selectEventType(eventType) {
        await super.clickOn(this.selectEventTypeDropDown)
        await super.waitBrowserToLoadPage(2000)
        let dropDown = await this.selectEventTypeDropDownList
        for (let i = 0; i < dropDown.length; i++) {
            let eventList = await dropDown[i]
            if (await dropDown[i].getText() === eventType) {
                await dropDown[i].scrollIntoView()
                await eventList.click();
                break;
            }
        }
    }

    //Collecting data in Descending;
    async eventIdColumnList(pageNumber) {
        let tableValues = await this.table;
        await tableValues.waitForExist({ timeout: 2000 }, 'Error: Title did not appear before timeout')
        await expect(tableValues).toBeDisplayedInViewport();
        let nextButton = await this.nextButtonPagination
        let uiData = [];
        while (nextButton.isEnabled) {
            let nextButtonClassAttribute = await this.nextButtonPagination.getAttribute("class");
            let eventIDRowValues = await this.eventID;
            let lengthOfArray = eventIDRowValues.length;
            for (let index = 0; index < lengthOfArray; index++) {
                uiData.push(await eventIDRowValues[index].getText())
            }
            await nextButton.scrollIntoView()
            await super.clickOn(nextButton)
            await super.waitBrowserToLoadPage(3000)
                if(nextButtonClassAttribute.includes('p-highlight') || await this.emActivePaginationNum.getText() === pageNumber) {
                break;
            }
        }
        return uiData;
    }

    async eventRowExpand() {
        let addEventValue = await this.addEventDropdown;
        await addEventValue.waitForExist({ timeout: 1000 }, 'Error: Dropdown did not appear before timeout')
        await addEventValue.click();
        let addEventValueList = await this.addEventDropdownList;
        for (let i = 0; i < addEventValueList.length - 1; i++) {
            expect(await addEventValueList[i].getText()).toEqual(EMData.addEventDropDown[i])
        }
    }

    async editActionBtn() {
        let table = await this.table;
        await table.waitForExist({ timeout: 2000 }, 'Err: table values did not appear before timeout')
        let editBtn = await this.editIconList;
        let editList = []
        for (let i = 0; i < editBtn.length; i++) {
            editList.push(editBtn[i])
        }
        await super.clickOn(editList[1])
    }

    async deleteActionBtn(value) {
        let msg = await this.deleteTxt
        let table = await this.tableFirstRow;
        await table.waitForExist({ timeout: 5000 }, 'Err: table values did not appear before timeout')
        let deleteBtn = await this.deleteIconList;
        let deleteList = []
        for (let i = 0; i < deleteBtn.length; i++) {
            deleteList.push(deleteBtn[i])
        }
        await super.clickOn(deleteList[0])
        await this.clickElementWithText(msg,EMData.deleteText)
        await this.clickElementWithText(await (this.okBtn),value)
    }

    async  clickElementWithText(selector, text) {
        const elements = await (selector);
        for (const element of elements) {
            await browser.pause(3000)
            const elementText = await element.getText();
            if (await elementText.trim() === text) {
                if (await element.isClickable()) {
                    await element.click();
                }
                return elementText;
            }
        }
        return null;
    }

    async releaseActionBtn() {
        let table = await this.table;
        await table.waitForExist({ timeout: 2000 }, 'Err: table values did not appear before timeout')
        let releaseBtn = await this.releasedNegativeList;
        let releaseList = []
        for (let i = 0; i < releaseBtn.length; i++) {
            releaseList.push(releaseBtn[i])
        }
        await super.clickOn(releaseList[1])

    }

    async filtersDropdown(item, toBeSelectedValue) {
        // Define lookup objects for locators and dropdowns
        const locators = {
            'Event Type': this.selectFiltersEventType,
            'Commodity': this.selectCommodity,
            'Division': this.selectDivision,
            'Period Week': this.selectPeriodWeek,
            'Department': this.selectDepartment,
            'Sub Department': this.selectSubDepartment,
        };
        const dropdowns = {
            'Event Type': this.eventTypeDropdownList,
            'Commodity': this.commodityDropdwnList,
            'Division': this.divisionDropdwnList,
            'Period Week': this.periodWeekDropdwnList,
            'Department': this.departmentDropdwnList,
            'Sub Department': this.subDepartmentDropdwnList,
        };
        // Get the appropriate locator and dropdown from the lookup objects
        const locator = await locators[item];
        const dropdown = await dropdowns[item];
        // Check if locator and dropdown exist, then proceed
        if (!locator || !dropdown) {
            console.error(`Error: Invalid filter item: ${item}`);
            return;
        }
        // Click on the locator and wait for the page to load
        await super.clickOn(locator);
        await super.waitBrowserToLoadPage(3000);
        // Loop through the dropdown options and select the desired value
        for (let i = 0; i < await dropdown.length; i++) {
            const dropdownList = dropdown[i];
            if (await dropdownList.getText() === toBeSelectedValue) {
                await dropdownList.scrollIntoView();
                await dropdownList.click();
                break;
            }
        }
    }


    async search() {
        let searchBTN = await this.searchBtn;
        await searchBTN.waitForClickable({ timeout: 2000 }, 'Error: Search button did not appear before timeout')
        await expect(searchBTN).toBeClickable()
        await super.clickOn(searchBTN)
    }

    async clickFilters() {
        let filterBTN = await this.filtersTxtBtn;
        await filterBTN.waitForClickable({ timeout: 2000 }, 'Error: filters button did not appear before timeout')
        await expect(filterBTN).toBeClickable()
        await super.clickOn(filterBTN)
    }

    async clickFiltersWithDiscp(description) {
        let filterBTN = await this.filtersTxtBtn;
        await filterBTN.waitForClickable({ timeout: 20000 }, 'Error: filters button did not appear before timeout')
        await super.waitBrowserToLoadPage(2000);
        await expect(await filterBTN).toBeClickable()
        await super.clickOn(await filterBTN)
        await super.setValue(await this.eventDescInput, await description)
        await this.waitBrowserToLoadPage(2000);
        await super.clickOn(await this.searchBtn);
        await super.waitBrowserToLoadPage(2000);
    }

    async refreshEvent() {
        let refreshEventListBTN = await this.refreshEventBtn;
        await refreshEventListBTN.waitForClickable({ timeout: 2000 }, 'Error: refresh button did not appear before timeout')
        await expect(refreshEventListBTN).toBeClickable()
        await super.clickOn(refreshEventListBTN)
    }


    async enterEventDesc(description) {
        let descValue = await this.textEventDesc;
        await descValue.waitForExist({ timeout: 20000 }, 'Error: Description field did not appear before timeout')
        await descValue.setValue(description);
        expect(await descValue.getValue()).toEqual(description)
    }

    async eventDescColumn() {
        let descValue = await this.table;
        await descValue.waitForExist({ timeout: 20000 }, 'Error: Description field did not appear before timeout')
        let columnRows = await this.eventDescColumnRowValues
        let columnData = []
        for (let i = 0; i < columnRows.length; i++) {
            columnData.push(columnRows[i].getText())
        }
        return columnData;

    }

    async getEventId(value) {
        await super.waitBrowserToLoadPage(4000)
        let eventId = await this.eventID
        await super.waitBrowserToLoadPage(4000)
        let idValue;
        for (let i = 0; i < eventId.length; i++) {
            if (await eventId[i].getText() === value) {
                idValue = await eventId[i].getText()
                break;
            }
        }
        return idValue
    }

    // saves the POG DBKey values into an array from Selecting POG/Commodities
    async getColumnValues(tableLocator, listLocator) {
        await super.waitBrowserToLoadPage(2000)
        let table = await this.genericClickTable(tableLocator);
        await table.waitForExist({ timeout: 3000 }, 'Error: table did not appear before time out')
        //await expect(table).toBeDisplayed()
        let values = await this.genericColumnList(listLocator);
        let valueList = [];
        await super.waitBrowserToLoadPage(1000)
        for (let i = 0; i < values.length; i++) {
            await browser.pause(3000)
            valueList.push(await values[i].getText())
            await super.waitBrowserToLoadPage(2000)
        }
        return valueList
    }

    async waitTillTableValueNotNull(value) {
        await browser.waitUntil(async () => this.resetScheduleTable !== value,
            {
                timeout: 9000,
                timeoutMsg: ' Refresh Event List table did not appear before timeout'
            })
    }

    // extracting all digits from a string
    async extractingDigit(clickLocator, stringLocator, index) {
        let el = await this.genericStringLocator(clickLocator);
        await el.waitForExist({ timeout: 3000 }, 'Error: element did not appear before time out');
        await expect(el).toBeExisting();
        await super.clickOn(el);
        await super.waitBrowserToLoadPage(3000)
        let phrase = await this.genericStringLocator(stringLocator);
        let text = await phrase.getText();
        return await text.match(/(\d[\d]*)/g)[index];
    }

    // converts object to string array
    async convertObjectToStringArray(object) {
        //converting object into array and printing values
        let arrayData = Object.keys(object).map(function (key) {
            return object[key]
        });
        //takes  all the arrays into a single array.
        let mergedArray = await (arrayData.flat());
        // converts into string arr
        return mergedArray.join("', '");
    }

    async validateNewTab(parentGUID) {
        let allGUID = await browser.getWindowHandles()
        for (let i = 0; i < allGUID.length; i++) {
            if (allGUID[i] !== parentGUID) {
                await browser.switchToWindow(allGUID[i])
                break;
            }
        }
    }
    async getLinkText(key) {
        let table = await this.table;
        await table.waitForExist({ timeout: 50000 }, 'Error: UI table did not appear before time out')
        const headerData = await this.tableHeaders;
        await super.waitBrowserToLoadPage(2000)
        let linkText
        for (let index = 0; index < headerData.length; index++) {
            if ((await headerData[index].getText()) === (key)) {
                n = index + 1;
                await super.waitBrowserToLoadPage(4000)
                linkText = await (await this.columnText.getText());
                    const columnEl = await this.columnLink;
                    const columnEl2 = await this.columnLinkPlus;
                    if (await columnEl.isClickable()) {
                    await super.clickOn(await columnEl);
                    }

                    if (await columnEl2.isClickable()) {
                    await super.clickOn(await columnEl2);
                    }
                
                break
            }
        }
        return linkText
    }

    async getpdfLink(key) {
        await super.waitForTableExist(this.table)
        let headerData = await this.storeReviewTableHeaders;
        await super.waitBrowserToLoadPage(2000)
        let linkText
        for (let j = 0; j < headerData.length; j++) {
            if ((await headerData[j].getText()) === (key)) {
                n = j + 1;
                linkText = await this.columnText.getText()
                if (await this.columnText.isClickable()) {
                    await super.clickOn(await this.columnLink)
                }
                break
            }
        }
        return linkText
    }

    async getValuesOfClickedNum(header) {
        await this.plusTable.waitForExist({ timeout: 59000, timeoutMsg: 'Error: UI Table did not appear before timeout' });
        await expect(this.plusTable).toBeDisplayedInViewport();
        await super.waitBrowserToLoadPage(2000);
        const headerData = await this.plusColumnHeaders;
        // Loop through the headers and find the matching one
        for (let j = 0; j < headerData.length; j++) {
            if ((await headerData[j].getText()) === header) {
                await super.waitBrowserToLoadPage(1000);
                const n = j + 1;
                const valueElements = await $$(`td>div>p-table>div>div>table>tbody>tr>td:nth-child(${n})>b`);
                let storeValueList = [];
                // Loop through value elements and click if clickable
                for (let i = 0; i < valueElements.length; i++) {
                    const value = valueElements[i];
                    await super.waitBrowserToLoadPage(1000);
                    if (await value.isClickable()) {
                        await super.waitBrowserToLoadPage(1000);
                        await value.click();
                        await super.waitBrowserToLoadPage(1000);
                        const values = await this.plusStoreList;
                        const valueList = [];
                        // Collect all values from the plusStoreList
                        for (let k = 0; k < values.length; k++) {
                            await super.waitBrowserToLoadPage(1000);
                            const text = await values[k].getText();
                            if (text) {
                                valueList.push(text);
                            }
                            await super.waitBrowserToLoadPage(1000);
                        }
                        storeValueList.push(...valueList);
                        await this.clickCloseButton("Close");
                        await super.waitBrowserToLoadPage(1000);
                    }
                }
                // Return the collected store values list
                return storeValueList;
            }
        }
        // Return an empty list if the header wasn't found
        return `Error: Header "${header}" not found.`;
    }

    async getValuesOfClickedNumExpanded(header) {
        await super.waitBrowserToLoadPage(3000);
        let headerData = await this.plusColumnHeadersExpand;
        for (let index = 0; index < headerData.length; index++) {
            if ((await headerData[index].getText()).toString() === header) {
                let columnIndex = index + 1;
                await super.waitBrowserToLoadPage(3000);
                let value = await $$(
                    `table > tbody > tr:nth-child(2) > td > div > p-table > div > div > table > tbody > tr> td > div > p-table > div > div > table > tbody > tr > td:nth-child(${columnIndex})>b`
                );
                let storeValueList = [];
                for (let indexRow = 0; indexRow < value.length; indexRow++) {
                    if (await value[indexRow].isClickable()) {
                        await super.clickOn(await value[indexRow]);
                        let valueList = [];
                        await super.waitBrowserToLoadPage(3000);
                        let values = await this.plusStoreList;
                        for (let indexColumn = 0; indexColumn < values.length; indexColumn++) {
                            valueList.push(await values[indexColumn].getText());
                        }
                        storeValueList.push(valueList);
                        await this.clickCloseButton("Close");
                        await super.waitBrowserToLoadPage(2000);
                    }
                }
                return storeValueList;
            }
        }
        return `Error: Header "${header}" not found.`;
    }
    
    // this function checks the  header and returns its all vales in array
    async getHeaderValuesExpanded(header) {  
        await super.waitBrowserToLoadPage(4000)
        let headerData = await this.plusColumnHeadersExpand;
        let columnList = []
        for (let rowIndex = 0; rowIndex < headerData.length; rowIndex++) {
            if ((await (await headerData[rowIndex].getText()).toString()) === (header)) {
                columnIndex = rowIndex + 1;
                await super.waitBrowserToLoadPage(1000);
                let value = await this.nthColumnListExpanded;
                await super.waitBrowserToLoadPage(1000);
                for (let index = 0; index < value.length; index++) {
                    await super.waitBrowserToLoadPage(1000)
                    let rowValueText = await value[index].getText()
                    await super.waitBrowserToLoadPage(1000)
                    columnList.push(rowValueText)
                }
            }
        }
        return columnList;
    }

    async getPlusTableHeaderValue(header) {
        const table = await this.plusTable;
        await table.waitForExist({ timeout: 59000 }, 'Error: UI Table did not appear before timeout');
        await expect(table).toBeDisplayedInViewport();
        const headerData = await this.plusColumnHeaders;
        await super.waitBrowserToLoadPage(2000);
        for (let j = 0; j < headerData.length; j++) {
            const headerText = await headerData[j].getText();
            if (headerText === header) {
                await super.waitBrowserToLoadPage(1000);
                const value = await this.nthColumnList;
                for (let i = 0; i < value.length; i++) {
                    const isClickable = await value[i].isClickable();
                    if (isClickable) {
                        await super.clickOn(value[i]);
                    } else {
                        return await (await value[i]).getText();
                    }
                }
                return await value[value.length - 1].getText();
            }
        }
        return null;
    }


    async expandEvent(eventID) {
        let table = await this.table;
        await table.waitForExist({ timeout: 50000 }, 'Error: UI table did not appear before time out')
        let headerData = await this.eventIDColumn;
        await super.waitBrowserToLoadPage(2000)
        for (let j = 0; j < headerData.length; j++) {
            if ((await headerData[j].getText()) === (eventID)) {
                n = j + 1;
                if (await this.eventPlus.isClickable()) {
                    await super.waitBrowserToLoadPage(2000)
                    await super.clickOn(await this.eventPlus)
                }
                break;
            }
        }
    }

    async expandEventPlus(eventID) {
        let headerData = await this.eventIDColumn;
        await super.waitBrowserToLoadPage(5000)
        for (let index = 0; index < headerData.length; index++) {
            if ((await headerData[index].getText()) === (eventID)) {
                rowIndex = index + 1;
                if (await this.eventPlusExpand.isClickable()) {
                    await super.waitBrowserToLoadPage(3000)
                    await super.clickOn(await this.eventPlusExpand)
                }
                break;
            }
        }
    }

    async selectFiltersDropdown(item,value,dropDownName) {
        let locator =  $(this.filtersDropdownSelect.replace('value', item))
        await locator.waitForDisplayed({timeout: 59000}, 'Error: dropdown field did not appear before timeout')
        await super.waitBrowserToLoadPage(2000)
        await super.clickOn(locator)
        await super.waitBrowserToLoadPage(2000)
        let dropDown = await $$(this.filterDropDownList.replace('name',dropDownName));
        for (let i = 0; i < dropDown.length; i++) {
            let DropDownList = dropDown[i]
            if (await dropDown[i].getText() === value) {
                await dropDown[i].scrollIntoView()
                await DropDownList.click();
                break;
            }
        }
        await browser.keys('Escape');
        !expect(super.doIsDisplay(this.filterDropDownList))
    }

    async getColumnList(header){
        let table = await this.table;
        await table.waitForExist({ timeout: 50000 }, 'Error: UI table did not appear before time out')
        let headerData = await this.tableHeaders;
        await super.waitBrowserToLoadPage(2000)
        let arrayList = await super.getArrayElementsText(await headerData);
        let headerIndex = arrayList.indexOf(header);
        n = headerIndex + 1;
        let columnValues = await this.columnList;
        let columnTextList = await super.getArrayElementsText(await columnValues);
        return columnTextList;
    }

    async getFiltersDropdownText(Locator) {
        let eventValue = $ ((this.filtersDropdownText).replace('value', Locator));
        await browser.pause(2000)
        return await eventValue.getText();
    }

    async clickClearFiltersDropdown(Locator) {
        let eventValue = $ ((this.clearFiltersDropdowns).replace('value', Locator));
        await browser.pause(2000)
        await super.clickOn(await eventValue );
    }

      
    async getValuesByKeysSlidePanel(value,keysToFind) {
        const keys = await $$(this.slidePanelKeys.replace("value", value));
        const values = await $$(this.slidePanelValues.replace("value", value));
        const keyValuePairs = {};
        for (let i = 0; i < keys.length; i++) {
            let keyText = await keys[i].getText();
            
            let valueText = await values[i].getText();
            keyValuePairs[keyText] = valueText;
        }
        let result = '';
        keysToFind.forEach((keyToFind) => {
            const key = Object.keys(keyValuePairs).find(
                (key) => key.trim() === keyToFind.trim()
            );
            const value = keyValuePairs[key] || null;
            result += `${value}\n`;
        });
        return result.trim();
    }

    async slidePanelTabsClick(Locator) {
        let eventValue = await $ ((this.slidePanelTabs).replace('Tab', Locator));
        await browser.pause(2000)
        await super.clickOn(eventValue);
    }


    async  getColumnValuesByHeaderNameSlidePanel(tabLocator, headerName) {
        const headerLocator = `#${tabLocator} div > table > thead > tr > th`;
        const columnLocator = `#${tabLocator} div > table > tbody > tr > td:nth-child(${n})`;
        await browser.pause(3000)
        const headers = await $$(headerLocator).map(async (header) => await (await header.getText()));
        const headerIndex =  await headers.findIndex((header) => header === headerName);
        if (headerIndex === -1) {
          console.error(`Header '${headerName}' not found.`);
          return null;
        }
        const columnLocatorWithIndex = columnLocator.replace(n, `${headerIndex + 1}`);
        const columnValues = await $$(columnLocatorWithIndex).map(async (column) => await column.getText());
        return columnValues.filter(Boolean);
      }

    async enterUsername(testUserNameValue){
        let userNameValue = await this.modifiedByUsernameInputText;
        await userNameValue.waitForExist({timeout: 9000}, 'Error: User name did not appear before timeout')
        await userNameValue.setValue(testUserNameValue);
       }
       

      async getFiscalWeekAndValue(keyText) {
        const week = await this.fiscalWeek;
        const values = await this.fiscalWeekValue;
        let result;
        for (let index = 0; index < week.length; index++) {
            const currentKey = (await week[index].getText()).trim();
            if (currentKey === keyText) {
                result = await values[index].getText();
                break;
            }
        }
        return result;
    }
    
    async clickCloseButton(text) {
        const buttons = await this.updateModalAction;
        await browser.pause(3000)
        for (let i = 0; i < buttons.length; i++) {
            await browser.pause(1000)
            const buttonText = await buttons[i].getText();
            if (await buttonText === await text) {
                await buttons[i].click();
                break;
            }
        }
    }

    // Note: First parameter is not used in the function; re-assess this function
    async getEventManagerTableHeaderIndex(eventManagerTableName, columnName) {
        let columnIdx = -1;
        let headers = await this.tableHeaders;
        for (let index = 0; index < headers.length - 1; index++) {
            let str = await this.GetText(headers[index]);
            if(str.toString() === columnName)
            {
                columnIdx = index;
                break;
            }
        }
        return columnIdx;
    }

    async tableColumnByIndex(columnIndex){
        n = await columnIndex;
        return this.columnByIndex;
    }

    async eventManagerDivisionDropDownMultiDivisionMatch(userRoleDivisions){

        let isMatch = true;
        await this.clickOn(await this.divisionDropDown);
        let divisionList = await this.divisionDropdownEventManList;
        await expect(divisionList.length).toEqual(await userRoleDivisions.length);
         for(let index = 0; index < divisionList.length; index++){
            if(userRoleDivisions[index] !== await this.GetText(await divisionList[index])){
                isMatch = false;
                break;
            }
        }
        return isMatch;
    }

    async clickEmMainPageDropdownCloseBtn(locatorName) {
        let closeOption = $(this.emDropdownCloseBtn.replace('?Dropdown', locatorName));
        await super.waitBrowserToLoadPage(2000)
        await super.clickOn(closeOption);
        await super.waitBrowserToLoadPage(2000)
    }

    async selectDropDownValuesByIndex(dropdownLocator, startIndex, endingIndex) {
        await super.waitBrowserToLoadPage(2000)
        let dropdownToBeClicked = await $((this.emMainPageDropdown).replace('?Dropdown', dropdownLocator));
        await super.clickOn(await dropdownToBeClicked);
        let dropdown;
        if (dropdownLocator === 'selectedStatusDropdown') {
            dropdown = await this.emMainPageStatusesDropdownList;
        }
         else {
            dropdown = await $$((this.emMainPageDropdownList).replace('?Dropdown', dropdownLocator));
         }
        if (await dropdown.length >= await endingIndex) {
            for (let index = startIndex; index <= endingIndex-1; index++) {
                await dropdown[index].click();
            }
        } else {
            console.error("Dropdown does not have enough options")
        }
        await browser.keys('Escape');
    }

    async getTextOfSelectedDropdown(dropdownLocator) {
        await super.waitBrowserToLoadPage(2000)
        let dropdownText = await $((this.emMainPageDropdown).replace('?Dropdown', dropdownLocator));
        return dropdownText.getText();
    }

    // This function is used when creating events for resetCompletion page
    async selectCommodityDropDownValuesByIndex(startIndex, endingIndex) {
        await super.waitBrowserToLoadPage(2000)
        let dropdownToBeClicked = await this.step4CommodityDropdown;
        await super.clickOn(await dropdownToBeClicked);
        let dropdown = await this.step4CommodityDropdownList;
        if (dropdown.length >= await endingIndex) {
            for (let index = startIndex; index <= endingIndex-1; index++) {
                await dropdown[index].click();
            }
        } else {
            console.error("Dropdown does not have enough options")
        }
        await browser.keys('Escape');
    }

    // here
    async clickRandomCheckboxEventRefresh() {

        const checkboxes = await this.aisleRefreshEventAisleList;
        // Ensure there are checkboxes present
        if (await (checkboxes.length) === 0) {
            throw new Error("No checkboxes found.");
        }
        // Generate a random index to select a checkbox
        const randomIndex = Math.floor(Math.random() * checkboxes.length);
        // Get the checkbox element at the random index
        const checkbox = checkboxes[randomIndex];
        const associatedText = await checkbox.getText();
        await checkbox.click();
        return associatedText;
    }

    async getModalEventIdText() {

        const elements = await this.eventModelEventIdStringList;
        // Loop through the elements to find the one containing "Event Id"
        for (let element of elements) {
            // Get the text content of the current element
            const text = await element.getText();
            // Check if the text includes "Event Id"
            if (await text.includes('Event Id')) {
                return text; // Return the full text if "Event Id" is found
            }
        }
        // If no element contains "Event Id"
        throw new Error("No element with 'Event Id' found.");
    }

}

module.exports = new EventMaintanencePage();
