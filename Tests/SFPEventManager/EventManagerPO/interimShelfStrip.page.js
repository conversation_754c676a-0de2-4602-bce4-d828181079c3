const Page = require('../../../GlobalObjectRepository/page');
const { datePicker } = require('./eventScheduling.page');

let colIndex, rowIndex;
class InterimShelfStripPage extends Page{

    get applicationMenuButton()                 { return $('li>a[id="menu"]') }
    get sfpEventManagerOption()                 { return $('ul > li> ul > li:nth-child(2) > a[href="/EventMaintenance"]') }
    get sfpEventManagerOptionAdmin()            { return $('ul > li.nav-item.dropdown.multi-level-dropdown.open > ul > li:nth-child(2) > ul > li.dropdown-item.dropdown-submenu> a') }
    get sfpEventManagerOptionAdminOptions()     { return $$('ul > li.nav-item.dropdown.multi-level-dropdown.open > ul > li:nth-child(2) > ul > li.dropdown-item.dropdown-submenu> a+ul>li') }
    get requestShelfStripButton()               { return $('kds-button[id="request-button-primary"]>button') }
    get shelfStripRequestMessage()              { return $('div[data-pc-section="header"] > span') }
    get pogDbekyInputBox()                      { return $('mx-input-text-box[id="pogDbKeyInput"]  kds-input > input') }
    get cancelSubmitButtonList()                { return $$('div[data-pc-section="footer"]>div>kds-button>button') }
    get submitButton()                          { return $('div[data-pc-section="footer"]>div>kds-button:nth-child(2)>button') }
    get cancelButton()                          { return $('div[data-pc-section="footer"]>div>kds-button:nth-child(1)>button') }
    get emailCheckBox()                         { return $('kds-label > label > kds-checkbox > input') }
    get addEmailButton()                        { return $('div[id="additionalEmailIds"] +div>kds-button>button') }
    get removeEmailButton()                     { return $('div[id="additionalEmailIds"]  kds-button > button') }
    get emailInputBox()                         { return $('#groupNameInput-0\} > div > mx-none-masked-input > div > kds-input > input') }
    get pogDbKeyRequiredMessage()               { return $('mx-input-text-box[id="pogDbKeyInput"] > div > div') } // This is required
    get closeSlideOutPannelButton()             { return $('timesicon[data-pc-section="closeicon"]> svg') }
    get cancelSubmitButtonList()                { return ('tr[id="filter-row"]  p-columnfilter[field="@columnName"]>div>p-columnfilterformelement>input') }
    get shelfStripRequestTableHeaderList()      { return $$('table>thead>tr:nth-child(1)>th') }
    get requestStatusDropdownList()             { return $$('div[id="kds-Portal-toast"]+div> div > div > div > ul > p-multiselectitem> li') }
    get requestStatusColumnDropdown()           { return $('tr[id="filter-row"]  p-columnfilter[field="requestStatus"]>div>p-columnfilterformelement') }
    get cancelColumnFilterButton()              { return $('#filter-row > th> p-columnfilter[field="@columnName"] > div > button') }
    get tableFirstRow()                         { return $('tbody[class="p-element p-datatable-tbody"]>tr:nth-child(1)') }
    get shelfStripRequestsText()                { return $('shelfStripRequestsText')}
    get stripRequestSubmittedMessage()          { return $('kds-toast> section > header > h1')}
    get invalidPogDbKeyWarningMessage()         { return $('mx-input-text-box[id="pogDbKeyInput"]>div>div')}


    async clickButtonByLabel2(locatorList, text) {
        const buttonList = await locatorList; 
    for (let index = 0; index < buttonList.length; index++) {
        const button = await buttonList[index]; 
        const buttonElement = await $(button);  
        const buttonText = await buttonElement.getText();
        if (buttonText.trim() === text) {
            await buttonElement.click();
            break;
            }
        }
    }

    async clickCreateNewRequest(){
        await (await this.requestShelfStripButton).waitForClickable({timeout: 3000})
        await this.clickOn(await this.requestShelfStripButton);
        await this.waitForPageLoad(await this.shelfStripRequestMessage, 'New Interim Shelf Strip Request');
    }

    async enterSearchInput(inputValue) {
        
        let searchInput = await this.pogDbekyInputBox;
        await searchInput.waitForExist({timeout: 20000}, 'Error: Pog DBKey field did not appear before timeout !')
        await this.clickOn(await searchInput);
        await searchInput.setValue(await inputValue);
        await super.waitBrowserToLoadPage(1000)
        await expect(await searchInput.getValue()).toEqual(inputValue);
        await browser.keys("Enter");
    }


    async clearInputAndValidate() {
        // Clear the input field
        await (await this.pogDbekyInputBox).clearValue();
        // Get the value of the input field
        const inputValue = await this.pogDbekyInputBox.getValue();
        // Check if the value is cleared (should be empty)
        if (inputValue === '') {
            return true; 
        } else {
            return false;
        }
    }

    async selectShelfStripRequestStatus(dropdownValueToBeSelected) {
        await super.waitBrowserToLoadPage(2000)
        let dropdownToBeClicked = await this.requestStatusColumnDropdown;
        await super.clickOn(await dropdownToBeClicked);
        await super.waitBrowserToLoadPage(2000)
        let dropdownList = await this.requestStatusDropdownList;
        for(let index=0; index<dropdownList.length;index++){
            if( await dropdownList[index].getText() === await dropdownValueToBeSelected){
                await dropdownList[index].scrollIntoView();
                await super.waitBrowserToLoadPage(2000)
                await (await dropdownList[index]).click();
                break;
            }
        }
        await browser.keys('Escape');
    }

    async shelfStripDatePicker(date) {
        let effectiveDate = await this.retailEffectiveDateSelect
        let dateCalender = await this.dateIcon
        await dateCalender.waitForExist({timeout: 2000}, 'Error: dateCalender did not appear before timeout')
        await super.clickOn(dateCalender)
        await super.waitBrowserToLoadPage(2000)
        await effectiveDate.waitForExist({timeout: 2000}, 'Error: effectiveDateInputField did not appear before timeout')
        await expect(effectiveDate).toBeClickable()
        await browser.execute(s => {
            s.value = null;
        }, effectiveDate);
        let monthDates = await this.calenderValuesList      
        for(let i=0;i<monthDates.length;i++){              
            let currentDate = await monthDates[i]
            if( await monthDates[i].getText() === date.toString()){
                await expect(currentDate).toBeClickable()
                await currentDate.click()
                break;
            }
        }
    }

    async getAdminDropdownSubMenue() {
        // Click on the model to reveal the values
        const model = await this.applicationMenuButton;
        await model.waitForExist({timeout: 2000}, 'Error: SFP Event Manager option did not appear before timeout');
        await model.click();
        await super.waitBrowserToLoadPage(1000);
        // Hover over "Manager" option
        const managerOption = await this.sfpEventManagerOption;
        await managerOption.moveTo();
        // Hover over "Admin" option
        const adminOption = await this.sfpEventManagerOptionAdmin;
        await adminOption.moveTo();
        // Wait for the 6 additional options to appear
        const adminValues = await this.sfpEventManagerOptionAdminOptions; 
        const adminValueTexts = await Promise.all(adminValues.map(async (adminValue) => {
            return await adminValue.getText();
        }));
        return adminValueTexts; 
    }



}

module.exports = new InterimShelfStripPage();