const Page = require('../../../GlobalObjectRepository/page')
const storeSelectionPage = require("./storeSelection.page");
let n;
class UplOrderPage extends Page {

    get dataTable()                                         { return $('div >p-table >* table > tbody')}
    get linkSubModule()                                     { return $('#page > app-breadcrumb > div > ol > li:nth-child(3) > a')}
    get selectLocationDropdown()                            { return $('mx-single-select[name="selectedLocation"] > * span')}
    get selectDepartmentDropdown()                          { return $('mx-single-select[name="selectedDepartment"] > * span')}
    get selectCategoryDropdown()                            { return $('div.col-md-9 > div > div:nth-child(3) > * button')}
    get selectProductGroupDropdown()                        { return $('div:nth-child(3)>div:nth-child(1)>div>button')}
    get selectATTNToInput()                                 { return $('div:nth-child(3)>div:nth-child(2)>input')}
    get selectSendDate()                                    { return $('p-calendar > span > input')}
    get selectDateRequest()                                 { return $('input[name="dateRequest"]')}
    get selectClearCurrentRequestBtn()                      { return $('button[id="clrbtn"]')}
    get getUpdateRequestTable()                             { return $('button[id="updtbtn"]')}
    get getMailLocationInput()                              { return $('div[class="row flex"]>div>div:nth-child(2)>input')}
    get getMailStopInput()                                  { return $('div[class="row flex"]>div>div:nth-child(3)>input')}
    get getRoutingInitialsInput()                           { return $('div[class="row flex"]>div>div:nth-child(4)>input')}
    get orderByDepartmentInput()                            { return $('#dept > div.row.flex.orderByDepartment > input')}
    get getSetupAllPOGS()                                   { return $('div > #setuppogs')}
    get getRemoveSelectedRecordsBtn()                       { return $('content > * div:nth-child(4) > * button')}
    get selectProcessUPLRequest()                           { return $('#page > * div:nth-child(4) > * kds-button.ml-8.hydrated > button')}
    get selectPrceedBtn()                                   { return $('ers-button[ng-reflect-label="Proceed"]')}
    get selectCancelBtn()                                   { return $('ers-button[ng-reflect-label="Cancel"]')}
    get closePopUpBtn()                                     { return $('ers-button[btndisplaytype="primaryInverse"]')}
    get deleteTxt()                                         { return $('div[class="header-title"]>div')}
    get proceedBtn()                                        { return $('button[aria-label="Process, UPL Request"]')}
    get proceedOkBtn()                                      { return $('mx-modal-footer > * kds-button:nth-child(2) > button')}
    get selectCategoryNameTable()                           { return $('p-table[id="categoryDropdownTable"] div[class="ui-table-scrollable-body"]')}
    get selectLocColumnTitle()                              { return $('p-table[id="dataTable"] thead[class="ui-table-thead"]>tr>th:nth-child(2)')}
    get selectCategoryNameTableFirstRadioBtn()              { return $('p-table[id="categoryDropdownTable"] tbody[class="ui-table-tbody"]>tr:nth-child(1)>td:nth-child(6)>p-radiobutton>div')}
    get selectProductGroupTableFirstRadioBtn()              { return $('p-table[id="productDropdownTable"] tbody[class="ui-table-tbody"]>tr:nth-child(1)>td:nth-child(6)>p-radiobutton>div>')}
    get selectCategoryNameTableNthRadioBoxList()            { return $('p-table[id="categoryDropdownTable"]> * tbody > tr:nth-child('+n+') > td:nth-child(6) > p-radiobutton > div > div.p-radiobutton-box')}
    get selectProductGroupTableNthRadioBoxList()            { return $('p-table[id="productDropdownTable"] tbody[class="ui-table-tbody"]>tr:nth-child('+n+')>td:nth-child(6)>p-radiobutton>div')}
    get selectCategoryNameTablePOGsList()                   { return $$('p-table[id="categoryDropdownTable"]> * tbody > tr > td:nth-child(1)')}
    get selectProductGroupTablePGList()                     { return $$('p-table[id="productDropdownTable"] tbody[class="ui-table-tbody"]>tr>td:nth-child(1)')}
    get dataTableLocNumColumnList()                         { return $$('p-table[id="dataTable"] tbody[class="ui-table-tbody"]>tr>td:nth-child(2)')}
    get dataTableDepartNumColumnList()                      { return $$('p-table[id="dataTable"] tbody[class="ui-table-tbody"]>tr>td:nth-child(3)')}
    get selectGeneralDropdownList()                         { return $$('p-dropdownitem>li>span')}
    get dataTableCheckBoxList()                             { return $('table > * p-tableheadercheckbox > div > div.p-checkbox-box')}
    get paginationDropdownList()                            { return $$('p-dropdownitem>li')}
    get dataTableFirstCheckBox()                            { return $('* tr:nth-child(1) > * div.p-checkbox-box.p-component')}
    get firstButtonPagination()                             { return $('p-paginator[styleclass="ui-paginator-bottom"]>div>a:nth-child(1)')}
    get nextButtonPagination()                              { return $('p-paginator > * button.p-ripple.p-element.p-paginator-next')}
    get lastButtonPagination()                              { return $('p-paginator[styleclass="ui-paginator-bottom"]>div>a:nth-child(5)')}
    get paginationDropdownBtn()                             { return $('p-paginator>div>p-dropdown>div>label')}
    get eventCreationText()                                 { return $('section')}
    get headerRow()                                         { return $$('#dataTable > * thead > tr > th') }
    get headerColoumnValues()                               { return $$(`#dataTable > div > div > div > div.ui-table-scrollable-body > table > tbody > tr > td:nth-child(${n})`) }
    get mappingReportTableValue()                           { return $('#dataTable > div > div > div > div.ui-table-scrollable-body > table > tbody') }
    get valueNColumns()                                     { return $$(`#dataTable > * tbody > * td:nth-child(${n})`)}
    get btnPageOne()                                        { return $('p-paginator[styleclass="p-paginator-bottom"] > div > span > button')}
    get btnSetupAllPogsForDep()                             { return $('button#setuppogs')}
    get alertModal()                                        { return $('#kds-Portal-toast>div>kds-toast section[role="alert"]>p')}
    get selectLocationDropdownList()                        { return $$('mx-single-select[name="selectedLocation"] >* mx-single-select-list-item > div')}
    get selectDepartmentDropdownList()                      { return $$('mx-single-select[name="selectedDepartment"] >* mx-single-select-list-item > div')}
    get dataTableFirstPageRows()                            { return $$('div >p-table >* table > tbody > tr')}
    get paginationDownArrow()                               { return $('div.p-dropdown-trigger > chevrondownicon > svg')}
    get sendDateFilled()                                    { return $('p-calendar.p-inputwrapper-filled')}
    get tableHeaderCheckboxChecked()                        { return $$(`tbody > tr > td:nth-child(1) > p-tablecheckbox > div > div.p-checkbox-box.p-component[aria-checked="${n}"]`)}
    get globalAlertHeaderStrip()                            { return $('#kr-header > kds-global-message > p > kds-paragraph')}

    async getDropdownText(item) {
        let locator;
        switch (item) {
            case 'Location':
                locator = await this.selectLocationDropdown;
                break;
            case 'Department':
                locator = await this.selectDepartmentDropdown;
                break;
            case 'CategoryName':
                locator = await this.selectCategoryDropdown;
                break;
            case 'ProductGroup':
                locator = await this.selectProductGroupDropdown;
                break;
        }
        await (locator).waitForExist({timeout: 50000}, 'Error: dropdown did not appear before timeout')
        return await locator.getText()
    }

    async genericClickDropdown(locator) {
        let item;
        switch (locator) {
            case 'Location':
                item = await this.selectLocationDropdown;
                break;
            case 'Department':
                item = await this.selectDepartmentDropdown;
                break;
            case 'CategoryName':
                item = await this.selectCategoryDropdown;
                break;
            case 'ProductGroup':
                item = await this.selectProductGroupDropdown;
                break;
            case 'CategoryNameRadioBox':
                item = await this.selectCategoryNameTableFirstRadioBtn;
                break;
            case 'ProductGroupRadioBox':
                item = await this.selectProductGroupTableFirstRadioBtn;
                break;
            case 'LocNum':
                item = await this.selectLocColumnTitle;
                break;
        }
        if (await item.isClickable()) {
            await super.waitBrowserToLoadPage(3000)
            super.clickOn(item);
            await super.waitBrowserToLoadPage(3000)
        }
    }

    async genericClickDropdownList(items) {
        let values;
        switch (items) {
            case 'Locations':
                values = await this.selectLocationDropdownList;
                break;
            case 'Departments':
                values = await this.selectDepartmentDropdownList;
                break;
            case 'CategoryNames':
                values = await this.selectCategoryNameTableNthRadioBoxList;
                break;
            case 'ProductGroups':
                values = await this.selectProductGroupTableNthRadioBoxList;
                break;
            case 'CategoryNamesPOGs':
                values = await this.selectCategoryNameTablePOGsList;
                break;
            case 'ProductGroupsPGs':
                values = await this.selectProductGroupTablePGList;
                break;
            case 'LocNumbers':
                values = await this.dataTableLocNumColumnList;
                break;
        }
        return values;
    }

    // select with dropdown by Text
    async selectSingleValue(Locator, dropdownList, dropdownValue) {
        await this.genericClickDropdown(Locator);
        await super.waitBrowserToLoadPage(3000)
        let text = await this.genericClickDropdownList(dropdownList);
        for (let i = 0; i < text.length; i++) {
            let appList = text[i]
            if (await text[i].getText() === dropdownValue) {
                await text[i].scrollIntoView()
                await super.waitBrowserToLoadPage(3000)
                await appList.click();
                break;
            }
        }
        await browser.keys('Escape')
    }

    async clickCheckBoxText(value) {
        await super.waitBrowserToLoadPage(2000)
        let text = await this.storeCheckBoxList
        for (let i = 0; i < text.length; i++) {
            let appList = text[i]
            if (await text[i].getText() === value) {
                await text[i].scrollIntoView()
                await super.waitBrowserToLoadPage(2000)
                await appList.click();
                break;
            }
        }
    }

    // select with index of value
    async selectRadioButton(Locator, radioValue) {
        let finalValue = await this.genericClickDropdown(Locator);
        await super.waitBrowserToLoadPage(20000);
        let text = await this.selectCategoryNameTablePOGsList;
        for (let i = 0; i < text.length; i++) {
            let appList = text[i];
            if (await text[i].getText() === radioValue) {
                await text[i].scrollIntoView();
                await super.waitBrowserToLoadPage(2000);
                n = i + 1;
                let radioBox = await this.selectCategoryNameTableNthRadioBoxList;
                await radioBox.click();
                break;
            }
        }
        await browser.keys('Escape')
    }

    //gets number of rows on the table
    async getColumnRowNumbers() {
        let columnTitle = await this.selectLocColumnTitle;
        await columnTitle.waitForExist({timeout: 50000}, 'Err: page title did not appear before timeout')
        await expect(columnTitle).toBeDisplayedInViewport()
        let rowCountList = [];
        let nextButton = await this.nextButtonPagination
        while (nextButton.isEnabled) {
            let nextBtnClassName = await this.nextButtonPagination.getAttribute("class")
            let rowCount = await this.dataTableLocNumColumnList;
            for (let i = 0; i < rowCount.length; i++) {
                rowCountList.push(await rowCount[i])
            }
            super.clickOn(nextButton)
            await super.waitBrowserToLoadPage(1000)
            if (nextBtnClassName.includes("disabled")) {
                break;
            }
        }
        return rowCountList.length;
    }

    async removeSelectedRecords() {
        let checkBox = await this.dataTableFirstCheckBox;
        await checkBox.waitForClickable({timeout: 3000}, 'Error: remove button did not appear before timeout')
        await expect(checkBox).toBeClickable();
        await this.getPaginationPageNumber('15')
        super.clickOn(checkBox);
        await super.waitBrowserToLoadPage(3000)
        let removeBtn = await this.getRemoveSelectedRecordsBtn;
        super.clickOn(removeBtn);
        await super.waitBrowserToLoadPage(2000)
        let proceed = await this.proceedOkBtn;
        super.clickOn(proceed)
    }

    async inputSendDate(date) {
        let sendDate = await this.selectSendDate
        await sendDate.waitForExist({timeout: 2000}, 'Error: effectiveDate did not appear before timeout')
        await expect(sendDate).toBeClickable()
        super.clickOn(sendDate)
        await browser.execute(s => {
            s.value = null;
        }, sendDate);
        await sendDate.setValue(date);
        await browser.keys('Escape')
    }

      async getPaginationPageNumber(selectValue) {
        let storeReviewBTN = await this.paginationDownArrow;
        await storeReviewBTN.waitForClickable({timeout: 2000}, 'Error: Search button did not appear before timeout')
        await expect(storeReviewBTN).toBeClickable()
        await super.clickOn(await storeReviewBTN)
        await super.waitBrowserToLoadPage(3000);
        let paginationNumber = await this.paginationDropdownList;
        for (let i = 0; i < paginationNumber.length; i++) {
            let pageList = paginationNumber[i];
            if (await paginationNumber[i].getText() === selectValue) {
                await paginationNumber[i].scrollIntoView()
                await super.waitBrowserToLoadPage(2000)
                await pageList.click()
                break;
            }
        }
        await browser.keys('Escape')
        !expect(super.doIsDisplay(this.paginationDropdownList))
    }

    // fetching values from column from UPL Order Table in String and returns the matched data passed
    async getAllColumnValue(key, data){
        let table = await this.dataTable;
        await table.waitForExist({timeout: 9000,timeoutMsg: 'Error: Mapping Report Table did not appear before time out'});
        let columnValue;
        let nextButton = await this.nextButtonPagination
        let headerData = await this.headerRow;
        await super.waitBrowserToLoadPage(2000)
        for(let j=0; j< headerData.length;j++) {
            if ((await headerData[j].getText()) === (key)) {
                await super.waitBrowserToLoadPage(1000)
                n = j + 1;
                break
            }
        }
        while (nextButton.isEnabled){
            let nextBtnClassName =  await this.nextButtonPagination.getAttribute("class")
            let value =  await this.valueNColumns;
            let ATTText
            for( let i =0; i < value.length; i++){
                ATTText= await value[i].getText()
                if(ATTText === await data ) {
                    columnValue= ATTText;
                    break;
                }
            }
            await super.clickOn(await nextButton)
            await super.waitBrowserToLoadPage(1000)
            await this.btnPageOne.waitForClickable({timeout: 9000, timeoutMsg: 'Error: Next button did not appear clickable before timeout'})
            await super.waitBrowserToLoadPage(1000)
            // either of "disable" or "0" UI value exists the function
            if(nextBtnClassName.includes("disabled") ) {
                break;
            }
        }
        return columnValue
    }

    async alertPopupMessageValidation(buttonLocator, requestTablePopupType){

        await this.clickOn(await buttonLocator);
        await this.waitBrowserToLoadPage(1500);
        let alertText = await this.GetText(await this.eventCreationText);
        alertText = await alertText.replace('\n', '').replace('\r', '');
        switch(await requestTablePopupType){
            case "success" :
                alertText = await alertText.replace("SuccessUPL", "UPL");
                await expect(alertText).toEqual("UPL ORDER Event Created Successfully");
                break;
            case "warning_event" :
                alertText = await alertText.replace("WarningEvent", "Event");
                await expect(alertText).toEqual("Event exists for current criteria, please finish processing before creating a new event");
                break;
            case "warning_fpstatus" :
                alertText = await alertText.replace("WarningPlease", "Please");
                await expect(alertText).toEqual("Please enter the values for Location, Department, ATTNTO and fpStatus (fpstatus value should be either 1, 2, 3 or 7 only)");
                break;
            case "processing complete" :
                alertText = await alertText.replace("Processing CompletedUPL", "UPL");
                await expect(alertText).toEqual("UPL order successfully processed");
                break;
            default :
                await expect('Error: There is no match for variable requestTablePopupType.').toEqual(false);
        }
    }

    async validateHeaderCheckbox(){

        n = "false";
        let checkedRowsBefore = await this.tableHeaderCheckboxChecked;
        await this.dataTableCheckBoxList.waitForClickable({timeout: 2000, timeoutMsg: 'Error: Next button did not appear clickable before timeout'});
        await this.clickOn(await this.dataTableCheckBoxList);
        n = "true";
        let checkedRowsAfter = await this.tableHeaderCheckboxChecked;
        await this.clickOn(await this.dataTableCheckBoxList);
        return checkedRowsBefore.length === checkedRowsAfter.length;
    }


}

module.exports = new UplOrderPage();
