const Page = require('../../../GlobalObjectRepository/page');
const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
const selectPOG = require('../EventManagerPO/selectingPogCommodities.page')
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const customStoreGroupPage = require("../EventManagerPO/customStoreGroup.page");


let flag = false

/**
 * sub page containing specific selectors and methods for Create SFP EVent page
 */
class CreateSFPEventPage extends Page {

    get dropDownSelect()                 {return ('mx-single-select[name="ne_?item"]')}
    get selectDropDownList()             {return $$('li[class*="mx-list-item items overlay-nc"]>label')}
    get textEventResetDescription()      {return $('#description-input-box>input')}
    get checkBoxSelect()                 {return ('input[name="?checkBox"]')}
    get btnSubmit()                      {return $('kds-button[aria-label="Submit"]')}
    get sfpAppModalButton()              {return $('div.mx-modal-footer-container-right > kds-button:nth-child(2) > button')}
    get radioBtnSelect()                 {return ('input[value="?radioBtn"]')}
    get selectHeadingPrefixDropDown()    {return $('#ne_TagHeading > select')}
    get textField()                      {return ('input[name*="ne_?field"]')}
    get linkSubModule()                  {return $('#page > app-breadcrumb > div > ol > li:nth-child(4) > a')}
    get defaultMin()                     {return $('input[name="event_default_minimum"]')}
    get defaultAllocation()              {return $('input[name="event_default_all"]')}
    get inputUPC()                       {return $('textarea[name="enteredValues"]')}
    get addUPC()                         {return $('#add-upc-btn > button')}
    get saveUPC()                        {return $('#save-upc-btn')}
    get linkCreateSfpRefreshEvent()      {return $('#page > app-breadcrumb > div > ol > li:nth-child(3) > a')}
    get radioAisleDelete()               {return $('input[value="aisledelete"]')}
    get storeDropdown()                  {return $('#locList>div')}
    get selectScheduleTypeDropdown()     {return $('mx-single-select[name="ne_scheduleType"]')}
    get selectTypeDropdown()             {return $('mx-single-select[name="ne_selectType"]')}
    get selectStoreDropDownList()        {return $$('li[class="ers-list-item items overlay-nc"]')}
    get radioAisleRebuild()              {return $('input[value="aislerebuild"]')}
    get inputStoreAisle()                {return $$('div.col-md-10 > div > kds-label> label')}
    get selectionList()                  {return $$('h5.ng-star-inserted')}
    get pogSubmitBtn()                   {return $('div.col-md-5.mt-16.ng-star-inserted > kds-button > button')}
    get refreshEventCreatedMsg()         {return $('div > div.ui-dialog-content.ui-widget-content')}
    get eventAlertRefreshContBtn()       {return $('p-footer button[type="button"]')}
    get eventDescriptionColumn()         {return $$('div#page tbody tr td:nth-child(5)')}
    get tagsSetsText()                   {return $('label.kds-Label.kds-Text--l.kds-Label--leftOfInput>span +input')}
    get sampleText()                     {return $('#sample > span')}
    get errorUserHeading()               {return $('#request-tags-content > div > div:nth-child(3) > kds-message > kds-text > span')}
    get errorSubmit()                    {return $('#form-content\\ flex\\ flex-row\\  > div.flex.flex-row > kds-message > kds-text > span')}
    get textUserComments()               {return $('textarea[name="ne_UserComment"]')}
    get shelfStripCheckBoxSelect()       {return ('input[value="X"]')}
    get saveScheduleText()               {return $('#ui-fieldset-8 > legend > p-header')}
    get radioButtonsVisible()            {return $('#form-content-extra > div > div > div > div')}
    get additionalCriteriaCheckBoxList() {return $$('#form-content-extra>div>div>div>kds-label>label> kds-checkbox > input')}
    get sfpEventFirstStep()              {return $('#page > content > div > app-normalevent > div > div > div > ul > li:nth-child(1) > a')}
    get sfpEventManagerBreadCrumb()      {return $('#page > app-breadcrumb > div > ol > li:nth-child(2) > a')}
    get activationRadioButton()          {return $$('#form-content-extra > div > div > div:nth-child(1) > kds-label > label > kds-radio > input')}
    get divisionDefaultSelectedValueForDivUser()      {return $('div[id="select-division-dropdown"]>kds-label>label>mx-single-select>div>div.mx-dropdown-input-wrapper')}
    get shelfStripOfficeRadio()          {return $('kds-radio[value="Office"]')}
    get footerEntryString()             {return $('p-table[datakey="dbKey"]>div div > span.p-paginator-current.ng-star-inserted')}
    get tableSecondPageBtn()            { return $('div > p-table > div > p-paginator > div > span button:nth-child(2)') }

    async selectDropdown(item, value) {
        let locator = $(this.dropDownSelect.replace('?item', item));
        if (item === 'Store') {
            locator = await this.storeDropdown;
        } else if (item === 'scheduleType') {
            locator = await this.selectScheduleTypeDropdown;
        } else if (item === 'selectType') {
            locator = await this.selectTypeDropdown;
        }
        try {
            await browser.waitUntil(async () => {
                await super.waitBrowserToLoadPage(2000);
                const isClickable = (await locator).isClickable();
                const isEnabled = await locator.isEnabled();
                return isClickable && isEnabled;
            }, {
                timeout: 59000,
                timeoutMsg: 'Element is not clickable and enabled'
            });
            await locator.click();
            await super.waitBrowserToLoadPage(2000);
            let dropDown = await this.selectDropDownList;
            for (let i = 0; i < dropDown.length; i++) {
                let dropdownList = dropDown[i];
                if (await dropDown[i].getText() === value || await value.includes(await (await dropDown[i].getText()).match(/\d+/g)   )) {
                    await dropDown[i].scrollIntoView();
                    await dropdownList.click();
                    break;
                }
            }
            return locator.getText();
        } catch (error) {
            console.error(error);
            throw error;
        }
    }


    async enterEventResetDescription(description) {
        let descValue = await this.textEventResetDescription;
        await descValue.waitForExist({timeout: 20000}, 'Error: Description field did not appear before timeout')
        await descValue.setValue(description);
        expect(await descValue.getValue()).toEqual(description)
    }

    async clickCheckBox(item) {
        let checkBox = await $(this.checkBoxSelect.replace('?checkBox', item))
        await super.clickOn(checkBox)
        await super.waitBrowserToLoadPage(2000)
    }

    async clickRadioButtonByText(text) {
    const radioButtons = await this.activationRadioButton
    for (let radioButton of radioButtons) {
        const label = await (await radioButton.parentElement()).parentElement();
        const labelText = await label.getText();
        if (labelText.includes(text)) {
            await radioButton.click();
            break;
        }
        }
    }
    
    async submitEventDetails() {
        await (await this.btnSubmit).waitForClickable({timeout: 2000})
        await super.clickOn(this.btnSubmit)
        await super.waitBrowserToLoadPage(6000)
        await this.linkSubModule.waitForExist({timeout: 2000}, 'Error: Event SChedule page did not appear before timeout')
        await super.waitForPageLoad(this.linkSubModule, "Event Schedule")
    }

    async submitEventRefreshDetails() {
        await super.clickOn(await this.btnSubmit)
        await this.linkCreateSfpRefreshEvent.waitForExist({
            timeout: 20000,
            timeoutMsg: 'Error: Event SChedule page did not appear before timeout'
        })
    }

    async selectHeadingPrefix(prefix) {
        let headingPrefix = await this.selectHeadingPrefixDropDown;
        let value = await headingPrefix.getValue();
        // Check if prefix is equal to value before selecting by attribute
        if (prefix !== value) {
            await headingPrefix.selectByAttribute("value", prefix);
        }
    }

    async selectRadioButton(item) {
        await (await this.radioButtonsVisible).isClickable({timeout: 20000})
        let radioButton = $(this.radioBtnSelect.replace('?radioBtn', item))
        await radioButton.waitForExist({timeout: 20000}, 'Error: radioAisleRebuild did not appear before timeout')
        await super.clickOn(radioButton)
    }

    async tagSetsValue() {
        let setText = await this.tagsSetsText
        await super.clickOn(setText)
    }

    async enterValue(field, value) {
        let txtField = $(this.textField.replace('?field', field))
        await txtField.waitForExist({timeout: 20000}, 'Error: User heading field did not appear before timeout')
        await txtField.setValue(value);
    }

    async enterUserComments(userComment) {
        let userCommentValue = await this.textUserComments;
        await userCommentValue.waitForExist({timeout: 20000}, 'Error: User comments field did not appear before timeout')
        await userCommentValue.setValue(userComment);
    }

    async storeSelectRadioButton(value) {
        await super.waitBrowserToLoadPage(6000)
        let setText = await this.inputStoreAisle
        for (let i = 0; i < setText.length; i++) {
            let checkboxList = setText[i]
            if (await setText[i].getText() === value) {
                await setText[i].scrollIntoView()
                await checkboxList.click();
                break;
            }
        }
    }

    async getSelectionList() {
        await super.waitBrowserToLoadPage(3000)
        let pogListText = this.selectionList
        let allSelectionList = [];
        if (await pogListText.length > 0) {
            let pogListMap = await pogListText.map(async link => await link.getText())
            for (let text of pogListMap) {
                allSelectionList.push(text)
            }
        }
        return allSelectionList
    }

    async getEventDescText(value) {
        await super.waitBrowserToLoadPage(6000)
        let eventData = await this.eventDescriptionColumn
        for (let i = 0; i < eventData.length; i++) {
            if ((await eventData[i].getText()).includes(value)) {
                flag = true
                break;
            }
        }
        return flag
    }

    async clickShelfStripCheckBox(item) {
        let checkBox = await $(this.shelfStripCheckBoxSelect.replace('X', item));
        await checkBox.waitForExist({timeout: 90000}, 'Error: locator  did not load before timeout')
        await super.clickOn(checkBox)
        await super.waitBrowserToLoadPage(2000)
    }

    async addEventDetails(event, division, scheduleType, eventDescription, vendor, checkBoxName) {
        await eventMaintanencePage.open('EventMaintanence');
        await eventMaintanencePage.tableFirstRow.waitForExist({timeout: 90000}, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await eventMaintanencePage.selectEventType(event)
        expect(await eventMaintanencePage.linkCreateSFPEvent.getText()).toHaveTextContaining(' Create SFP Event ')
        await this.selectDropdown('division', division)
        await this.selectDropdown('scheduleType', scheduleType);
        let description = await selectPOG.stringGenerator(eventDescription)
        await this.enterEventResetDescription(description)
        await this.selectDropdown('vendor', vendor)
        if (event === emData.SFP_Activation) {
            await this.additionalCriteriaActivation(3)
            return description;
        } else if (event === 'SFP Refresh' && checkBoxName) {
            await this.additionalCriteria(checkBoxName)
            return description;
        }
        return description
    }

    async addEventDetails1(user = '', event, division, scheduleType, selectType, eventDescription, vendor, checkBoxName, radioButton) {

        await eventMaintanencePage.waitBrowserToLoadPage(6000);
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.waitBrowserToLoadPage(6000);
        await eventMaintanencePage.tableSecondPageBtn.waitForExist({ timeout: 90000 , timeoutMsg: 'Error: Table contents for event maintenance page didn`t load before timeout'});

        await eventMaintanencePage.selectEventType(event);
        //await expect(eventMaintanencePage.linkCreateSFPEvent).toHaveTextContaining('Create SFP Event');
        // Skip selecting the division if user is equal to division
        if (user !== "div") {
            await this.selectDropdown('division', division);
        }
        await this.selectDropdown('scheduleType', scheduleType);
        if (event !== 'SFP Refresh') {
            await this.selectDropdown('selectType', selectType);
        }
    
        let description = await selectPOG.stringGenerator(eventDescription);
        await this.enterEventResetDescription(description);
        await this.selectDropdown('vendor', vendor);
        // If event is SFP Activation, call additionalCriteriaActivation
        if (event === emData.SFP_Activation) {
            await this.additionalCriteriaActivation(3);
        } else if (event === 'SFP Refresh' && checkBoxName) {
            if (Array.isArray(checkBoxName)) {
                for (const name of checkBoxName) {
                    await this.additionalCriteria(name);
                }
            }
        } else if (checkBoxName) {
            await this.additionalCriteria(checkBoxName);
        }
        if (radioButton) {
            await this.clickRadioButtonByText(radioButton);
        }
        return description;
    }
    

    async additionalCriteria(checkBoxes) {
        await this.clickCheckBox(checkBoxes)
    }

    async additionalCriteriaActivation(value) {
        await super.setValue(this.defaultMin, value)
        await super.setValue(this.defaultAllocation, value - 2)
    }

    async upcTabDetail(value) {
        await storeSelectionPage.waitForPageLoad(storeSelectionPage.linkSubModule, "Upload UPC(s)")
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(pogPage).toEqual(emData.Upload_UPC)
        await this.inputUPC.setValue(value)
        await super.clickOn(this.addUPC)
        await storeSelectionPage.tableFirstRow.waitForExist({timeout: 70000}, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await super.clickOn(this.saveUPC)
    }

    async singleCheckBoxIsSelected(checkBoxList, checkBoxName) {
        for (let el of await checkBoxList) {
            if (await el.getValue() === await checkBoxName) {
                return await el.isSelected();
            }
        }
        return false;
    }

    async clickAndValidateSingleCheckBox(checkBoxLocator, checkBoxName) {
        let checkBoxElement = await checkBoxLocator;
        for (let el of checkBoxElement) {
            if (await el.getValue() === await checkBoxName) {
                await el.click();
                await browser.pause(2000)
                return await el.isSelected();
            }
        }
        return false;
    }

    async addEventScheduleStep2(){
        if(await storeSelectionPage.saveSchedule.waitForClickable()){
            await super.clickOn(await storeSelectionPage.saveSchedule);
        }
        await eventMaintanencePage.tableFirstRow.waitForExist({
            timeout: 9000,
            timeoutMsg: 'Error: Table contents for event ScheduleList page did not load before timeout'
        })
        await storeSelectionPage.saveGoToStoreSelection();
    }

    async selectRandomStoresStep3(){
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await storeSelectionPage.clickRandomEnabledStoreCheckboxes(1, 7);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
    }

    async selectCustomStoreGroup(){
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await customStoreGroupPage.clickAndRandomlySelectCustomStoreGroup();
        await eventMaintanencePage.waitBrowserToLoadPage(1000);
        await customStoreGroupPage.selectScheduleDropDownValue();
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await customStoreGroupPage.storeSelectionSelectButton.click();
    }

    async selectPogCommoditiesStep4(){
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        let intParams = await storeSelectionPage.generateTwoIntParams();
        await eventMaintanencePage.selectCommodityDropDownValuesByIndex(intParams[0], intParams[1]);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(2000);
        await this.clickOn(selectPOG.selectChoosePogByCommodityBtn);
        await selectPOG.waitForPageLoad(selectPOG.selectText, emData.Department);
        await eventMaintanencePage.waitBrowserToLoadPage(3000);
        await this.clickOn(selectPOG.gotoEventListButton);
    }

    async openEventMaintenancePage(){
        await eventMaintanencePage.waitBrowserToLoadPage(6000)
        await eventMaintanencePage.open('EventMaintenance');
        await eventMaintanencePage.waitBrowserToLoadPage(3000)
        await this.tableSecondPageBtn.waitForExist({
            timeout: 90000,
            timeoutMsg: 'Error: Table contents for event maintenance page did not load before timeout'
        })
    }

}

module.exports = new CreateSFPEventPage();
