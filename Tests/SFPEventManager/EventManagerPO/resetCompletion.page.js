const Page = require('../../../GlobalObjectRepository/page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const HomePage = require("../../../GlobalObjectRepository/home.page");
const eventMaintenancePage = require("./eventMaintanence.page");
let columnIndex, rowIdx, j, n, m, h;

class ResetCompletionPage extends Page {

    get inputFilterName() {return ('mx-multi-select[name="selected"]> div > div:nth-child(3) > div > mx-search-input > div > div > input')}
    get selectDivision() {return $('mx-multi-select[name="selectedDivision"]')}
    get selectDivisionTxt() {return $('#divisionList>div>div>span')}
    get selectLocationtext() {return $('#locationList>div>div>span')}
    get selectLocation() {return $('mx-multi-select[name="selectedLocation"]')}
    get selectDepartment() {return $('mx-multi-select[name="selectedDepartment"]')}
    get selectResetType() {return $('mx-multi-select[name="selectedResetType"]')}
    get selectCommodity() {return $('mx-multi-select[name="selectedCommodity"]')}
    get selectResetComplete() {return $('mx-multi-select[name="selectedCompletionType"]>div>kds-label+div')}
    get selectYear() {return $('mx-single-select[input-id="year-dropdown"]>div')}
    get selectPeriod() {return $('mx-single-select[input-id="period-dropdown"]>div')}
    get selectWeek() {return $('mx-single-select[input-id="week-dropdown"]>div')}
    get searchBtn() {return $('kds-button[id="searchBtn"]>button')}
    get clearFiltersBtn() {return $('kds-button[id="searchBtn"] + kds-button > button')}
    get exportToExcelBtn()               {return $('app-reset-completion > div > div > div > kds-button > button >kds-icon-export')}
    get exportToExcelBtnDivUser()        {return $('#page > content > div > app-reset-completion > div > div.row.pb-8 > div.col-md-9 > div > kds-button > button > kds-icon-export')}
    get commodityRecapBtn() {return $('div[role="tablist"]>button:nth-child(1)')}
    get storeReview() {return $('div[role="tablist"]>button:nth-child(2)')}
    get storeReviewTable() {return $('#storeReviewTable>div>div>table>tbody')}
    get commodityRecapTable() {return $(`p-table[id='commodityRecapTable'] > * table > tbody`)}
    get columnTitleDiv() {return $('p-table[id="storeReviewTable"] th[ng-reflect-field="division"]')}
    get columnTitleLoc() {return $('p-table[id="storeReviewTable"] th[ng-reflect-field="location"]')}
    get columnTitleVendor() {return $('p-table[id="storeReviewTable"] th[ng-reflect-field="vendorName"]')}
    get columnTitleResetType() {return $('p-table[id="storeReviewTable"] th[ng-reflect-field="resetType"]')}
    get columnTitleYear() {return $('p-table[id="storeReviewTable"] th[ng-reflect-field="year"]')}
    get columnTitlePeriod() {return $('p-table[id="storeReviewTable"] th[ng-reflect-field="period"]')}
    get columnTitleWeek() {return $('p-table[id="storeReviewTable"] th[ng-reflect-field="week"]')}
    get linkSubModule() {return $('div[class="row ng-star-inserted"]>ol>li:nth-child(3)>a')}
    get resetScheduleDropdownValues() {return $$('div[class="menu-item-container ng-star-inserted"]>kds-label')}
    get resetDropDownListValues() {return ('#dropDownName-select > div > div:nth-child(3) > div > div > mx-multi-select-list > ul > div > mx-menu-item > li > div > div.w-full > kds-label > label > div')}
    get resetDropDownSelectAllValues() {return ('#dropDownName-select > div > div:nth-child(3) > div > div > mx-multi-select-list > ul > mx-menu-item > li > div > div.w-full > kds-label > label > div')}
    get tableValuesStoreReview() {return $$('p-table[id="storeReviewTable"] tbody[class="ui-table-tbody"] td')}
    get firstRowValuesStoreReview() {return $$('p-table[id="storeReviewTable"] tbody[class="p-element p-datatable-tbody"] tr:nth-child(1)>td')}
    get tableColumnsCommodityRecap() {return $$('p-table[id="commodityRecapTable"] tbody[class="ui-table-tbody"] tr:nth-child(1)>td')}
    get tableRowsStoreReview() {return $$('p-table[id="storeReviewTable"] tbody[class="p-element p-datatable-tbody"]>tr')}
    get tableRowsCommodityRecap() {return $$('p-table[id="commodityRecapTable"] tbody[class="ui-table-tbody"]>tr')}
    get tableNthColumns() {return $$(`p-table[id="storeReviewTable"] tr:nth-child(${columnIndex}) td`)}
    get tableNthColumnsCommodityRecap() {return $$('p-table[id="commodityRecapTable"] tbody[class="ui-table-tbody"]>tr:nth-child(' + columnIndex + ') td')}
    get allRadioValues() {return $$('p-table[id="storeReviewTable"] tr>td>kds-radio')}
    get firstRowRadioValuesTxt() {return $$('p-table[id="storeReviewTable"] tr:nth-child(1)>td>kds-radio')}
    get reasonColumnList() {return $$('p-table[id="storeReviewTable"] tbody>tr>td:nth-child(6) +td')}
    get getDeleteStatus() {return $$('p-table[id="storeReviewTable"] tr>td:nth-child(6)')}
    get getCompleteStatusList() {return $$('p-table[id="storeReviewTable"] tr>td:nth-child(3)>kds-radio')}
    get reasonColumnOptionList() {return $$('p-table[id="storeReviewTable"] select[class="kds-Select kds-Select--compact"]>option:nth-child(1)')}
    get firstRowRadioInput() {
        return $$('p-table[id="storeReviewTable"] tr:nth-child(1)>td>kds-radio:nth-child(1)>input')
    }

    get firstRowRadioValues() {
        return $$('p-table[id="storeReviewTable"] tr:nth-child(1)>td>kds-radio:nth-child(1)')
    }

    get radioValues() {
        return $$('p-table[id="storeReviewTable"] tr>td>kds-radio:nth-child(1)')
    }

    get firstRowValues() {
        return $$('p-table[id="storeReviewTable"] tr:nth-child(1)>td')
    }

    get highlightedValues() {
        return $$('p-table[id="storeReviewTable"] tr[class="deleted ng-star-inserted"] td')
    }

    get columnDivisionList()           {return $$('p-table[id="storeReviewTable"] tbody[class="ui-table-tbody"] >tr>td:nth-child(2)')}
    get columnLocList()                {return $$('p-table[id="storeReviewTable"] tbody[class="ui-table-tbody"] >tr>td:nth-child(3)')}
    get columnVendorList()             {return $$('p-table[id="storeReviewTable"] tbody[class="ui-table-tbody"] >tr>td:nth-child(4)')}
    get columnResetTypeList()          {return $$('p-table[id="storeReviewTable"] tbody[class="ui-table-tbody"] >tr>td:nth-child(5)')}
    get columnYearList()               {return $$('p-table[id="storeReviewTable"] tbody[class="ui-table-tbody"] >tr>td:nth-child(6)')}
    get columnPeriodList()             {return $$('p-table[id="storeReviewTable"] tbody[class="ui-table-tbody"] >tr>td:nth-child(7)')}
    get columnWeekList()               {return $$('p-table[id="storeReviewTable"] tbody[class="ui-table-tbody"] >tr>td:nth-child(8)')}
    get backToFirstBtnPagination() {
        return $('p-table[id="storeReviewTable"] p-paginator[styleclass="ui-paginator-bottom"]>div>a:nth-child(1)')
    }

    get backToFirstBtnPaginationCommodity() {
        return $('p-table[id="commodityRecapTable"] p-paginator[styleclass="ui-paginator-bottom"]>div>a:nth-child(1)')
    }

    get backButtonPagination() {
        return $('p-table[id="storeReviewTable"] p-paginator[styleclass="p-paginator-bottom"]>div>button:nth-child(2)')
    }

    get nextButtonPagination() {
        return $('p-table[id="storeReviewTable"] p-paginator[styleclass="p-paginator-bottom"]>div>button:nth-child(4)')
    }

    get nextButtonPaginationCommodityRecap() {
        return $('p-table[id="commodityRecapTable"] p-paginator[styleclass="ui-paginator-bottom"]>div>a:nth-child(4)')
    }

    get paginationDropdownBtn() {
        return $('p-table[id="storeReviewTable"] p-paginator> div>p-dropdown')
    }

    get paginationDropdownList() {
        return $$('p-table[id="storeReviewTable"] ul[role="listbox"]>p-dropdownitem>li')
    }

    get storeReviewTableCheckBox() {
        return $('p-table[id="storeReviewTable"] p-tableheadercheckbox')
    }

    //dynamic locator takes the value of row(m) and column(n) from the code based on the header
    get inputNo() {
        return $(`p-table[id="storeReviewTable"] * tbody > tr:nth-child(${n}) > td:nth-child(${m}) > kds-radio > input`)
    }

    get btnUpdate() {
        return $('#updateBtn')
    }

    get tableHeaders() {
        return $$('p-table[id="storeReviewTable"] table > thead >tr>th')
    }

    get completeStatusStoreReview() {
        return $$('#storeReviewTable>div>div>table>thead>tr:nth-child(2)>th')
    }

    get resetCompleteDropdownList() {
        return $$('div>ers-menu-item>li>div>kds-label>label')
    }

    get headersStoreReviewTable() {return $$('#storeReviewTable>div>div>table>thead>tr>th')}

    get storeReviewCheckBoxList() {return $$('#storeReviewTable table>tbody>tr>td:nth-child(1)>p-tablecheckbox>div>div+div')}

    get storeReviewCancelBtn() {
        return $('#cancelBtn>button')
    }

    get storeReviewDeleteBtn() {
        return $('#removeBtn>button')
    }

    get storeReviewUpdateBtn() {
        return $('kds-button[id="updateBtn"]>button')
    }

    get storeReviewCancelResetBtn() {
        return $('kds-button[id="cancelResetBtn"]>button')
    }

    get storeReviewCheckBoxOne() {
        return $('#storeReviewTable table>tbody>tr:nth-child(1)>td:nth-child(1)>p-tablecheckbox>div>div+div')
    }

    get storeReviewRowList() {
        return $('p-table[id="storeReviewTable"] tbody[class="ui-table-tbody"] >tr')
    }

    get storeReviewFirstRowList() {
        return $$('#storeReviewTable table>tbody>tr:nth-child(1)>td')
    }

    get storeReviewTableRows() {
        return $$('p-table[id="storeReviewTable"] tbody[class="ui-table-tbody"] tr')
    }

    get proceedBtn() {
        return $('ers-button[ng-reflect-label="Proceed"]>button')
    }

    get deleteText() {
        return $('#ers-refresh-store-selection-modal>div>div>div')
    }

    get storeReviewTableHeaders() {return $$('form[id="storeReviewForm"] thead[class="p-datatable-thead"]>tr>th')}

    get nthColumnListStoreReview() {
        return $$(`p-table#storeReviewTable tbody>tr>td:nth-child(${n})`)
    }

    get tableStoreReview() {
        return $('p-table[id="storeReviewTable"] tbody[class="ui-table-tbody"]')
    }

    get commodityColumnHeaders() {
        return $$('p-table[id="commodityRecapTable"] thead>tr>th')
    }

    get completeHeaderList() {
        return $$('p-table[id="storeReviewTable"] table tr:nth-child(2)>th')
    }

    get completeCheckboxList() {
        return $$('p-table[id="storeReviewTable"] table tr>td>kds-radio')
    }

    get commodityFirstRowValues() {
        return $(`p-table[id="commodityRecapTable"] table > tbody > tr > td:nth-child(${h}) > span`)
    }

    get commodityColumns() {
        return $('p-table[id="commodityRecapTable"] table > tbody > tr > td:nth-child(' + rowIdx + ')>a)')
    }

    get storeReviewColumns() {
        return $('p-table[id="storeReviewTable"] table > tbody > tr > td:nth-child(' + columnIndex + ')')
    }

    get tableColumnsStoreReview() {
        return $$('p-table[id="storeReviewTable"] tbody[class="ui-table-tbody"] tr')
    }

    get commodityRecapTableHeaders() {
        return $$('p-table[id="commodityRecapTable"] table > thead >tr>th')
    }

    get nthCompleteCommodityRecapColumns() {
        return $(`p-table[id="commodityRecapTable"] table > tbody > tr > td:nth-child(${n}) > a`)
    }

    get storeReviewNotCompleteColumnList() {
        return $$('p-table[id="storeReviewTable"] table>tbody>tr>td:nth-child(14)')
    }

    get notCompletedHeaderText() {
        return $('#commodityRecapTable>div>div>table>thead>tr>th:nth-child(8)')
    }

    get commodityPaginationPageNumber() {
        return $$('#commodityRecapTable > div > p-paginator > div > span > a.ui-paginator-page')
    }

    get commodityRecapPaginationDropdownBtn() {
        return $('p-table[id="commodityRecapTable"] div[class="ui-dropdown-trigger ui-state-default ui-corner-right"]')
    }

    get commodityRecapPaginationDropdownList() {
        return $$('p-table[id="commodityRecapTable"] ul[role="listbox"]>p-dropdownitem>li')
    }

    get activePaginationNum() {
        return $('#storeReviewTable > div > p-paginator > div > span > a.ui-paginator-page.ui-paginator-element.ui-state-default.ui-corner-all.ng-star-inserted.ui-state-active')
    }

    get activePaginationNumcommodityRecapTable() {
        return $('#commodityRecapTable > div > p-paginator > div > span > a.ui-paginator-page.ui-paginator-element.ui-state-default.ui-corner-all.ng-star-inserted.ui-state-active')
    }

    get dropdownList() {
        return $$('ul>cdk-virtual-scroll-viewport>div>ers-menu-item>li')
    }

    get activePaginationNumStoreReview() {
        return $('#storeReviewTable  span > a.ui-paginator-page.ui-paginator-element.ui-state-default.ui-corner-all.ng-star-inserted.ui-state-active')
    }

    get zeroRecordsFoundText() {
        return $('div > p:nth-child(2)')
    }

    get divTopRowValueStoreReview() {
        return $('#storeReviewTable>div>div>table>tbody>tr:nth-child(1)>td:nth-child(2)')
    }

    get paginationPagefive() {
        return $('#storeReviewTable > div > p-paginator > div > span > a:nth-child(5)')
    }

    get divisionDropDown() {
        return $('#divisionMultiSelect > div > div > span')
    }

    get allRowsStoreReview() {
        return $$('p-table[id="storeReviewTable"] table>tbody>tr')
    }

    get nthColumnCommodityRecap() {
        return $$(`p-table[id="commodityRecapTable"] table>tbody>tr>td:nth-child(${columnIndex})`)
    }

    get spfEventManagerDropDown() {
        return $(`#headerWrapper > * a[href='/EventMaintenance']`)
    }

    get adminMenuDropDown() {
        return $(`#headerWrapper > * a[href='/EventMaintenance'] + ul > li:nth-of-type(1) > a`)
    }

    get resetCompletionMenuDrownDown() {
        return $(`#headerWrapper > * a[href='/EventMaintenance/resetCompletion']`)
    }

    get tableRowsStoreReviewElements() {
        return $$('p-table[id="storeReviewTable"] > * tbody > tr > td')
    }

    get tableStoreReviewColumns() {
        return $$(`p-table[id="storeReviewTable"] > * tbody > tr:nth-child(1) > td`)
    }

    get nthColumnStoreReview()         {return $$(`p-table[id="storeReviewTable"] tbody > tr > td:nth-child(${columnIndex})`)}
    get nthColumnStoreReview0()        {return $$(`p-table[id="storeReviewTable"] tbody > tr > td:nth-child(${columnIndex})>mx-single-select`)}

    get nthColumnStoreReview2()          { return $$(`form[id="storeReviewForm"] tbody > tr> td:nth-child(${columnIndex})`)}

    get resetCompletionLocatorList() {
        return $$('#locationList > * div:nth-child(3) > * li > * label > div')
    }

    get commodityRecapTableElements() {
        return $$('p-table[id=\'commodityRecapTable\'] > * table > tbody > tr > td')
    }

    get tableColumnCommodityRecap() {
        return $$(`p-table[id='commodityRecapTable'] > * tbody > tr:nth-child(1) > td`)
    }

    get divisionDropDownElements() {
        return $$('#divisionMultiSelect > * label > div')
    }

    get resetCompleteDropDown() {
        return $('#resetCompleteList > div > div> span')
    }

    get loadingSpinnerRestCompletion() {
        return $('p-table > * div > spinnericon > svg')
    }

    get updateButtonPopupText() {
        return $('#updateStoreReviewModal > * div > p')
    }

    get updateButtonPopupProceedButton() {
        return $('#updateStoreReviewModal > * kds-button:nth-child(2) > button')
    }

    get updateButtonPopUp() {
        return $('#updateStoreReviewModal')
    }

    get dropDownResetCompletionSearch() {
        return $('#resetCompleteList > div > div:nth-child(3) > div > mx-search-input > div > div > input')
    }

    get dropDownResetCompletionFirstItem() {
        return $('#resetCompleteList >* mx-menu-item:nth-child(1) > * kds-checkbox > input')
    }

    get resetCompleteDropDownSelectAll() {
        return $('#resetCompleteList > * ul > mx-menu-item >* kds-checkbox > input')
    }

    get commodityDropDown() {
        return $('#commodityList > div > div > span')
    }

    get commodityDropDownSelectAll() {
        return $('#commodityList > * ul > mx-menu-item >* kds-checkbox > input')
    }

    get dropdownItemLabels() {
        return 'mx-multi-select[name="selected"] > * mx-multi-select-list > * div > mx-menu-item:nth-child(1) > * kds-label > * div'
    }

    get dropdownSearchInput() {
        return 'mx-multi-select[name="selected"] > * mx-multi-select-list > * div > mx-menu-item:nth-child(1) > * kds-label > * input'
    }

    get cancelResetModifyBtnList()                     {return $$('#page > content > div > app-reset-completion > div > div> div> div')}
    get cancelResetBtn()                               {return $('kds-button[id="cancelBtn"]>button')}
    get editModeButton()                               {return $('app-reset-completion>div>div>div>div>span>kds-button>button')}
    get popUpCancelResetText()                         {return $('div[aria-label="Cancel Reset(s)"]>mx-modal-header>header')}
    get popUpWarningMessage()                          {return $('#mx-modal-id>div>div>kds-message>kds-text>span')}
    get popUpAffirmationMessage()                      {return $('#mx-modal-id>div>div>p')}
    get noKeepResetsBtn()                              {return $('#mx-modal-id>div>div>kds-message>kds-text>span')}
    get noYesCancelResetBtnList()                      {return $$('mx-modal[id="cancelResetEventModal"] div>mx-modal-body[id="mx-modal-id"] + mx-modal-footer>div>div+div>kds-button>button')}
    get cancelResetConfirmationMessage()               {return $('#mx-modal-id > div > div')}
    get resetsCancelledMessage()                       {return $('section[aria-live="assertive"] > header')}
    get selectedResetCancelledMessage()                {return $('section[aria-live="assertive"] > p')}
    get closeCancelResetModal()                        {return $('mx-modal[id="cancelResetEventModal"]>div>div>mx-modal-header button')}
    get resetStatusColumnActiveFirstRow()              {return $('tbody > tr:nth-child(1) > td.ng-star-inserted > kds-select>select')}
    get modifyPopUpWarningMessage()                    {return $('mx-modal[id="updateStoreReviewModal"]>div>div>mx-modal-body[id="mx-modal-id"]>div')}
    get modifyPopUpBtbList()                           {return $$('mx-modal[id="updateStoreReviewModal"]>div>div>mx-modal-footer>div>div+div>kds-button>button')}
    get modifyPopUpCancelBtn()                         {return $('mx-modal[id="updateStoreReviewModal"]>div>div>mx-modal-footer>div>div+div>kds-button:nth-child(1)')}
    get modifyPopUpProceedBtn()                        {return $('mx-modal[id="updateStoreReviewModal"]>div>div>mx-modal-footer>div>div+div>kds-button:nth-child(2)')}
    get modifySuccessMessage()                         {return $('div[id="kds-Portal-toast"]>div>kds-toast>section')}
    get headerCheckBoxStoreReview()                    {return $('p-table[id="storeReviewTable"] table>thead>tr>th>p-tableheadercheckbox>div>div+div')}
    get firstRowCheckBoxStoreReview()                  {return $('p-table[id="storeReviewTable"] tbody > tr:nth-child(1) > td:nth-child(1) > p-tablecheckbox>div')}
    get resetCompleteDropdownCloseBtn()                {return ('mx-multi-select[id="?dropdown"] > div > div.mx-dropdown-input-wrapper > kds-icon-close > kds-icon > svg')}
    get editModeMessage()                              {return $('form[id="storeReviewForm"]>div>kds-tag>span')}
    get editModeBtn()                                  {return $('header[id="kr-header"]+app-breadcrumb+content app-reset-completion>div>div>div>div>span>kds-button')}
    get saveChangesBtn()                               {return $('app-reset-completion > div > div.row.pb-8 > div.col-md-9 > div > span > kds-button:nth-child(1) > button')}
    get clearChangesBtn()                              {return $('app-reset-completion > div > div.row.pb-8 > div.col-md-9 > div > span > kds-button:nth-child(2) > button')}
    get resetStatusColumnFirstValueActive()            {return $('tbody > tr:nth-child(1) > td:nth-child(3) > mx-single-select > div > div > span')}
    get resetStatusReasonColumnFirstValueActive()      {return $('tbody > tr:nth-child(1) > td:nth-child(4) > mx-single-select > div > div > span')}
    get resetStatusColumnFirstChevron()                {return $('tbody > tr:nth-child(1) > td:nth-child(3) > mx-single-select > div > div > kds-icon-chevron-down > kds-icon > svg')}
    get resetStatusReasonColumnFirstChevron()          {return $('tbody > tr:nth-child(1) > td:nth-child(4) > mx-single-select > div > div > kds-icon-chevron-down > kds-icon > svg')}
    get resetStatusReasonColumnDynamicChevron()        {return $(`tbody > tr:nth-child(${rowIdx}) > td:nth-child(4) > mx-single-select > div > div > kds-icon-chevron-down > kds-icon > svg`)}
    get resetStatusColumnDynamicChevron()              {return $(`tbody > tr:nth-child(${rowIdx}) > td:nth-child(3) > mx-single-select > div > div > kds-icon-chevron-down > kds-icon > svg`)}
    get resetStatusColumnDynamicChevronList()          {return $(`tbody > tr:nth-child(${rowIdx}) > td:nth-child(3) > mx-single-select > div > div > kds-icon-chevron-down > kds-icon > svg`)}
    get resetStatusReasonColumnChevronList()           {return $$('tbody > tr> td:nth-child(4) > mx-single-select > div > div > kds-icon-chevron-down > kds-icon > svg')}
    get resetStatusTableDropdownList()                 {return $$('body > div> mx-single-select-list > ul > div > mx-single-select-list-item> div > li > label')}
    get resetStatusReasonColumnDropdownList()          {return $$('body > div > mx-single-select-list > ul > div > mx-single-select-list-item> div > li > label')}
    get resetStatusColumnFirstValueStatic()            {return $$('form[id="storeReviewForm"] tbody > tr> td:nth-child(3)>kds-tag')}
    get resetStatusColumnStaticList()                  {return $$('form[id="storeReviewForm"] tbody > tr> td:nth-child(3)>kds-tag')}
    get resetStatusReasonColumnStaticList()            {return $$('form[id="storeReviewForm"] tbody > tr> td:nth-child(4)>kds-tag')}
    get storeReviewTableNthColumns()                   {return $$(`form[id="storeReviewForm"] tbody>tr>td:nth-child(${columnIndex})>kds-tag`)}
    get completeDropdownList()                         {return $$('body > div:nth-child(11) > mx-single-select-list > ul > div > mx-single-select-list-item > div > li')}
    get resetStatusReasonColumnFirstActiveChevron()    {return $('form[id="storeReviewForm"] tbody > tr:nth-child(1) > td:nth-child(4) > mx-single-select > div > div > span')}
    get resetStatusReasonColumnActiveValue()           {return $('form[id="storeReviewForm"] tbody > tr > td:nth-child(4) > mx-single-select > div > div > span')}
    get reasonColumnDynamicValue()                     {return $(`form[id="storeReviewForm"] tbody > tr:nth-child(${columnIndex}) > td:nth-child(4) > p`)}
    get resetCompletedDropdownList()                   {return $$('body > div:nth-child(11) > mx-single-select-list > ul > div > mx-single-select-list-item > div > li')}
    get changesSavedMessage()                          {return $('section>header>h1')}
    get zeroRecordsMessage()                           {return $('#kds-Portal-toast section>header+p')}
    get resetStatusColumnList()                        {return $$('form[id="storeReviewForm"] tbody > tr> td:nth-child(3)>kds-tag')}
    get resetStatusReasonColumnList()                  {return $$('form[id="storeReviewForm"] tbody > tr> td:nth-child(3)>mx-single-select')}
    get resetStatusDynamicColumnList()                 {return $$(`form[id="storeReviewForm"] tbody > tr:nth-child(${columnIndex}) > td:nth-child(3) > mx-single-select > div > div> span`)}
    get resetIdDynamicColumnValue()                    {return $(`p-table[id="storeReviewTable"] > * tbody > tr:nth-child(${rowIdx}) > td:nth-child(2)`)}
    get reasonDropdownColumnList()                     {return $$('body > div > mx-single-select-list > ul > div > mx-single-select-list-item > div > li > label')}
    get resetStatusReasonColumnFirstValue()            {return $('#pn_id_2-table > tbody > tr:nth-child(1) > td.ng-star-inserted > kds-select > select')}
    get resetStatusReasonColumnDynamicValue()          {return $(`form[id="storeReviewForm"] tbody > tr:nth-child(${rowIdx}) > td:nth-child(4) > mx-single-select`)}
    get resetStatusReasonColumnNonActiveValue()        {return $(`form[id="storeReviewForm"] tbody > tr:nth-child(${rowIdx}) > td:nth-child(4) > p`)}
    get resetStatusColumnDynamicValue()                {return $(`form[id="storeReviewForm"] tbody > tr:nth-child(${rowIdx}) > td:nth-child(3) > mx-single-select > div`)}
    get resetStatusReasonColumnFirstStaticValue()      {return $('form[id="storeReviewForm"] tbody > tr:nth-child(1)> td:nth-child(4)')}
    get resetStatusReasonColumnClickableList()         {return $$('form[id="storeReviewForm"] tbody > tr:nth-child(1) > td:nth-child(3) > mx-single-select')}
    get resetStatusReasonColumnUnClickableList()       {return $(`form[id="storeReviewForm"] tbody > tr:nth-child(${rowIdx})> td:nth-child(3)>kds-tag`)}


    async genericClickDropdown(Locator) {
        let dropDown;
        switch (Locator) {
            case 'selectedDivision':
                dropDown = await this.selectDivision;
                await (dropDown).waitForExist({timeout: 30000}, 'Error: Division dropdown did not appear before timeout')
                break;
            case 'selectedLocation':
                dropDown = await this.selectLocation;
                await (dropDown).waitForExist({timeout: 30000}, 'Error: Location dropdown did not appear before timeout')
                break;
            case 'selectedDepartment':
                dropDown = await this.selectDepartment;
                await (dropDown).waitForExist({timeout: 30000}, 'Error: Department dropdown did not appear before timeout')
                break;
            case 'selectedResetType':
                dropDown = await this.selectResetType;
                await (dropDown).waitForExist({timeout: 30000}, 'Error: ResetType dropdown did not appear before timeout')
                break;
            case 'selectedCommodity':
                dropDown = await this.selectCommodity;
                await (dropDown).waitForExist({timeout: 30000}, 'Error: Commodity dropdown did not appear before timeout')
                break;
            case 'selectedCompletionType':
                dropDown = await this.selectResetComplete;
                await (dropDown).waitForExist({timeout: 30000}, 'Error: ResetComplete dropdown did not appear before timeout')
                break;
        }
        await super.waitBrowserToLoadPage(2000)
        await super.clickOn(dropDown);
    }

    async genericClickColumnTitle(locator) {
        let tabTitle;
        switch (locator) {
            case 'division':
                tabTitle = await this.columnTitleDiv;
                break;
            case 'location':
                tabTitle = await this.columnTitleLoc;
                break;
            case 'vendorName':
                tabTitle = await this.columnTitleVendor;
                break;
            case 'resetType':
                tabTitle = await this.columnTitleResetType;
                break;
            case 'year':
                tabTitle = await this.columnTitleYear;
                break;
            case 'period':
                tabTitle = await this.columnTitlePeriod;
                break;
            case 'week':
                tabTitle = await this.columnTitleWeek;
                break;
        }
        return tabTitle;
    }

    async genericColumnValueList(items) {
        let values;
        switch (items) {
            case 'divisions':
                values = await this.columnDivisionList;
                break;
            case 'locations':
                values = await this.columnLocList;
                break;
            case 'vendorNames':
                values = await this.columnVendorList;
                break;
            case 'resetTypes':
                values = await this.columnResetTypeList;
                break;
            case 'years':
                values = await this.columnYearList;
                break;
            case 'periods':
                values = await this.columnPeriodList;
                break;
            case 'weeks':
                values = await this.columnWeekList;
                break;
            case 'storeReviewRows':
                values = await this.tableRowsStoreReview;
                values = await this.storeReviewNotCompleteColumnList;
                break;
            case 'commodityrecapRows':
                values = await this.commodityRecapTableHeaders;
                break;
        }
        return values;
    }

    // select with dropdown by Text
    async selectSingleValue(Locator, dropDownName, dropdownListLocatorName, selectValue) {
        await this.genericClickDropdown(Locator);
        await super.waitBrowserToLoadPage(2000)
        let textLocator;
        if (await dropDownName === "scheduleType") {
            textLocator = await $$(this.resetDropDownListValues.replace('dropDownName-select', dropdownListLocatorName));
        } else {
            textLocator = await $(this.resetDropDownSelectAllValues.replace('dropDownName-select', dropdownListLocatorName));
        }
        await super.waitBrowserToLoadPage(2000)
        if (selectValue === "Select All") {
            await textLocator.getText();
            await super.waitBrowserToLoadPage(2000);
            await textLocator.click();
        } else {
            for (let i = 0; i < textLocator.length; i++) {
                let appList = await textLocator[i]
                if (await textLocator[i].getText() === selectValue) {
                    await textLocator[i].scrollIntoView();
                    await super.waitBrowserToLoadPage(2000);
                    await appList.click();
                    break;
                }
            }
        }
        await browser.keys('Escape')
    }

    // select with dropdown by Text
    async setMultipleValues(Locator, dropdownSelectionValues) {

        for (let selectValueIndex = 0; selectValueIndex < dropdownSelectionValues.length - 1; selectValueIndex++) {
            await this.genericClickDropdown(Locator);
            let setText = await $((this.inputFilterName).replace('selected', Locator));
            await setText.setValue(dropdownSelectionValues[selectValueIndex]);
            let textLocator = await $((this.dropdownItemLabels).replace('selected', Locator));
            let appList = await $((this.dropdownSearchInput).replace('selected', Locator));
            let text = await this.GetText(await textLocator);
            if (text === dropdownSelectionValues[selectValueIndex]) {
                await super.waitBrowserToLoadPage(1500)
                await this.clickOn(await appList);
            }
        }
        await browser.keys('Escape')
    }

    async getPaginationPageNumber(selectValue) {
        let storeReviewBtn = await this.paginationDropdownBtn;
        await super.clickOn(storeReviewBtn)
        await super.waitBrowserToLoadPage(3000)
        let paginationNumber = await this.paginationDropdownList;
        for (let i = 0; i < paginationNumber.length; i++) {
            let pageList = paginationNumber[i];
            if (await paginationNumber[i].getText() === selectValue) {
                await paginationNumber[i].scrollIntoView()
                await super.waitBrowserToLoadPage(2000)
                await pageList.click()
                break;
            }
        }
        await browser.keys('Escape')
        !expect(super.doIsDisplay(this.paginationDropdownList))
    }

    //Clicks column title and collecting column data in  in Ascending order
    async ascending(values, pageNum) {
        let tableValues = await this.storeReviewTable;
        await tableValues.waitForExist({timeout: 3000}, 'Error: Page  did not appear before timeout')
        await expect(tableValues).toBeDisplayedInViewport();
        let nextButton = await this.nextButtonPagination
        await super.waitBrowserToLoadPage(3000)
        let finalOrder = [];
        while (nextButton.isEnabled) {
            let nextBtnClassName = await this.nextButtonPagination.getAttribute("class")
            let RowValues = await this.genericColumnValueList(values);
            let lengthOfArray = RowValues.length;
            await super.waitBrowserToLoadPage(3000)
            for (let j = 0; j < lengthOfArray; j++) {
                finalOrder.push(await RowValues[j].getText())
            }
            await nextButton.scrollIntoView()
            await super.clickOn(nextButton)
            await super.waitBrowserToLoadPage(4000)
            if (nextBtnClassName.includes("disabled") || await this.activePaginationNum.getText() === pageNum) {
                break;
            }
        }
        return finalOrder;
    }

    //Clicks column title and collecting column data in Descending order
    async descending(locator, values, pageNum) {
        let tableValues = await this.storeReviewTable;
        await tableValues.waitForExist({timeout: 3000}, 'Error: Title did not appear before timeout')
        await expect(tableValues).toBeDisplayedInViewport();
        let columnBtn = await this.genericClickColumnTitle(locator);
        await super.clickOn(columnBtn);
        await super.waitBrowserToLoadPage(3000)
        let nextButton = await this.nextButtonPagination;
        let finalOrder = [];
        while (nextButton.isEnabled) {
            let nextBtnClassName = await this.nextButtonPagination.getAttribute("class")
            let RowValues = await this.genericColumnValueList(values);
            let lengthOfArray = RowValues.length;
            for (let j = 0; j < lengthOfArray; j++) {
                finalOrder.push(await RowValues[j].getText())
            }
            await nextButton.scrollIntoView()
            await super.clickOn(nextButton)
            await super.waitBrowserToLoadPage(3000)
            if (nextBtnClassName.includes("disabled") || await this.activePaginationNum.getText() === pageNum) {
                break;
            }
        }
        return finalOrder;
    }

    async getStoreReviewAllUIData(pageNum) {
        let table = await this.storeReviewTable;
        await table.waitForExist({timeout: 59000}, 'Error: UI table of data did not appear before timeout')
        await expect(table).toBeExisting();
        await browser.pause(4000)
        let columns = await this.firstRowValuesStoreReview
        let uiData = []
        let completionStatus = [];
        let reason = [];
        let deleteStatus = []
        let nextButton = await this.nextButtonPagination
        while (await nextButton.isEnabled) {
            let nextButtonClassName = await this.nextButtonPagination.getAttribute("class")
            let dataStatus = await this.getCompleteStatusList;
            let dataReason = await this.reasonColumnList;
            let dataDeleted = await this.getDeleteStatus
            for (columnIndex = 1; columnIndex <= await this.tableRowsStoreReview.length; columnIndex++) {
                let getAttr = await dataStatus[columnIndex - 1].getAttribute('ng-star-inserted hydrated');
                let statusDeleted = await dataDeleted[columnIndex - 1].getText();
                let getAttrReason = await dataReason[columnIndex - 1].getText();
                if (await getAttr === 'Y') {
                    completionStatus.push('Yes');

                } else if (await getAttr === 'N') {
                    completionStatus.push('No');

                } else {
                    completionStatus.push('No Response');
                }
                deleteStatus.push(statusDeleted);
                reason.push(getAttrReason)
                let uiRowData = [];
                // prints first 11 columns
                for (j = 1; j < columns.length - 4; j++) {
                    // ('+i+') column
                    // skips column 11 of pdf
                    if (j !== 11) {
                        let data = await this.tableNthColumns[j];
                        await uiRowData.push(await data.getText());
                    }
                }
                // Push completion status for this row
                uiRowData.push(completionStatus[completionStatus.length - 1]);
                // Push delete status for this row
                uiRowData.push(deleteStatus[deleteStatus.length - 1]);
                uiRowData.push(reason[reason.length - 1]);
                uiData.push(uiRowData);
            }
            await super.clickOn(nextButton)
            if (nextButtonClassName.includes("disabled") || (pageNum && await this.activePaginationNum.getText() === pageNum)) {
                break;
            }
        }
        return uiData
    }

    async getCommodityRecapAllUIData() {
        let table = await this.commodityRecapTable;
        await table.waitForExist({timeout: 79000}, 'Error: UI table of data did not appear before timeout')
        await super.waitBrowserToLoadPage(5000)
        await expect(table).toBeDisplayedInViewport();
        let columns = await this.tableColumnsCommodityRecap;
        let uiData = []
        let nextButton = await this.nextButtonPaginationCommodityRecap;
        while (nextButton.isEnabled) {
            let nextButtonClassName = await this.nextButtonPaginationCommodityRecap.getAttribute("class")
            for (columnIndex = 1; columnIndex <= await this.tableRowsCommodityRecap.length; columnIndex++) {
                let uiRowData = []
                for (rowIdx = 1; rowIdx < columns.length; rowIdx++) {
                    let data = await this.tableNthColumnsCommodityRecap[rowIdx];
                    uiRowData.push(await data.getText())
                }
                uiData.push(uiRowData)
            }
            await super.clickOn(nextButton)
            await super.waitBrowserToLoadPage(1000)
            if (nextButtonClassName.includes("disabled")) {
                break;
            }
        }
        return uiData
    }

    async getCommodityRecapNthPageUIData(pageNum) {
        let table = await this.commodityRecapTable;
        await table.waitForExist({timeout: 79000}, 'Error: UI table of data did not appear before timeout')
        await expect(table).toBeDisplayedInViewport();
        let columns = await this.tableColumnsCommodityRecap;
        let uiData = []
        let nextButton = await this.nextButtonPaginationCommodityRecap;
        while (nextButton.isEnabled) {
            let nextButtonClassName = await this.nextButtonPaginationCommodityRecap.getAttribute("class")
            for (columnIndex = 1; columnIndex <= await this.tableRowsCommodityRecap.length; columnIndex++) {
                let uiRowData = []
                for (rowIdx = 1; rowIdx < columns.length; rowIdx++) {
                    let data = await this.tableNthColumnsCommodityRecap[rowIdx];
                    await super.waitBrowserToLoadPage(1000)
                    uiRowData.push(await data.getText())
                }
                uiData.push(uiRowData)
            }
            await super.clickOn(nextButton)
            await super.waitBrowserToLoadPage(1000)
            if (nextButtonClassName.includes("disabled") || await (await this.activePaginationNumcommodityRecapTable).getText() === pageNum) {
                break;
            }
            await browser.keys('Escape');
        }
        return uiData
    }

    // select with dropdown by Text
    async setSingleValue(Locator,selectValue) {
        await this.genericClickDropdown(Locator);
        await super.waitBrowserToLoadPage(2000)
        let setText = $((this.inputFilterName).replace('selected', Locator));
        await setText.setValue(await selectValue)
        await super.waitBrowserToLoadPage(4000)
        let text = await this.dropdownList
        await super.waitBrowserToLoadPage(4000)
        for (let index = 0; index < text.length; index++) {
            let appList = await text[index]
            if (await (await text[index].getText()).includes(selectValue)) {
                await super.waitBrowserToLoadPage(3000)
                await appList.click();
                break;
            }
        }
        await browser.keys('Escape')
        !expect(super.doIsDisplay(this.dropdownList))
    }

    async validateCheckBoxesSelected(num) {
        let table = await this.tableStoreReview;
        await table.waitForExist({timeout: 79000}, 'Error: UI table of data did not appear before timeout')
        await expect(table).toBeDisplayedInViewport();
        let columns = await this.storeReviewCheckBoxList;
        let flag = false;
        for (let index = 0; index < columns.length - num; index++) {
            let getAttr = await columns[index].getAttribute("class")
            if (getAttr.includes("ui-state-active")) {
                await super.waitBrowserToLoadPage(2000);
                flag = true
            }
        }
        return flag
    }

    async getFirstRowValue() {
        await super.waitBrowserToLoadPage(2000)
        let rowList = [];
        let rows = await this.storeReviewFirstRowList;
        for (let index = 1; index < rows.length; index++) {
            rowList.push(await rows[index].getText())
        }
        return rowList;
    }

    // num => number of rows
    async getColumnArrayList(header) {
        await super.waitBrowserToLoadPage(2000);
        let headerData = await this.storeReviewTableHeaders;
        await super.waitBrowserToLoadPage(2000)
        let arrayList = await this.getArrayElementsText(headerData, headerData.length);
        let headerIndex = arrayList.indexOf(header);
        n = headerIndex + 1;
        let columnList = await this.nthColumnListStoreReview;
        return await this.getArrayElementsText(columnList);
    }

    async getColumnValue(key) {
        await super.waitBrowserToLoadPage(2000)
        let headerData = await this.commodityColumnHeaders;
        let header = []
        for (let i = 6; i < headerData.length; i++) {
            if (await headerData[i].getText() === key) {
                h = i + 1;
                let value = await this.commodityFirstRowValues;
                await super.waitBrowserToLoadPage(2000)
                let text = await value.getText();
                header.push(text)
                break;
            }
        }
        return header
    }

    async getHeaderStoreReview(key) {
        await super.waitBrowserToLoadPage(2000)
        let headerData = await this.storeReviewTableHeaders;
        let header = []
        for (let index = 0; index < headerData.length; index++) {
            if (await headerData[index].getText() === key) {
                columnIndex = index + 1;
                let value = await this.storeReviewColumns
                await super.waitBrowserToLoadPage(2000)
                let text = await value.getText()
                header.push(text)
                break;
            }
        }
        return header
    }

    // select with index of value
    async getRowNumber(header, RowList) {
        let table = await this.commodityRecapTable;
        await table.waitForExist({timeout: 50000}, 'Error: UI table did not appear before time out')
        await expect(table).toBeDisplayedInViewport()
        let headerData = await this.commodityRecapTableHeaders;
        await super.waitBrowserToLoadPage(2000)
        let arrayList = await this.getArrayElementsText(headerData);
        let headerIndex = arrayList.indexOf(header);
        n = headerIndex + 1;
        let rowValue = await this.nthCompleteCommodityRecapColumns;
        if (await rowValue.isClickable()) {
            await super.clickOn(rowValue)
            await super.waitBrowserToLoadPage(2000)
            let nextButton = await this.nextButtonPagination;
            let result = [];
            while (nextButton.isEnabled) {
                let nextButtonClassName = await this.nextButtonPagination.getAttribute("class")
                let text = await this.genericColumnValueList(RowList)
                let counter = 1;
                for (let i = 0; i < text.length; i++) {
                    let getAttr = await text[i].getAttribute('class')
                    if (getAttr === emData.ng_star_inserted) {
                        await super.waitBrowserToLoadPage(2000)
                        result.push(counter++)
                    }
                }
                await super.clickOn(nextButton)
                await super.waitBrowserToLoadPage(2000)
                if (nextButtonClassName.includes("disabled")) {
                    break;
                }
            }
            return result.length;
        }
        return 0;
    }

    // select with index of value
    async getDeletedRowNumber(header, RowList) {
        let table = await this.commodityRecapTable;
        await table.waitForExist({timeout: 50000}, 'Error: UI table did not appear before time out')
        await expect(table).toBeDisplayedInViewport()
        let headerData = await this.commodityRecapTableHeaders;
        await super.waitBrowserToLoadPage(2000)
        let arrayList = await this.getArrayElementsText(headerData);
        let headerIndex = arrayList.indexOf(header);
        n = headerIndex + 1;
        let rowValue = await this.nthCompleteCommodityRecapColumns;
        if (await rowValue.isClickable()) {
            await super.clickOn(rowValue)
            await super.waitBrowserToLoadPage(2000)
            let nextButton = await this.nextButtonPagination;
            let result = [];
            while (nextButton.isEnabled) {
                let nextButtonClassName = await this.nextButtonPagination.getAttribute("class")
                let text = await this.genericColumnValueList(RowList)
                let counter = 1;
                for (let i = 0; i < text.length; i++) {
                    let getAttr = await text[i].getAttribute('class')
                    if (getAttr === emData.deleted_ng_star_inserted) {
                        result.push(counter++)
                    }
                }
                await super.clickOn(nextButton)
                await super.waitBrowserToLoadPage(1000)
                if (nextButtonClassName.includes("disabled")) {
                    break;
                }
            }
            return result.length;
        }
        return 0;
    }

    /* Collects values from all pages for the column and returns an array*/
    async getStoreReviewColumnArrayList(header, pageNum) {
        let activePage = await this.activePaginationNumStoreReview;
        await activePage.waitForExist({timeout: 500000}, 'Error: active pagination button did not appear of time')
        let headerData = await this.storeReviewTableHeaders;
        await super.waitBrowserToLoadPage(4000)
        let arrayList = await this.getArrayElementsText(headerData, headerData.length);
        let headerIndex = arrayList.indexOf(header);
        n = headerIndex + 1;
        let columnTextList = []
        let columnList = await this.nthColumnListStoreReview;
        let nextButton = await this.nextButtonPagination;
        while (nextButton.isEnabled) {
            for (let rowNumber = 0; rowNumber < columnList.length; rowNumber++) {
                let text = await columnList[rowNumber].getText();
                columnTextList.push(text)
            }
            let nextButtonClassName = await this.nextButtonPagination.getAttribute("class")
            await super.clickOn(nextButton)
            await super.waitBrowserToLoadPage(1000)
            if (nextButtonClassName.includes("disabled") || await (this.activePaginationNumStoreReview.getText()) === pageNum) {
                break;
            }
        }
        return columnTextList;
    }

    async commodityRecapColumnValue(inputColumnIndex) {
        columnIndex = await inputColumnIndex;
        return this.nthColumnCommodityRecap;
    }

    async navigateWithHoverEventManager() {

        await this.waitBrowserToLoadPage(3000);
        await expect(await HomePage.dropdownSingleItem('Applications')).toEqual(true);
        await this.spfEventManagerDropDown.moveTo();
        await this.waitBrowserToLoadPage(1000);
        await this.adminMenuDropDown.moveTo();
        await this.waitBrowserToLoadPage(3000);
        await this.resetCompletionMenuDrownDown.moveTo();
        await this.waitBrowserToLoadPage(3000);
        await this.clickOn(this.resetCompletionMenuDrownDown);
    }

    async getTableHeaderIndex(eventManagerTableName, columnName) {
        let headers = [];
        let columnIdx = -1;
        if (eventManagerTableName === emData.storeReviewTableName) {
            headers = await this.tableHeaders;
        } else if (eventManagerTableName === emData.commodityRecapTableName) {
            headers = await this.commodityRecapTableHeaders;
        }
        for (let index = 0; index < headers.length; index++) {
            let str = await this.GetText(headers[index]);
            if (str.toString().search(columnName) >= 0) {
                columnIdx = index;
                break;
            }
        }
        return columnIdx;
    }

    async searchAttribute(locator, attributeName) {
        let attribute = (await locator.getAttribute("class")).toString();
        return attribute.search(attributeName) >= 0
    }

    async commodityRecapDivisionColumnCompare(division, altDivision) {

        let divElements = await this.commodityRecapColumnValue(await this.getTableHeaderIndex(emData.commodityRecapTableName, emData.ResetComplete_StoreReview_Div) + 1);
        return await this.divisionRestCompletionDivisionMatch(divElements, division, altDivision);
    }

    async storeReviewDivisionColumnCompare(division, altDivision) {

        let divElements = await this.storeReviewColumnValue((await this.getTableHeaderIndex(emData.storeReviewTableName, emData.ResetComplete_StoreReview_Div)) + 4);
        return await this.divisionRestCompletionDivisionMatch(divElements, division, altDivision);
    }

    async divisionRestCompletionDivisionMatch(divElements, division, altDivision) {
        let isMatch = true;
        for (let index = 0; index < divElements.length; index++) {
            let tableDiv = await this.GetText(divElements[index]);
            if (!(tableDiv === division || tableDiv === altDivision)) {
                isMatch = false;
                break;
            }
        }
        return isMatch;
    }

    async storeReviewColumnValue(inputColumnIndex) {

        columnIndex = await inputColumnIndex;
        return this.nthColumnStoreReview;
    }

    async getCommodityRecapTableFirstPage() {

        return super.arrayTo2DArraySkipFirstColumn(await this.commodityRecapTableElements, (await this.tableColumnCommodityRecap).length, 5);
    }

    async getStoreReviewTableFirstPage() {

        let firstRowBeforeClearFilters = await this.allRowsStoreReview;
        let rowBeforeClearFilters = await this.GetText(firstRowBeforeClearFilters[0]);
        let rowElementsBeforeClearFilter = rowBeforeClearFilters.toString().split(" ");
        //return rowElementsBeforeClearFilter[0];
        return super.arrayTo2DArraySkipFirstColumn(await this.tableRowsStoreReviewElements, (await this.tableStoreReviewColumns).length, 5);
    }
    async indexHeaderStoreReview(headerName) {
        let index = 0;
        let headers = await this.tableHeaders;
        for (; index < headers.length; index++) {
            let value = await this.GetText(headers[index]);
            let valueParsed = value.replace(/(\r\n1|\r1|\n1)/g, '').trim();
            if (valueParsed === headerName) {
                break;
            }
        }
        return index;
    }

    async firstRowResetIDDivStore() {

        let row = await this.getFirstRowValue();
        let preOffset = -1
        let postOffset = 2;
        let resetIdIndex = await this.indexHeaderStoreReview(emData.Reset_Id) + preOffset;
        let divIndex = await this.indexHeaderStoreReview(emData.ResetComplete_StoreReview_Div) + postOffset;
        let locIndex = await this.indexHeaderStoreReview(emData.ResetComplete_StoreReview_Loc) + postOffset;
        return row[resetIdIndex] + "|" + row[divIndex] + "|" + row[locIndex];
    }

    async resetCompletionDivisionDropDownMultiDivisionMatch(userRoleDivisions) {

        let isMatch = true;
        await this.clickOn(await this.divisionDropDown);
        let divisionElements = await this.divisionDropDownElements;
        await expect(divisionElements.length).toEqual(await userRoleDivisions.length);
        for (let index = 0; index < divisionElements.length; index++) {
            if (userRoleDivisions[index] !== await this.GetText(await divisionElements[index])) {
                isMatch = false;
                break;
            }
        }
        return isMatch;
    }


    async locatorIsHidden(locator) {
        let isHidden = false;
        if (await locator.getAttribute("hidden")) {
            isHidden = true;
        }
        return isHidden;
    }

    async getResetCompleteDropdownList(dropdownLocator, dropdownListLocatorName) {
        await this.genericClickDropdown(dropdownLocator);
        let dropdownList = await $$(this.resetDropDownListValues.replace('dropDownName-select', dropdownListLocatorName));
        return await this.getArrayElementsText(await dropdownList)
    }

    async clickResetCompleteDropdownCloseBtn(locatorName) {
        let closeOption = $(this.resetCompleteDropdownCloseBtn.replace('?dropdown', locatorName));
        await super.waitBrowserToLoadPage(2000)
        await super.clickOn(closeOption);
        await super.waitBrowserToLoadPage(2000)
    }

    async selectStoreReviewResetStatusDropdown(selectDropdownText) {
        let valueSelected = false;
        let dropdownList;
        try {
            let chevron = await this.resetStatusColumnFirstChevron;
            await super.waitBrowserToLoadPage(2000)
            if (await chevron.waitForDisplayed()) {
                await chevron.click();
            }
            await super.waitBrowserToLoadPage(2000)
            dropdownList = await this.resetStatusTableDropdownList;
            await super.waitBrowserToLoadPage(2000)
            if (await dropdownList.length > 0) {
                for (let dropdownValue = 0; dropdownValue <= await dropdownList.length; dropdownValue++) {
                    let dropdownSingleValue = await dropdownList[dropdownValue];
                    if (await dropdownList[dropdownValue].getText() === await selectDropdownText) {
                        await dropdownList[dropdownValue].scrollIntoView();
                        await super.waitBrowserToLoadPage(2000)
                        await dropdownSingleValue.click();
                        await super.waitBrowserToLoadPage(2000)
                        valueSelected = true;
                        break
                    }
                }
            }
            if (!valueSelected) {
                console.error('No different value found to select !')
            }
        } catch (error) {
            console.error('An error occurred: something went wrong in selecting value ! ', error)
        }
        return valueSelected;
    }


    async selectStoreReviewReasonDropdown(selectDropdownText) {
        let valueSelected = false;
        let reasonDropdownList;
        try {
            let chevron = await this.firstArrayElement();
            if (await chevron && await chevron.waitForDisplayed()) {
                await chevron.click();
            }
            reasonDropdownList = await this.resetStatusReasonColumnDropdownList;
            await super.waitBrowserToLoadPage(2000)
            if (await reasonDropdownList.length > 0) {
                for (let reasonValue = 0; reasonValue < await reasonDropdownList.length; reasonValue++) {
                    let dropdownSingleValue = await reasonDropdownList[reasonValue];
                    if (await reasonDropdownList[reasonValue].getText() === await selectDropdownText) {
                        await reasonDropdownList[reasonValue].scrollIntoView();
                        await super.waitBrowserToLoadPage(2000)
                        await dropdownSingleValue.click();
                        await super.waitBrowserToLoadPage(2000)
                        valueSelected = true;
                        break
                    }
                }
            }
            if (!valueSelected) {
                console.error('No different value found to select !')
            }
        } catch (error) {
            console.error('An error occurred: something went wrong in selecting value ! ', error)
        }
        return valueSelected;
    }

    async getStoreReviewTableDropdownListText(columnFirstChevronLocator, dropdownName) {

        let chevron = await columnFirstChevronLocator;
        await super.waitBrowserToLoadPage(2000);
        if (await chevron || await chevron.waitForDisplayed()) {
            await chevron.click();
        }
        let dropdownList;
        if (await dropdownName === 'Reset Status') {
            dropdownList = await this.resetStatusTableDropdownList;
        } else if (await dropdownName === 'Reset Status Reason') {
            dropdownList = await this.resetStatusTableDropdownList;
        } else {
            console.error('Error: dropdown name does not exist !')
        }
        return await this.getArrayElementsText(await dropdownList);
    }

    async updateReasonColumnDefaultSelectedDropdownValue( headerName, typeColumnName) {

        let reasonColumnFirstValueBefore;
        let storeReviewTableHeaderList = await this.headersStoreReviewTable;
        await super.waitBrowserToLoadPage(2000);
        let reasonHeaderIndex = await this.findIndexNumFromArray(await storeReviewTableHeaderList, headerName);
        columnIndex = await reasonHeaderIndex + 1;
        let columnList = await this.nthColumnStoreReview;
        await super.waitBrowserToLoadPage(2000);
        for (let column = 0; column<=columnList.length; column++) {
            if( await columnList[column].getText() === await typeColumnName) {
                rowIdx = column + 1;
                reasonColumnFirstValueBefore = await this.resetStatusReasonColumnDynamicValue.getText();
                await super.waitBrowserToLoadPage(2000);
                let chevList = await this.resetStatusReasonColumnDynamicChevron;
                let reasonDropdownList = await this.getStoreReviewTableDropdownListText(await chevList, 'Reset Status Reason');
                let reasonDropdownListClean = await this.removeElementByText(reasonDropdownList, reasonColumnFirstValueBefore);
                let reasonDropdownListCleanRandomValue = await this.getRandomArrayValue(await reasonDropdownListClean);
                await super.waitBrowserToLoadPage(2000);
                reasonDropdownList = await this.resetStatusReasonColumnDropdownList;
                await super.waitBrowserToLoadPage(2000);
                if (await reasonDropdownList.length > 0) {
                    for (let index = 0; index < await reasonDropdownList.length; index++) {
                        let dropdownSingleValue = await reasonDropdownList[index];
                        if (await reasonDropdownList[index].getText() === await reasonDropdownListCleanRandomValue) {
                            await reasonDropdownList[index].scrollIntoView();
                            await dropdownSingleValue.click();
                            await super.waitBrowserToLoadPage(2000);
                        }
                    }
                }
            }
            let reasonColumnFirstValueAfter = await this.resetStatusReasonColumnDynamicValue.getText();
            if (await reasonColumnFirstValueBefore !== reasonColumnFirstValueAfter) {
                await this.saveChangesBtn.click();
                await super.waitBrowserToLoadPage(2000);
                break;
            }
        }
        await browser.keys('Escape')
    }

    async resetCompleteElementIsClickable(locator) {
        let flag;
        const element = await locator;
        await super.waitBrowserToLoadPage(2000)
        const elementClassValue = await element.getAttribute('class');
        await super.waitBrowserToLoadPage(2000)
        if (await elementClassValue.includes('ng-dirty')) {
            flag = true
        } else {
            flag = false
        }
        return flag;
    }

    async firstArrayElement() {
        let elList = await this.resetStatusReasonColumnChevronList;
            if (await elList.length > 0 || await elList[0] !== '') {
                return elList[0];
            } else {
                console.error('Error: Element list is empty !')
                return null;
            }
        }

    async dynamicSelectResetStatusNotCompletedToCompleted( headerName, typeColumnName) {
        let storeReviewTableHeaderList = await this.headersStoreReviewTable;
        await super.waitBrowserToLoadPage(2000);
        let reasonHeaderIndex = await this.findIndexNumFromArray(await storeReviewTableHeaderList, headerName);
        columnIndex = await reasonHeaderIndex + 1;
        let columnList = await this.nthColumnStoreReview;
        await super.waitBrowserToLoadPage(2000);
        for (let column = 0; column < columnList.length; column++) {
            if (await columnList[column].getText() === typeColumnName) {
                rowIdx = column + 1;
                await super.waitBrowserToLoadPage(2000);
                let chevList = await this.resetStatusColumnDynamicChevronList;
                let resetStatusDropdownList = await this.getStoreReviewTableDropdownListText(await chevList, 'Reset Status');
                await super.waitBrowserToLoadPage(2000);
                resetStatusDropdownList = await this.resetStatusTableDropdownList;
                if (await resetStatusDropdownList.length > 0) {
                    for (let index = 0; index < await resetStatusDropdownList.length; index++) {
                        let dropdownSingleValue = await resetStatusDropdownList[index];
                        if (await resetStatusDropdownList[index].getText() === "Completed") {
                            await super.waitBrowserToLoadPage(2000);
                            await dropdownSingleValue.click();
                            await super.waitBrowserToLoadPage(2000);
                            let resetCompletedText = await (await this.resetStatusReasonColumnNonActiveValue).getText();
                            if(await resetCompletedText === 'Reset Completed'){
                                await this.saveChangesBtn.click();
                                await super.waitBrowserToLoadPage(2000);
                            }
                        }
                    }
                    break;
                } else{
                    console.error('Error: Reset Status dropdown list values did not loop !')
                }
            }
        }
        await browser.keys('Escape')
    }

    async dynamicSelectResetStatusNotCompletedToNoResponse( headerName, typeColumnName) {
        let storeReviewTableHeaderList = await this.headersStoreReviewTable;
        await super.waitBrowserToLoadPage(2000);
        let reasonHeaderIndex = await this.findIndexNumFromArray(storeReviewTableHeaderList, headerName);
        columnIndex = await reasonHeaderIndex + 1;
        let columnList = await this.nthColumnStoreReview;
        await super.waitBrowserToLoadPage(2000);
        for (let column = 0; column < columnList.length; column++) {
            if (await columnList[column].getText() === typeColumnName) {
                rowIdx = column + 1;
                await super.waitBrowserToLoadPage(2000);
                let chevList = await this.resetStatusColumnDynamicChevronList;
                let resetStatusDropdownList = await this.getStoreReviewTableDropdownListText(await chevList, 'Reset Status');
                await super.waitBrowserToLoadPage(2000);
                resetStatusDropdownList = await this.resetStatusTableDropdownList;
                if (await resetStatusDropdownList.length > 0) {
                    for (let index = 0; index < await resetStatusDropdownList.length; index++) {
                        let dropdownSingleValue = await resetStatusDropdownList[index];
                        if (await resetStatusDropdownList[index].getText() === "No Response") {
                            await super.waitBrowserToLoadPage(2000);
                            await dropdownSingleValue.click();
                            await super.waitBrowserToLoadPage(2000);
                            let resetCompletedText = await (await this.resetStatusReasonColumnNonActiveValue).getText();
                            if(await resetCompletedText === 'Available for reset'){
                                await this.saveChangesBtn.click();
                                await super.waitBrowserToLoadPage(2000);
                            }
                        }
                    }
                    break;
                } else{
                    console.error('Error: Reset Status dropdown list values did not loop !')
                }
            }
        }
        await browser.keys('Escape')
    }

    async dynamicSelectResetStatusDropdownKomNoResponseToNotCompleted( headerName, typeColumnName) {
        let storeReviewTableHeaderList = await this.headersStoreReviewTable;
        await super.waitBrowserToLoadPage(2000);
        let referenceHeaderIndex = await this.findIndexNumFromArray(storeReviewTableHeaderList, headerName);
        columnIndex = await referenceHeaderIndex + 1;
        let referenceColumnList = await this.nthColumnStoreReview;
        await super.waitBrowserToLoadPage(2000);
        for (let i = 0; i < referenceColumnList.length; i++) {
            if (await referenceColumnList[i].getText() === typeColumnName) {
                rowIdx = i + 1;
                let chevList = await this.resetStatusColumnDynamicChevronList;
                await super.waitBrowserToLoadPage(1000);
                let resetStatusDropdownListText = await this.getStoreReviewTableDropdownListText(await chevList, 'Reset Status');
                await super.waitBrowserToLoadPage(2000)
                let resetStatusDropdownList = await this.resetStatusTableDropdownList;
                if (resetStatusDropdownListText.length > 0) {
                    for (let index = 0; index < resetStatusDropdownListText.length; index++) {
                        let dropdownSingleValue = await resetStatusDropdownList[index];
                        if (await resetStatusDropdownList[index].getText() === "Not Completed") {
                            await super.waitBrowserToLoadPage(2000)
                            await dropdownSingleValue.click();
                            await super.waitBrowserToLoadPage(2000)
                            await this.resetStatusReasonColumnActiveValue.click();
                            await super.waitBrowserToLoadPage(2000)
                            await this.iterateDropdownListAndClickValue(await this.reasonDropdownColumnList, 'Store Manager Postponed')
                            await this.selectStoreReviewReasonDropdown('Store Manager Postponed')
                            await this.saveChangesBtn.click()
                            await super.waitBrowserToLoadPage(2000);
                            break;
                        }
                    }
                }
                break;
            }
        }
        await browser.keys('Escape');
    }

    async dynamicSelectResetStatusDropdownKomNoResponseToCompleted( headerName, typeColumnName) {
        let storeReviewTableHeaderList = await this.headersStoreReviewTable;
        await super.waitBrowserToLoadPage(2000);
        let referenceHeaderIndex = await this.findIndexNumFromArray(storeReviewTableHeaderList, headerName);
        columnIndex = await referenceHeaderIndex + 1;
        let referenceColumnList = await this.nthColumnStoreReview;
        await super.waitBrowserToLoadPage(2000);
        for (let column = 0; column < referenceColumnList.length; column++) {
            if (await referenceColumnList[column].getText() === typeColumnName) {
                await super.waitBrowserToLoadPage(2000);
                rowIdx = column + 1;
                let chevList = await this.resetStatusColumnDynamicChevronList;
                let resetStatusDropdownListText = await this.getStoreReviewTableDropdownListText(await chevList, 'Reset Status');
                await super.waitBrowserToLoadPage(2000)
                let resetStatusDropdownList = await this.resetStatusTableDropdownList;
                if (resetStatusDropdownListText.length > 0) {
                    for (let index = 0; index < resetStatusDropdownListText.length; index++) {
                        let dropdownSingleValue = await resetStatusDropdownList[index];
                        if (await resetStatusDropdownList[index].getText() === "Completed") {
                            await super.waitBrowserToLoadPage(2000)
                            await dropdownSingleValue.click();
                            await super.waitBrowserToLoadPage(2000);
                            columnIndex = rowIdx;
                            let reasonColumnText = await (await this.reasonColumnDynamicValue).getText();
                            if (await reasonColumnText === 'Reset Completed') {
                                await this.saveChangesBtn.click()
                                await super.waitBrowserToLoadPage(2000);
                                break;
                            }
                            break;
                        }
                    }
                }
                break;
            }
        }
        await browser.keys('Escape');
    }

    async validateChangeSavedMessage(){
        let changeSavedConfirmationMessage = await this.changesSavedMessage.getText();
        return changeSavedConfirmationMessage === emData.changedSavedMessage
    }

    async validateInactiveResetStatusColumnValues(headerName, typeColumnName) {
        let storeReviewTableHeaderList = await this.headersStoreReviewTable;
        await super.waitBrowserToLoadPage(2000);
        let referenceHeaderIndex = await this.findIndexNumFromArray(storeReviewTableHeaderList , headerName);
        columnIndex = await referenceHeaderIndex + 1;
        let typeColumnList = await this.nthColumnStoreReview;
        await super.waitBrowserToLoadPage(2000);
        for (let column = 0; column < typeColumnList.length; column++) {
            let text = await typeColumnList[column].getText()
            if (await text === typeColumnName) {
                await super.waitBrowserToLoadPage(2000);
                rowIdx = column + 1;
                let flag;
                let unClickableResetStatusList = await this.resetStatusReasonColumnUnClickableList;
                    const classAttributed = await unClickableResetStatusList.getAttribute('class');
                    if(await classAttributed.includes('palette-negative')  ){
                        flag = true
                    }else{
                        console.error("Element is not disabled !")
                        flag = false
                    }
                    return flag;
                }
        }
        await browser.keys('Escape');
        return false;
    }

    async returnStoreReviewColumnIndex(columnName, incrementIndex){
        let resetStatusColumnIndex = await this.findIndexNumFromArray(await this.storeReviewTableHeaders, columnName);
        columnIndex = await (resetStatusColumnIndex + incrementIndex);
        return this.storeReviewTableNthColumns;
    }

    async clearResetStatusAndSelect(valueToBeSelected){
        await this.clickResetCompleteDropdownCloseBtn('resetCompleteList');
        await this.selectSingleValue('selectedCompletionType', 'scheduleType', 'resetCompleteList', await valueToBeSelected);
        await eventMaintenancePage.clickOn(await this.searchBtn);
    }

    async  validateDropdownText(element, expectedValues, label) {
        const actualText = await super.GetText(element);
        console.log(`${label} Text Retrieved:`, actualText); // Debugging log
        await expect(await expectedValues.some(val => actualText.includes(val))).toBe(true);
    }

    async getUniqueValuesFromSelectedRadios(mainHeader, subHeader) {
        console.log(`Looking for Main Header: ${mainHeader}`);
        console.log(`Looking for Sub Header: ${subHeader}`);
        // Select all main headers
        const mainHeaders = await $$('p-table[id="storeReviewTable"] table[id="pn_id_2-table"]>thead>tr>th');
        console.log(`Found ${mainHeaders.length} main headers.`);
        // Find index of the main header
        let mainHeaderIndex = -1;
        for (let i = 0; i < mainHeaders.length; i++) {
            let text = await mainHeaders[i].getText();
            console.log(`Main Header [${i + 1}]: ${text.trim()}`);
            if (text.trim() === mainHeader) {
                mainHeaderIndex = i + 1; // Adjust for nth-child indexing
                break;
            }
        }
        if (mainHeaderIndex === -1) {
            throw new Error(`❌ Main header '${mainHeader}' not found.`);
        }
        console.log(`Main Header '${mainHeader}' found at index: ${mainHeaderIndex}`);
        // Select all sub-headers
        const subHeaders = await $$(`#pn_id_2-table > thead > tr:nth-child(2) > th`);
        console.log(`Found ${subHeaders.length} sub-headers.`);
        // Find index of the sub-header under the main header
        let subHeaderIndex = -1;
        for (let i = 0; i < subHeaders.length; i++) {
            let text = await subHeaders[i].getText();
            console.log(`Sub Header [${i + 1}]: ${text.trim()}`);
            if (text.trim() === subHeader) {
                subHeaderIndex = i + 2; // Adjust for nth-child indexing
                break;
            }
        }
        if (subHeaderIndex === -1) {
            throw new Error(`❌ Sub-header '${subHeader}' not found.`);
        }
        console.log(`✅ Sub Header '${subHeader}' found at index: ${subHeaderIndex}`);
        // Select all radio buttons under the identified column
        const radioButtons = await $$(`p-table#storeReviewTable tbody>tr>td:nth-child(${subHeaderIndex})>kds-radio`);
        console.log(`Found ${radioButtons.length} radio buttons under '${subHeader}'.`);
        // Store unique values from selected radio buttons (those without a "value" attribute)
        const uniqueValues = new Set();
        for (let i = 0; i < radioButtons.length; i++) {
            let radio = radioButtons[i];
            let value = await radio.getAttribute('value');
            console.log(`Radio Button [${i + 1}] - value attribute: ${value}`);
            if (!value) { // Only add radios that do NOT have the "value" attribute (i.e., clicked/selected ones)
                let text = await radio.getText();
                console.log(`Selected Radio Button Text: ${text.trim()}`);
                uniqueValues.add(text.trim());
            }
        }
        console.log(`Unique Selected Values: ${Array.from(uniqueValues)}`);
        return Array.from(uniqueValues); // Convert Set to Array and return
    }
        
    async getUniqueSelectedValues(subHeaderText) {
        console.log(`Checking for sub-header text: ${subHeaderText}`);
        // Get all radio buttons
        const radioButtons = await $$('p-table#storeReviewTable tbody > tr > td > kds-radio');
        console.log(`Found ${radioButtons.length} radio buttons.`);
        const uniqueValues = new Set();
        for (let radio of radioButtons) {
            // Find the closest parent <td> using `..`
            const cellTextElement = await radio.$('..');
            if (!cellTextElement || !(await cellTextElement.isExisting())) {
                console.log(`⚠️ No parent <td> found for radio button. Skipping...`);
                continue;
            }
            //Get text from <td>
            const cellText = await cellTextElement.getText();
            console.log(`Found cell text: "${cellText}"`);
            //Check if this is the correct column (e.g., "No Response")
            if (cellText.trim() !== subHeaderText) {
                console.log(`⏭Skipping: Not a "${subHeaderText}" column.`);
                continue;
            }
            //Check if the radio button has the 'value' attribute (unselected ones have it)
            const valueAttr = await radio.getAttribute('value');
            console.log(`Radio Button - value attribute: ${valueAttr}`);
    
            //Only process **selected** radio buttons (ones **without** 'value' attribute)
            if (!valueAttr) {
                console.log(`Selected Radio Button Found: "${cellText.trim()}"`);
                uniqueValues.add(cellText.trim());
            }
        }
        console.log(`Unique Selected Values: ${Array.from(uniqueValues)}`);
        return Array.from(uniqueValues);
    }

}

module.exports = new ResetCompletionPage()
