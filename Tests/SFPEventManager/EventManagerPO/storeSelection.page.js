const Page = require('../../../GlobalObjectRepository/page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
let index
/**
 * sub page containing specific selectors and methods for Create SFP EVent page
 */
class StoreSelection extends Page {

    get selectCustomGroups()        { return $('div[aria-label="Select Custom Group(s)"]') }
    get selectSchedule()            { return $('div[aria-label="Select Schedule"]') }
    get selectCustomGroupsList()    { return $$('.menu-item.ers-tree-list-parent') }
    get selectScheduleList()        { return $$('.menu-item.ers-tree-list-parent') }
    get selectButton()              { return $('#customGroupForm > ers-button > button >span') }
    get saveStoreSchedule()         { return $('kds-button[aria-label="Save Store Schedule(s)"]') }
    get linkSubModule()             { return $('#page > app-breadcrumb > div > ol > li:nth-child(4) > a') }
    get linkCreatSfpRefreshEvent()  { return $('#page > app-breadcrumb > div > ol > li:nth-child(3) > a')}
    get plusExpand()                { return $$('div > div.panel-heading > h4 > p') }
    get checkBox()                  { return $$('ui-chkbox-box ui-widget ui-corner-all ui-state-default') }
    get storeCheckBoxList()         { return $$('div>p-tristatecheckbox > label') }
    get saveSchedule()              { return $('kds-button[aria-label="Save Schedule"]') }
    get saveGoToStore()             { return $('kds-button[aria-label="Done, go to Store Selection"]') }
    get storeWeekSchedule()         { return $('div#page tbody tr:nth-child(1) td:nth-child(1)') }
    get storeWeekSchedule2()         { return $('div#page tbody tr:nth-child(2) td:nth-child(1)') }
    get storeCheckBox()             { return $$('div> p-tristatecheckbox > div > div:nth-child(2)')}
    get plusLocatorList()           { return $$('div>div>div.panel-heading>h4>i') }
    get storeCheckBoxSecondTableList()        { return $$('div.panel-group > div:nth-child(2) > div.panel-body.collapse.in > div > div > div.panel-body.collapse.in > div>p-tristatecheckbox>div+label') }
    get storeCheckBoxThirdTableList()         { return $$('div.panel-body.collapse.in > div > div:nth-child(3)>div>div>p-tristatecheckbox#divisions>div+label') }
    get goToStoreSelection()                    { return $('kds-button[aria-label="Done, go to Store Selection"]')}
    get storeSelectionPage()                    { return $('div>div>div>ul>li:nth-child(3)>a')}
    get storeSelectionAlertMessageCapital()     { return $$('#mx-Modal > mx-modal-header > header > div > span')}
    get storeSelectionAlertMessage()            { return $$('#mx-modal-id > div')}
    get storeSelectionAlertOKBtn()              { return $('#store-selection-no-schedule-alert-overlay>div>div>mx-modal-footer>div>div+div')}
    get storeSelectionNoStoreSelectedTxt()      { return $('p-toastitem > div > div > div > div.p-toast-detail')}
    get noStoreSelectedEventRefreshTxt()        { return $('p-toast > div > p-toastitem > div > div > div > div.p-toast-detail')}
    get tableFirstRow()                         { return $('tbody[class="ui-table-tbody"]>tr:nth-child(1)') }
    get divStoreVisible()                       { return $('div.panel-body.collapse.in > div > div > div.panel-body.collapse.in')}
    get storeCheckBoxthirdTableList()           { return (`div>div:nth-child(index) > div>div>div>div.panel-body>div>p-tristatecheckbox div+label`) }
    get goToScheduleSelectionPage()             { return $('div>div>div>ul>li:nth-child(2)>a')}
    get goToAddEventDetail()                    { return $('div>div>div>ul>li:nth-child(1)>a')}
    get storeSelectionCheckBoxList()            { return $$('#divisions > div > div.p-checkbox-box')}
    get storeSelectionCheckBoxTextList()        {return $$('p-tristatecheckbox[id="divisions"]>div+label')}
    get storeSelectionCheckBoxHighLightList()   {return $$('p-tristatecheckbox[id="divisions"]>div>div+div')}
    get storeNumberBoxList()                 { return $$('div.p-element.checkbox_block')}
    get storeNumberList()                    { return $$('div.p-element.checkbox_block>p-tristatecheckbox[id="divisions"]>div+label')}


    async selectDropdown(item, value) {
        let locator;
        let dropDown;
        switch (item) {
            case 'CustomGroup':
                locator = await this.selectCustomGroups;
                break;
            case 'Schedule':
                locator = await this.selectSchedule;
                break;
        }
        await super.clickOn(locator)
        await super.waitBrowserToLoadPage(2000)
        switch (item) {
            case 'CustomGroup':
                dropDown = await this.selectCustomGroupsList
                break;
            case 'Schedule':
                dropDown = await this.selectScheduleList
                break;
        }
        for (let i = 0; i < dropDown.length; i++) {
            let dropdownList = dropDown[i]
            if (await dropDown[i].getText() === value) {
                await dropDown[i].scrollIntoView()
                await dropdownList.click();
                break;
            }
        }
    }

    async clickPlusText(value) {
        await super.waitBrowserToLoadPage(6000)
        let checkBoxText = await this.plusExpand
        for (let i = 0; i < checkBoxText.length; i++) {
            let Text = checkBoxText[i]
            if (await checkBoxText[i].getText() === value.replace(',', ' Year')) {
                await super.clickOn(Text);
                break;
            }
        }
    }

    async selectStoreList(){
        let checkBox = await this.storeCheckBox
        let storeName
        await super.waitBrowserToLoadPage(3000)
        for( let j=0;j<=checkBox.length-1;j++){
            let data = await this.storeCheckBoxList
            let getAttrContains = await checkBox[j].getAttribute("class")
            if(!getAttrContains.includes("disabled")){
                storeName =await data[j].getText()
                await super.clickOn(checkBox[j])
                break
            }
        }
        return storeName
    }

    async clickCheckBoxText(value) {
        await super.waitBrowserToLoadPage(2000)
        await (await this.divStoreVisible).waitForExist({timeout:59000})
        let checkBoxText = await this.storeCheckBoxList
        for (let i = 0; i < checkBoxText.length; i++) {
            let Text = await checkBoxText[i]
            if (await checkBoxText[i].getText() === await value) {
                await checkBoxText[i].waitForDisplayed({timeout: 59000}, 'Error: store did not appear before timeout')
                await super.clickOn(Text);
                break;
            }
        }
    }

    async waitForPageLoad(element, value) {
        await browser.waitUntil(
            async () => (await element.getText()) === value,
             
            {
                timeout: 50000,
                timeoutMsg: 'expected text to be different after 5s'
            }
        )
    }

    async arrayLength(array){
        if(await array === undefined || await array.length === 0){
            return 0;
        }else{
            return await array.length;
        }
    }

    async clickSecondScheduleStoreCheckbox(value) {
        await super.waitBrowserToLoadPage(2000)
        await (await this.divStoreVisible).waitForExist({timeout:59000})
        let checkBoxText = await this.storeCheckBoxSecondTableList;
        await super.waitBrowserToLoadPage(4000)
        for (let i = 0; i < checkBoxText.length; i++) {
            let Text = checkBoxText[i]
            await super.waitBrowserToLoadPage(1000)
            if (await checkBoxText[i].getText() === value) {
                await checkBoxText[i].isDisplayed()
                await super.clickOn(Text);
                break;
            }
        }
    }

    async clickSecondSchedulethirdCheckbox(value) {
        await super.waitBrowserToLoadPage(2000)
        await (await this.divStoreVisible).waitForExist({timeout:59000})
        let checkBoxText = await this.storeCheckBoxThirdTableList;
        await super.waitBrowserToLoadPage(4000)
        for (let i = 0; i < checkBoxText.length; i++) {
            let Text = checkBoxText[i]
            await super.waitBrowserToLoadPage(1000)
            if (await checkBoxText[i].getText() === value) {
                await checkBoxText[i].isDisplayed()
                await super.clickOn(Text);
                break;
            }
        }
    }

    async clickScheduleStoreCheckbox(value,week) {
        await super.waitBrowserToLoadPage(3000)
        let checkBoxText = await $$((this.storeCheckBoxthirdTableList).replace('index',week));
        for (let i = 0; i < checkBoxText.length; i++) {
            let Text = checkBoxText[i]
            if (await checkBoxText[i].getText() === value) {
                await checkBoxText[i].isDisplayed()
                await super.clickOn(Text);
                break;
            }
        }
    }

    async saveGoToStoreSelection () {
        await super.waitBrowserToLoadPage(10000);
        await super.clickOn(this.goToStoreSelection)
        await this.linkSubModule.waitForExist({timeout: 50000}, 'Error: Event SChedule page did not appear before timeout')
        await super.waitForPageLoad(this.linkSubModule, "Store Selection")

    }

    async storeSelection(dbScheduledStores,index,week){
        //make querry generic pass param to call schedule store or remodel store
        await this.waitForPageLoad(this.linkSubModule, emData.Store_Selection);
        let storePage = await this.linkSubModule.getText()
        await expect(storePage).toEqual(emData.Store_Selection)
        
        await super.waitBrowserToLoadPage(3000);
        await this.clickScheduleStoreCheckbox(await dbScheduledStores[index], week);
        await super.waitBrowserToLoadPage(2000)
    }

    async saveStoreScheduleButton(){
        await super.waitBrowserToLoadPage(4000)
       // await super.clickOn(await this.saveStoreSchedule)
        await (await this.saveStoreSchedule).click()
    }

    async selectStoresOfCorpEventSchedules(weekText, division, dbScheduledStores) {
        await this.clickPlusText(weekText);
        await this.clickPlusText(division);
        await this.clickCheckBoxText(dbScheduledStores);
        await super.waitBrowserToLoadPage(1000)
        await this.clickPlusText(weekText);
    }

    async clickRandomEnabledStoreCheckboxes(minCount = 1, maxCount = 5){
        let checkboxList = await this.storeSelectionCheckBoxList;
        const enabledCheckboxes = [];
        for(let index=0; index<checkboxList.length; index++){
            const checkbox = checkboxList[index];
            const classAttribute = await checkbox.getAttribute('class');
            if(!classAttribute.includes('p-disabled')){
                await (enabledCheckboxes.push(await checkbox))
            }
        }
        await super.waitBrowserToLoadPage(2000)
        const availableCount = enabledCheckboxes.length;
        if(await availableCount > 0){
        const count = Math.floor(Math.random() * (maxCount - minCount + 1)) + minCount;
        const shuffleCheckBoxList = await enabledCheckboxes.sort(() => 0.5 - Math.random());
        let checkBoxesToClick = await shuffleCheckBoxList.slice(0, count);
                for( const checkbox of checkBoxesToClick){
                    await checkbox.click();
                }
    }else{
            console.error("No enabled checkboxes found");
        }
    }

    async generateTwoIntParams(){
        const min = 2;
        const max = 80;
        const firstParam = Math.floor(Math.random() * await (max - min - 10) + min);
        const secondParam = firstParam + 3;
        return [firstParam, secondParam];
    }

    async returnSelectedStores(){
        let selectedStores = [];
        const selectList = await this.storeSelectionCheckBoxTextList;
        for(let index=0; index<selectList.length;index++){
            const selected = await selectList[index].getAttribute('class');
            if ( await selected.includes('active')){
                selectedStores.push(await selectList[index].getText())
            }
        }
        return selectedStores;
    }

    async genericReturnSelectedStores(){
        let selectedStores = [];
        const selectList = await this.storeSelectionCheckBoxTextList;
        for(let index=0; index<selectList.length;index++){
            const selected = await selectList[index].getAttribute('class');
            if ( await selected.includes('active')){
                selectedStores.push(await selectList[index].getText())
            }
        }
        return selectedStores;
    }

}
module.exports = new StoreSelection();
