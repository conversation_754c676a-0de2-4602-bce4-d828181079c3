const Page = require('../../../GlobalObjectRepository/page');

let colIndex, rowIndex;
class CustomStoreGroupPage extends Page{

    get customGroupTable()                            { return $('[id^="pn_id_"][id$="-table"]') }
    get storeGroupManagementText()                    { return $('app-store-group > div > div > div > p')}
    get createNewGroupText()                          { return $('app-csg-panel > p-sidebar > div > div> span')}
    get storeGroupHeaderList2()                       { return $$('class="p-datatable-thead"')}
    get storeGroupHeaderList()                        { return $$('thead[class="p-datatable-thead"]>tr:nth-child(1)>th')}
    get divisionDropdownDefaultSelectedValue()        { return $('mx-single-select[id="divisionSelectDropdown"]>div>kds-label+div')}
    get divisionDropdownClearValueIcon()              { return $('mx-single-select[id="divisionSelectDropdown"]>div>kds-label+div>kds-icon-close')}
    get customGroupMenuDrownDown()                    {return $(`#headerWrapper > * a[href='/EventMaintenance/storeGroupManagement']`)}
    get storeGroupSingleColumnHeader()                { return ('thead>tr>th[id="@headerName"]>p-sorticon')}
    get storeGroupInputList()                         { return $$('#filter-row > th> p-columnfilter > div > p-columnfilterformelement > input"')}
    get storeGroupNthColumnList()                     { return $$(`tbody>tr>td:nth-child(${colIndex})`)}
    get storeGroupNthColumnRowList()                   { return $$(`tbody>tr:nth-child(${colIndex})>td`)}
    get individualStoreGroupHeader()                  { return $(`thead[class="p-datatable-thead"]>tr>th[id="@columnId"]`)}
    get storeGroupBreadCrumb()                        { return $(`#page > app-breadcrumb > div > ol > li:nth-child(3) > a`)}
    get createNewGroupButton()                        { return $('kds-button[id="create-button-primary"] > button')}
    get groupNameCreateInputBox()                     { return $('mx-input-text-box[label="Group Name"]>div>mx-none-masked-input > div > kds-input > input')}
    get groupNameInputBox()                           { return $('mx-input-text-box[label="Group Name"]>div>mx-none-masked-input > div > kds-input > input')}
    get groupNameTableInputBox()                      { return $('#filter-row > th:nth-child(2) > p-columnfilter > div > p-columnfilterformelement > input')}
    get selectDivisionDropdown()                      { return $('mx-single-select[label="Division"]>div>div>span')}
    get divisionDropdownList()                        { return $$('mx-single-select[label="Division"] mx-single-select-list-item>div>li')}
    get selectedStoresNumberText()                    { return $(`p-sidebar > div > div.p-sidebar-content > div > div.ng-star-inserted > div:nth-child(1)`)}
    get storeNumberList()                             { return $$('#page > content > div > app-store-group > div > app-csg-panel > p-sidebar > div > div> div > div> div> div')}
    get storeNumberCheckBoxList()                     { return $$('div>p-checkbox')}
    get storeNumberCheckBoxHighLightList()            { return $$('p-sidebar > div > div > div > div.ng-star-inserted > div > div > p-checkbox > div>div+div')}
    get storeNumberCheckBoxTextList()                 { return $$('p-sidebar > div > div > div > div > div> div')}
    get cancelButton()                                { return $('p-sidebar>div>div>div>kds-button:nth-child(1)>button')}
    get submitButton()                                { return $('p-sidebar>div>div>div>kds-button:nth-child(2)>button')}
    get submitCloseButtonList()                       { return $$('p-sidebar>div>div>div>kds-button>button')}
    get closeSlideOutPanelButton()                    { return $('p-sidebar > div > div > button')}
    get slideOutCancelButton()                        { return $('app-store-group > div > app-csg-panel > p-sidebar > div > div> button > timesicon')}
    get divisionDropdownClearButton()                 { return $('mx-single-select > div > div > kds-icon-close > kds-icon > svg')}
    get groupAndDivisionMustBeSelectedWarningMessage() { return $$('p-sidebar > div > div> div > div > div > kds-message')}
    get selectStoreWarningMessage()                   { return $('app-csg-panel > p-sidebar > div > div > div > div > kds-message > kds-text>span')}
    get storeNotSelectedWarningMessage()              { return $('p-sidebar > div > div > div > div.ng-star-inserted > kds-message > kds-text > span')}
    get selectAllStoresCheckBox()                     { return $('p-sidebar > div > div> div > div> div > p-checkbox')}
    get noStoresMatchingMessage()                     { return $('p-sidebar > div > div > div > div.ng-star-inserted > div:nth-child(2) > kds-message > kds-text')}
    get linkSubModule()                               {return $('div[class="row ng-star-inserted"]>ol>li:nth-child(3)>a')}
    get firstElementOnTopRow()                        {return $(`tbody>tr:nth-child(1)>td:nth-child(${colIndex})`)}
    get firstRow()                                    {return $(`tbody > tr:nth-child(1)`)}
    get customGroupRowList()                            {return $$(`tbody > tr`)}
    get searchByGroupNameInput()                      {return $(`#filter-row>th:nth-child(2)>p-columnfilter>div>p-columnfilterformelement>input`)}
    get searchByStoreNumberInput()                    {return $(`#filter-row>th:nth-child(5)>p-columnfilter>div>p-columnfilterformelement>input`)}
    get searchByNameInput()                           {return $(`#filter-row>th:nth-child(6)>p-columnfilter>div>p-columnfilterformelement>input`)}
    get genericSearchInputBox()                       {return $(`#filter-row>th:nth-child(${colIndex})>p-columnfilter>div>p-columnfilterformelement>input`)}
    get customGroupNthSearchInputBox()                {return $(`#filter-row>th:nth-child(${colIndex})>p-columnfilter>div>p-columnfilterformelement>input`)}
    get clearCustomGroupNthSearchInputBoxButton()     {return $(`#filter-row > th:nth-child(${colIndex}) > p-columnfilter > div > button`)}
    get searchByInput()                               {return $(`#filter-row>th:nth-child(${colIndex})>p-columnfilter>div>p-columnfilterformelement>input`)}
    get divisionSearchByInput()                       {return $(`body > div > div > div > div> div > input`)}
    get clearSearchInputButton()                      {return $(`#filter-row>th:nth-child(${colIndex})>p-columnfilter>div>button`)}
    get customStoreChevronDownIcon()                  {return $(`p-paginator > div > p-dropdown > div > div.p-dropdown-trigger > chevrondownicon > svg`)}
    get customGroupPageDropdownList()                 {return $$('p-dropdownitem[class="p-element ng-star-inserted"]')}
    get customGroupFooterString()                     {return $(`p-paginator > div > span.p-paginator-current.ng-star-inserted`)}
    get customGroupTopEditButton()                    {return $(`tbody > tr:nth-child(1)  #editRowButton>button>kds-icon-edit>kds-icon>svg`)}
    get customGroupEditList()                         {return $$(`kds-button[id="editRowButton"] > button> kds-icon-edit > kds-icon > svg`)}
    get customGroupDeleteList()                       {return $$(`kds-button[id="deleteRowButton"] > button > kds-icon-trash`)}
    get customGroupTopDeleteButton()                  {return $(`tbody > tr:nth-child(1) kds-button[id="deleteRowButton"] > button > kds-icon-trash`)}
    get noKeepItButton()                              { return $('kds-button[id="deleteModalFooter-secondary-btn"]>button')}
    get yesDeleteItButton()                           { return $('kds-button[id="deleteModalFooter-primary-btn"]>button')}
    get storeSelectionSelectScheduleDropdown()        { return $('#custom-store-group-schedule-selector > div > div> span') }
    get storeSelectionSelectCustomGroupDropdown()     { return $('mx-single-select[id="custom-store-group-selector"]') }
    get storeSelectionCustomGroupDropdownList()       { return $$('#custom-store-group-selector>div>div>mx-single-select-list>ul>div>mx-single-select-list-item>div>li>label') }
    get storeSelectionSelectScheduleDropdownValue()   { return $('#custom-store-group-schedule-selector > div > div.mx-single-select-container.mx-dropdown-list.is-compact > mx-single-select-list > ul > div > mx-single-select-list-item > div > li > label') }
    get groupCategoryDropdown()                       { return $('p-dropdown[id="categoryDropdown"]>div>span') }
    get groupCategoryDropdownList()                   { return $$('div[id="kds-Portal-toast"]+div+div ul>p-dropdownitem>li') }
    get groupCategoryDropdownDisabledList()           { return $$('div[id="kds-Portal-toast"]+div+div ul>p-dropdownitem> li>div') }
    get groupCategoryErrorMessage()                   { return $('p-sidebar > div > div > div > div:nth-child(3) > div > kds-message > kds-text>span') }
    get divisionFilterDropdown()                      { return $('tr[id="filter-row"]  p-columnfilter[field="division"] > div p-multiselect > div > div > div') }
    get categoryFilterDropdown()                      { return $('tr[id="filter-row"]  p-columnfilter[field="category"] > div p-multiselect > div > div > div') }
    get customGroupDivisionDropdownList()             { return $$('div[id="kds-Portal-toast"] +div ul>p-multiselectitem>li') }
    get customGroupDivisionDropdownCheckBoxList()     { return $$('div[id="kds-Portal-toast"] +div ul>p-multiselectitem>li>div>div.p-checkbox-box') }
    get customGroupDivisionClickAllCheckBox()         { return $('div[id="kds-Portal-toast"]+div>div>div>div>div>div+div[role="checkbox"]') }
    get customGroupCategoryDropdownList()             { return $$('div[id="kds-Portal-toast"]+div ul > p-multiselectitem > li') }
    get customGroupDivisionInputBox()                 { return $('div[id="kds-Portal-toast"]+div>div>div>div>div>input') }
    get divisionCloseDropdownButton()                 { return $('body>div>div>div>div>button>timesicon') }
    get customGroupExpandButtonList()                 { return $$('button[id="expandRowButton"]') }
    get customGroupTableTopExpandButton()             { return $('tr[class="table-row-data ng-star-inserted"]:nth-child(1)>td>button[id="expandRowButton"]') }
    get customGroupTableStoreList()                   { return $$('tr:nth-child(4) > td.loc-list>kds-tag') }
    get groupNameExistedMessage()                     { return $('mx-input-text-box[id="groupNameInput"]+kds-message')}
    get customGroupCreatedMessage()                   { return $('div[id="kds-Portal-toast"] section>p')}
    get customGroupTableStoreNumberString()           { return $('tbody > tr > td.loc-list') }
    get editTopRowButton()                            { return $('tr:nth-child(1) kds-button[id="editRowButton"]> button> kds-icon-edit > kds-icon > svg') }
    get defaultDivisionSelectValue()                  { return $('mx-single-select[id="divisionSelectDropdown"] > div > div[aria-label="Division"]') }
    get deleteStoreGroupModalText()                   { return $('mx-Modal[id="deleteStoreGroupModal"]>div>div>mx-modal-header') }
    get deleteStoreModalFooterButtonList()            { return $$('mx-Modal[id="deleteStoreGroupModal"]>div>div>mx-modal-footer kds-button button') }
    get closeStoreDeleteModalButton()                 { return $('#mx-Modal > mx-modal-header > header > div > button > kds-icon-close > kds-icon > svg') }
    get deleteTopRowButton()                          { return $('tr:nth-child(1) kds-button[id="deleteRowButton"]> button > kds-icon-trash > kds-icon > svg') }
    get customGroupNextButtonPagination()             { return $('p-paginator[class="p-element ng-star-inserted"]>div>button[class="p-ripple p-element p-paginator-next p-paginator-element p-link"]') }
    get customGroupActivePaginationNum()              {return $('p-paginator > div > span.p-paginator-pages.ng-star-inserted > button.p-ripple.p-element.p-paginator-page.p-paginator-element.p-link.ng-star-inserted.p-highlight')}
    get customGroupSingleTableRowList()               {return $$('p-table[datakey="groupId"]>div tbody > tr:nth-child(1)> td')}
    get customGroupExpandButton()                     {return $('tbody > tr:nth-child(1) > td.w-1')}
    get customGroupTableNthColumns()                  { return $(`div>app-comparison-name-report-table>div>p-table>div>div>table>tbody>tr:nth-child(${rowIndex}) > td`) }
    get customGroupTableRowList()                     {return $$('tbody[class="p-element p-datatable-tbody"] > tr')}
    get customGroupDivisionCloseButton()              {return $('body > div> div > div > div > button')}
    get storeSelectionSelectButton()                  {return $('#customGroupForm > kds-button > button')}
    get storeCustomStoreGroupDropdownOnStep3()        {return $('mx-single-select[id="custom-store-group-selector"]>div>kds-label+div')}
    get storeSelectionOverrideWarningMessage()        {return $('mx-modal[id="store-selection-override-warning"] > div>div>mx-modal-header')}
    get storeSelectionModalFooterButtonList()         {return $$('mx-modal[id="store-selection-override-warning"] > div>div>mx-modal-footer>div>div>kds-button')}
    get storeSelectionListOnStep3()                   {return $$('p-tristatecheckbox[id="divisions"]')}
    get storeGroupUpdatedMessage()                    {return $('section > header > h1')}
    get storeMatchMessage()                           {return $('div > div> div > kds-message')}
    get groupCategoryColumnClear()                    {return $('#filter-row > th:nth-child(3) > p-columnfilter > div > button')}
    get topRowSingleColumnValue()                     {return $(`p-table[datakey="groupId"] tbody > tr:nth-child(1) > td:nth-child(${colIndex})`)}
    get duplicateStoreMessageList()                   {return $$('div[id="mx-Modal"] > mx-modal-header > header > div > span')}
    get yesSubmitItButtonList()                       {return $$('#mx-Modal > mx-modal-footer > div > div > kds-button:nth-child(2) > button')}
    get customGroupDeletedMessage()                   { return $('div[id="kds-Portal-toast"] section h1')}
    get storeNumberBoxList()                          { return $$('div.p-element.checkbox_block')}


    async clickCreateNewGroup(){
        await (await this.createNewGroupButton).waitForClickable({timeout: 3000})
        await this.clickOn(await this.createNewGroupButton);
        await this.waitForPageLoad(await this.createNewGroupButton, 'Create New Group');
    }

    async clickEditNewGroup(){
        await (await this.editTopRowButton).waitForClickable({timeout: 3000})
        await this.clickOn(await this.editTopRowButton);
        await this.waitForPageLoad(await this.createNewGroupButton, 'Create New Group');
    }

    async clickDeleteCustomStore(){
        await (await this.deleteTopRowButton).waitForClickable({timeout: 15000})
        await super.clickOn(await this.deleteTopRowButton);
        await this.waitForPageLoad(await this.deleteStoreGroupModalText, 'Delete Store Group');
    }

    async clickAndRandomlySelectCustomStoreGroup(){
        await super.waitBrowserToLoadPage(1000);
        await (await this.storeCustomStoreGroupDropdownOnStep3).waitForClickable({timeout: 2000})
        await this.clickOn(await this.storeCustomStoreGroupDropdownOnStep3);
        await super.waitBrowserToLoadPage(2000);
        await this.randomSelectCustomGroupDropdown();
    }

    async clickStoreSelectionOverrideContinueButton(){
        await (await this.storeCustomStoreGroupDropdownOnStep3).waitForClickable({timeout: 2000})
        await this.clickOn(await this.storeCustomStoreGroupDropdownOnStep3);
        await this.randomSelectCustomGroupDropdown();
    }

    async enterSearchInputForDivisionDropdown(value){
        await (await this.divisionFilterDropdown).waitForClickable({timeout: 2000})
        await this.clickOn(await this.divisionFilterDropdown);
        let inputBox = await this.divisionSearchByInput;
        await inputBox.waitForExist({timeout: 20000}, 'Error: Description field did not appear before timeout')
        await inputBox.setValue(await value);
        const allDropdownValueCheckBox = await this.customGroupDivisionClickAllCheckBox;
        await (await allDropdownValueCheckBox).waitForClickable({timeout: 2000})
        allDropdownValueCheckBox.click();
        await this.customGroupDivisionCloseButton.click();
    }

    async updateGroupNameValue(updatedValue){
        let inputBox = await this.groupNameInputBox;
        await inputBox.waitForExist({timeout: 20000}, 'Error: Description field did not appear before timeout')
        await inputBox.clearValue();
        const emptyValue = await this.groupNameInputBox.getValue();
        if(emptyValue === ''){
            await inputBox.setValue(await updatedValue);
        }
        !expect(await inputBox.getValue()).toEqual(updatedValue);
    }

    async updateStoreValue(tableLocatorName){
        await this.waitForTableExist(tableLocatorName);
        let checkList = await this.storeNumberCheckBoxTextList;
        let selectedStore;
        for (let index = 0; index< checkList.length; index++) {
            if (await ((await checkList[index].getAttribute("class")).includes('p-highlight')) === true)
                selectedStore = await checkList[index];
                await selectedStore.click();
        }
    }

    async fillGroupNameInputBox(inputValue){
        let inputBox = await this.groupNameTableInputBox;
        await inputBox.waitForExist({timeout: 20000}, 'Error: Description field did not appear before timeout')
        await inputBox.setValue(await inputValue);
        await expect(await inputBox.getValue()).toEqual(inputValue);
        let classAttribute = await inputBox.getAttribute('class');
        if(await classAttribute.includes('p-filled')){
            await browser.keys("Enter");
        }
    }

    async genericFillGroupNameInputBox(inputLocatorName, inputValue){
        let inputBox;
        if(inputLocatorName === 'createdInputBox'){
            inputBox = await this.groupNameInputBox;
        }
        if(inputLocatorName === 'tableInputBox' ){
            inputBox = await this.groupNameTableInputBox;
        }
        await inputBox.waitForExist({timeout: 20000}, 'Error: Description field did not appear before timeout')
        await inputBox.setValue(await inputValue);
        await expect(await inputBox.getValue()).toEqual(inputValue);
        let classAttribute = await inputBox.getAttribute('class');
        if(await classAttribute.includes('p-filled')){
            await browser.keys("Enter");
        }
    }

    async clearValueFromInputBox(header){
        await super.waitBrowserToLoadPage(2000);
        let headerData = await this.storeGroupHeaderList;
        await super.waitBrowserToLoadPage(2000)
        let arrayList = await this.getArrayElementsText(headerData, headerData.length);
        let headerIndex = arrayList.indexOf(header);
        colIndex = headerIndex + 1;
        const inputBox = await this.customGroupNthSearchInputBox;
        await inputBox.clearValue();
        await browser.keys("Enter");
        await this.clearCustomGroupNthSearchInputBoxButton.click();
        const emptyValue = await this.customGroupNthSearchInputBox.getValue();
        return emptyValue === '';
    }

    async selectCustomStoreDivisionValue(value){
        let divisionDropdown = await this.selectDivisionDropdown;
        await super.waitBrowserToLoadPage(3000);
        await divisionDropdown.click();
        await super.waitBrowserToLoadPage(2000);
        let dropDown = await this.divisionDropdownList;
        for (let index = 0; index < dropDown.length; index++) {
            let dropdownList = await dropDown[index];
            if (await dropDown[index].getText() === value) {
                await dropDown[index].scrollIntoView();
                await super.waitBrowserToLoadPage(2000);
                await dropdownList.click();
                break;
            }
        }
    }

    async randomClickStoreCheckBox() {
        try {
            const uncheckedCheckboxes = [];
            let checkBoxList = await this.storeNumberCheckBoxHighLightList;
            let checkBoxTextList = await this.storeNumberCheckBoxTextList;
            for (let checkbox of checkBoxList) {
                const isSelected = await checkbox.isSelected();
                if (!isSelected) {
                    uncheckedCheckboxes.push(await checkbox);
                }
            }
            const checkedBox = [];
            if (uncheckedCheckboxes.length > 0) {
                const randomIndex = Math.floor(Math.random() * uncheckedCheckboxes.length);
                await uncheckedCheckboxes[randomIndex].click();
                await super.waitBrowserToLoadPage(2000);
                checkedBox.push(await checkBoxTextList[randomIndex].getText())
            }
            return checkedBox;
        }catch (error){
            console.error("Something went wrong !");
        }
        return console.error("Error: did not select store value !");
    }

    async clickCustomGroupHeader(columnHeaderName){
        const header = await $(this.storeGroupSingleColumnHeader.replace('@headerName',columnHeaderName));
        await super.clickOn(await header);
    }

    async getCustomGroupColumnList(header) {
        await super.waitBrowserToLoadPage(2000);
        let headerData = await this.storeGroupHeaderList;
        await super.waitBrowserToLoadPage(2000)
        let arrayList = await super.getArrayElementsText(headerData, headerData.length);
        let headerIndex = arrayList.indexOf(header);
        colIndex = headerIndex + 1;
        let columnList = await this.storeGroupNthColumnList;
        return await super.getArrayElementsText(columnList);
    }

    async getColumnTopElement(key) {
        let table = await this.firstRow;
        await table.waitForExist({ timeout: 50000 }, 'Error: UI table did not appear before time out');
        let headerData = await this.storeGroupHeaderList;
        await super.waitBrowserToLoadPage(2000);
        for (let index = 0; index < headerData.length; index++) {
            if ((await headerData[index].getText()) === key) {
                colIndex = index + 1;
                let elementText = await this.firstElementOnTopRow.getText();
                return elementText;
            }
        }
        throw new Error(`Error: Header "${key}" not found in the table.`);
    }

    async enterSearchInput(headerName, inputValue) {
        await super.waitBrowserToLoadPage(2000);
        let headerData = await this.storeGroupHeaderList;
        await super.waitBrowserToLoadPage(2000)
        let arrayList = await this.getArrayElementsText(headerData, headerData.length);
        let headerIndex = arrayList.indexOf(headerName);
        colIndex = headerIndex + 1;
        let searchInput= await this.searchByInput;
        await searchInput.waitForExist({timeout: 20000}, 'Error: Description field did not appear before timeout')
        await this.clickOn(await searchInput);
        await searchInput.setValue(inputValue);
        await super.waitBrowserToLoadPage(2000)
        await this.clickOn(await searchInput);
        await expect(await searchInput.getValue()).toEqual(inputValue);
        await browser.keys("Enter");
    }

    async clearSearchInput(headerName) {
        await super.waitBrowserToLoadPage(2000);
        let headerData = await this.storeGroupHeaderList;
        await super.waitBrowserToLoadPage(2000)
        let arrayList = await this.getArrayElementsText(headerData, headerData.length);
        let headerIndex = arrayList.indexOf(headerName);
        colIndex = headerIndex + 1;
        let clearsSearchInput= await this.clearSearchInputButton;
        await clearsSearchInput.waitForExist({timeout: 20000}, 'Error: Description field did not appear before timeout')
        await this.clickOn(await clearsSearchInput);
    }

    async getCustomGroupPageDropdownList() {
        let chevDropdown = await this.customStoreChevronDownIcon;
        await chevDropdown.waitForExist({
            timeout: 9000,
            timeoutMsg: 'Error: page numbers did not appear before timeout'
        })
        await super.clickOn(chevDropdown)
        await super.waitBrowserToLoadPage(3000)
        let chevDropdownListTxt = await this.customGroupPageDropdownList;
        let dropdownList = [];
        await super.waitBrowserToLoadPage(2000)
        for (let index = 0; index < chevDropdownListTxt.length; index++) {
            dropdownList.push(await chevDropdownListTxt[index].getText());
        }
        await browser.keys('Escape');
        return dropdownList;
    }

    async customGroupSelectPageNumber(pageNumber) {
        await super.waitBrowserToLoadPage(2000)
        let pageNumberLocator = await this.customStoreChevronDownIcon;
        if(await pageNumberLocator.isClickable()){
            await pageNumberLocator.click();
        }
        await super.waitBrowserToLoadPage(2000)
        let pageDropdownTxt = await this.customGroupPageDropdownList;
        for (let index = 0; index < pageDropdownTxt.length; index++) {
            if (await pageDropdownTxt[index].getText() === pageNumber) {
                await pageDropdownTxt[index].scrollIntoView();
                await super.waitBrowserToLoadPage(3000)
                await (await pageDropdownTxt[index]).click();
               break;
            }
        }
        await browser.keys('Escape');
    }

   async getPageRowNumFromFooterString(){
       const startDigit = (await this.customGroupFooterString.getText()).split(" ")[1];
       const endDigit = (await this.customGroupFooterString.getText()).split(" ")[3];
       return  (endDigit-startDigit+1);
   }

    async randomSelectCustomGroupDropdown() {
            let dropdownList = await this.storeSelectionCustomGroupDropdownList;
            const randomIndex = Math.floor(Math.random() * dropdownList.length);
            const selectedElement = await dropdownList[randomIndex];
            await super.waitBrowserToLoadPage(1000)
            await super.clickOn(selectedElement);
    }

    async selectScheduleDropDownValue() {
        await super.waitBrowserToLoadPage(2000)
        let dropdownToBeClicked = await this.storeSelectionSelectScheduleDropdown;
        await super.clickOn(await dropdownToBeClicked);
        await super.waitBrowserToLoadPage(2000)
        let dropdown = await this.storeSelectionSelectScheduleDropdownValue;
        await super.waitBrowserToLoadPage(2000)
        await dropdown.click();
        await browser.keys('Escape');
    }

    async selectCustomStoreGroupDropdown(dropdownValue) {
        await super.waitBrowserToLoadPage(2000)
        let dropdownToBeClicked = await this.storeSelectionSelectCustomGroupDropdown;
        await super.clickOn(await dropdownToBeClicked);
        await super.waitBrowserToLoadPage(2000)
        let dropdownList = await this.storeSelectionCustomGroupDropdownList;
        for(let index=0; index<dropdownList.length;index++){
            if( await dropdownList[index].getText() === dropdownValue){
                await dropdownList[index].scrollIntoView();
                await super.waitBrowserToLoadPage(2000)
                await (await dropdownList[index]).click();
                break;
            }
        }
        await browser.keys('Escape');
    }

    async selectGroupCategory(toBeSelectedValue) {
        await super.waitBrowserToLoadPage(2000);
        const dropdownLocator = await this.groupCategoryDropdown;
        await (await dropdownLocator).waitForClickable({timeout: 3000})
        await dropdownLocator.click();
        const dropdownList = await this.groupCategoryDropdownList;
        await dropdownLocator.waitForExist({
                timeout: 3000,
                timeoutMsg: 'Error: Description field did not appear before timeout'
            });
        for (let i = 0; i < dropdownList.length; i++) {
            const singleValue = await dropdownList[i];
            const optionText = await singleValue.getText();
            // Match with the provided value
            if (optionText === toBeSelectedValue) {
                // Click the matching option
                await singleValue.click();
                await super.waitBrowserToLoadPage(1000);
                return;
            }
        }
        //throw new Error(`Value "${toBeSelectedValue}" not found in dropdown.`);
    }

    async selectAllDivisionDropdownValues(){
        const dropdownLocator = await this.divisionFilterDropdown;
        await (await dropdownLocator).waitForClickable({timeout: 3000})
        await dropdownLocator.click();
        let allDropdownValueCheckBox = await this.customGroupDivisionClickAllCheckBox;
        await super.waitBrowserToLoadPage(2000)
        await (await allDropdownValueCheckBox).waitForDisplayed({timeout: 3000})
        await allDropdownValueCheckBox.click();
    }

    async selectCategoryColumnDropdownValue(categoryValue){
        const dropdownLocator = await this.categoryFilterDropdown;
        await (await dropdownLocator).waitForClickable({timeout: 3000})
        await dropdownLocator.click();
        await super.waitBrowserToLoadPage(1000);
        let dropdownList = await this.customGroupCategoryDropdownList;
        for (let index = 0; index < dropdownList.length; index++) {
            let singleDropdown = await dropdownList[index];
            let dropdownText = await singleDropdown.getText();
            if (dropdownText === await categoryValue) {
                await super.waitBrowserToLoadPage(1000);
                await singleDropdown.click();
                break;
            }
        }
        //return  await singleDropdown.getText();
    }

        async genericSelectDropDownValuesByIndex(startIndex, endingIndex) {
        await super.waitBrowserToLoadPage(2000)
        let dropdownToBeClicked = await this.divisionFilterDropdown;
        await (await dropdownToBeClicked).waitForClickable({timeout: 3000})
        await super.clickOn(await dropdownToBeClicked);
        await super.waitBrowserToLoadPage(2000)
        let dropdown = await this.customGroupDivisionDropdownCheckBoxList;
        await super.waitBrowserToLoadPage(2000)
        if (dropdown.length >= await endingIndex) {
            for (let index = startIndex; index <= endingIndex-1; index++) {
                await super.waitBrowserToLoadPage(2000)
                await dropdown[index].click();
            }
        } else {
            console.error("Dropdown does not have enough options")
        }
    }

    async clickCustomStoreCheckBoxText(storeValue){
        await super.waitBrowserToLoadPage(2000)
        const storeList = await this.storeNumberCheckBoxTextList;
        for(let index=0;index<storeList.length;index++){
            const singleStore = await storeList[index];
            if(await singleStore.getText() === storeValue){
                await singleStore.click();
                break;
            }
        }
    }

    async clickSingleButtonFromList(buttonList, buttonToBeClicked) {
        const buttons = await buttonList;
        await super.waitBrowserToLoadPage(1000);
        for (let index = 0; index < buttons.length; index++) {
            await browser.pause(1000)
            const buttonText = await buttons[index].getText();
            if (await buttonText === await buttonToBeClicked) {
                await buttons[index].click();
                break;
            }
        }
    }

    async returnTopRowStoreValue(){
        await super.waitBrowserToLoadPage(2000);
        const topRowStore = await this.customGroupTableTopExpandButton;
        await (await topRowStore).waitForClickable({timeout: 3000});
        await super.clickOn(await topRowStore);
        await super.waitBrowserToLoadPage(2000);
        //await (await  this.customGroupTableStoreNumberString).waitForExist({timeout: 9000}); // waitForDisplayed
        const storeValue = await this.customGroupTableStoreNumberString.getText();
        await super.waitBrowserToLoadPage(1000);
        return storeValue;
    }

    async getRandomDivisionValueExceptExistingOne(existingValue){
        let divisionDropdown = await this.selectDivisionDropdown;
        await super.waitBrowserToLoadPage(3000);
        await divisionDropdown.click();
        await super.waitBrowserToLoadPage(2000);
        const dropdownValues= [];
        let dropDownList = await this.divisionDropdownList;
        for(let element of dropDownList){
            const text = await element.getText()
            dropdownValues.push({text, element});
        }
        const filteredValues = dropdownValues.filter(item => item.text !== existingValue);
        if(filteredValues.length === 0){
            throw new Error('Dropdown does not have values to filter 1')
    }
        const randomIndex = Math.floor(Math.random() * filteredValues.length);
        await filteredValues[randomIndex].element.click();
        const selectedValue = await filteredValues[randomIndex].text;
        return selectedValue;
    }

    async genericGetCustomGroupColumnList(header) {
        await super.waitBrowserToLoadPage(2000);
        let headerData = await this.storeGroupHeaderList;
        await super.waitBrowserToLoadPage(2000)
        let arrayList = await this.getArrayElementsText(headerData, headerData.length);
        let headerIndex = arrayList.indexOf(header);
        colIndex = headerIndex + 1;
        return this.storeGroupNthColumnList;
    }

    async paginateCustomGroupColumn(headerName, pageNumber) {
        let table = await this.customGroupTable;
        await table.waitForExist({ timeout: 2000 }, 'Error: Title did not appear before timeout')
        await expect(await table).toBeDisplayedInViewport();
        let nextButton = await this.customGroupNextButtonPagination;
        let uiData = [];
        let headerData = await this.storeGroupHeaderList;
        await super.waitBrowserToLoadPage(2000)
        for(let dataCount=0; dataCount < headerData.length; dataCount++) {
            if ((await headerData[dataCount].getText()) === (headerName)) {
                await super.waitBrowserToLoadPage(1000)
                colIndex = dataCount + 1;
                break;
            }
        }
        while (await nextButton.isEnabled()) {
            let nextButtonClassAttribute = await this.customGroupNextButtonPagination.getAttribute("class");
            let columnList = await this.storeGroupNthColumnList;
            for (let index = 0; index < columnList.length; index++) {
                let columnData = await (await columnList[index].getText())
                uiData.push(await columnData )
            }
                await nextButton.scrollIntoView();
            nextButtonClassAttribute = await this.customGroupNextButtonPagination.getAttribute("class");
            if(await nextButton.isClickable()){
                await super.clickOn(nextButton)
            }
            await super.waitBrowserToLoadPage(2000)
            if (nextButtonClassAttribute.includes('p-disabled') || await this.customGroupActivePaginationNum.getText() === pageNumber) {
                break;
                }
        }
        return uiData;
    }

    async selectMultipleStoreValues( selectValue) {
        await super.waitBrowserToLoadPage(2000)
        let storeListText = await this.storeNumberCheckBoxTextList;
        let storeListCheckBox = await this.storeNumberCheckBoxHighLightList;
        for (let len = 0; len < selectValue.length; len++) {
            for (let index = 0; index < storeListText.length; index++) {
                let appList = await storeListCheckBox[index]
                let textList = await storeListText[index].getText();
                if (await (await selectValue[len]).includes(await textList)) {
                    await super.waitBrowserToLoadPage(2000)
                    await appList.click();
                    break;
                }
            }
        }
    }

    async getRandomUniqueValue(array1, array2) {
        // Filter array2 to remove any values that exist in array1
        const uniqueValues = await array2.filter(value => !array1.includes(value));
        // Check if there are any unique values left
        if (await uniqueValues.length === 0) {
            throw new Error("No unique values available in array2.");
        }
        // Randomly select one value from the uniqueValues array
        const randomIndex = Math.floor(Math.random() * uniqueValues.length);
        return uniqueValues[randomIndex].toString();
    }

    async getAllDropdownValues(dropdownLocator, dropdownListLocator){
        let divisionDropdown = await dropdownLocator;
        await super.waitBrowserToLoadPage(2000);
        await divisionDropdown.click();
        let finalArray = [];
        await super.waitBrowserToLoadPage(2000);
        let dropDown = await dropdownListLocator;
        for (let index = 0; index < dropDown.length; index++) {
            let dropdownList = await dropDown[index].getText();
                finalArray.push(await dropdownList);
        }
        await browser.keys("Enter");
        return finalArray;
    }

    async findDisabledOption() {
        const dropdownLocator = await this.groupCategoryDropdown;
        await (await dropdownLocator).waitForClickable({timeout: 2000})
        await dropdownLocator.click();
        const options = await this.groupCategoryDropdownDisabledList;
        for (const option of options) {
            const optionId = await option.getAttribute("id");
            if (optionId === 'disabledOption') {
                return option.getText();
            }
        }
        return "No disabled option found in the dropdown.";
    }

    async getFirstColumnValueFromTopRow(header){
        await super.waitBrowserToLoadPage(1000);
        let headerData = await this.storeGroupHeaderList;
        await super.waitBrowserToLoadPage(1000)
        let arrayList = await this.getArrayElementsText(headerData, headerData.length);
        let headerIndex = arrayList.indexOf(header);
        colIndex = headerIndex + 1;
        const inputBox = await this.topRowSingleColumnValue;
        return await inputBox.getText();
    }

    async getSelectCustomStoreGroupDropdownList() {
        await super.waitBrowserToLoadPage(2000)
        let dropdownToBeClicked = await this.storeSelectionSelectCustomGroupDropdown;
        await super.clickOn(await dropdownToBeClicked);
        await super.waitBrowserToLoadPage(2000)
        let dropdownList = await this.storeSelectionCustomGroupDropdownList;
        let dropdownTextList = await this.getArrayElementsText(dropdownList, dropdownList.length);
        await browser.keys('Escape');
        return dropdownTextList

    }

    async getCheckedBoxesWithClass() {

        const checkboxes = await this.storeNumberBoxList;
        await super.waitBrowserToLoadPage(2000)
        const filteredCheckboxes = [];
        for (const checkbox of checkboxes) {
            const classes = await checkbox.getAttribute('class');
            if (classes.includes('alert-success') || classes.includes('alert-info')) {
                await super.waitBrowserToLoadPage(1000)
                filteredCheckboxes.push(await checkbox.getText());
            }
        }
        return filteredCheckboxes;
    }

}


module.exports = new CustomStoreGroupPage();
