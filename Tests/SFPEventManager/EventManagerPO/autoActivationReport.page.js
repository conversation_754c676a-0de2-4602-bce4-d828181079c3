const Page = require('../../../GlobalObjectRepository/page');
import allureReporter from '@wdio/allure-reporter';
const underscoreLib = require('underscore')
const autoActivationReport = require('../../../SQLConnection/server.js.ts');
let n, i, j;
class AutoActivationPage extends Page{

    get selectDivision()                                    { return $('div[aria-label="Division"]')}
    get inputFilterName()                                   { return ('div[aria-label="name"] +div >div >ers-filter-search >div>div>input')}
    get selectLocation()                                    { return $('div[aria-label="Location"]')}
    get selectDepartment()                                  { return $('div[aria-label="Department"]')}
    get selectCommodity()                                   { return $('div[aria-label="Commodity"]')}
    get selectErrorCode()                                   { return $('div[aria-label="Error Code"]')}
    get selectYear()                                        { return $('mx-single-select[input-id="year-dropdown"]')}
    get selectPeriod()                                      { return $('mx-single-select[input-id="period-dropdown"]')}
    get selectWeek()                                        { return $('mx-single-select[input-id="week-dropdown"]>div >div>span')}
    get inputUPC()                                          { return $('input[inputmode="text"]')}
    get searchBtn()                                         { return $('kds-button[id="searchBtn"]')}
    get autoActivationRecapBtn()                            { return $('div[role="tablist"]>button:nth-child(1)')}
    get autoActivationDetailSmry()                          { return $('div[role="tablist"]>button:nth-child(2)')}
    get selectDropDownList()                                { return $$('label.kds-Label.kds-Text--m.kds-Label--hasRadioOrCheckbox')}
    get selectYPWDropDownList()                             { return $$('#ypw > div > div > mx-picker-pill mx-single-select >div>div div')}
    get linkSubModule()                                     { return $('#page > app-breadcrumb > div > ol > li:nth-child(3) > a')}
    get btnExportToExcel()                                  { return $('button[aria-label="Export To Excel"]')}
    get btnClearFilters()                                   { return $('button[aria-label="Clear Filters"]')}
    get btnDeleteErrorLog()                                 { return $('button[aria-label="Delete Error Log"]')}
    get btnChangeUPC()                                      { return $('button[aria-label="Change UPC"]')}
    get headerRow()                                         { return $$('div.ui-table-wrapper.ng-star-inserted > table > thead > tr>th')}
    get rows()                                              { return $$('div#page tbody tr:nth-child(1) td')}
    get selectText()                                        { return $('#page > content > app-auto-activation-report > div >p')}
    get tableRecapHeaders()                                 { return $$('#page > content > app-auto-activation-report > div > div.ers-datatable.row.col-md-12.mt-12 > kds-tabs > section:nth-child(2)>p>p-table>div>div>table>thead>tr>th')}
    get tableDetailHeaders()                                { return $$('#page > content > app-auto-activation-report > div > div.ers-datatable.row.col-md-12.mt-12 > kds-tabs > section:nth-child(3)>div>div:nth-child(2)>p-table>div>div>table>thead>tr>th')}
    get tableRecap()                                        { return $('p > p-table > div > div.ui-table-wrapper.ng-star-inserted > table')}
    get tableRecapColumn()                                  { return $$('p > p-table > div > div.ui-table-wrapper.ng-star-inserted > table>tbody>tr:nth-child(1)>td')}
    get tableValueList()                                    { return $(`tr[class="ng-star-inserted"]:nth-child(1)>td:nth-child(${n})`) }
    get nextButtonPagination()                              { return $('p > p-table > div > p-paginator > div > a.ui-paginator-next.ui-paginator-element.ui-state-default.ui-corner-all') }
    get tableDetailRows()                                   { return $$('#page > content > app-auto-activation-report > div > div.ers-datatable.row.col-md-12.mt-12 > kds-tabs > section:nth-child(3)>div>div:nth-child(2)>p-table>div>div>table>tbody>tr')}
    get tableNthColumns()                                   { return $$('#page > content > app-auto-activation-report > div > div.ers-datatable.row.col-md-12.mt-12 > kds-tabs > section:nth-child(3)>div>div:nth-child(2)>p-table>div>div>table>tbody>tr:nth-child('+i+') td')}
    get inputNewUPC()                                       { return $('#cdk-overlay-0 > app-change-upc-modal > div > div.ers-dialog-content > kds-label:nth-child(2) > label > kds-input > input')}
    get submitBtn()                                         { return $('button[aria-label="Submit"]')}
    get btnYes()                                            { return $('button[aria-label="Yes"]')}
    get msgToast()                                          { return $('#kds-Portal-toast > div >kds-toast >section>p')}
    get btnYesDelete()                                      { return $('button[aria-label="Yes, Delete"]')}
    get msgUPCSummary()                                     { return $('div#page section kds-text span ')}

    async getDropdownSingleText(Iteam) {
        let locator;
        switch (Iteam) {
            case 'Division':    
                locator = await this.selectDivision;
                (await locator).waitForExist({timeout: 50000}, 'Error: Division did not appear before timeout')
                break;
            case 'Location':    
                locator = await this.selectLocation;
                (await locator).waitForExist({timeout: 50000}, 'Error: Location did not appear before timeout')
                break;
            case 'Department':    
                locator = await this.selectDepartment;
                (await locator).waitForExist({timeout: 50000}, 'Error: Department did not appear before timeout')
                break;
            case 'Error Code':
                locator = await this.selectErrorCode;
                (await locator).waitForExist({timeout: 50000}, 'Error: Error Code did not appear before timeout')
                break;
            case 'Commodity':
                locator = await this.selectCommodity;
                (await locator).waitForExist({timeout: 50000}, 'Error: Commodity did not appear before timeout')
                break;
            case 'Year':
                locator = await this.selectYear;
                (await locator).waitForExist({timeout: 50000}, 'Error: Year did not appear before timeout')
                break;
            case 'Period':
                locator = await this.selectPeriod;
                (await locator).waitForExist({timeout: 50000}, 'Error: Period did not appear before timeout')
                break;
            case 'Week':
                locator = await this.selectWeek;
                (await locator).waitForExist({timeout: 50000}, 'Error: Week did not appear before timeout')
                break;         
            
        }
        return await locator.getText()
    }

    async genericClickDropdwn(Locator) {
        let dropdown;
        switch (Locator) {
            case 'Division':
                dropdown = await this.selectDivision;
                break;
            case 'Location':
                dropdown = await this.selectLocation;
                break;
            case 'Department':
                dropdown = await this.selectDepartment;
                break;
            case 'Error Code':
                dropdown = await this.selectErrorCode;
                break;
            case 'Commodity':
                dropdown = await this.selectCommodity;
                break;
            case 'Year':
                dropdown = await this.selectYear;
                break;
            case 'Period':
                dropdown = await this.selectPeriod;
                break;
            case 'Week':
                dropdown = await this.selectWeek;
                break;
        }
        if(await dropdown.isClickable()){
        await super.clickOn(dropdown);
        await super.waitBrowserToLoadPage(2000)
    }
    }   
    async enterUPC(upcValue) {
        const upc = await this.inputUPC;
        await upc.waitForExist({ timeout: 59000 }, 'Error: UPC input field did not appear before timeout')
        await upc.setValue(upcValue);
        await super.clickOn(this.searchBtn)
    }

    // select with dropdown by Text
    async selectSingleValue(Locator, selectValue) {
        await this.genericClickDropdwn(Locator);
        await super.waitBrowserToLoadPage(2000)
        let text = await this.selectDropDownList
        for (let i = 0; i < text.length; i++) {
            let appList = text[i]
            if (await text[i].getText() === selectValue) {
                await text[i].scrollIntoView()
                await appList.click();
                break;
            }
        }
        await browser.keys('Escape')
        !expect(super.doIsDisplay(this.selectDropDownList)) 
    }

    //select with dropdown by Text
    async selectYPW(Locator, selectValue) {
        await this.genericClickDropdwn(Locator);
        await super.waitBrowserToLoadPage(3000);
        let options;
        let attempts = 0;
        let maxAttempts = 5;
        let presentYear = new Date().getFullYear();
        while (attempts < maxAttempts) {
            options = await this.selectYPWDropDownList;
                await Promise.all(options.map(async (opt) => await opt.getText()));
            for (let index = 0; index < options.length; index++) {
                let appList = options[index];
                let yearText = (await appList.getText()).trim();
                // **If present year (2025) is already highlighted and matches selectValue, skip clicking**
                if (yearText === presentYear.toString() && yearText === selectValue.toString()) {
                    await browser.keys("Escape");
                    return;
                }
                // **If the required year (past or future) is found, select it**
                if (yearText === selectValue.toString()) {
                    await appList.scrollIntoView();
                    await browser.pause(1000);
                    await appList.click();
                    return;
                }
            }
            await browser.keys("ArrowDown");
            await browser.pause(1000);
            attempts++;
        }
    
        console.error(`Year ${selectValue} not found after scrolling!`);
    }
    
    

    // select with dropdown by Text
    async selectMultipleValues(Locator, selectValue) {
        await this.genericClickDropdwn(Locator);
        await super.waitBrowserToLoadPage(2000)
        let text = await this.selectDropDownList
        for (let h = 0; h < selectValue.length - 1; h++) {
            for (let i = 0; i < text.length; i++) {
                let appList = await text[i]  
                if (await text[i].getText() === selectValue[h]) {
                    await text[i].scrollIntoView({behavior: "smooth"})
                    await appList.click();
                    break;
                }
            }
        }
        await browser.keys('Escape')
        !expect(super.doIsDisplay(this.selectDropDownList))
    }

    // select with dropdown by Text
    async setMultipleValues(Locator,selectValue) {
        for(let j=0;j<selectValue.length-1;j++){
            await this.genericClickDropdwn(Locator);
            let setText = $((this.inputFilterName).replace('name',Locator))
            await setText.setValue(selectValue[j])
            let  text = await this.selectDropDownList
        for (let h = 0; h < selectValue.length - 1; h++) {
            for (let i = 0; i < text.length; i++) {
                let appList = await text[i]
                if (await text[i].getText() === selectValue[h]) {      
                    await appList.click();
                    break;
                }
            }
        }
    }
        await browser.keys('Escape')
        !expect(super.doIsDisplay(this.selectDropDownList))
    }


    async exportToExcel() {
        let exportBtn = await this.btnExportToExcel;
        await exportBtn.waitForClickable({ timeout: 59000 }, 'Error: EXPORT TO EXCEL button is not clickable before timeout')
        await expect(exportBtn).toBeClickable()
        await super.clickOn(exportBtn)
        await super.waitBrowserToLoadPage(2000)
    }

    async goToDetailSummaryReport(key) {
        let headerData = await this.tableRecapHeaders
        let totalStoreCount
        for (let i = 0; i < headerData.length; i++) {
            if ((await headerData[i].getText()).includes(key)) {
                n = i + 1;
                let value = await this.tableValueList
                await super.waitBrowserToLoadPage(4000)
                totalStoreCount = await value.getText()
                await super.clickOn(value)
                break
            }
        }
        return totalStoreCount
    }

    async getAllUIData() {
        let table = await this.tableRecap;
        await table.waitForExist({timeout: 59000}, 'Error: UI table of data did not appear before timeout')
        await expect(table).toBeDisplayedInViewport()
        let columns = await this.tableRecapColumn;
        let uiData=[]
        let nextButton = await this.nextButtonPagination
        while (nextButton.isEnabled) {
            let nextButtonClassName = await this.nextButtonPagination.getAttribute("class")
            for (i = 1; i <= await this.tableDetailRows.length; i++) {
                let uiRowData = []
                for (j = 0; j < columns.length; j++) {
                    let data = await this.tableNthColumns[j]
                    uiRowData.push(await data.getText())
                }
                uiData.push(uiRowData)

            }
            await super.clickOn(nextButton)
            await super.waitBrowserToLoadPage(2000)
            if (nextButtonClassName.includes("disabled")) {
                break
            }
        }
        return uiData
    }

    async uiDataCompareAllRow(uiData, query) {
        const dataQuery = await autoActivationReport.getResult(query);
    
        // Validate row counts
        await expect(uiData.length).toEqual(dataQuery.length);
    
        let flag = true;
    
        // Helper function to filter and join row data
        const processRow = (row) => row.filter((val) => val !== undefined && val !== null).join(", ");
    
        for (let i = 0; i < uiData.length; i++) {
            const dbRow = processRow(dataQuery[i]);
            const uiRow = processRow(uiData[i]);
    
            if (!underscoreLib.isEqual(dbRow, uiRow)) {
                // Log mismatched rows
                allureReporter.addArgument(`Data mismatch at row from DB: ${i + 1}`, dbRow);
                allureReporter.addArgument(`Data mismatch at row from Excel: ${i + 1}`, uiRow);
    
                flag = false;
            }
        }
    
        return flag;
    }
    
    async changeUPC(newUPC) {
        let changeUPCButton = await this.btnChangeUPC;
        await changeUPCButton.waitForExist({timeout: 59000}, 'Error: Change UPC did not appear before timeout')
        await changeUPCButton.click()
        await super.waitBrowserToLoadPage(1000)
        await this.inputNewUPC.clearValue()
        await this.inputNewUPC.setValue(newUPC);
        await super.clickOn(this.submitBtn)
        await this.btnYes.waitForExist({timeout: 10000}, 'Error: confirmation popup did not appear before timeout')
        await super.clickOn(await this.btnYes)
    }

    async getDisplayMessage(){
        let toastMsg=await this.msgToast
        await toastMsg.waitForExist({timeout: 20000}, 'Error: Display message did not appear before timeout')
        return toastMsg.getText()
    }

    async deleteErrorLog() {
        let deleteButton = await this.btnDeleteErrorLog;
        await deleteButton.waitForExist({timeout: 59000}, 'Error: Delete button did not appear before timeout')
        await deleteButton.click()
        await super.waitBrowserToLoadPage(1000)
        await this.btnYesDelete.waitForExist({timeout: 10000}, 'Error: confirmation popup did not appear before timeout')
        await super.clickOn(await this.btnYesDelete)
    }

}
module.exports = new AutoActivationPage()
