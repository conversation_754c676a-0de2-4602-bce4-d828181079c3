const Page = require('../../../GlobalObjectRepository/page');
let emData = require('../../../TestData/EventManagerData/eventManagerUIData.json')
const eventMaintanencePage = require('../EventManagerPO/eventMaintanence.page');
const storeSelectionPage = require('../EventManagerPO/storeSelection.page');
/**
 * sub page containing specific selectors and methods for selecting PogCommodities page
 */
let flag = false
let n;
let i, j;
class SelectingPogCommoditiesPage extends Page {

    get selectDepartment()                  { return $('div[aria-label="Department"]') }
    get selectSubDepartment()               { return $('div[aria-label="Sub-Department"]') }
    get selectCommodity()                   { return $('div[aria-label="Commodity *"]') }
    get selectCommodityGroup()              { return $('div[aria-label="Commodity Group"]') }
    get selectCommodityGroupDropdown()      { return ('div[aria-label="dropdownName"]') }
    get selectCommodityGroupDropdownList()  { return ('mx-multi-select[label="@labelName"] > div > div> div > div > mx-multi-select-list > ul > div > mx-menu-item > li > div') }
    get createEventDropdownList()           { return $$(' mx-single-select > div > div> mx-single-select-list > ul > div > mx-single-select-list-item> div > li > label') }
    get selectDropDownList()                { return $$('label.kds-Label.kds-Text--m.kds-Label--hasRadioOrCheckbox') }
    get selectDropDownListMulti()           { return ('mx-multi-select#name div ul li div label kds-checkbox +div') }
    get linkSubModule()                     { return $('#page > app-breadcrumb > div > ol > li:nth-child(4) > a') }
    get selectChoosePogByCommodityBtn()     { return $('kds-button[aria-label="Choose POGs by Commodity(s)"]>button') }
    get selectRemoveSelectedPogButton()     { return $('div.col-md-5.text-right > kds-button:nth-child(1) > button') }
    get gotoEventListButton()               { return $('kds-button.ml-8.hydrated > button') }
    get headerRow()                         { return $$('#pn_id_11>div>table thead>tr>th') }
    get rows()                              { return $$('div#page tbody tr td') }
    get selectText()                        { return $('#deptList > div > kds-label > label') }
    get pogDeleteMsg()                      { return $('p-toastitem > div > div > div > div.p-toast-summary') }
    get checkBox()                          { return $('div#page tbody tr:nth-child(1) td>p-tablecheckbox') }
    get selectAllCheckBoxes()               { return $('thead > tr > th.w-2vw > p-tableheadercheckbox > div > div.p-checkbox-box') }
    get eventDescriptionColumn()            { return $$('div#page tbody tr td:nth-child(5)') }
    get table()                             { return $('tbody[class="ui-table-tbody"]') }
    get inputFilterName()                   { return ('div[aria-label="name"] +div >div  div div> input') }
    get headerColumnValues()                { return $$(`div#page tbody tr td:nth-child(${n})`) }
    get nextButtonPagination()              { return $('p-paginator>div>button.p-ripple.p-element.p-paginator-next') }
    get btnPageOne()                        { return $('p-paginator[class="p-element ng-star-inserted"]>div >span>button:nth-child(1)') }
    get tableRefreshEvent()                 { return $(`div#page tbody`)}
    get tableRefreshEventColumn()           { return $$(`div#page tbody tr:nth-child(1) td`)}
    get tableNthColumns()                   { return $$('div#page tbody tr:nth-child('+i+') td')}
    get tableRefreshEventRows()             { return $$('div#page tbody tr')}
    get pogCommodityPage()                  { return $('div>div>div>ul>li:nth-child(4)>a')}
    get uploadUPCPage()                     { return $('div>div>div>ul>li:nth-child(4)>a')}
    get pogCommodityAlertMessageCapitalList()    { return $$('#mx-Modal > mx-modal-header > header > div > span')}
    get pogCommodityAlertMessageCapital()   { return $('mx-modal[id="pog-selection-no-store-alert-overlay"]>div>div mx-modal-header>header>div>span')}
    get pogCommodityAlertMessageHeader()    { return $$('#mx-Modal > mx-modal-header > header > div > span')}
    get uploadUPCAlertMessageCapital()      { return $('mx-modal[id="uploadupc-no-store-alert-overlay"] mx-modal-body>div>p:nth-child(1)')}
    get pogCommodityAlertMessage()          { return $('#mx-modal-id > div>p')}
    get pogCommodityAlertMessageList()      { return $$('#mx-modal-id > div')}
    get uploadUPCAlertMessage()             { return $('#mx-modal-id > div > p:nth-child(3)')}
    get pogCommodityAlertOKBtn()            { return $('mx-modal[id="pog-selection-no-store-alert-overlay"]>div>div mx-modal-footer>div>div+div>kds-button:nth-child(2)>button')}
    get pogCommodityAlertOKBtnList()        { return $$('#mx-Modal > mx-modal-footer > div > div.float-right.mx-modal-footer-container-right > kds-button:nth-child(2) > button')}
    get uploadUPCAlertOKBtn()               { return $('#uploadupc-no-store-alert-overlay>div>div.ers-modal>div>ers-button')}
    get sfpAppModalButton()                 { return $('button[aria-label="Proceed"]')}
    get tableRowNumber()                    { return $$('tbody > tr')}


    async selectDropdown(item, value) {
        const itemLocators = {
            'Department': this.selectDepartment,
            'Sub-Department': this.selectSubDepartment,
            'Commodity *': this.selectCommodity,
            'Commodity Group': this.selectCommodityGroup,
        };
        const dropDownSelector = this.selectDropDownList;
        // Validate that the provided `item` exists in the locators map
        const locator = itemLocators[item];
        if (!locator) {
            throw new Error(`Invalid item: ${item}. Please provide a valid dropdown item.`);
        }
        await (await locator).waitForExist({ timeout: 50000 }, `Error: ${item} did not appear before timeout`);
        await super.clickOn(await locator);
        await super.waitBrowserToLoadPage(2000);
        // Get the dropdown options
        const dropDown = await dropDownSelector;
        for (const dropdownList of dropDown) {
            const text = await dropdownList.getText();
            if (text === value) {
                await dropdownList.scrollIntoView();
                await dropdownList.click();
                break;
            }
        }
    }
    
    async waitForPageLoad(element, value) {
        await browser.waitUntil(
            async () => (await element.getText()) === value,
            {
                timeout: 90000,
                timeoutMsg: 'expected text not found  after 9s'
            }
        )
    }

    async getPogRowText(value) {
        await super.waitBrowserToLoadPage(7000)
        let dbKey = await this.rows
        await super.waitBrowserToLoadPage(4000)
        await expect(dbKey).toBeElementsArrayOfSize(emData.PogCommodities_Array_Size);
        for (let i = 0; i < dbKey.length; i++) {
            if (await dbKey[i].getText() === value) {
                flag = true
                break;
            }
        }
        return flag
    }

    async getEventDescText(eventDescription, week) {
        try {
            await super.waitBrowserToLoadPage(6000);
            let eventData = await this.eventDescriptionColumn;
            // Loop through elements to find a match
            for (let i = 0; i < eventData.length; i++) {
                const text = await eventData[i].getText();
                if (await text.includes(await eventDescription) && await text.includes(await week)) {
                    // Return early if a match is found
                    return true;
                }
            }
            // Return false if no match is found
            return false;
        } catch (error) {
            console.error("Error in getEventDescText:", error);
            // Optionally, handle or propagate the error further
            throw error;
        }
    }

    async getHeaderValue(key) {
        const headerData = await this.headerRow;
        const rowValueText = [];
        for (let index = 0; index < headerData.length; index++) {
            if ((await headerData[index].getText()).includes(key)) {
                const value = await this.rows;
                await super.waitBrowserToLoadPage(4000);
                rowValueText.push(await value[index].getText());
                return rowValueText;
            }
        }
        throw new Error(`Header with key '${key}' not found`);
    }

    async getAllHeader() {
        await super.waitBrowserToLoadPage(2000)
        let headerData = await this.headerRow
        let headerText = []
        for (let i = 1; i < headerData.length; i++) {
                headerText.push(await headerData[i].getText())
                flag = true
        }
        return headerText
    }

    async getHeaderColumnValue(key) {
        const headerData = await this.headerRow;
        const rowValueText = [];
        for (let i = 0; i < headerData.length; i++) {
            n = i + 1;
            await super.waitBrowserToLoadPage(3000);
            if ((await headerData[i].getText()) === key) {
                const value = await this.headerColumnValues;
                // Get the text for each column value asynchronously using Promise.all
                const columnTexts = await Promise.all(value.map(async (data) => {
                    return await data.getText();
                }));
                // Push each column value text into rowValueText array
                for (const text of columnTexts) {
                    rowValueText.push(text);
                }
                await super.waitBrowserToLoadPage(3000);
                // Return the populated rowValueText when a match is found
                return rowValueText;
            }
        }
        throw new Error(`Header with key '${key}' not found`);
    }

    // get all column values in all pagination
    async getHeaderValues(key) {
        let columnValues = [];
        let nextButton = await this.nextButtonPagination
        let headerData = await this.headerRow
        await super.waitBrowserToLoadPage(2000)
        for (let j = 0; j < headerData.length; j++) {
            if ((await headerData[j].getText()) === (key)) {
                n = j + 1;
                break
            }
        }
        while (nextButton.isEnabled) {
            let nextBtnClassName = await this.nextButtonPagination.getAttribute("class")
            let value = await this.headerColumnValues;
            for (let i = 0; i < value.length; i++) {
                columnValues.push(await value[i].getText())
            }
            await super.clickOn(nextButton)
            await this.btnPageOne.waitForClickable({ timeout: 59000 }, 'Error: Next button did not appear clickable before timeout')
            await super.waitBrowserToLoadPage(1000)
            // either of "disable" or "0" UI value exists the function
            if (nextBtnClassName.includes("p-disabled")) {
                break;
            }
        }
        return columnValues
    }

    async genericClickDropdown(Locator) {
        let dropdown;
        switch (Locator) {
            case 'Department':
                dropdown = await this.selectDepartment;
                break;
            case 'Commodity *':
                dropdown = await this.selectCommodity;
                break;
        }
        await super.clickOn(dropdown);
        await super.waitBrowserToLoadPage(3000)
    }

    // select with dropdown by Text
    async setMultipleValues(Locator, selectValue,dropdown) {
        for (let j = 0; j < selectValue.length; j++) {
            await this.genericClickDropdown(Locator);
            await super.waitBrowserToLoadPage(2000)
            let inputText = await $((this.inputFilterName).replace('name', Locator))
            await inputText.setValue(selectValue[j])
            await super.waitBrowserToLoadPage(2000)
            let text = await $$((this.selectDropDownListMulti).replace('name',dropdown))
            for (let i = 0; i < text.length; i++) {
                let appList = await text[i]
                if ((await text[i].getText()).includes(selectValue[j])) {
                    await super.waitBrowserToLoadPage(2000)
                    await super.clickOn(await appList)
                    await super.waitBrowserToLoadPage(2000)
                    await browser.keys('Escape')
                    break;
                }
            }
        }
    }
    

    async getAllEventRefreshUIData() {
        (await this.tableRefreshEvent).waitForExist({ timeout: 180000 }, 'Error: Department did not appear before timeout')
        let columns = await this.tableRefreshEventColumn;
        let uiData=[]
        let nextButton = await this.nextButtonPagination
        while (nextButton.isEnabled) {
            let nextButtonClassName = await this.nextButtonPagination.getAttribute("class")
            for (i = 1; i <= await this.tableRefreshEventRows.length; i++) {
                let uiRowData = []
                for (j = 1; j < columns.length-1; j++) {
                    let data = await this.tableNthColumns[j]
                    uiRowData.push(await data.getText())
                }
                uiData.push(uiRowData)
            }
            await super.clickOn(nextButton)
            await super.waitBrowserToLoadPage(2000)
            if (nextButtonClassName.includes("disabled")) {
                break
            }
        }
        return uiData
    }

    async selectingPogCommodities(deptName,deptNameValue,SubdeptName,SubdeptNameValue,Commodity,CommodityValue,CommodityGroup,CommodityGroupValue){
        //await this.waitForPageLoad(await storeSelectionPage.linkSubModule, emData.Select_Pogs)
        await super.waitBrowserToLoadPage(2000);
        let pogPage = await storeSelectionPage.linkSubModule.getText()
        await expect(pogPage).toEqual(emData.Select_Pogs)
        //await this.waitForPageLoad(this.selectText, emData.Department);
        await this.waitForPageLoad(this.selectText, emData.Department);
        await super.waitBrowserToLoadPage(2000);
        if(deptName!=='') {
            await this.selectDropdown(deptName, deptNameValue);
        }
        
        await this.waitForPageLoad(this.selectText, emData.Department);
        await super.waitBrowserToLoadPage(2000);

        if(SubdeptName!=='') {
            await this.selectDropdown(SubdeptName, SubdeptNameValue);
        }
     
        await this.waitForPageLoad(this.selectText, emData.Department);
        await super.waitBrowserToLoadPage(2000);

        if(Commodity!=='') {
            await this.selectDropdown(Commodity, CommodityValue);
        }
       
        await this.waitForPageLoad(this.selectText, emData.Department);
        await super.waitBrowserToLoadPage(2000);

        if(CommodityGroup!=='') {
            await this.selectDropdown(CommodityGroup, CommodityGroupValue);
        }
        await super.waitBrowserToLoadPage(2000);
        await super.clickOn(this.selectChoosePogByCommodityBtn);
        await this.waitForPageLoad(this.selectText, emData.Department);
        await super.waitBrowserToLoadPage(2000);
    }
    async eventListButton(){
        await eventMaintanencePage.tableFirstRow.waitForExist({ timeout: 90000 }, 'Error: Table contents for event maintenance page didn`t load before timeout')
        await (await this.gotoEventListButton).waitForExist({ timeout: 90000 }, 'Error: EventList button not visible')
        await super.clickOn(this.gotoEventListButton);

    }
}
module.exports = new SelectingPogCommoditiesPage();
