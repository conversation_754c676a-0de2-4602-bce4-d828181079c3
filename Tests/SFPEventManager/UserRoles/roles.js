module.exports = {

    "storeUserEM": {
        "dev": '',
        "qa": '',
        "stage": 'SFPE030',
        "qa1": 'SFPE030',
        "qa2": 'SFPE030',
        "prod": ''
    },

    "storePasswordEM": {
        "dev": '',
        "qa": '',
        "stage": 'uoiFcG8n7Hk',
        "qa1": 'uoiFcG8n7Hk',
        "qa2": 'uoiFcG8n7Hk',
        "prod": ''
    },
    
    "divisionalUserEM": {
        "dev": '',
        "qa": '',
        "stage": 'SFPE020',
        "qa1": 'SFPE020',
        "qa2": 'SFPE020',
        "prod": ''
    },

    "divisionalPasswordEM": {
        "dev": '',
        "qa": '',
        "stage": 'otf53aLBDesCr7LZmjNUGc',
        "qa1": 'otf53aLBDesCr7LZmjNUGc',
        "qa2": 'otf53aLBDesCr7LZmjNUGc',
        "prod": ''
    },
    "CorpUserEM": {
        "dev": '',
        "qa": '',
        "stage": 'SFPE010',
        "qa1": 'SFPE010',
        "qa2": 'SFPE010',
        "prod": ''
    },

    "CorpPasswordEM": {
        "dev": '',
        "qa": '',
        "stage": 'TTzAf2dymsOOVy',
        "qa1": 'TTzAf2dymsOOVy',
        "qa2": 'TTzAf2dymsOOVy',
        "prod": ''
    },
    "multipleDivisionalUserEM": {
        "dev": '',
        "qa": '',
        "stage": 'SFPE031',
        "qa1": 'SFPE031',
        "qa2": 'SFPE031',
        "prod": ''
    },
    "multipleDivisionalPasswordEM": {
        "dev": '',
        "qa": '',
        "stage": 'tR9UtCz8P',
        "qa1": 'tR9UtCz8P',
        "qa2": 'tR9UtCz8P',
        "prod": ''
    }
};
