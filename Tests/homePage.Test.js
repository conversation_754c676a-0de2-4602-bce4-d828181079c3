
import allureReporter from '@wdio/allure-reporter';
const homePage = require('../GlobalObjectRepository/home.page');
const data = require('../TestData/data.json');
const navigatorPage = require('../Tests/SFPNavigator/NavigatorPO/navigator.page.js');

describe('homePage: SFP App application homePage', () => {

    it('should login with valid credentials', async () => {
        let title = await browser.getTitle();
        await expect(browser).toHaveTitle(await title)
    })

    it('validate homePage logo', async () => {
        allureReporter.addFeature("logo verification")
        let SfpLogo = await homePage.sfpLogo
        await navigatorPage.waitBrowserToLoadPage(3000);
        await expect(SfpLogo).toBeDisplayed();
    })

    it('validate a link ', async () => {
        allureReporter.addSeverity("blocker")
        let menu = await homePage.applicationMenu;
        await menu.click()
        let links = await homePage.applicationMenuList
        let urls = await links.map(async link => await link.getAttribute('href'))
        // make request to the url
        await urls.forEach(async function (url) {
            let [response] = await Promise.all([fetch(url)])
            expect(response.status).to.be.below(400)
        })
    })

    it('Validate all the dropdown list from Application menu', async () => {
        allureReporter.addSeverity('critical')
        let menu = await homePage.applicationMenu;
        await menu.isClickable()
        let list = await homePage.applicationMenuList;
        await expect(list).toHaveText(data.apppMenuList);
        if(await list.length === 5){
            await expect(list).toBeElementsArrayOfSize(5);
        } else{
            await expect(list).toBeElementsArrayOfSize(1);
        }
    })

    it('validate the homePageText', async () => {
        allureReporter.addSeverity("minor")
        let text = await homePage.pageText();
        await expect(text).toBeDisplayed;
    })

    it('Get all the header list from SFP home page', async () => {
        await homePage.getTextHeaderNames()
    })
})
