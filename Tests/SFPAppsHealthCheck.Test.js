const axios = require("axios");
const https = require("https");
const httpsAgent = new https.Agent({rejectUnauthorized: false});
const {assert} = require("chai");
const envConfig = require("../envConfig.js");
const { async } = require("node-stream-zip");
const { databaseCleanup } = require("../helpers/testSetupTeardown.js");
const ENV = process.env.ENV


describe('SFPAppsHealthCheck: SFP App Health Check', () => {

    it('should validate the health of the application', async () => {
        let response = await axios({
            method: 'get',
            baseURL: envConfig.url[ENV],
            url: envConfig.healthURL,
            httpsAgent
        })
        assert.equal(response.status, 200,'The application is down with status code ' + response.status)
        assert.equal(response.data['status'], 'UP','The Application is down with status ' + response.data['status'])
    })
    it('should cleanup DB after test runs', async()=> {
        await databaseCleanup
    })

})
