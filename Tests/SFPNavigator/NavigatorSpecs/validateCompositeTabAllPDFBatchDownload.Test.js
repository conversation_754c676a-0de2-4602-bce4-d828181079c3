const navigatorPage = require("../NavigatorPO/navigator.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const {
  getFilesCount,
  extractFile,
} = require("../../../util/fileExtractor");
const { assert } = require("chai");
const compositePage = require("../NavigatorPO/composite.page");
const { clickOn } = require("../NavigatorPO/navigator.page");

describe('validateCompositeTabAllPDFBatchDownload: Validate Batch download of "All" PDF files on Composite tab', () => {
  const division = navigatorData.div_016_Columbus_Division;
  const store = navigatorData.store_016_00506;

  it('Validate Batch download error message and Batch Download of PDF files on Composite Tab when "all" PDFs are selected', async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
    ]);
    await navigatorPage.goTo("Composite");
    await navigatorPage.waitForPageLoad(
      await navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
    await clickOn(await compositePage.checkBoxColumn);
    let initialFilesCount = getFilesCount();
    await navigatorPage.batchDownload();
    const downloadComplete = await navigatorPage.waitForDownload(
      initialFilesCount
    );
    if (downloadComplete) {
      const finalFilesCount = getFilesCount();
      expect(finalFilesCount - initialFilesCount).toEqual(1);

      await extractFile();
      await navigatorPage.waitBrowserToLoadPage(2000);
    } else {
      throw new Error("PDF didn't get to download!");
    }
  });
});
