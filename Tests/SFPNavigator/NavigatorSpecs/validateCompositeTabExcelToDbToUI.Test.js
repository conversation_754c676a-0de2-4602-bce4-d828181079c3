const navigatorPage = require("../NavigatorPO/navigator.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const qrString = require("../../../SQLConnection/queryString");
const { DBNames } = require("../../../envConfig.js");
const excelReader = require("../../../util/excelReader");
const result = require("../../../SQLConnection/server.js.ts");

let divNumber, storeNumber;

describe("validateCompositeTabExcelToDbToUI: Validate Composite table data when pogKey or Division and store are selected", () => {
  const division = navigatorData.divisionlist;
  const store = navigatorData.storeListStoreTabComposite;
  beforeEach(async () => {
    await navigatorPage.open("navigator");
  });

  it("Validate Composite tab data when pogKey is selected - compare Excel data, DB data, UI, and search criteria", async () => {
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
    ]);
    let selectedDivisionMainPage = await navigatorPage.getTextOfElement(
      await navigatorPage.divisionDropdown
    );
    let selectedStoreMainPage = await navigatorPage.getTextOfElement(
      await navigatorPage.storeDropdown
    );
    await navigatorPage.goTo("Composite");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.waitForPageLoad(
      navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
    await navigatorPage.exportToExcel();
    let uiComposite = await navigatorPage.getAllUIData();
    uiComposite = await navigatorPage.removeElementsFromArrayOfArrays(
      uiComposite,
      "PDF",
      ""
    );
    await navigatorPage.waitBrowserToLoadPage(3000);
    await expect(
      await excelReader.excelUiDataCompareAllRows(uiComposite)
    ).toEqual(true);
    let dbData = await result.getResult(
      qrString.compositeTabDBKeyQuery.replace(
        "@DBKey",
        navigatorData.searchCriteriaLocatorNames
      ),
      DBNames.SFP_STAGE
    );
    await expect(
      await excelReader.excelDataCompareAllRow(
        qrString.compositeTabDBKeyQuery.replace(
          "@DBKey",
          navigatorData.searchCriteriaLocatorNames
        ),
        DBNames.SFP_STAGE
      )
    ).toEqual(true);
    await expect(
      await excelReader.dataCompare(uiComposite, dbData, dbData.length)
    ).toEqual(true);
    await navigatorPage.searchCriteria();
    await navigatorPage.waitBrowserToLoadPage(2000);
    let selectedDivisionSearchCriteria =
      await navigatorPage.getSearchCriteriaDropdownText("div");
    let selectedStoreSearchCriteria =
      await navigatorPage.getSearchCriteriaDropdownText("store");
    await expect(selectedDivisionSearchCriteria).toEqual(
      selectedDivisionMainPage
    );
    await expect(selectedStoreSearchCriteria).toEqual(selectedStoreMainPage);
    await navigatorPage.closeSearchCriteria();
  });

  it("Validate Composite tab data when Division and store are selected - compare excel data to db ", async () => {
    divNumber =
      navigatorData.div_014_Cincinnati_Division.match(/(\d[\d]*)/g)[0];
    storeNumber = navigatorData.store_014_00353.split("- ")[1];
    await navigatorPage.selectMultipleDropdowns([
      {
        id: navigatorData.divisionDropdown,
        values: navigatorData.div_014_Cincinnati_Division,
      },
      {
        id: navigatorData.storeDropdown,
        values: navigatorData.store_014_00353,
      },
    ]);
    await navigatorPage.goTo("Composite");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.exportToExcel();
    await navigatorPage.waitBrowserToLoadPage(6000);
    let dbCompareExcelComposite = await excelReader.excelDataCompareAllRow(
      await qrString.compositeTabQuery
        .replace("@Div", divNumber)
        .replace("@Store", storeNumber),
      DBNames.SFP_STAGE
    );
    await expect(dbCompareExcelComposite).toEqual(true);
  });
});
