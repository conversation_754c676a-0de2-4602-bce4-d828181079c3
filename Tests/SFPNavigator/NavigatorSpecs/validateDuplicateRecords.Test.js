const navigatorPage = require("../NavigatorPO/navigator.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const { validateDuplicates } = require("../../../util/excelReader");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
const result = require("../../../SQLConnection/server.js.ts");
const { DBNames } = require("../../../envConfig.js");

describe("validateDuplicateRecords: Validate tabs do not have duplicated data using excel file  ", () => {
  const division = navigatorData.div_016_Columbus_Division;
  const store = navigatorData.store_016_00506;
  it(
    "Validate there is no duplicate records with downloaded Excel data on ItemByPOG," +
      "Store, Composite, Floorplans, MappingReport, Pogs and PogItemByStore tabs ",
    async () => {
      await navigatorPage.open("navigator");
      await navigatorPage.selectMultipleDropdowns([
        { id: navigatorData.divisionDropdown, values: division },
        { id: navigatorData.storeDropdown, values: store },
      ]);
      let pogKey = await (
        await (
          await await result.getResult(
            dynamicQuery.pogKeyForNonDuplicatedRecords,
            DBNames.SFP_STAGE
          )
        ).flat()
      ).toString();
      await navigatorPage.searchByValue("POG Key", pogKey);
      let tabNameList = [
        "Item by POG",
        "Store",
        "Composite",
        "Floorplans",
        "Mapping Report",
        "POGs",
        "POG & Item by Store",
      ];
      for (let tabName = 0; tabName < tabNameList.length; tabName++) {
        await navigatorPage.goTo(tabNameList[tabName]);
        await navigatorPage.exportToExcel();
        await expect(await validateDuplicates()).toEqual(true);
        await navigatorPage.waitBrowserToLoadPage(3000);
      }
    }
  );
});
