const navigatorPage = require('../NavigatorPO/navigator.page');
const qrString = require('../../../SQLConnection/queryString');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const result = require('../../../SQLConnection/server.js.ts');
const {assert} = require('chai');
const { DBNames } = require('../../../envConfig.js');


describe('validateNavigatorFilterDropdowns: Handling dropdown in Navigator home page', () => {
    const division = navigatorData.filterDivisionlist
    const department = navigatorData.departmentList
    const subDepartment = navigatorData.filterSubDeptList
    const commodities = navigatorData.filterCommodityList
    before(async () => {
        await navigatorPage.open("navigator");
    })

    it('Should validate the department dropdown based on the division selected', async () => {
        await navigatorPage.genericClearOptions('Drop Downs');
        for (let index = 0; index < navigatorData.filterDivisionlist.length; index++) {
            
            let div = division[index].split('- ')[0]
            await navigatorPage.selectMultipleDropdowns([
                { id: navigatorData.divisionDropdown, values: division[index] },
          ]);
            let deptListUI = (await navigatorPage.getDropDownList('Department', 'dept')).filter(item => !item.includes('Select All'))
            let deptListDB = await (await result.getResult(qrString.getNavigatorDeptDropdown.replace('@div', div), DBNames.SFP_STAGE)).flat();
            let deptListDbRemoveOcado = await navigatorPage.removeStartsWithElements(deptListDB, "00-");
            await navigatorPage.waitBrowserToLoadPage(3000)
            await assert.equal(deptListUI.length, deptListDbRemoveOcado.length, 'Error: failed at div ' + division[index]);
            for (let indexNum = 0; indexNum < deptListUI.length; indexNum++) {
                await assert.equal(deptListUI[indexNum], deptListDbRemoveOcado[indexNum], 'Error: failed at div ' + division[index]);
            }
            await navigatorPage.clearMultipleDropdowns([
                {id: navigatorData.divisionDropdown},
            ])
        }
    })

    it('Should validate the sub department dropdown based on the division & dept selected. Also validates sub-Depart dropdown includes values only specific to individual Depart value', async () => {
        await navigatorPage.genericClearOptions('Drop Downs');
        await navigatorPage.selectMultipleDropdowns([
            { id: navigatorData.divisionDropdown, values: division[0] },
        ]);
        for (let index = 0; index < department.length - 1; index++) {
            await navigatorPage.selectMultipleDropdowns([
                { id: navigatorData.departmentDropdown, values: department[index] },
          ]);
            let deptDropDownText = await navigatorPage.getTextOfElement(await navigatorPage.deptDropdown);
            await navigatorPage.waitBrowserToLoadPage(1000)
            let subDeptListUI = (await navigatorPage.getDropDownList('SubDept', 'sub-dept')).filter(item => !item.includes('Select All'))
            let subDeptListDB = await ( await result.getResult(qrString.getNavigatorSubDeptDropdown.replace('@dept', department[index]), DBNames.SFP_STAGE)).flat();
            await navigatorPage.waitBrowserToLoadPage(3000)
            let subDeptListDbRemoveOcado = await navigatorPage.removeStartsWithElements(subDeptListDB, "00-");
            await assert.equal(subDeptListUI.length, subDeptListDbRemoveOcado.length, 'Error: failed at div ' + department[index]);
            for (let indexNum = 0; indexNum < subDeptListUI.length; indexNum++) {
                await assert.equal(subDeptListUI[indexNum].split('- ')[1], subDeptListDbRemoveOcado[indexNum], 'Error: failed at div ' + department[index]);
                let newArray = await navigatorPage.returnNewArrayBySplitArray(subDeptListUI, subDeptListUI, indexNum, '- ', 0 );
                let subDeptListUIUnique = await navigatorPage.getUniqueArrayValue(newArray);
                await expect(subDeptListUIUnique.length + ' Item Selected').toEqual(deptDropDownText);
            }
            await navigatorPage.clearMultipleDropdowns([
                {id: navigatorData.departmentDropdown},
            ])
        }
    })

    it('Should validate the Commodity dropdown based on the division , dept & Sub dept selected. Also validates commodity dropdown includes values only specific to individual sub-Depart value', async () => {
        await navigatorPage.genericClearOptions('Drop Downs');
        await navigatorPage.selectMultipleDropdowns([
            { id: navigatorData.divisionDropdown, values: division[0]},
            { id: navigatorData.departmentDropdown, values: department[0] },
      ]);
        for (let index = 0; index < subDepartment.length; index++) {
            let subDept = subDepartment[index].split('- ')[1]
            await navigatorPage.selectMultipleDropdowns([
                { id: navigatorData.subDeptDropdown, values: subDepartment[index] },
          ]);
            let subDeptDropDownText = (await (await navigatorPage.subDeptDropdown).getText());
            let commodityListUI = (await navigatorPage.getDropDownList('Commodity', 'commodity')).filter(item => !item.includes('Select All'))
            let commodityListDB = await (await result.getResult(qrString.getNavigatorCommodityDropdown.replace('@subDept', subDept), DBNames.SFP_STAGE)).flat();
            let commodityListDBRemoveOcado = await navigatorPage.removeStartsWithElements(commodityListDB, "00-");
            await navigatorPage.waitBrowserToLoadPage(3000)
            await assert.equal(commodityListUI.length, commodityListDBRemoveOcado.length, 'Error: failed at div ' + subDepartment[index]);
            for (let indexNum = 0; indexNum < commodityListUI.length; indexNum++) {
                await assert.equal(commodityListUI[indexNum].split('- ')[1], commodityListDBRemoveOcado[indexNum], 'Error: failed at div ' + subDepartment[index]);
                let newArray = await navigatorPage.returnNewArrayBySplitArray(commodityListUI, commodityListUI, indexNum, '- ', 0 );
                let commodityListUIUnique = await navigatorPage.getUniqueArrayValue(newArray);
                await expect([commodityListUIUnique].length + ' Item Selected').toEqual(subDeptDropDownText);
            }
            await navigatorPage.clearMultipleDropdowns([
                {id: navigatorData.subDeptDropdown},
            ])
        }
    })

    it('Should validate the Commodity Group dropdown based on the division , dept , Sub dept & Commodity selected. Also validates commodityGroup dropdown includes values only specific to individual commodity value', async () => {
        await navigatorPage.genericClearOptions('Drop Downs');
        await navigatorPage.selectMultipleDropdowns([
            { id: navigatorData.divisionDropdown, values: division[1] },
            { id: navigatorData.departmentDropdown, values: department[0] },
            { id: navigatorData.subDeptDropdown, values: subDepartment[1] },
        ]);
        await navigatorPage.enterValue.waitForExist({timeout: 59000}, 'Error: Navigator page did not appear before timeout')
        let subDept = navigatorData.filterSubDept.split('- ')[1]
        for (let index = 0; index < commodities.length; index++) {
            let commodity = commodities[index].split('- ')[1]
            await navigatorPage.selectMultipleDropdowns([
                { id: navigatorData.commodityDropdown, values: commodities[index] },
          ]);
            let commodityDropdownText = await (await navigatorPage.getTextOfElement(await navigatorPage.commodityDropdown));
            await navigatorPage.waitBrowserToLoadPage(1000)
            let commodityGrpListUI = (await navigatorPage.getDropDownList('CommodityGroup', 'comm-group')).filter(item => !item.includes('Select All'));
            let commodityGrpListDB = await (await result.getResult(qrString.getNavigatorCommodityGrpDropdown.replace('@subDept', subDept).replace('@commodity', commodity), DBNames.SFP_STAGE)).flat();
            let commodityGrpListDBRemoveOcado = await navigatorPage.removeStartsWithElements(commodityGrpListDB, "00-");
            await navigatorPage.waitBrowserToLoadPage(3000)
            await assert.equal(commodityGrpListUI.length, commodityGrpListDBRemoveOcado.length, 'Error: failed at div ' + commodities[index]);
            for (let indexNum = 0; indexNum < commodityGrpListUI.length; indexNum++) {
                await assert.equal(commodityGrpListUI[indexNum].split('- ')[1], commodityGrpListDBRemoveOcado[indexNum], 'Error: failed at div ' +commodities[index]);
                let newArray = await navigatorPage.returnNewArrayBySplitArray(commodityGrpListUI, commodityGrpListUI, indexNum, '- ', 0 );
                let commodityGrpListUIUnique = await navigatorPage.getUniqueArrayValue(newArray);
                await expect(commodityGrpListUIUnique.length + ' Item Selected').toEqual(commodityDropdownText)
            }
            await navigatorPage.clearMultipleDropdowns([
                {id: navigatorData.commodityDropdown},
            ])
        }
    })
})
