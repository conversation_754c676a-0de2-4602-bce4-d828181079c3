const navigatorPage = require('../NavigatorPO/navigator.page');
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const {assert} = require("chai");

let pageNum = navigatorData.subString_4.toString();
let excludedPageNum = navigatorData.subString_1;

describe('validateTabPagination: Validate pagination in  Mapping Report tab and POG Tab', () => {

    beforeEach(async () => {
    await navigatorPage.waitBrowserToLoadPage(3000);
await navigatorPage.open('navigator');
    await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect', navigatorData.div_011_Atlanta_Division);
    await navigatorPage.selectSingleValue('Stores', 'store', navigatorData.store_011_00020);
    })

    afterEach(async () => {
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.waitForPageLoad(await navigatorPage.btnExportToExcel, navigatorData.Export_To_Excel_text);
        let numFromPageDropdown = await navigatorPage.GetText(await navigatorPage.pageDropdownText);
        let countResult = await navigatorPage.pagination(pageNum);
        if( countResult > navigatorData.page_row_size_120){
            await expect(countResult).toEqual(parseInt(numFromPageDropdown) * (pageNum-excludedPageNum) );
        } else{
            await assert.fail(0, 1, "Error: tab did not paginate !");
        }
    })

    it('Mapping Report Tab: validate the paginating first five pages and clicks the first page before test closes off', async () => {

        await navigatorPage.goTo('Mapping Report');
    })

    it('POG Tab: validate the paginating first five pages and clicks the first page before test closes off', async () => {

        await navigatorPage.selectSingleValue('PlanogramStatus','pog-status', navigatorData.planogramStatusLive);
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.goTo('POGs');
    })

})
