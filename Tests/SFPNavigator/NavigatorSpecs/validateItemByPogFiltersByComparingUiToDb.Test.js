const navigatorPage = require("../NavigatorPO/navigator.page");
const itemByPOGPage = require("../NavigatorPO/itemByPOG.page");
const result = require("../../../SQLConnection/server.js.ts");
const qrString = require("../../../SQLConnection/queryString");
const { DBNames } = require("../../../envConfig.js");
const compareAllRow = require("../../../util/excelReader.js");
const dataJson = require("../../../TestData/SFPNavigatorData/navigatorQueryData.json");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const { assert } = require("chai");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
let tableEntryDataNum, uiTableDataSort, dbResultSort;
let queryCondition, pogKey;

describe("validateItemByPogFiltersByComparingUiToDb: Validate itemByPog UI table data with 7 different filter buttons", () => {
  before(async () => {
    pogKey = await (
      await (
        await await result.getResult(
          dynamicQuery.pogKeyForItemByPog,
          DBNames.SFP_STAGE
        )
      ).flat()
    ).toString();
    await navigatorPage.open("navigator");
    await navigatorPage.searchByValue("POG Key", await pogKey);
    await navigatorPage.goTo(navigatorData.item_By_Pog_Text);
    await navigatorPage.waitBrowserToLoadPage(5000);
    await navigatorPage.waitForPageLoad(
      await navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
    queryCondition = [
      "REPLACE(REPLACE(REPLACE( prd.NAME, ' ', '{}'), '}{', ''), '{}', ' ')",
    ];
  });

  it(
    "Validate ItemByPog table data by comparing UI table data to db when each filter button is clicked individually." +
      "Also validates the row entry number by comparing compare ui data length to row entry number ",
    async () => {
      let buttonList = await itemByPOGPage.getArrayElementsText(
        await itemByPOGPage.filterBtnList
      );
      let indexOfMerchAssortBtn = await itemByPOGPage.findIndexNumFromArray(
        await itemByPOGPage.filterBtnList,
        navigatorData.Merchandised_Assortment_text
      );
      for (
        let index = indexOfMerchAssortBtn;
        index < buttonList.length;
        index++
      ) {
        await itemByPOGPage.clickFilterTab([buttonList[index]]);
        await navigatorPage.waitBrowserToLoadPage(3000);
        tableEntryDataNum = (
          await itemByPOGPage.entryNumberString.getText()
        ).split(" ")[5];
        let uiTableData = await await navigatorPage.getAllUIData();
        if ((await uiTableData.length) > 0) {
          uiTableDataSort = await navigatorPage.sortArrayOfArrays(
            await uiTableData
          );
          let uiTableDataSortRemovedZero =
            await navigatorPage.removeZerosFromArrays(await uiTableDataSort);
          await navigatorPage.waitBrowserToLoadPage(5000);
          // await expect(await uiTableData.length === 1 ? '0' || '1' : await (await uiTableDataSort.length).toString()).toEqual(await tableEntryDataNum);
          let dbResult = await result.getResult(
            await qrString.ItemByPOGQueryPOGDBKeyFilter.replace(
              /productNameCondition/g,
              await queryCondition[0]
            )
              .replace(/[?]/g, await pogKey)
              .replace(
                /FILTER_CONDITION/g,
                dataJson.ItemByPog_Filter_Conditions[
                  index - indexOfMerchAssortBtn
                ]
              ),
            DBNames.SFP_STAGE
          );
          dbResultSort = await navigatorPage.sortArrayOfArrays(await dbResult);
          await navigatorPage.waitBrowserToLoadPage(3000);
          let dbResultTreated = await dbResultSort.map((arr) =>
            arr.map((val) => (val === null ? "" : String(val).trim()))
          );
          let dbResultTreatedRemovedZero =
            await navigatorPage.removeZerosFromArrays(await dbResultTreated);
          await navigatorPage.waitBrowserToLoadPage(4000);
          if (
            (await (await uiTableDataSort.flat()).length) > 0 &&
            (await (await dbResultTreated.flat()).length) > 0
          ) {
            let compareUiToDb = await compareAllRow.dataCompare(
              await uiTableDataSortRemovedZero,
              await dbResultTreatedRemovedZero,
              (await dbResultTreated.length) === 1
                ? 1
                : (await dbResultTreated.length) - 1
            );
            await navigatorPage.waitBrowserToLoadPage(2000);
            await expect(compareUiToDb).toEqual(true);
          } else {
            await expect(await (await uiTableData.flat()).toString()).toEqual(
              "No records found"
            );
          }
        } else {
          await assert.fail(0, 1, "Error: UI table does not have data !");
        }
      }
    }
  );
});
