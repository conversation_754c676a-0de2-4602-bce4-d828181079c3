const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const excelReader = require('../../../util/excelReader');
const qrString = require('../../../SQLConnection/queryString');
const navigatorPage = require('../NavigatorPO/navigator.page');
const {DBNames} = require("../../../envConfig.js");
const result = require("../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");


describe('validatePogItemByStoreExcelData: Validate pogItemByStore excel data with using UPC and PogKey flows', () => {
    let divNumber,storeNumber, conditionValue, upc, pogKey;

    conditionValue = ['AND PDT.UPC IN (\'@upcValue\')','AND POG.dbkey IN (\'@dbKey\')'];
    divNumber =  navigatorData.div_016_Columbus_Division.substring(navigatorData.subString_0,navigatorData.subString_3);
    storeNumber =  navigatorData.store_016_00506.split('- ')[1];


    it('Validate UPC data on Pog & ItemByStore tab', async () => {

        await navigatorPage.waitBrowserToLoadPage(5000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000);
        upc =  await (await (await (await result.getResult(dynamicQuery.upcListForPageNumbers, DBNames.SFP_STAGE))).flat());
        await navigatorPage.searchByValue('UPC', upc.join("\n"));
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_016_Columbus_Division);
        await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_016_00506);
        await navigatorPage.goTo(navigatorData.POG_Item_by_Store);
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.exportToExcel();
        await navigatorPage.waitBrowserToLoadPage(6000);
        let data = await excelReader.excelDataCompareAllRow(qrString.pogItemByStoreQuery.replace('@Div',divNumber)
            .replace('@Store',storeNumber).replace("'@Condition'", conditionValue[0].replace('@upcValue', upc.join("','"))), DBNames.SFP_STAGE);
        await expect(data).toEqual(true);
    })

    it('Validate POG data on Pog & ItemByStore tab', async () => {

        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        //await navigatorPage.waitBrowserToLoadPage(3000);
await navigatorPage.open('navigator');
        pogKey = await (await (await (await result.getResult(dynamicQuery.dbKeyForPogItemByStore, DBNames.SFP_STAGE))).flat()).toString();
        await navigatorPage.searchByValue('POG Key', await pogKey);
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_016_Columbus_Division);
        await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_016_00506);
        await navigatorPage.goTo(navigatorData.POG_Item_by_Store);
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.exportToExcel();
        await navigatorPage.waitBrowserToLoadPage(6000);
        let dataCompare = await excelReader.excelDataCompareAllRow(qrString.pogItemByStoreQuery.replace('@Div',divNumber)
            .replace('@Store', storeNumber).replace("'@Condition'", await conditionValue[1].replace('@dbKey', await pogKey)), DBNames.SFP_STAGE);
        await expect(await dataCompare).toEqual(true);
    })
})
