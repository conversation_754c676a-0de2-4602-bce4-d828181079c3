import { validateExcelDataMultiSheet } from "../../../util/excelReader";
import result from "../../../SQLConnection/server.js.ts";
const navigatorPage = require("../NavigatorPO/navigator.page");
const dataJson = require("../../../TestData/SFPNavigatorData/navigatorQueryData.json");
const qrString = require("../../../SQLConnection/queryString");
const { DBNames } = require("../../../envConfig.js");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");

let queryCondition, pogDBKeyNum;
describe("validateItemByPOGTabFiltersExcelToDB: Download exel file, read contents and compare with DB data", () => {
  before(async () => {
    await navigatorPage.open("navigator");
    pogDBKeyNum = await (
      await result.getResult(
        qrString.getPogDBKeyQuery
          .replace("@topNum", "10")
          .replace("@div", "016"),
        DBNames.SFP_STAGE
      )
    )[navigatorData.pogDbKey_5901406_8][1];
    await navigatorPage.searchByValue("POG Key", await pogDBKeyNum);
    await navigatorPage.goTo("Item by POG");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.exportToExcel();
    queryCondition = [
      "prd.NAME",
      "REPLACE(REPLACE(REPLACE( prd.NAME, ' ', '{}'), '}{', ''), '{}', ' ')",
    ];
  });

  it.skip("Validate Full Assortment data on Item by POG tab", async () => {
    let data = await validateExcelDataMultiSheet(
      qrString.ItemByPOGQueryPOGDBKeyFilter.replace(
        /productNameCondition/g,
        queryCondition[0]
      )
        .replace(/[?]/g, pogDBKeyNum)
        .replace(/FILTER_CONDITION/g, dataJson.fullAssortment_filter_condition),
      "Full Assortment",
      DBNames.SFP_STAGE
    );
    await expect(data).toEqual(true);
  });

  it("Validate Move-Adjustments data on Item by POG tab", async () => {
    let data = await validateExcelDataMultiSheet(
      qrString.ItemByPOGQueryPOGDBKeyFilter.replace(
        /productNameCondition/g,
        queryCondition[1]
      )
        .replace(/[?]/g, pogDBKeyNum)
        .replace(/FILTER_CONDITION/g, dataJson.move_filter_condition),
      "Move-Adjustments",
      DBNames.SFP_STAGE
    );
    await expect(data).toEqual(true);
  });

  it("Validate Pusher-Tray data on Item by POG tab", async () => {
    let data = await validateExcelDataMultiSheet(
      qrString.ItemByPOGQueryPOGDBKeyFilter.replace(
        /productNameCondition/g,
        queryCondition[1]
      )
        .replace(/[?]/g, pogDBKeyNum)
        .replace(/FILTER_CONDITION/g, dataJson.pusherTray_filter_condition),
      "Pusher-Tray",
      DBNames.SFP_STAGE
    );
    await expect(data).toEqual(true);
  });

  it("Validate Adds data on Item by POG tab", async () => {
    let data = await validateExcelDataMultiSheet(
      qrString.ItemByPOGQueryPOGDBKeyFilter.replace(
        /productNameCondition/g,
        await queryCondition[1]
      )
        .replace(/[?]/g, pogDBKeyNum)
        .replace(/FILTER_CONDITION/g, dataJson.adds_filter_condition),
      "Adds",
      DBNames.SFP_STAGE
    );
    await expect(data).toEqual(true);
  });

  it("Validate NII Info data on Item by POG tab", async () => {
    await navigatorPage.waitBrowserToLoadPage(3000);
    let data = await validateExcelDataMultiSheet(
      qrString.ItemByPOGQueryPOGDBKeyFilter.replace(
        /productNameCondition/g,
        await queryCondition[1]
      )
        .replace(/[?]/g, pogDBKeyNum)
        .replace(/FILTER_CONDITION/g, dataJson.nii_filter_condition),
      "NII Info",
      DBNames.SFP_STAGE
    );
    await expect(data).toEqual(true);
  });

  it("Validate Merchandised-Assortment data on Item by POG tab", async () => {
    let data = await validateExcelDataMultiSheet(
      qrString.ItemByPOGQueryPOGDBKeyFilter.replace(
        /productNameCondition/g,
        queryCondition[0]
      )
        .replace(/[?]/g, pogDBKeyNum)
        .replace(
          /FILTER_CONDITION/g,
          dataJson.Merchandise_Assortment_condition
        ),
      "Merchandised Assortment",
      DBNames.SFP_STAGE
    );
    await expect(data).toEqual(true);
  });
});
