const navigatorPage = require('../../SFPNavigator/NavigatorPO/navigator.page.js');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const {assert} = require("chai");

let pogDbKey,upc,floorKey,ssn;

describe('validateNavigatorTabHighlight: Validate specific tabs are highlighted on Navigator main page when : 1)UPC, POG Key, Floor Key,' +
    'SSN, SKU   ', () => {

    before(async () => {

        await navigatorPage.open("navigator");
        await navigatorPage.selectDropDownValues('Division', 'divisionMultiSelect',1,2);
        await navigatorPage.waitBrowserToLoadPage(3000);
        const navBarHeaderListDiv = await navigatorPage.getEnabledHeaders(await navigatorPage.navBarHeaderList);
        await expect(navBarHeaderListDiv).toEqual(await navigatorData.navBarList_first_3);
        await navigatorPage.selectDropDownValues('Stores', 'store',1,5);
        await navigatorPage.goTo('Mapping Report');
        await navigatorPage.waitBrowserToLoadPage(3000);
        pogDbKey = await navigatorPage.getLinkText("POG DBKey");
        floorKey = await navigatorPage.getLinkText("FP DBKey");
        ssn = await navigatorPage.getLinkText("Space Station Name");
        await navigatorPage.waitBrowserToLoadPage(3000)
        let itemByPogTab = await navigatorPage.getTextOfElement(await navigatorPage.breadCrumb);
        await expect(await itemByPogTab).toEqual('Item By POG');
        upc = await navigatorPage.getLinkText("UPC");
        await navigatorPage.navigatorTabBtn.click();
        await navigatorPage.waitBrowserToLoadPage(3000)
    })

    it('Validate Navigator tabs are highlighted when Div and Store are selected  ', async () => {

        const navBarHeaderListDivStore = await navigatorPage.getEnabledHeaders(await navigatorPage.navBarHeaderList);
        await expect(navBarHeaderListDivStore).toEqual(await navigatorData.navBarList_first_4);
        await navigatorPage.clickNavDropdownCloseBtn('Division');
        await navigatorPage.waitBrowserToLoadPage(3000)
        const navBarHeaderListDiv = await navigatorPage.getEnabledHeaders(await navigatorPage.navBarHeaderList);
        await expect(navBarHeaderListDiv).toEqual(await navigatorData.navBarList_first_3);
        await navigatorPage.waitBrowserToLoadPage(3000)
        await navigatorPage.selectDropDownValues('Division', 'divisionMultiSelect',1,2);
        await navigatorPage.waitBrowserToLoadPage(2000)
    })

    it('Validate  Navigator tabs are highlighted when UPC, PogDb Key, Floorplans and SSN are selected individually  ', async () => {

        const searchType = [ 'UPC', 'POG Key', 'Floor Key', 'SSN' ];
         let filterSearchList =  [upc, pogDbKey, floorKey, ssn];
        if(await pogDbKey.length>0 && await upc.length>0  && await floorKey.length>0  && await ssn.length>0) {
            await navigatorPage.waitBrowserToLoadPage(2000)
            for(let i=0; i< await filterSearchList.length; i++){
                await navigatorPage.searchByValue(await searchType[i], await filterSearchList[i]);
                const navBarHeaderList = await navigatorPage.getEnabledHeaders(await navigatorPage.navBarHeaderList);
                await navigatorPage.waitBrowserToLoadPage(2000)
                if(navBarHeaderList.length === 4){
                    await navigatorPage.waitBrowserToLoadPage(2000)
                    await expect(navBarHeaderList).toEqual(navigatorData.navBarList_first_4);
                } else if (navBarHeaderList.length === 6){
                    await navigatorPage.waitBrowserToLoadPage(2000)
                    await expect(navBarHeaderList).toEqual(navigatorData.navBarList_first_6);
                } else if (navBarHeaderList.length === 7) {
                    await navigatorPage.waitBrowserToLoadPage(2000)
                    await expect(navBarHeaderList).toEqual(navigatorData.navBarList_first_7);
                } else if (navBarHeaderList.length === 8){
                    await navigatorPage.waitBrowserToLoadPage(2000)
                    await expect(navBarHeaderList).toEqual(navigatorData.navBarList_first_8);
                }else{
                    await assert.fail(0, 1, "Error: Something went wrong ! Navigator Tab headers are not highlighted !")
                }
            }
        }else {
            await assert.fail(0, 1, "Error: One or more of List Filter Criteria values are entered into search box !")
        }
    })

})
