const navigatorPage = require('../NavigatorPO/navigator.page');
const storePlanogramItemPage = require('../NavigatorPO/storePlanogramItem.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const qrString = require("../../../SQLConnection/queryString");
const {DBNames} = require("../../../envConfig.js");
const uiDataCompare = require("../../../util/excelReader");
const {assert} = require("chai");
const result = require("../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");

let divNumber,storeNumber, conditionValue,upcList;
describe('validatePogItemByStoreUiDataToDb: Validate UPC data on Store Planogram Item tab by comparing UI data to DB', () => {

    before(async () =>{
        divNumber =  navigatorData.div_016_Columbus_Division.substring(navigatorData.subString_0,navigatorData.subString_3);
        storeNumber =  navigatorData.store_016_00506.split('- ')[1];
        conditionValue = ['AND PDT.UPC IN (\'@upcValue\')'];
        upcList =  (await (await (await result.getResult(dynamicQuery.upcListForPogItemByStore, DBNames.SFP_STAGE))).flat());
    })

    it('Validate UPC data on Store Planogram Item tab', async () => {
        await navigatorPage.waitBrowserToLoadPage(3000);
await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.searchByValue('UPC', await upcList.join('\n'));
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_016_Columbus_Division);
        await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_016_00506);
        await navigatorPage.goTo('POG & Item by Store');
        let uiTableData = await navigatorPage.getAllUIData();
        let uiTableDataClean = await navigatorPage.replaceEmptyStringWithNull(uiTableData);
        if(uiTableData.length > 0){
            await storePlanogramItemPage.waitForTableExist(await storePlanogramItemPage.storePlanogramTableValue)
            let dbData = await result.getResult(qrString.pogItemByStoreQuery.replace('@Div',divNumber)
                .replace('@Store',storeNumber).replace("'@Condition'", conditionValue[0].replace("@upcValue", upcList.join(","))), DBNames.SFP_STAGE);
            let compare = await uiDataCompare.dataCompareWithNullValue( uiTableDataClean, await dbData);
            await expect(compare).toEqual(true);
        } else{
            await assert.fail(0, 1, "Error: UI table does not have data !");
        }

    })
})
