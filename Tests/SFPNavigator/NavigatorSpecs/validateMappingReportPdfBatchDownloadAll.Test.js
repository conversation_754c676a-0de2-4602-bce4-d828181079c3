const navigatorPage = require("../NavigatorPO/navigator.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const {
  getFilesCount,
  extractFile,
  getExtractedFilesCount,
} = require("../../../util/fileExtractor");
const { assert } = require("chai");
const mappingReportPage = require("../NavigatorPO/mappingReport.page");

let pogDbKeyListExtractedUniqueLength;

describe("validateMappingReportPdfBatchDownloadAll: Validate Batch download of all PDF files on mapping report tab", () => {
  const division = navigatorData.div_708_Food_4_Less_Midwest_Division;
  const store = navigatorData.store_708_00552;
  const department = navigatorData.dept_58_Fuel;
  it('Validate Batch download of PDF files on MappingReport Tab when "all" PDFs are selected', async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
      { id: navigatorData.departmentDropdown, values: department },
    ]);
    await navigatorPage.goTo("Mapping Report");
    await navigatorPage.waitForPageLoad(
      await navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
    await navigatorPage.waitForTableExist(await mappingReportPage.table);
    let pogDbKeyIndex = await navigatorPage.findIndexNumFromArray(
      await mappingReportPage.mrTabHeaders,
      navigatorData.pog_DBKey_text
    );
    let tableEntryDisplayNum = (
      await navigatorPage.footerEntryString.getText()
    ).split(" ")[5];
    let uiData = await navigatorPage.getAllUIData();
    await expect(uiData.length.toString()).toEqual(tableEntryDisplayNum);
    let pogDbKeyColumnList = uiData.map(function (innerArray) {
      return innerArray[pogDbKeyIndex - 1];
    });
    let initialFilesCount = getFilesCount();
    await navigatorPage.selectColumnCheckBox("PDF");
    pogDbKeyListExtractedUniqueLength = await (
      await navigatorPage.getUniqueArrayValue(pogDbKeyColumnList)
    ).length;
    await navigatorPage.batchDownload();
    await navigatorPage.waitBrowserToLoadPage(3000);
    let finalFilesCount = getFilesCount();
    await navigatorPage.waitBrowserToLoadPage(3000);
    await expect(finalFilesCount - initialFilesCount).toEqual(1);
    await extractFile();
    await navigatorPage.waitBrowserToLoadPage(3000);
    if (getFilesCount() !== 0) {
      let extractedFileCount = await getExtractedFilesCount();
      await navigatorPage.waitBrowserToLoadPage(3000);
      await expect(await extractedFileCount).toEqual(
        await pogDbKeyListExtractedUniqueLength
      );
    } else {
      await assert.fail(
        0,
        1,
        "Error: PDFs are not downloaded! Update the input data !"
      );
    }
  });
});
