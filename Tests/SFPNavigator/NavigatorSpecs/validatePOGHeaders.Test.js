const navigatorPage = require('../NavigatorPO/navigator.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const {getExcelHeaders} = require('../../../util/excelReader');
const {getFilesCount} = require('../../../util/pdfReader');
const planogramPage = require("../NavigatorPO/mappingReport.page");
const result = require("../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
const {DBNames} = require("../../../envConfig.js");


describe('validatePOGHeaders: Validate Table Headers on POG tab by comparing UI headers to excel headers', () => {

    it('Validate Table Headers on POG tab', async () => {

        await navigatorPage.waitBrowserToLoadPage(3000);
await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(5000);
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_016_Columbus_Division);
        await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_016_00506);
        const upc =  await  (await (await result.getResult(dynamicQuery.upcForPogHeaders, DBNames.SFP_STAGE))).toString();
        await navigatorPage.searchByValue('UPC', upc);
        await navigatorPage.selectSingleValue('PlanogramStatus', 'pog-status', navigatorData.planogramStatusLive);
        await navigatorPage.goTo('POGs')
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.waitForTableExist(await planogramPage.table);
        let pogHeaders = await navigatorPage.getTableHeaderNames(await navigatorPage.tableHeaders);
        for(let index = 0; index <= pogHeaders.length-1; index++) {
            pogHeaders[index] = pogHeaders[index].replaceAll('\n', ' ');
            await expect(pogHeaders[index]).toEqual(navigatorData.POGHeaders[index]) }
        let initialFilesCount=getFilesCount()
        await navigatorPage.exportToExcel()                                                                     
        await navigatorPage.waitBrowserToLoadPage(6000)
        let finalFilesCount=getFilesCount()
        await expect(finalFilesCount-initialFilesCount).toEqual(1)
        let excelHeaders=await getExcelHeaders()
        let values = ["PDF","PSA"]
        let filteredPogHeaders=pogHeaders.filter(item => !values.includes(item))
        for(let index = 0; index <= excelHeaders.length-1; index++) {
            await expect(excelHeaders[index]).toEqual(filteredPogHeaders[index]) }
    })
})
