const navigatorPage = require('../NavigatorPO/navigator.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const {getPDFText} = require('../../../util/pdfReader');
const { assert } = require('chai');
const result = require("../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
const {DBNames} = require("../../../envConfig.js");

 describe('validatePDFContentOnPlanograms: Validate DBKey in PDF on planogram tab', () => {

     it('POG Tab: Validate open PDF and verify DBKey matches on POG tab:  @Headless', async () => {

         await navigatorPage.waitBrowserToLoadPage(3000);
         await navigatorPage.open('navigator');
         await navigatorPage.waitBrowserToLoadPage(3000)
         let upc = await (await (await (await result.getResult(dynamicQuery.upcForMappedPogs, DBNames.SFP_STAGE)))).flat().toString();
         await navigatorPage.searchByValue('UPC', await (upc));
         await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_016_Columbus_Division);
         await navigatorPage.selectSingleValue('PlanogramStatus', 'pog-status', navigatorData.planogramStatusLive);
         await navigatorPage.goTo('POGs');
         await navigatorPage.waitBrowserToLoadPage(3000)
         let parentGuId = await browser.getWindowHandle()                                                        
         let dbKey = await navigatorPage.getLinkText('DBKey');
         await navigatorPage.getLinkText('PDF')                                                                          
         await navigatorPage.waitBrowserToLoadPage(5000)                                                                    
         await navigatorPage.validateNewTab(parentGuId)
         let data = await getPDFText();
         let pogDbKeyPdf = await (data.split('DBKey:')[1].substr(1,7));
         if(typeof pogDbKeyPdf !== 'undefined' || pogDbKeyPdf.length === 0){
             await assert.equal(await pogDbKeyPdf, await dbKey)
             await browser.switchToWindow(parentGuId)
         } else {
             await assert.fail(0, 1, "Could not read DBKey value from downloaded PDF ! ")
         }
     })

 })
