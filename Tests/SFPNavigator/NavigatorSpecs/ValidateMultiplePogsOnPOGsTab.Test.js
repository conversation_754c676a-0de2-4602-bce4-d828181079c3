import { assert } from "chai";
import { DBNames } from "../../../envConfig.js";
import { clickOn } from "../../../GlobalObjectRepository/page";
const navigatorPage = require("../NavigatorPO/navigator.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const planogramsPage = require("../NavigatorPO/planograms.page");
const result = require("../../../SQLConnection/server.js.ts");
const qrString = require("../../../SQLConnection/queryString");
const excelReader = require("../../../util/excelReader");

let divNumber;
describe("ValidateMultiplePogsOnPOGsTab: Validate multiple pog selection on POGs tab", () => {
  const division = navigatorData.div_018_Michigan_Division;
  const commodity =
    navigatorData.comm_01_GROC_ALL_OTHER_001_CANNED_FRUIT_FRUIT_BOWLS;
  beforeEach(async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.commodityDropdown, values: commodity },
    ]);
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.goTo("POGs");
    await navigatorPage.waitBrowserToLoadPage(3000);
    divNumber = navigatorData.div_018_Michigan_Division.substring(
      navigatorData.subString_0,
      navigatorData.subString_3
    );
  });

  it("Validate totalSelectedPogs and totalSelectedStores when row checkbox lists are selected: 1, 3", async () => {
    let lengthOfTableRow = await navigatorPage.tableRowList.length;
    let storeCountColumnList = await navigatorPage.getColumnArrayList(
      navigatorData.store_Count
    );
    await navigatorPage.waitBrowserToLoadPage(3000);
    if (lengthOfTableRow >= 3) {
      let numOfSelectedCheckBoxes = [
        lengthOfTableRow - 1,
        lengthOfTableRow - 3,
      ];
      for (let index = 0; index < numOfSelectedCheckBoxes.length; index++) {
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.clickRowCheckBox(
          await planogramsPage.table,
          numOfSelectedCheckBoxes[index]
        );
        let countSelectedCheckBox = await navigatorPage.returnNumOfCheckBox(
          await planogramsPage.table
        );
        let selectedPOGDbKeysText =
          await planogramsPage.totalSelectedDisplayString.getText();
        let numOfSelectedPogFromString =
          await planogramsPage.extractDigitsByIndex(selectedPOGDbKeysText, 0);
        let numOfSelectedStoreFromString =
          await planogramsPage.extractDigitsByIndex(selectedPOGDbKeysText, 1);
        await navigatorPage.waitBrowserToLoadPage(3000);
        let storeCountListSum = await planogramsPage.sumArrayElements(
          storeCountColumnList,
          countSelectedCheckBox
        );
        await expect(countSelectedCheckBox.toString()).toEqual(
          numOfSelectedPogFromString
        );
        await expect(storeCountListSum.toString()).toEqual(
          numOfSelectedStoreFromString
        );
        await planogramsPage.clickOn(await planogramsPage.selectedPogInfo);
        await navigatorPage.waitBrowserToLoadPage(3000);
        let pogDBKeyColumnList = await navigatorPage.getColumnArrayList(
          navigatorData.pog_DBKey_text
        );
        let pogDBKeyColumnListUnique = await planogramsPage.getUniqueArrayValue(
          pogDBKeyColumnList
        );
        await navigatorPage.waitBrowserToLoadPage(3000);
        let displayPogDbKeysFromString = await planogramsPage.extractDigits(
          await planogramsPage.displayMappingPogString.getText()
        );
        if (displayPogDbKeysFromString.toString().length >= 4) {
          await expect(pogDBKeyColumnListUnique.toString()).toEqual(
            displayPogDbKeysFromString.toString()
          );
          await navigatorPage.goTo("POGs");
        } else {
          await navigatorPage.waitBrowserToLoadPage(5000);
          await expect(pogDBKeyColumnListUnique.length.toString()).toEqual(
            displayPogDbKeysFromString.toString()
          );
          await navigatorPage.goTo("POGs");
        }
        await navigatorPage.waitBrowserToLoadPage(5000);
      }
    } else {
      await assert.fail(
        0,
        1,
        "Row numbers must be equal or bigger than 3! Update the input data !"
      );
    }
  });

  it("Validate StoreCount column value matches with number of rows when storeCount value is clicked", async () => {
    let dbKeyValuePogsTab = await (
      await navigatorPage.getColumnArrayList("DBKey")
    )[0];
    let storeCountValuePogsTab = await navigatorPage.getLinkText(
      navigatorData.store_Count
    );
    await navigatorPage.waitBrowserToLoadPage(3000);
    let displayMappingPogDbKey = await planogramsPage.extractDigits(
      await planogramsPage.displayMappingPogString.getText()
    );
    await expect(dbKeyValuePogsTab).toEqual(displayMappingPogDbKey);
    let mappingReportFilterByPogKeyUiData = await navigatorPage.getAllUIData();
    let mappingReportFilterByPogKeyUiDataFilter =
      await navigatorPage.removeElementsFromArrayOfArrays(
        mappingReportFilterByPogKeyUiData,
        "PDF",
        "PSA"
      );
    let dbData = await await result.getResult(
      qrString.mappingReportNoOfStores.replace(
        "@pogKey",
        displayMappingPogDbKey.toString()
      ),
      DBNames.SFP_STAGE
    );
    if ((await dbData) !== null) {
      let dbDataFilter = await navigatorPage.replaceArrayElement(
        dbData,
        "",
        null
      );
      let dbDataFilterRemoveEmptySpace =
        await planogramsPage.removeWhiteSpaceFromArrayOfArrays(dbDataFilter);
      let dataCompare = await excelReader.dataCompare(
        mappingReportFilterByPogKeyUiDataFilter,
        dbDataFilterRemoveEmptySpace,
        mappingReportFilterByPogKeyUiDataFilter.length
      );
      await expect(dataCompare).toEqual(true);
      let rowLengthMappingReport = mappingReportFilterByPogKeyUiData.length;
      await expect(storeCountValuePogsTab).toEqual(
        rowLengthMappingReport.toString()
      );
      let pogDbKeyColumnIndex = await planogramsPage.findIndexNumFromArray(
        await planogramsPage.pogTabHeaders,
        navigatorData.pog_DBKey_text
      );
      let pogDBKeyListOnMappingReport =
        await navigatorPage.extractArrayFromArrays(
          mappingReportFilterByPogKeyUiData,
          pogDbKeyColumnIndex - 1
        );
      let pogDBKeyListOnMappingReportUnique =
        await planogramsPage.getUniqueArrayValue(pogDBKeyListOnMappingReport);
      await expect(displayMappingPogDbKey).toEqual(
        pogDBKeyListOnMappingReportUnique.toString()
      );
      let numOfStoresListIndex = await planogramsPage.findIndexNumFromArray(
        await planogramsPage.pogTabHeaders,
        navigatorData.col_noOfStores
      );
      let numOfStoresListOnMappingReport =
        await navigatorPage.extractArrayFromArrays(
          mappingReportFilterByPogKeyUiData,
          numOfStoresListIndex - 1
        );
      let numOfStoresListOnMappingReportUnique =
        await planogramsPage.getUniqueArrayValue(
          numOfStoresListOnMappingReport
        );
      await expect(rowLengthMappingReport.toString()).toEqual(
        numOfStoresListOnMappingReportUnique.toString()
      );
      await expect(rowLengthMappingReport.toString()).toEqual(
        storeCountValuePogsTab.toString()
      );
    } else {
      await assert.fail(0, 1, "Error: Db did not print any data !");
    }
  });

  it("Validate copy to clipboard feature on Pogs tab", async () => {
    let lengthOfTableRow = await navigatorPage.tableRowList.length;
    let rowIndexNum = 3;
    let dbKeyColumnList = await navigatorPage.getColumnArrayList("DBKey");
    let pogDBKeyListOnPOGsTab = dbKeyColumnList.slice(0, rowIndexNum);
    if (lengthOfTableRow >= rowIndexNum) {
      let numOfSelectedCheckBoxes = [lengthOfTableRow - rowIndexNum];
      for (let i = 0; i < numOfSelectedCheckBoxes.length; i++) {
        await navigatorPage.clickRowCheckBox(
          await planogramsPage.table,
          numOfSelectedCheckBoxes[i]
        );
        let countSelectedCheckBox = await navigatorPage.returnNumOfCheckBox(
          await planogramsPage.table
        );
        let pogDisplayString =
          await planogramsPage.totalSelectedDisplayString.getText();
        let pogCountFromPogString = await planogramsPage.extractDigitsByIndex(
          pogDisplayString,
          0
        );
        await expect(countSelectedCheckBox).toEqual(rowIndexNum);
        await expect(parseInt(pogCountFromPogString)).toEqual(rowIndexNum);
        await planogramsPage.clickOn(await planogramsPage.copySelectedPogBtn);
        await planogramsPage.clickOn(await navigatorPage.navigatorTabBtn);
        await navigatorPage.waitForPageLoad(
          await navigatorPage.filterCriteriaText,
          navigatorData.selectList_FilterCriteria_Text
        );
        await navigatorPage.clickFilterCriteria("POG Key");
        await browser.keys(["Command", "v"]);
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.goTo("POGs");
        await navigatorPage.waitBrowserToLoadPage(5000);
        let pogTabFilterByPogKeyUiData = await navigatorPage.getAllUIData();
        let pogTabFilterByPogKeyUiDataClean =
          await navigatorPage.removeElementsFromArrayOfArrays(
            pogTabFilterByPogKeyUiData,
            "PDF",
            "PSA"
          );
        let pogDbKeyColumnIndex = await planogramsPage.findIndexNumFromArray(
          await planogramsPage.pogTabHeaders,
          "DBKey"
        );
        let dBKeyListFilteredOnPOGsTab =
          await navigatorPage.extractArrayFromArrays(
            pogTabFilterByPogKeyUiData,
            pogDbKeyColumnIndex - 1
          );
        await expect(pogDBKeyListOnPOGsTab).toEqual(dBKeyListFilteredOnPOGsTab);
        await expect(dBKeyListFilteredOnPOGsTab.length).toEqual(rowIndexNum);
        let dbData = await result.getResult(
          await qrString.pogTabQueryWithDivDbKey
            .replace("@Div", divNumber)
            .replace("@DbKey", dBKeyListFilteredOnPOGsTab.join("', '")),
          DBNames.SFP_STAGE
        );
        if ((await dbData.length) >= 1) {
          let dbDataFilter = await navigatorPage.replaceArrayElement(
            dbData,
            "",
            null
          );
          let dbDataFilterRemoveEmptySpace =
            await planogramsPage.removeWhiteSpaceFromArrayOfArrays(
              dbDataFilter
            );
          let data = await excelReader.dataCompare(
            await dbDataFilterRemoveEmptySpace,
            pogTabFilterByPogKeyUiDataClean,
            await dbDataFilterRemoveEmptySpace.length
          );
          await expect(data).toEqual(true);
        } else {
          await assert.fail(0, 1, "Error: Db did not print any data !");
        }
      }
    }
  });
});
