const navigatorPage = require('../NavigatorPO/navigator.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const result = require("../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
const {DBNames} = require("../../../envConfig.js");

let upc;
describe('verifyClearOptions: Verify clear options functionality', async () => {

    before(async () => {

        await navigatorPage.waitBrowserToLoadPage(3000);
await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000);
        upc =  (await (await (await result.getResult(dynamicQuery.upcListForPogItemByStore, DBNames.SFP_STAGE))).flat())[0];
    })

    it('Verify DropList function with Single data selection on Navigator page', async () => {
        await navigatorPage.searchByValue('UPC', upc);
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect', navigatorData.div_706_Smiths_Division);
        await navigatorPage.selectSingleValue('Stores', 'store', navigatorData.store_706_00028);
        await navigatorPage.selectSingleValue('Department', 'dept', navigatorData.dept_06_PHARMACY);
        await navigatorPage.selectSingleValue('SubDept', 'sub-dept', navigatorData.PHARMACY_65_RX_OTC);
        await navigatorPage.genericClearOptions('List');
        await navigatorPage.validateEmptySelectList();
    })

    it('verify DropDropdowns function with Multiple data selection on Navigator page', async () => {
        await navigatorPage.searchByValue('UPC', upc);
        await navigatorPage.selectMultipleValues('Division', 'divisionMultiSelect',  navigatorData.divisionlist);
        await navigatorPage.selectMultipleValues('Stores', 'store', navigatorData.storelist);
        await navigatorPage.selectMultipleValues('Department', 'dept',  navigatorData.departmentList);
        await navigatorPage.selectMultipleValues('SubDept', 'sub-dept', navigatorData.subDepartmentList);
        await navigatorPage.selectMultipleValues('Commodity', 'commodity', navigatorData.commodityList);
        await navigatorPage.selectMultipleValues('CommodityGroup', 'comm-group', navigatorData.commodityGroupList);
        await navigatorPage.selectMultipleValues('PlanogramStatus', 'pog-status',   navigatorData.planogramStatusList);
        await navigatorPage.selectSingleValue('EventType', 'event-type', navigatorData.eventType_KOM)
        await navigatorPage.selectSingleValue('EventName', 'event-name', navigatorData.eventName_SPE)
        await navigatorPage.genericClearOptions('Drop Downs');
        await navigatorPage.validateDropdownValue('Division','Select Division');
        await navigatorPage.validateDropdownValue('Stores','Select Store');
        await navigatorPage.validateDropdownValue( 'Department','Select Department');
        await navigatorPage.validateDropdownValue( 'SubDept','Select Sub Department');
        await navigatorPage.validateDropdownValue( 'PlanogramStatus','Select Planogram Status');
        await navigatorPage.validateDropdownValue( 'Commodity','Select Commodity');
        await navigatorPage.validateDropdownValue( 'CommodityGroup','Select Commodity Group');
    })

    it('Verify DropAll function with Single data selection on Navigator page', async () => {
        await navigatorPage.searchByValue('UPC', upc);
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect', navigatorData.div_706_Smiths_Division)
        await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_706_00028)
        await navigatorPage.selectSingleValue('Department', 'dept', navigatorData.dept_06_PHARMACY)
        await navigatorPage.selectSingleValue('SubDept', 'sub-dept', navigatorData.PHARMACY_65_RX_OTC)
        await navigatorPage.selectSingleValue('Commodity', 'commodity', navigatorData.RX_OTC_250_DIABETIC_TESTING_SUPPLIES)
        await navigatorPage.selectSingleValue('CommodityGroup', 'comm-group', navigatorData.DIABETIC_TESTING_SUPPLIES)
        await navigatorPage.selectSingleValue('PlanogramStatus', 'pog-status', navigatorData.planogramStatusLive)
        await navigatorPage.selectSingleValue('FloorplanStatus', 'floorplan', navigatorData.planogramStatusLive)
        await navigatorPage.genericClearOptions('All');
        await navigatorPage.validateEmptySelectList();
        await navigatorPage.validateDropdownValue('Division','Select Division');
        await navigatorPage.validateDropdownValue('Stores','Select Store');
        await navigatorPage.validateDropdownValue( 'Department','Select Department');
        await navigatorPage.validateDropdownValue( 'SubDept','Select Sub Department');
        await navigatorPage.validateDropdownValue( 'PlanogramStatus','Select Planogram Status');
        await navigatorPage.validateDropdownValue( 'Commodity','Select Commodity');
        await navigatorPage.validateDropdownValue( 'CommodityGroup','Select Commodity Group');
    })

})
