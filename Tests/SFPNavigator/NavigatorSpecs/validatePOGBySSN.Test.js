const navigatorPage = require('../NavigatorPO/navigator.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const excelReader = require('../../../util/excelReader');
const qrString = require('../../../SQLConnection/queryString');
const { DBNames } = require('../../../envConfig.js');
const result = require("../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");

let divNumber, ssn;
describe('validatePOGBySSN: Validate UI data has no duplicates, excel data matches to db and verify SSN value' +' POG tab', () => {

    divNumber = navigatorData.div_016_Columbus_Division.substring(navigatorData.subString_0,navigatorData.subString_3)

    beforeEach(async ()=>{
        await navigatorPage.waitBrowserToLoadPage(3000);
await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_016_Columbus_Division);
        await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_016_00506);
        ssn =  await  (await (await result.getResult(dynamicQuery.ssnListForPogsTab, DBNames.SFP_STAGE)))[0].toString();
        await navigatorPage.searchByValue('SSN', await ssn );
        await navigatorPage.goTo('POGs');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.waitForPageLoad(navigatorPage.btnExportToExcel,navigatorData.Export_To_Excel_text);
    })

    it('POG tab: Should validate the downloaded excel file and compare the contents with DB data', async () => {

        await navigatorPage.exportToExcel();
        await navigatorPage.waitBrowserToLoadPage(6000);
        let dataCompare = await excelReader.excelDataCompareAllRow(qrString.pogSsnQuery.replace('@Div',divNumber).replace('@Upc', ssn), DBNames.SFP_STAGE);
        await expect(dataCompare).toEqual(true);
    })

    it('POG tab: should validate duplicates on excel data from POG tab ', async () => {

        await navigatorPage.exportToExcel();
        await navigatorPage.waitBrowserToLoadPage(6000);
        await expect (await excelReader.validateDuplicates()).toEqual(true);
    })

    it('POG tab: Should validate Space Station Column values from UI results match with input SSN value', async () => {

        let spaceStationRowValues = await navigatorPage.getColumnArrayList('Space Station Name');
        spaceStationRowValues.forEach(item => {
          expect(item).toEqual(ssn);
        })
    })

})
