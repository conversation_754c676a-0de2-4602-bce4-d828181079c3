const navigatorPage = require('../NavigatorPO/navigator.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const result = require("../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
const {DBNames} = require("../../../envConfig.js");
const assert = require('chai').assert;

describe('validatePageNumbers: Validate page numbers against column count on MappingReport, Floorplans and Store tabs', () => {

    beforeEach(async () =>{

        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(5000);
        await navigatorPage.selectDropDownValues('Division', 'divisionMultiSelect',0,4);
    })

    afterEach(async () =>{

        await navigatorPage.waitBrowserToLoadPage(3000);
        let pageNumbers = await navigatorPage.getPageNumberList();
        for (let index = 0; index < pageNumbers.length-1; index++) {
            await navigatorPage.waitBrowserToLoadPage(2000)
            await navigatorPage.selectPageNumber(await pageNumbers[index]);
            await navigatorPage.waitBrowserToLoadPage(2000)
            let rowCount = await navigatorPage.divColumnList.length;
            await assert.isAtLeast(parseInt(pageNumbers[index]), parseInt(rowCount), 'PageNumber index value should be equal or bigger than table row number');
        }
    })

    it('Validate dropdown page numbers against column numbers on Mapping Report', async () => {

        await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_016_00251);
        await navigatorPage.goTo('Mapping Report');

    })

    it('Validate dropdown page numbers against column number on Floorplans', async () => {

        await navigatorPage.selectMultipleValues('Stores', 'store', navigatorData.storelist);
        await navigatorPage.goTo('Floorplans');
    })

    it('Validate dropdown page numbers against column number on Store', async () => {

        const upc =  (await (await (await result.getResult(dynamicQuery.upcListForPageNumbers, DBNames.SFP_STAGE))).flat())[0];
        await navigatorPage.searchByValue('UPC',  upc);
        await navigatorPage.goTo('Store');
    })
})
