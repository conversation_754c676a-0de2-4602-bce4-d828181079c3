const { assert } = require("chai");
const DBNames = require("../../../envConfig.js");
const navigatorPage = require("../NavigatorPO/navigator.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const planogramsPage = require("../NavigatorPO/planograms.page");
const result = require("../../../SQLConnection/server.js.ts");
const qrString = require("../../../SQLConnection/queryString");
const excelReader = require("../../../util/excelReader");
const mappingReportPage = require("../NavigatorPO/mappingReport.page");

let rowLength;
describe("validateEditPogsOnPogsTab: Validate edit functionality of selected pogs from POGs tab", () => {
  const division = navigatorData.div_018_Michigan_Division;
  const commodity =
    navigatorData.comm_01_GROC_ALL_OTHER_001_CANNED_FRUIT_FRUIT_BOWLS;
  it("Validate deletion of a pog and compare pogInfoTable UI data to db", async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.commodityDropdown, values: commodity },
    ]);
    await navigatorPage.goTo("POGs");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.waitForPageLoad(
      navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
    await navigatorPage.waitBrowserToLoadPage(3000);
    rowLength = 3;
    let lengthOfTableRow = await navigatorPage.tableRowList.length;
    if (lengthOfTableRow >= rowLength) {
      let numOfSelectedCheckBoxes = [lengthOfTableRow - rowLength];
      for (let index = 0; index < numOfSelectedCheckBoxes.length; index++) {
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.clickRowCheckBox(
          await planogramsPage.table,
          numOfSelectedCheckBoxes[index]
        );
        let countSelectedCheckBox = await navigatorPage.returnNumOfCheckBox(
          await planogramsPage.table
        );
        let text = await planogramsPage.totalSelectedDisplayString.getText();
        let numOfSelectedPogFromString =
          await planogramsPage.extractDigitsByIndex(text, 0);
        await expect(countSelectedCheckBox.toString()).toEqual(
          numOfSelectedPogFromString
        );
        await planogramsPage.clickOn(await planogramsPage.selectedPogInfo);
        await mappingReportPage.waitForPageLoad(
          await mappingReportPage.filtersBtn,
          "Filters"
        );
        let numOfSelectedPogBeforeDelete =
          await mappingReportPage.extractDigits(
            await mappingReportPage.pogDbKeyFilterString.getText()
          );
        // // validate cancel pog feature
        await mappingReportPage.validatePogEdit(
          "Cancel",
          await mappingReportPage.pogInfoTable
        );
        // validate delete pog feature
        await mappingReportPage.validatePogEdit(
          "Apply",
          await mappingReportPage.pogInfoTable
        );
        let infoPogTableUiData = await mappingReportPage.getAllUIData(
          await mappingReportPage.pogInfoTable
        );
        let infoPogTablePogList =
          await mappingReportPage.extractArrayFromArrays(infoPogTableUiData, 0);
        let numOfSelectedPogAfterDelete = await mappingReportPage.extractDigits(
          await mappingReportPage.pogDbKeyFilterString.getText()
        );
        await expect(numOfSelectedPogBeforeDelete).toEqual(
          (parseInt(numOfSelectedPogAfterDelete) + 1).toString()
        );
        let dbData = await result.getResult(
          await qrString.pogInfoTableQuery.replace(
            "@DbKey",
            infoPogTablePogList.join("', '")
          ),
          DBNames.SFP_STAGE
        );
        let dataCompare = await excelReader.dataCompare(
          infoPogTableUiData.sort(),
          dbData.sort(),
          dbData.length
        );
        await expect(await dataCompare).toEqual(true);
      }
    } else {
      await assert.fail(
        0,
        1,
        "Row numbers must be bigger than rowLength! Update the input data !"
      );
    }
  });
});
