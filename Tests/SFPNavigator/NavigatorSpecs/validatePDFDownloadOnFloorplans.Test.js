const navigatorPage = require('../NavigatorPO/navigator.page');
const floorplansPage = require('../NavigatorPO/floorPlan.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const pdfReader = require('./../../../util/pdfReader')
const {assert} = require("chai");

describe('validatePDFDownloadOnFloorplans: Validate pdf and POG on Store Planogram Item tab', () => {

    it(' should validate the pdf is clickable and downloadable from Floorplans tab @Headless', async () => {
        await navigatorPage.waitBrowserToLoadPage(3000);
await navigatorPage.open('navigator');
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_021_Central_Division);
        await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_021_00016);
        await navigatorPage.selectSingleValue('Department', 'dept', navigatorData.dept_06_PHARMACY);
        await navigatorPage.goTo('Floorplans');
        let initialFilesCount = pdfReader.getFilesCount()
        let parentGUID = await browser.getWindowHandle()
        await navigatorPage.getLinkText('FP Layout')
        await navigatorPage.waitBrowserToLoadPage(5000)
        await floorplansPage.validateNewTab(parentGUID)       
        await navigatorPage.waitBrowserToLoadPage(5000)
        let finalFilesCount = pdfReader.getFilesCount()
         if(finalFilesCount > initialFilesCount) {
             await expect(finalFilesCount-initialFilesCount).toEqual(1);
             await browser.switchToWindow(parentGUID);
         } else {
             await assert.fail(0, 1, "Error: Could not download pdf; finalFilesCount should be higher than 1 !");
         }
    })

    it(' should validate the pdf is clickable and downloadable from Floorplans when FloorplanStatus is pending tab @Headless', async () => {
        await navigatorPage.waitBrowserToLoadPage(3000);
await navigatorPage.open('navigator');
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',  navigatorData.div_701_Fred_Meyer_Stores);
        // eslint-disable-next-line camelcase
        //comment out following line in the TEST environment
        // await navigatorPage.selectSingleValue('FloorplanStatus', navigatorData.floorPlanStatusPending);
        await navigatorPage.goTo('Floorplans');
        let initialFilesCount = pdfReader.getFilesCount()
        let parentGUID = await browser.getWindowHandle()
        await navigatorPage.waitBrowserToLoadPage(6000)
        await navigatorPage.getLinkText('FP Layout');
        await navigatorPage.waitBrowserToLoadPage(5000)
        await floorplansPage.validateNewTab(parentGUID)       
        await navigatorPage.waitBrowserToLoadPage(5000)
        let finalFilesCount = pdfReader.getFilesCount()
        if(finalFilesCount > initialFilesCount) {
            await expect(finalFilesCount-initialFilesCount).toEqual(1);
            await browser.switchToWindow(parentGUID);
        } else {
            await assert.fail(0, 1, "Error: Could not download pdf; finalFilesCount should be higher than 1 !");
        }
    })

})
