const navigatorPage = require("../NavigatorPO/navigator.page");
const mappingReportPage = require("../NavigatorPO/mappingReport.page");
const floorPlanPage = require("../NavigatorPO/floorPlan.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const underscorelib = require("underscore");
const { assert } = require("chai");

describe("validateFpDepartmentDropdowns: Validate Department data on Mapping Report tab and FloorPlan tab", () => {
  const division = navigatorData.div_014_Cincinnati_Division;
  const store = navigatorData.storeList_Dual_MappingReport;
  beforeEach(async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
    ]);
  });

  it("Mapping Report Select FP Department (dropdown):  compare FPDropdown options to UI Dept column data", async () => {
    await navigatorPage.goTo("Mapping Report");
    await navigatorPage.clickOn(navigatorPage.moduleLink);
    await navigatorPage.waitBrowserToLoadPage(3000);
    let fpValuesUi = await mappingReportPage.getAllColumnValues("Dept");
    let uniqueDeptValuesUi = await mappingReportPage.getUniqueArrayValue(
      fpValuesUi
    );
    await navigatorPage.clickOn(await mappingReportPage.filtersBtn);
    let fpDepartDropdownList = await mappingReportPage.getFilterDropdownList(
      "FloorplanDepartment",
      "fp-department"
    );
    let fpDepartDropdownListFiltered = fpDepartDropdownList.filter(
      (item) => !item.includes("Select All")
    );
    await expect(
      underscorelib.isEqual(
        fpDepartDropdownListFiltered.sort(),
        uniqueDeptValuesUi.sort()
      )
    ).toBe(true);
  });

  it("FloorPlan Select FP Department (dropdown):  compare FPDropdown options to UI Dept column data", async () => {
    await navigatorPage.waitBrowserToLoadPage(1000);
    await navigatorPage.goTo("Floorplans");
    await navigatorPage.clickOn(await floorPlanPage.moduleLink);
    await navigatorPage.waitBrowserToLoadPage(1000);
    let fpValuesUi = await floorPlanPage.getAllDepartColumnValues();
    let uniqueDeptValuesUi = await floorPlanPage.getUniqueArrayValue(
      fpValuesUi
    );
    let floorplanFpDropdownList = await floorPlanPage.getFPDepartDropdownList();
    let filteredFpValuesUi = floorplanFpDropdownList.filter(
      (item) => !item.includes("Select All")
    );
    await navigatorPage.waitBrowserToLoadPage(3000);
    if (fpValuesUi !== null && filteredFpValuesUi !== null) {
      await expect(
        underscorelib.isEqual(filteredFpValuesUi, uniqueDeptValuesUi)
      ).toBe(true);
    } else {
      await assert.fail(0, 1, "Ui table data did not print ! ");
    }
  });
});
