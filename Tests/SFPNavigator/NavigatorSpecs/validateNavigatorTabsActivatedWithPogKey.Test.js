const navigatorPage = require('../NavigatorPO/navigator.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const result = require("../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
const {DBNames} = require("../../../envConfig.js");

describe('validateNavigatorTabsActivatedWithPogKey: Search By POG Key and Validate tabs getting activated ,POG, Planogram Assortment,Item by POG, POG & Item by store', () => {
    const division = navigatorData.div_016_Columbus_Division
    const store = navigatorData.store_016_00506
    it('Validate the search By POG key and relevant tabs getting activated', async () => {

        await navigatorPage.open("navigator");
        const pogKey =  (await (await (await result.getResult(dynamicQuery.pogKeyForTabHighLight, DBNames.SFP_STAGE))).flat()).toString();
        await navigatorPage.searchByValue('POG Key', await pogKey);
         await navigatorPage.selectMultipleDropdowns([
                    { id: navigatorData.divisionDropdown, values: division },
                    { id: navigatorData.storeDropdown, values: store },
        ]);
        let pogTabClickable = await navigatorPage.elementIsClickable(await navigatorPage.validateTab('POGs'));
        await expect(pogTabClickable).toEqual(true);
        let itemByPogTabClickable = await navigatorPage.elementIsClickable(await navigatorPage.validateTab('Item by POG'));
        await expect(itemByPogTabClickable).toEqual(true);
        let pogItemByStoreTabClickable = await navigatorPage.elementIsClickable(await navigatorPage.validateTab('POG & Item by Store'));
        await expect(pogItemByStoreTabClickable).toEqual(true);
        let displayReportTabNotClickable = await (await (await navigatorPage.validateTab('Display Report'))).isClickable();
        await expect(displayReportTabNotClickable).toEqual(false);
    })

})
