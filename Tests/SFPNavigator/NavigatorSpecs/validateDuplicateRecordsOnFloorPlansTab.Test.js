const navigatorPage = require("../NavigatorPO/navigator.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const excelReader = require("../../../util/excelReader");

describe("validateDuplicateRecordsOnFloorPlansTab: Validate functionality and data on FloorPlans", () => {
  const division = navigatorData.div_014_Cincinnati_Division;
  const store = navigatorData.store_014_00335;
  const planogram = navigatorData.planogramStatusLive;
  const floorplan = navigatorData.floorPlanStatusLive;
  beforeEach(async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
      { id: navigatorData.planogramDropdown, values: planogram },
      { id: navigatorData.floorplanDropdown, values: floorplan },
    ]);
  });

  it("Validate duplicates in Floorplan tab with POG and FP status Live and Pending", async () => {
    await navigatorPage.genericClearOptions("All");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.selectMultipleDropdowns([
      {
        id: navigatorData.divisionDropdown,
        values: navigatorData.divisional_user_division_016,
      },
      {
        id: navigatorData.storeDropdown,
        values: navigatorData.store_016_00506,
      },
      { id: navigatorData.planogramDropdown, values: "Select All" },
      { id: navigatorData.floorplanDropdown, values: "Select All" },
    ]);
  });

  it("Validate duplicates in Floorplan tab with FP status Live", async () => {
    await navigatorPage.selectSingleValue(
      "FloorplanStatus",
      "floorplan",
      navigatorData.floorPlanStatusLive
    );
  });

  it("Validate duplicates in Floorplan tab with FP status Pending", async () => {
    await navigatorPage.selectSingleValue(
      "FloorplanStatus",
      "floorplan",
      navigatorData.floorPlanStatusPending
    );
  });

  it("Validate duplicates in Floorplan tab with FP status Live and Pending", async () => {
    await navigatorPage.selectSelectAllValue(
      "FloorplanStatus",
      "floorplan",
      "Select All"
    );
  });

  it("Validate duplicates in Floorplan tab with Division and Store selected", async () => {
    await navigatorPage.genericClearOptions("All");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store }
    ]);
  });

  afterEach(async () => {
    await navigatorPage.goTo("Floorplans");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.waitForPageLoad(
      navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
    let rowCount = await navigatorPage.getRowCount();
    if (rowCount.toString() !== "0") {
      await navigatorPage.exportToExcel();
      await navigatorPage.waitBrowserToLoadPage(4000);
      await expect(await excelReader.validateDuplicates()).toEqual(true);
    } else {
      await expect(rowCount.toString()).toEqual("0");
    }
  });
});
