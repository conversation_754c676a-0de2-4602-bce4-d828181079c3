const navigatorPage = require("../NavigatorPO/navigator.page");
const mappingReportPage = require("../NavigatorPO/mappingReport.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const result = require("../../../SQLConnection/server.js.ts");
const qrString = require("../../../SQLConnection/queryString");
const { DBNames } = require("../../../envConfig.js");
const compareAllRow = require("../../../util/excelReader.js");
const { assert } = require("chai");

describe("validateAisleDropdownMappingReport: Validate Aisle dropdown on mapping report with Pending and Live pog status", () => {
  const division = navigatorData.div_014_Cincinnati_Division;
  const divNumber = division.substring(
    navigatorData.subString_0,
    navigatorData.subString_3
  );
  const store = navigatorData.storeList_SelectAisleMappingReport;
  const aisleLiveStoreList = navigatorData.storeList_selectAisleListLive;
  const planogram = navigatorData.planogramStatusLive;
  const planogramPending = navigatorData.planogramStatusPending;

  beforeEach(async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
    ]);
  });

  it(
    "PogLive: select multi-checkboxes from Aisle dropdown and compare the data to Aisle number column; POG status - live;" +
      "also validate mapping report table data filtered with Aisle dropdown by comparing ui data to db ",
    async () => {
      await navigatorPage.selectMultipleDropdowns([
        { id: navigatorData.storeDropdown, values: store },
        { id: navigatorData.planogramDropdown, values: planogram },
      ]);

      const storeNumber = await mappingReportPage.splittingDigits(
        await store,
        1
      );
      await navigatorPage.goTo("Mapping Report");
      await navigatorPage.clickOn(await mappingReportPage.filtersBtn);
      await navigatorPage.selectMultipleDropdowns([
        { id: navigatorData.aisleDropDown, values: aisleLiveStoreList },
      ]);
      await navigatorPage.waitBrowserToLoadPage(3000);
      let uiTableData = await navigatorPage.getAllUIData();
      if (uiTableData > 0) {
        let filteredUiData =
          await mappingReportPage.removeElementsFromArrayOfArrays(
            uiTableData,
            "PDF",
            "PSA"
          );
        let aisleColumnIndex = await mappingReportPage.findIndexNumFromArray(
          await mappingReportPage.mrTabHeaders,
          "Aisle #"
        );
        let aisleColumnValues = await mappingReportPage.extractArrayFromArrays(
          uiTableData,
          aisleColumnIndex - 1
        );
        await navigatorPage.waitBrowserToLoadPage(4000);
        let checkIfArrayEmpty = await mappingReportPage.arrayHasNoEmptyValues(
          aisleColumnValues
        );
        await expect(checkIfArrayEmpty).toEqual(true);
        let uniqueAisleColumnValues =
          await mappingReportPage.getUniqueArrayValue(aisleColumnValues);
        let dbData = await result.getResult(
          qrString.mappingReportAisleQuery
            .replace("@Div", divNumber)
            .replace("@Stores", storeNumber.join("', '"))
            .replace("@Aisle", uniqueAisleColumnValues.join("', '")),
          DBNames.SFP_STAGE
        );
        await expect(await uniqueAisleColumnValues).toEqual(
          await aisleLiveStoreList
        );
        let dbDataTreated = dbData.map((arr) =>
          arr.map((val) => (val === "" ? null : String(val).trim()))
        );
        let filteredUiDataTreated = dbData.map((arr) =>
          arr.map((val) => (val === "" ? null : String(val).trim()))
        );
        if (
          filteredUiData.flat().length !== 0 &&
          dbDataTreated.flat().length !== 0
        ) {
          let compareUiToDb = await compareAllRow.dataCompare(
            dbDataTreated,
            filteredUiDataTreated,
            dbDataTreated.length - 1
          );
          await expect(compareUiToDb).toEqual(true);
        } else {
          await assert.fail(
            0,
            1,
            "Error: Table does not have data or could not read table data !"
          );
        }
      }
    }
  );

  it("PogPending:  Select multi-checkboxes from Aisle dropdown and compare the data to Aisle number column; POG status - pending;", async () => {
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.storeDropdown, values: "Select All" },
      { id: navigatorData.planogramDropdown, values: planogramPending },
    ]);
    await navigatorPage.goTo("Mapping Report");
    await navigatorPage.waitForPageLoad(
      await navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
    await navigatorPage.clickOn(await mappingReportPage.filtersBtn);
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.aisleDropDown, values: aisleLiveStoreList },
    ]);
    let aisleColumnValues = await mappingReportPage.getAllColumnValues(
      navigatorData.mappingReport_AisleColumn
    );
    await navigatorPage.waitBrowserToLoadPage(4000);
    let checkIfArrayEmpty = await mappingReportPage.arrayHasNoEmptyValues(
      aisleColumnValues
    );
    await expect(checkIfArrayEmpty).toEqual(true);
    let uniqAisleColumnValues = await mappingReportPage.getUniqueArrayValue(
      aisleColumnValues
    );
    const digitArray = await mappingReportPage.extractingDigitFromArray(
      aisleLiveStoreList,
      "0",
      "4"
    );
    await navigatorPage.waitBrowserToLoadPage(2000);
    await expect(uniqAisleColumnValues).toEqual(digitArray);
  });

  it("PogLive with empty dropdown value: select EMPTY dropdown value from Select Aisle dropdown and compare the data to Aisle Desc column; POG status - Live", async () => {
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.storeDropdown, values: store },
      { id: navigatorData.planogramDropdown, values: planogram },
    ]);
    await navigatorPage.goTo("Mapping Report");
    await navigatorPage.waitForPageLoad(
      await navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
    await navigatorPage.clickOn(await mappingReportPage.filtersBtn);
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.aisleDropDown, values: "Empty" },
    ]);
    let aisleColumnValues = await mappingReportPage.getAllColumnValues(
      navigatorData.mappingReport_AisleColumn
    );
    let uniqAisleColumnValues = await mappingReportPage.getUniqueArrayValue(
      aisleColumnValues
    );
    let stringArray = await uniqAisleColumnValues.join().split(",");
    let checkIfArrayEmpty = await mappingReportPage.arrayHasNoEmptyValues(
      stringArray
    );
    await navigatorPage.waitBrowserToLoadPage(2000);
    await expect(checkIfArrayEmpty).toEqual(false);
  });
});
