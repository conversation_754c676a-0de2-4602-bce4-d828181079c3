const navigatorPage = require("../NavigatorPO/navigator.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");

let divNumberList;
describe("validateCompositeTabDisplaysDifferentDivNums: Validate different divisions appear when the same store values are selected", () => {
  const division = navigatorData.divisionList_011_014;
  const store = navigatorData.sameStoreDifferentDivision;
  it("Validate different divisions display on Composite tab when the same store values are selected for different divisions", async () => {
    divNumberList =
      await navigatorPage.extractDigitsFromArrayAndConcatenateWithSeparator(
        navigatorData.divisionList_011_014,
        ""
      );
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
    ]);
    await navigatorPage.goTo("Composite");
    await navigatorPage.waitForPageLoad(
      navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
    let divColumnList = await navigatorPage.getColumnArrayList("Div");
    let divColumnListUnique = await navigatorPage.getUniqueArrayValue(
      divColumnList
    );
    let dinColumnListUniqueConcat = await navigatorPage.concatDigitsArray(
      divColumnListUnique
    );
    await expect(divNumberList).toEqual(dinColumnListUniqueConcat);
  });
});
