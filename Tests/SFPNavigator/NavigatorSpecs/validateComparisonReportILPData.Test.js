const navigatorPage = require("../NavigatorPO/navigator.page");
const comparisonReport = require("../NavigatorPO/comparisonReport.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const excelReader = require("../../../util/excelReader");
const { assert } = require("chai");
const result = require("../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
const { DBNames } = require("../../../envConfig.js");

let upcList;
describe("validateComparisonReportILPData:validate comparisonReport ILP table data: compare UI to Excel", () => {
  const division = navigatorData.div_016_Columbus_Division;
  const store = navigatorData.store_016_00506;
  const store2 = navigatorData.store_016_multiple;
  before(async () => {
    upcList = await await (
      await await result.getResult(
        dynamicQuery.upcListForILPComparisonTab,
        DBNames.SFP_STAGE
      )
    ).flat();
  });
  beforeEach(async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
    ]);
  });

  it("validate ILP table data by comparing UI to excel when SINGLE store and UPCs are selected", async () => {
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.storeDropdown, values: store },
    ]);
    let upcListFirstFour = await (await upcList.slice(0, 4)).join("\n");
    await navigatorPage.searchByValue("UPC", await upcListFirstFour);
  });

  it("validate ILP table data by comparing UI to excel when MULTIPLE stores and UPCs are selected", async () => {
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.storeDropdown, values: store2 },
    ]);
    let upcListLastFour = await (await upcList.slice(5, 8)).join("\n");
    await navigatorPage.searchByValue("UPC", upcListLastFour);
  });

  afterEach(async () => {
    await navigatorPage.goTo("Comparison Report");
    await navigatorPage.waitForPageLoad(
      comparisonReport.exportExcelBtn,
      navigatorData.Export_To_Excel_text
    );
    await comparisonReport.clickOn(await comparisonReport.filterBtn);
    await navigatorPage.waitForPageLoad(
      comparisonReport.upcText,
      navigatorData.comparisonReport_UPC
    );
    await comparisonReport.clickOn(await comparisonReport.upcDropDown);
    await comparisonReport.clickAndGetUPCDropdownList();
    await navigatorPage.waitForPageLoad(
      comparisonReport.upcText,
      navigatorData.comparisonReport_UPC
    );
    await comparisonReport.clickOn(await comparisonReport.exportExcelBtn);
    await navigatorPage.waitBrowserToLoadPage(4000);
    let excelData = await excelReader.readExcelFile();
    let ilpData = await comparisonReport.getSystemData(
      excelData,
      navigatorData.systemData_ILP
    );
    for (let index = 0; index < ilpData.length; index++) {
      await assert.include(ilpData[index], navigatorData.systemData_ILP);
    }
    let sfpData = await comparisonReport.getSystemData(
      excelData,
      navigatorData.systemData_SFP
    );
    await expect(excelData.length).toEqual(ilpData.length + sfpData.length);
  });
});
