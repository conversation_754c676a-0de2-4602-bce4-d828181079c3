const navigatorPage = require("../NavigatorPO/navigator.page");
const mappingReportPage = require("../NavigatorPO/mappingReport.page");
const dataJson = require("../../../TestData/data.json");
const excelReader = require("../../../util/excelReader");
const qrString = require("../../../SQLConnection/queryString");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const result = require("../../../SQLConnection/server.js.ts");
const { readExcelFile } = require("../../../util/excelReader");
const { DBNames } = require("../../../envConfig.js");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");

let div, divNumber, storeNumber, pogKey, floorKey;
describe("validateMappingReport: Validate data on Mapping Report tab", () => {
  const division = navigatorData.div_014_Cincinnati_Division;
  const store = navigatorData.store_014_00335;
  const planogram = navigatorData.planogramStatusLive;
  const floorplan = navigatorData.floorPlanStatusLive;
  before(async () => {
    pogKey = await (
      await (
        await await result.getResult(
          dynamicQuery.pogKeyForItemByPog,
          DBNames.SFP_STAGE
        )
      ).flat()
    ).toString();
    floorKey = await (
      await (
        await await result.getResult(
          dynamicQuery.floorKeyForMappingReport,
          DBNames.SFP_STAGE
        )
      ).flat()
    ).toString();
    await navigatorPage.open("navigator");
  });

  it("Mapping Report: Should validate the downloaded excel file and compare the contents with DB data", async () => {
    expect(browser).toHaveTitle(dataJson.homeTitle);
    div = division;
    divNumber = div.match(/(\d[\d]*)/g)[0];
    storeNumber = store.split("- ")[1];
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
      { id: navigatorData.planogramDropdown, values: planogram },
      { id: navigatorData.floorplanDropdown, values: floorplan },
    ]);
    await navigatorPage.goTo("Mapping Report");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.waitForPageLoad(
      await navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
    await navigatorPage.clickOn(await mappingReportPage.filtersBtn);
    await (await navigatorPage.btnExportToExcel).waitForClickable();
    await navigatorPage.exportToExcel();
    await navigatorPage.waitBrowserToLoadPage(6000);
    let dataCompare = await excelReader.excelDataCompareAllRow(
      qrString.mappingReportQuery
        .replace("Div", divNumber)
        .replace("Store", storeNumber),
      DBNames.SFP_STAGE
    );
    await expect(dataCompare).toEqual(true);
  });

  it("validateMappingReport: should validate the downloaded excel file and compare the contents with UI data", async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      {
        id: navigatorData.divisionDropdown,
        values: navigatorData.div_016_Columbus_Division,
      },
      {
        id: navigatorData.storeDropdown,
        values: navigatorData.store_016_00506,
      },
    ]);
    await navigatorPage.searchByValue("POG Key", await pogKey);
    await navigatorPage.goTo("Mapping Report");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.waitForTableExist(await mappingReportPage.table);
    await navigatorPage.waitBrowserToLoadPage(6000);
    await navigatorPage.exportToExcel();
    await navigatorPage.waitBrowserToLoadPage(3000);
    let uiData = await await navigatorPage.getAllUIData();
    await navigatorPage.waitBrowserToLoadPage(3000);
    let filteredUiData =
      await mappingReportPage.removeElementsFromArrayOfArrays(
        uiData,
        "PDF",
        "PSA"
      );
    let filteredUiDataClean =
      await mappingReportPage.replaceEmptyStringWithNull(filteredUiData);
    let excelData = await excelReader.readExcelFile();
    await navigatorPage.waitBrowserToLoadPage(6000);
    let excelDataRemoveDoubleSpace = await mappingReportPage.removeDoubleSpaces(
      excelData
    );
    let dataCompare = await excelReader.dataCompareWithNullValue(
      filteredUiDataClean,
      excelDataRemoveDoubleSpace
    );
    await navigatorPage.waitBrowserToLoadPage(3000);
    await expect(dataCompare).toEqual(true);
  });

  it("Validate Mapping Report tab is activated when minimum search criteria is met", async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.searchByValue("Floor Key", floorKey);
    await expect(navigatorPage.validateTab("Mapping Report")).toBeClickable();
    await navigatorPage.goTo("Mapping Report");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.waitForPageLoad(
      await navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
    await navigatorPage.clickOn(await mappingReportPage.filtersBtn);
    await (await navigatorPage.btnExportToExcel).waitForClickable();
  });

  it("Validate No of Item link and data on Mapping Report tab for multiple stores", async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
    ]);
    await navigatorPage.goTo("Mapping Report");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.waitForPageLoad(
      await navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
    await navigatorPage.clickOn(await mappingReportPage.filtersBtn);
    await navigatorPage.waitBrowserToLoadPage(2000);
    let pogKey = await navigatorPage.getLinkText(navigatorData.col_POGDBKey);
    await navigatorPage.getLinkText(navigatorData.col_noOfItems);
    await navigatorPage.waitBrowserToLoadPage(5000);
    await navigatorPage.exportToExcel();
    let excelData = await readExcelFile();
    await navigatorPage.waitBrowserToLoadPage(2000);
    let dbData = await result.getResult(
      await qrString.ItemByPOGQuery_POGDBKey.replace(/[?]/g, pogKey.toString()),
      DBNames.SFP_STAGE
    );
    let dbDataTreat = dbData.map((arr) =>
      arr.map((val) => (val === "null" ? null : val))
    );
    await navigatorPage.waitBrowserToLoadPage(5000);
    let dataCompare = await excelReader.dataCompare(
      excelData.sort(),
      dbDataTreat.sort(),
      dbDataTreat.length
    );
    await expect(dataCompare).toEqual(true);
  });
});
