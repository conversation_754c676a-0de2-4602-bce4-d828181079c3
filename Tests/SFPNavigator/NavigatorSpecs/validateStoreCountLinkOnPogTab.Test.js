const navigatorPage = require("../NavigatorPO/navigator.page");
const mappingReportPage = require("../NavigatorPO/mappingReport.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const result = require("../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
const {DBNames} = require("../../../envConfig.js");
const chaiAssert = require('chai').assert

describe('validateStoreCountLinkOnPogTab: Validate StoreCount link on POGs tab by pogKey and Commodity dropdown', () => {

    beforeEach(async () => {

        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000)
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_016_Columbus_Division);
        await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_016_00506);
    })

        afterEach(async () => {

            await navigatorPage.goTo('POGs');
            await navigatorPage.waitBrowserToLoadPage(3000)
            let pogTab = await navigatorPage.GetText(await navigatorPage.moduleLink);
            await expect(await pogTab).toEqual(navigatorData.Planograms);
            await navigatorPage.waitBrowserToLoadPage(3000)
            let dbKeyColumnValPog = await navigatorPage.getColumnArrayList(navigatorData.dBKey);
            let storeCountColumnValPog = await navigatorPage.getLinkText(navigatorData.store_Count);
            await navigatorPage.waitForPageLoad(await navigatorPage.moduleLink, navigatorData.mapping_Report);
            let mappingReportTab = await navigatorPage.GetText(await navigatorPage.moduleLink);
            await expect(mappingReportTab).toEqual(navigatorData.mapping_Report);
            let mappingStoreCountVal = await navigatorPage.getUniqueArrayValue(await navigatorPage.getColumnArrayList(navigatorData.col_noOfStores));
            let pogKeyDigitMappingReport = await mappingReportPage.extractDigits(await mappingReportPage.textSelectedPOGKey.getText());
            await navigatorPage.waitBrowserToLoadPage(3000)
            await expect(dbKeyColumnValPog.toString()).toEqual(pogKeyDigitMappingReport);
            let numOfRowEntryMappingReport = (await mappingReportPage.displayEntryString.getText()).split(' ')[5];
            await navigatorPage.waitBrowserToLoadPage(2000);
            // Note: storeCountLink value not necessaritly equals to rowNumber on mappingReport in stage; this is known issue; update pogDbeky if necessary
            //await expect(await (await mappingStoreCountVal).toString()).toEqual(await numOfRowEntryMappingReport);
            let mappingReportUiTableData = await navigatorPage.getAllUIData();
            let numOfStoreColumnIndex = await mappingReportPage.findIndexNumFromArray(await mappingReportPage.mrTabHeaders, navigatorData.col_noOfStores);
            let numOfStoreListUniqueMappingReport = await mappingReportPage.returnUniqueStringByArrayIndex(mappingReportUiTableData, numOfStoreColumnIndex - 1);
            await expect(numOfStoreListUniqueMappingReport.toString()).toEqual(storeCountColumnValPog)
            //await expect(numOfRowEntryMappingReport).toEqual(storeCountColumnValPog)
            let pogDbKeyColumnIndex = await mappingReportPage.findIndexNumFromArray(await mappingReportPage.mrTabHeaders, navigatorData.pog_DBKey_text);
            let pogDbKeyListUniqueMappingReport = await mappingReportPage.returnUniqueStringByArrayIndex(mappingReportUiTableData, pogDbKeyColumnIndex - 1);
            await expect(pogDbKeyListUniqueMappingReport.toString()).toEqual(pogKeyDigitMappingReport)
            await expect(pogDbKeyListUniqueMappingReport).toEqual(dbKeyColumnValPog.toString())
            let storeColumnIndex = await mappingReportPage.findIndexNumFromArray(await mappingReportPage.mrTabHeaders, navigatorData.store);
            let storeColumnListUnique = await mappingReportPage.returnUniqueStringByArrayIndex(mappingReportUiTableData, storeColumnIndex - 1);
            await navigatorPage.clickOn(await mappingReportPage.textSelectedPOGKey);
            if ((await mappingReportPage.textSelectedPOGKey.length) === 0) {
                let storeColumnListAfterPogKeyCancelledUnique = await mappingReportPage.returnUniqueStringByArrayIndex(mappingReportUiTableData, storeColumnIndex - 1);
                await chaiAssert.notEqual(storeColumnListUnique, storeColumnListAfterPogKeyCancelledUnique, 'Store column list before and after cancelling pogKey is different')
            }
        })

        it('should validate StoreCount column link on POGs tab when Division and PogKeys are selected on Navigator main page ', async () => {

            const pogKey =  (await (await (await result.getResult(dynamicQuery.dbKeyForStoreCountPogTab, DBNames.SFP_STAGE))).flat()).toString();
            await navigatorPage.searchByValue('POG Key', await pogKey);
        })

        it('should validate StoreCount column link on POGs tab when Division and Commodity dropdowns are selected on Navigator main page', async () => {
            await navigatorPage.selectSingleValue('Commodity', 'commodity', navigatorData.comm_01_GROC_ALL_OTHER_005_CAN_SEAFOOD_SHELF_STABLE);
        })

    })


