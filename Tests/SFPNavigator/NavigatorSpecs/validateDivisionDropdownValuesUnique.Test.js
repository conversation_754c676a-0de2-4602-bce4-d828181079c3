const navigatorPage = require("../NavigatorPO/navigator.page");

describe("validateDivisionDropdownValuesUnique: Validate division dropdown does not contain duplicate values", () => {
  it("Validate division dropdown values are unique ", async () => {
    await navigatorPage.open("navigator");
    let divisionDropdownList = await navigatorPage.getDropDownList(
      "Division",
      "divisionMultiSelect"
    );
    const noDuplicates = await navigatorPage.arrayHasNoDuplicates(
      divisionDropdownList
    );
    await expect(noDuplicates).toEqual(true);
  });
});
