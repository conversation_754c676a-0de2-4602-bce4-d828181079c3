const navigatorPage = require('../NavigatorPO/navigator.page');
const mappingReportPage = require('../NavigatorPO/mappingReport.page.js');
const itemByPOGPage = require('../NavigatorPO/itemByPOG.page')
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const {validateDuplicates} = require('../../../util/excelReader');
const qrString = require('../../../SQLConnection/queryString');
const result = require("../../../SQLConnection/server.js.ts");
const {DBNames} = require("../../../envConfig.js");
const excelReader = require("../../../util/excelReader");

let pogDbKey
describe('validateSSNLinkFromMappingReportOnItemByPog: compare excel data to db and search criteria data on Item By Pog ', () => {

    beforeEach(async () => {
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(5000);
    })

    it('ItemByPogCompareExcelToDB: validate downloaded excel file and compare all data to DB on Item By Pog after clicking SSN link on mapping report', async () => {

        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_014_Cincinnati_Division);
        await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_014_00353);
        await navigatorPage.selectSingleValue('PlanogramStatus', 'pog-status', navigatorData.planogramStatusLive);
        await navigatorPage.goTo('Mapping Report');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.waitForPageLoad(mappingReportPage.filtersBtn, navigatorData.mappingReport_Filters);
        await navigatorPage.getLinkText(navigatorData.space_station_name);
        let modelTxt = await itemByPOGPage.moduleLink.getText();
        await expect(modelTxt).toEqual(navigatorData.Item_By_POG);
        await navigatorPage.waitBrowserToLoadPage(3000);
        pogDbKey = await navigatorPage.getColumnArrayList(navigatorData.col_POGDBKey);
        await navigatorPage.waitForPageLoad(navigatorPage.btnExportToExcel, 'Export To Excel');
        await navigatorPage.exportToExcel();
        await navigatorPage.waitBrowserToLoadPage(3000);
        let excelData = await excelReader.readExcelFile();
        let dbData = await result.getResult(await (qrString.ItemByPOGQuery_POGDBKey.replace(/[?]/g, await navigatorPage.getUniqueArrayValue(pogDbKey))), DBNames.SFP_STAGE);
        let dbDataClean = await navigatorPage.removeElementsFromArrayOfArrays(dbData,"null",null);
        let dataCompare = await excelReader.dataCompare(excelData.sort(), dbDataClean.sort(), 50);
        await expect(dataCompare).toEqual(true);
    })

    it('ItemByPogValidateSearchCriteriaPage: validate Search Criteria data on Item By Pog after clicking SSN link on mapping report', async () => {

        let divText;
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',  navigatorData.div_014_Cincinnati_Division)
        divText = await navigatorPage.divisionDropdown.getText();
        await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_014_00353);
        let storeText = await navigatorPage.storeDropdown.getText();
        await navigatorPage.selectSingleValue('PlanogramStatus', 'pog-status', navigatorData.planogramStatusLive);
        let pogStatusText = await navigatorPage.planogramStatusDropdown.getText();
        await navigatorPage.goTo('Mapping Report');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.waitForPageLoad(await mappingReportPage.filtersBtn, navigatorData.mappingReport_Filters);
        await navigatorPage.getLinkText(navigatorData.space_station_name);
        await navigatorPage.waitForPageLoad(navigatorPage.btnSearchCriteria, navigatorData.ItemByPOG_SearchCriteria);
        await navigatorPage.waitBrowserToLoadPage(2000);
        await navigatorPage.clickOn(await navigatorPage.btnSearchCriteria);
        let divValue = await navigatorPage.getTextOfElement(await navigatorPage.criteriaSearchDivision);
        await navigatorPage.waitBrowserToLoadPage(2000);
        await expect(divText).toEqual(await divValue);
        let storeValue = await itemByPOGPage.searchPopupStore.getText();
        await expect(storeValue).toEqual(storeText)
        let PogStatusValue = await itemByPOGPage.searchPopupPogStatus.getText();
        await navigatorPage.waitBrowserToLoadPage(2000);
        await expect(PogStatusValue).toEqual(pogStatusText);
    })

    it('ItemByPogValidateNoDuplicateOnExcel: validate excel data has no duplicates on Item By Pog after clicking SSN link on mapping report', async () => {

        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_014_Cincinnati_Division);
        await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_014_00335);
        await navigatorPage.selectSingleValue('PlanogramStatus', 'pog-status', navigatorData.planogramStatusLive);
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.goTo('Mapping Report');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.waitForPageLoad(mappingReportPage.filtersBtn, navigatorData.mappingReport_Filters);
        await navigatorPage.getLinkText(navigatorData.space_station_name);
        await navigatorPage.waitBrowserToLoadPage(2000);
        let modelText = await itemByPOGPage.moduleLink.getText();
        await navigatorPage.waitBrowserToLoadPage(2000);
        await expect(await modelText).toEqual(navigatorData.Item_By_POG);
        await navigatorPage.exportToExcel();
        await navigatorPage.waitBrowserToLoadPage(4000);
        await expect (await validateDuplicates()).toEqual(true);                           
    })

})
