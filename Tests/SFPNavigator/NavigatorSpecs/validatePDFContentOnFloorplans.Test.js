const navigatorPage = require('../NavigatorPO/navigator.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const {getPDFText} = require('../../../util/pdfReader');
const { assert } = require('chai');

describe('validatePDFContentOnFloorplans: Validate DBKey in PDF', () => {

    it('Should validate DBKey in PDF for Floorplans tab @Headless', async () => {

        await navigatorPage.waitBrowserToLoadPage(3000);
await navigatorPage.open('navigator');                                                             
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_021_Central_Division);
        await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_021_00016);
        await navigatorPage.selectSingleValue('Department', 'dept', navigatorData.dept_06_PHARMACY);
})

    it('Should validate DBKey in PDF for Floorplans tab when FloorplanStatus is pending @Headless', async () => {

        await navigatorPage.waitBrowserToLoadPage(3000);
await navigatorPage.open('navigator');                                                             
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_014_Cincinnati_Division);
        await navigatorPage.selectSingleValue('Stores', 'store',  "014 - 00335");
        await navigatorPage.selectSingleValue('Department', 'dept', navigatorData.dept_06_PHARMACY);
        // eslint-disable-next-line camelcase
        //comment out following line in the TEST environment
        // await navigatorPage.selectSingleValue('FloorplanStatus', 'floorplan', navigatorData.floorPlanStatusPending);
})

    afterEach( async ()=> {
        await navigatorPage.waitBrowserToLoadPage(4000);
        await navigatorPage.goTo('Floorplans');
        let parentGuId = browser.getWindowHandle();
        await navigatorPage.waitBrowserToLoadPage(4000);
        let dbKey = await navigatorPage.getLinkText('DBKey');
        await navigatorPage.getLinkText('FP Layout');
        await navigatorPage.waitBrowserToLoadPage(8000);
        await navigatorPage.validateNewTab(parentGuId);
        let data = await getPDFText();
        let pogDbKeyPdf = await ((data.split('DBKey:')[1]).substr(1,7));
        if(typeof pogDbKeyPdf !== 'undefined' || pogDbKeyPdf.length === 0){
            await assert.equal(await pogDbKeyPdf.trim(), await dbKey);
            await browser.switchToWindow(parentGuId);
        } else {
            await assert.fail(0, 1, "Error: Could not read pdf content; data is empty !");
        }
    })


})
