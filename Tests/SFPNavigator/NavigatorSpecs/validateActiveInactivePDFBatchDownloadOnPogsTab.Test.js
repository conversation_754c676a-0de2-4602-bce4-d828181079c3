const navigatorPage = require("../NavigatorPO/navigator.page");
const planogramsPage = require("../NavigatorPO/planograms.page");
const {
  extractFile,
  getFilesCount,
  zipCount,
  getExtractedFilesCount,
} = require("../../../util/fileExtractor");
const result = require("../../../SQLConnection/server.js.ts");
const qrString = require("../../../SQLConnection/queryString");
const { DBNames } = require("../../../envConfig.js");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const assert = require("chai").expect;

let inactivePogs, activePogs;
describe("validateActiveInactivePDFBatchDownloadOnPogsTab: Validate Batch download of PDF files", () => {
  const division = navigatorData.filterDivisionListPOG;
  beforeEach(async () => {
    await navigatorPage.open("navigator");
    inactivePogs = await (
      await result.getResult(
        qrString.selectInactivePDFPOGKeys,
        DBNames.SFP_STAGE
      )
    ).flat();
    activePogs = await (
      await result.getResult(qrString.selectActivePDFPOGKeys, DBNames.SFP_STAGE)
    ).flat();
  });

  it("Validate Batch download of PDF files on Planograms Tab when there are both active and inactive PDF Links: @Headless ", async () => {
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
    ]);

    // Search By POGs
    const allPogs = [...inactivePogs, ...activePogs].join("\n");
    await navigatorPage.searchByValue("POG Key", await allPogs);
    await navigatorPage.goTo("POGs");

    // Initial File Count
    const expectedPDFCount = await planogramsPage.getActivePDFCount("PDF");
    const initialFilesCount = getFilesCount();

    // Trigger Batch Download
    await navigatorPage.selectColumnCheckBox("PDF");
    await navigatorPage.batchDownload();

    // Wait for files to download
    const downloadComplete = await navigatorPage.waitForDownload(
      initialFilesCount
    );
    if (!downloadComplete) {
      throw new Error("PDFs did not finish downloading within expected time.");
    }
    await extractFile();
    await navigatorPage.waitBrowserToLoadPage(5000);
    const extractedFileCount = await getExtractedFilesCount();
    // Compare Extracted vs Expected
    expect(extractedFileCount).toEqual(expectedPDFCount);
  });

  it("Validate Batch download of PDF files on Planograms Tab when there are only inactive PDF Links: @Headless", async () => {
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: "Select All" },
    ]);
    // Search using only inactive POGs
    const pogList = inactivePogs.join("\n");
    await navigatorPage.searchByValue("POG Key", pogList);
    await navigatorPage.goTo("POGs");

    // Get initial download count
    const initialFilesCount = getFilesCount();

    // Trigger download
    await navigatorPage.selectColumnCheckBox("PDF");
    await navigatorPage.batchDownload();

    // Wait for files to download

    const downloadComplete = await navigatorPage.waitForDownload(
      initialFilesCount
    );
    if (downloadComplete) {
      const finalFilesCount = getFilesCount();
      expect(finalFilesCount - initialFilesCount).toEqual(1);

      await extractFile();
      await navigatorPage.waitBrowserToLoadPage(2000);

      const extractedCount = await zipCount();
      expect(extractedCount).toEqual(0); // Nothing inside ZIP
    } else {
      throw new Error("PDF didn't get to download!");
    }
  });
});
