
const {assert} = require("chai");
const navigatorPage = require('../NavigatorPO/navigator.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const planogramsPage = require("../NavigatorPO/planograms.page");
const mappingPage = require("../NavigatorPO/mappingReport.page");
let lengthOfTableRow, lengthOfTableRowList, pogFromStringInitialValue, storeCountStringInitialValue;
let numberOfStoreCountClicks = 2, minimumEntries = 5;


describe('ValidatePOGTabStoreCountCheckBox: validate POG storeCount checkbox overrides selected store count',  () => {
    const division = navigatorData.div_018_Michigan_Division
    const commodity = navigatorData.comm_01_GROC_ALL_OTHER_001_CANNED_FRUIT_FRUIT_BOWLS
    beforeEach(async () => {
        await navigatorPage.open('navigator');
        await navigatorPage.selectMultipleDropdowns([
            { id: navigatorData.divisionDropdown, values: division },
            { id: navigatorData.commodityDropdown, values: commodity },  
        ]); 
        await navigatorPage.goTo('POGs');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.waitForPageLoad(navigatorPage.btnExportToExcel, navigatorData.Export_To_Excel_text);
        lengthOfTableRowList = await navigatorPage.getRowCount();
        lengthOfTableRow = await planogramsPage.sumArrayElements(lengthOfTableRowList, lengthOfTableRowList.length)
        await navigatorPage.waitBrowserToLoadPage(6000);
        let totalSelectedInitialValue = await planogramsPage.GetText(await planogramsPage.totalSelectedDisplayString);
        storeCountStringInitialValue = await planogramsPage.extractDigitsByIndex(totalSelectedInitialValue, 1);
        pogFromStringInitialValue = await planogramsPage.extractDigitsByIndex(totalSelectedInitialValue, 0);
        await expect(navigatorData.subString_0).toEqual(Number(storeCountStringInitialValue));
        await expect(navigatorData.subString_0).toEqual(Number(pogFromStringInitialValue));
    })

    it('Header select all, then click store count header to override - validate label and checkmarks ', async () => {

        if (await lengthOfTableRow >= minimumEntries) {
            let storeCountColumnList = await navigatorPage.getColumnArrayList(navigatorData.store_Count);
            await planogramsPage.clickOn(await planogramsPage.checkBoxHeader);
            await navigatorPage.waitBrowserToLoadPage(3000);
            let totalSelectedAfterSelectAll = await planogramsPage.GetText(planogramsPage.totalSelectedDisplayString);
            let numOfSelectedPogFromStringAfterSelectAll = await planogramsPage.extractDigitsByIndex(totalSelectedAfterSelectAll, 0);
            let storeCountListSumAfterSelectFirstPage = await planogramsPage.sumArrayElements(storeCountColumnList, lengthOfTableRowList[0]);
            let storeCountStringAfterSelectAll = await planogramsPage.extractDigitsByIndex(totalSelectedAfterSelectAll, 1);
            let numOfSelectedEntriesBefore = await navigatorPage.returnNumOfCheckBox(await planogramsPage.table);
            for (let index = 0; index < numberOfStoreCountClicks; index++) {
                await expect(true).toEqual(await navigatorPage.columnCheckBoxIsClickable(await navigatorPage.tableValue, navigatorData.navigator_POG_StoreCount));
                // Handle checkbox actions for index 0 and 1
                if (index === 0) {
                    await navigatorPage.clickColumnCheckBox(await navigatorPage.tableValue, navigatorData.navigator_POG_StoreCount);
                } else if (index === 1) {
                    await navigatorPage.unclickColumnCheckBox(await navigatorPage.tableValue, navigatorData.navigator_POG_StoreCount);
                }
                await navigatorPage.waitBrowserToLoadPage(6000);
                let totalSelectedAfterCheckBox = await planogramsPage.GetText(await planogramsPage.totalSelectedDisplayString);
                let numOfSelectedPogFromStringAfterCheckBox = await planogramsPage.extractDigitsByIndex(totalSelectedAfterCheckBox, 0);
                let numOfSelectedStoreFromStringAfterCheckBox = await planogramsPage.extractDigitsByIndex(totalSelectedAfterCheckBox, 1);
                let numOfSelectedEntriesAfter = await navigatorPage.returnNumOfCheckBox(await planogramsPage.table);
                if (index === 0) {
                    storeCountColumnList = await mappingPage.getAllColumnValues(navigatorData.store_Count);
                    let storeCountListSumAfterCheckBox = await planogramsPage.sumArrayElements(storeCountColumnList, storeCountColumnList.length);
                    await expect(Number(storeCountStringInitialValue)).toEqual(numOfSelectedEntriesAfter);
                    await expect(Number(lengthOfTableRowList[index])).toEqual(Number(numOfSelectedPogFromStringAfterSelectAll));
                    await expect(Number(lengthOfTableRow)).toEqual(Number(numOfSelectedPogFromStringAfterCheckBox));
                    await expect(Number(lengthOfTableRowList[index])).toEqual(numOfSelectedEntriesBefore);
                    await expect(storeCountListSumAfterCheckBox.toString()).toEqual(numOfSelectedStoreFromStringAfterCheckBox);
                    await expect(Number(storeCountStringAfterSelectAll)).toEqual(storeCountListSumAfterSelectFirstPage);
                }
                else if (index === 1) {
                    await expect(pogFromStringInitialValue).toEqual(numOfSelectedPogFromStringAfterCheckBox);
                    await expect(storeCountStringInitialValue).toEqual(numOfSelectedStoreFromStringAfterCheckBox);
                    await expect(Number(storeCountStringInitialValue)).toEqual(numOfSelectedEntriesAfter);
                }
            }
        } else {
            await assert.fail(0, 1, "Row numbers must be bigger than 5. Update the input data.");
        }
    })

    it('select 3 rows - unselect each row - Validate label is zero after last row unselected. ', async () => {

        let numOfSelectedCheckBoxes =  lengthOfTableRowList[0] - 3;
        if(lengthOfTableRow >= minimumEntries) {
            await navigatorPage.clickRowCheckBox(await planogramsPage.table, numOfSelectedCheckBoxes);
            await navigatorPage.waitBrowserToLoadPage(3000);
            let totalSelectedAfterFirstClick = await planogramsPage.GetText(await planogramsPage.totalSelectedDisplayString);
            let numberOfSelectedRow = await navigatorPage.returnNumOfCheckBox(await planogramsPage.table);
            let numOfSelectedStoreFromString = await planogramsPage.extractDigitsByIndex(totalSelectedAfterFirstClick, 1);
            let numOfSelectedPogFromStringAfterSelectAll = await planogramsPage.extractDigitsByIndex(totalSelectedAfterFirstClick, 0);
            let storeCountColumn = await navigatorPage.getColumnArrayList(navigatorData.store_Count);
            let storeCountListSumAfterCheckBox = await planogramsPage.sumArrayElements(storeCountColumn, navigatorData.subString_3);
            await expect(storeCountListSumAfterCheckBox).toEqual(Number(numOfSelectedStoreFromString));
            await expect(navigatorData.subString_3).toEqual(numberOfSelectedRow);
            await expect(navigatorData.subString_3).toEqual(Number(numOfSelectedPogFromStringAfterSelectAll));
            await navigatorPage.clickRowCheckBox(await planogramsPage.table, numOfSelectedCheckBoxes);
            totalSelectedAfterFirstClick = await planogramsPage.GetText(await planogramsPage.totalSelectedDisplayString);
            numberOfSelectedRow = await navigatorPage.returnNumOfCheckBox(await planogramsPage.table);
            numOfSelectedStoreFromString = await planogramsPage.extractDigitsByIndex(totalSelectedAfterFirstClick, 1);
            numOfSelectedPogFromStringAfterSelectAll = await planogramsPage.extractDigitsByIndex(totalSelectedAfterFirstClick, 0);
            await expect(storeCountStringInitialValue).toEqual(numOfSelectedStoreFromString);
            await expect(Number(storeCountStringInitialValue)).toEqual(numberOfSelectedRow);
            await expect(pogFromStringInitialValue).toEqual(numOfSelectedPogFromStringAfterSelectAll);
        }
        else {
            await assert.fail(0, 1, "Row numbers must be bigger than 5. Update the input data.")
        }
    })

    it('Select [1,3,5] rows - select store count checkbox to override - unselect store count checkbox - Validate label POGs and Stores  ', async () => {

        if (lengthOfTableRow >= minimumEntries) {
            let storeCountColumnList = await mappingPage.getAllColumnValues(navigatorData.store_Count);
            await planogramsPage.clickOn(await planogramsPage.btnPageOne);
            await navigatorPage.waitBrowserToLoadPage(3000);
            // Define the checkboxes to select
            let numOfSelectedCheckBoxes = [lengthOfTableRowList[0] - 1, lengthOfTableRowList[0] - 3, lengthOfTableRowList[0] - 5];
            // Iterate over the selected checkboxes
            for (let rowsSelectedIndex = navigatorData.subString_0; rowsSelectedIndex < numOfSelectedCheckBoxes.length; rowsSelectedIndex++) {
                let currentRow = numOfSelectedCheckBoxes[rowsSelectedIndex];
                await navigatorPage.clickRowCheckBox(await planogramsPage.table, currentRow);
                await navigatorPage.waitBrowserToLoadPage(6000);
                // Calculate store count sum and extract selected data
                let storeCountListSum = await planogramsPage.sumArrayElements(storeCountColumnList, Math.abs(currentRow - lengthOfTableRowList[0]));
                let totalSelectedAfterFirstClick = await planogramsPage.GetText(await planogramsPage.totalSelectedDisplayString);
                let numOfSelectedStoreFromString = await planogramsPage.extractDigitsByIndex(totalSelectedAfterFirstClick, 1);
                let numOfSelectedPogFromString = await planogramsPage.extractDigitsByIndex(totalSelectedAfterFirstClick, 0);
                // Validate selected store count and POG values
                await expect(storeCountListSum).toEqual(Number(numOfSelectedStoreFromString));
                await expect(Math.abs(currentRow - lengthOfTableRowList[0])).toEqual(Number(numOfSelectedPogFromString));
                // Handle column checkbox clicks and validation
                for (let storeCountHeaderSelectedIndex = 0; storeCountHeaderSelectedIndex < navigatorData.subString_2; storeCountHeaderSelectedIndex++) {
                    await expect(true).toEqual(await navigatorPage.columnCheckBoxIsClickable(await navigatorPage.tableValue, navigatorData.navigator_POG_StoreCount));
                    // Toggle the checkbox based on the index
                    if (storeCountHeaderSelectedIndex === navigatorData.subString_0) {
                        await navigatorPage.clickColumnCheckBox(await navigatorPage.tableValue, navigatorData.navigator_POG_StoreCount);
                    } else if (storeCountHeaderSelectedIndex === navigatorData.subString_1) {
                        await navigatorPage.unclickColumnCheckBox(await navigatorPage.tableValue, navigatorData.navigator_POG_StoreCount);
                    }
                    await navigatorPage.waitBrowserToLoadPage(3000);
                    // Extract updated selected values
                    totalSelectedAfterFirstClick = await planogramsPage.GetText(await planogramsPage.totalSelectedDisplayString);
                    numOfSelectedPogFromString = await planogramsPage.extractDigitsByIndex(totalSelectedAfterFirstClick, 0);
                    numOfSelectedStoreFromString = await planogramsPage.extractDigitsByIndex(totalSelectedAfterFirstClick, 1);
                    storeCountListSum = await planogramsPage.sumArrayElements(storeCountColumnList, lengthOfTableRow);
                    // Validation after checkbox click
                    if (storeCountHeaderSelectedIndex === navigatorData.subString_0) {
                        await expect(storeCountListSum.toString()).toEqual(numOfSelectedStoreFromString);
                        await expect(lengthOfTableRow).toEqual(Number(numOfSelectedPogFromString));
                    } else if (storeCountHeaderSelectedIndex === navigatorData.subString_1) {
                        await expect(storeCountStringInitialValue).toEqual(numOfSelectedStoreFromString);
                        await expect(pogFromStringInitialValue).toEqual(numOfSelectedPogFromString);
                    }
                    // Validate the page entry count
                    let pageEntriesCount = await planogramsPage.GetText(planogramsPage.pageEntryCountLabel);
                    let pageEntriesFromString = await planogramsPage.extractDigitsByIndex(pageEntriesCount, navigatorData.subString_2);
                    await expect(lengthOfTableRow).toEqual(Number(pageEntriesFromString));
                }
            }
        } else {
            await assert.fail(0, 1, "Row numbers must be bigger than 5. Update the input data.");
        }
    })

})
