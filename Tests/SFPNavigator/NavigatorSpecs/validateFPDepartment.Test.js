const result = require("../../../SQLConnection/server.js.ts");
const qrString = require("../../../SQLConnection/queryString");
const navigatorPage = require("../NavigatorPO/navigator.page");
const mappingReportPage = require("../NavigatorPO/mappingReport.page");
const floorPlanPage = require("../NavigatorPO/floorPlan.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const underscorelib = require("underscore");
const { DBNames } = require("../../../envConfig.js");

let divNum = navigatorData.div_014_Cincinnati_Division.match(/(\d[\d]*)/g)[0];
let storeNum = navigatorData.store_014_00353.match(/(\d[\d]*)/g)[1];
describe("validateFPDepartment: Validate Floorplan FP Department and Mapping Report FP Department dropdowns: compare db to UI dropdown list", () => {
  const division = navigatorData.div_014_Cincinnati_Division;
  const store = navigatorData.store_014_00353;
  const planogram = navigatorData.planogramStatusLive;
  beforeEach(async () => {
    await navigatorPage.open("navigator");
  });

  it("Mapping Report Select FP Department (dropdown):  compare DB to UI", async () => {
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
      { id: navigatorData.planogramDropdown, values: planogram },
    ]);
    await navigatorPage.goTo("Mapping Report");
    await navigatorPage.waitForPageLoad(
      await mappingReportPage.filtersBtn,
      "Filters"
    );
    await navigatorPage.clickOn(await mappingReportPage.filtersBtn);
    await navigatorPage.waitBrowserToLoadPage(3000);
    let mappingReportFpDepartUiList =
      await mappingReportPage.getFilterDropdownList(
        "FloorplanDepartment",
        "fp-department"
      );
    await navigatorPage.waitBrowserToLoadPage(3000);
    let filteredMappingReportFpDepartUiList =
      mappingReportFpDepartUiList.filter(
        (item) => !item.includes(navigatorData.SelectAll)
      );
    let mappingReportFpDepartDbList = await (
      await result.getResult(
        qrString.FpDepartmentMR.replace("@Div", divNum).replace(
          "@Store",
          storeNum
        ),
        DBNames.SFP_STAGE
      )
    ).flat();
    await expect(
      underscorelib.isEqual(
        filteredMappingReportFpDepartUiList,
        mappingReportFpDepartDbList
      )
    ).toBe(true);
  });

  it(" FloorPlan Select FP Department (dropdown):  compare DB to UI", async () => {
    await navigatorPage.selectMultipleDropdowns([
      {
        id: navigatorData.divisionDropdown,
        values: navigatorData.div_016_Columbus_Division,
      },
      {
        id: navigatorData.storeDropdown,
        values: navigatorData.store_016_00268,
      },
    ]);
    await navigatorPage.goTo("Floorplans");
    await navigatorPage.clickOn(await navigatorPage.moduleLink);
    await navigatorPage.waitBrowserToLoadPage(3000);
    let floorPlansFpDepartUiList =
      await floorPlanPage.getFPDepartDropdownList();
    await navigatorPage.waitBrowserToLoadPage(3000);
    let filteredFloorPlansFpDepartUiList = floorPlansFpDepartUiList.filter(
      (item) => !item.includes(navigatorData.SelectAll)
    );
    let floorPlansFpDepartDbList = await (
      await result.getResult(
        qrString.FpDepartFloorPlansQuery,
        DBNames.SFP_STAGE
      )
    ).flat();
    await expect(
      underscorelib.isEqual(
        filteredFloorPlansFpDepartUiList.sort(),
        floorPlansFpDepartDbList.sort()
      )
    ).toBe(true);
  });
});
