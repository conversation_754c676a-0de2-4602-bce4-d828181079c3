const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const queryData = require('../../../TestData/SFPNavigatorData/navigatorQueryData.json');
const navigatorPage = require('../NavigatorPO/navigator.page');
const planogramsPage = require("../NavigatorPO/planograms.page");
const mappingReportPage = require("../NavigatorPO/mappingReport.page");
const qrString = require("../../../SQLConnection/queryString");
const result = require("../../../SQLConnection/server.js.ts");
const {DBNames} = require("../../../envConfig");
const excelReader = require("../../../util/excelReader");
const {assert} = require("chai");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");


describe('validatePOGMappingReportFlow: Validate content loads with Flow - POG (select 3 rows, select mapping info), redirects to Mapping tab, click POG tab ', () => {

    it("Validate page loads - Flow - POG (select 3 rows, select mapping info), redirects to Mapping tab, click POG tab ", async () => {

        let divNumber = navigatorData.div_016_Columbus_Division.match(/(\d[\d]*)/g)[0];
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(4000);
        const pogKeyList =  (await (await (await result.getResult(dynamicQuery.dbKeyListForPogMappingReport, DBNames.SFP_STAGE))).flat());
        await navigatorPage.searchByValue('POG Key', pogKeyList.join('\n'));
        const pogKeyListForQuery = await (await pogKeyList.join("','"));
        await navigatorPage.selectSingleValue("Division", 'divisionMultiSelect', navigatorData.div_016_Columbus_Division);
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.selectMultipleValues('Stores', 'store',  navigatorData.storeList_StoreType_POG);
        await navigatorPage.goTo('POGs');
        await navigatorPage.waitBrowserToLoadPage(6000);
        let POGTableSize = await navigatorPage.getRowCount();
        await navigatorPage.clickOn(await navigatorPage.previousButtonClass);
        await navigatorPage.waitBrowserToLoadPage(2000);
        await navigatorPage.clickRowCheckBox(await planogramsPage.table, POGTableSize[0] - 3);
        await planogramsPage.clickOn(await planogramsPage.selectedPogInfo);
        await navigatorPage.waitBrowserToLoadPage(3000);
        await mappingReportPage.cancelCircleButton.click();
        await expect(await mappingReportPage.moduleLink.getText()).toEqual(navigatorData.mapping_Report);
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.goTo('POGs');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await expect(await mappingReportPage.moduleLink.getText()).toEqual(navigatorData.Planograms);
        await expect(await navigatorPage.footerLabelValidation()).toEqual(true);
        await navigatorPage.clickOn(await navigatorPage.previousButtonClass);
        let dbData = await result.getResult(qrString.pogTabQueryWithDivStoreDBKey.replace('@Div', divNumber).replace('@Store', queryData.POG_StoreNumber).replace('@DBKey', pogKeyListForQuery),DBNames.SFP_STAGE);
        let uiPOG = await navigatorPage.getAllUIData();
        if(dbData.length > 0 ){
            uiPOG = await navigatorPage.removeElementsFromArrayOfArrays(uiPOG,'PDF','PSA');
            let dbDataNoSpaces = await navigatorPage.removeWhiteSpaceFromArrayOfArrays(dbData);
            await navigatorPage.waitBrowserToLoadPage(3000);
            let dbUIPOGCompare = await excelReader.dataCompare(uiPOG, dbDataNoSpaces,dbDataNoSpaces.length-1);
            await expect(dbUIPOGCompare).toEqual(true);
        }else{
            await assert.fail(0, 1, "Error: Db did not print any data !");
        }

         })
            })
