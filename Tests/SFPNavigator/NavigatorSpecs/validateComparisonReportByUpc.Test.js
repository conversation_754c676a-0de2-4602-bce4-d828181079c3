const navigatorPage = require("../NavigatorPO/navigator.page");
const comparisonReport = require("../NavigatorPO/comparisonReport.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
// Commented as dynamic query is currently commented
// const result = require('../../../SQLConnection/server.js.ts');
// const dynamicQuery = require('../../../SQLConnection/dynamicQueryNav');
// const { DBNames } = require('../../../envConfig.js');

describe("validateComparisonReportByUpc: Validate UPC data on ItemLocationComparisonView table from Comparison Report tab", () => {
  let divSubstring, storeSubstring, upcList;
  const division = navigatorData.div_016_Columbus_Division;
  const store = navigatorData.store_016_00506;

  beforeEach(async () => {
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.open("navigator");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
    ]);
    divSubstring = division.substring(
      navigatorData.subString_0,
      navigatorData.subString_3
    );
    storeSubstring = store.substring(navigatorData.subString_6);
    await navigatorPage.goTo("Mapping Report");
    await navigatorPage.clickSSNRowNumber(0);
    upcList = await navigatorPage.getColumnArrayList("UPC");

    // NOTE: dynamic Query commented due to bug found of duplicate UPCs
    // upcList = await (await (await (await result.getResult(dynamicQuery.upcListForComparisonTab, DBNames.SFP_STAGE))).flat());

    upcList = upcList.slice(0, 3); // Limit to 3 UPCs
    await navigatorPage.navigatorTabBtn.click();
  });

  it("Validates UPC data on ItemLocationComparison table for a single UPC", async () => {
    await navigatorPage.searchByValue("UPC", upcList[0]);
    await navigatorPage.goTo("Comparison Report");
    await navigatorPage.waitBrowserToLoadPage(4000);
    await navigatorPage.waitForPageLoad(
      comparisonReport.exportExcelBtn,
      navigatorData.Export_To_Excel_text
    );

    const upc = upcList[0];
    const divTable = (
      await comparisonReport.getColumnArrayList("DIV")
    ).toString();
    const storeTable = (
      await comparisonReport.getColumnArrayList("Store")
    ).toString();
    const upcTable = (
      await comparisonReport.getColumnArrayList("UPC")
    ).toString();

    await expect(divSubstring).toEqual(divTable);
    await expect(storeSubstring).toEqual(storeTable);
    await expect(upc).toEqual(upcTable);

    await comparisonReport.clickOn(comparisonReport.filterBtn);
    await navigatorPage.waitForPageLoad(comparisonReport.upcText, "UPC:");
    const upcFromDropdownText = await comparisonReport.upcDropDown.getText();
    await expect(`${[upcTable].length} Item Selected`).toEqual(
      upcFromDropdownText
    );
  });

  it("Validates multiple UPCs in both the UPC dropdown and ItemLocationComparison table", async () => {
    const upcListTreated = upcList.join("\n");
    await navigatorPage.searchByValue("UPC", upcListTreated);
    await navigatorPage.goTo("Comparison Report");
    await navigatorPage.waitBrowserToLoadPage(2000);
    await navigatorPage.waitForPageLoad(
      comparisonReport.exportExcelBtn,
      navigatorData.Export_To_Excel_text
    );

    const upcStringArray = upcListTreated.split("\n").sort();
    await comparisonReport.clickOn(comparisonReport.filterBtn);
    await navigatorPage.waitForPageLoad(
      comparisonReport.upcText,
      navigatorData.comparisonReport_UPC
    );
    await comparisonReport.clickOn(comparisonReport.upcDropDown);

    const upcFromDropdown = await comparisonReport.clickAndGetUPCDropdownList();
    const uniqueDiv = (
      await comparisonReport.getUniqueArrayValue(
        await comparisonReport.getColumnArrayList("DIV")
      )
    ).toString();
    const uniqueStore = (
      await comparisonReport.getUniqueArrayValue(
        await comparisonReport.getColumnArrayList("Store")
      )
    ).toString();
    const upcIlcTable = (
      await comparisonReport.getColumnArrayList("UPC")
    ).sort();

    await expect(divSubstring).toEqual(uniqueDiv);
    await expect(storeSubstring).toEqual(uniqueStore);
    await expect(upcStringArray.sort()).toEqual(upcIlcTable.sort());
    await expect(upcFromDropdown.sort()).toEqual(upcIlcTable);
  });
});
