const navigatorPage = require("../NavigatorPO/navigator.page");
const comparisonReport = require("../NavigatorPO/comparisonReport.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const { clickOn } = require("../NavigatorPO/navigator.page");
const excelReader = require("../../../util/excelReader");
const queryString = require("../../../SQLConnection/queryString");
const { DBNames } = require("../../../envConfig.js");
const { assert } = require("chai");
const result = require("../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");

let divValueForQuery,
  storeValueForQuery,
  excelData,
  comparisonTableDBData,
  upcList;
describe("validateComparisonReportExcelData: Validate UPC data on SFP table from Comparison Report tab", () => {
  const division = navigatorData.div_016_Columbus_Division;
  divValueForQuery = division.split(" - ")[0];
  const store = navigatorData.store_016_00506;
  storeValueForQuery = store.split(" - ")[1];
  before(async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
    ]);
    upcList = await await (
      await await result.getResult(
        dynamicQuery.upcListForComparisonTab,
        DBNames.SFP_STAGE
      )
    ).flat();
    let upcListTreated = await upcList.join("\n");
    await navigatorPage.searchByValue("UPC", upcListTreated);
    await navigatorPage.goTo("Comparison Report");
    await navigatorPage.waitForPageLoad(
      comparisonReport.exportExcelBtn,
      navigatorData.Export_To_Excel_text
    );
    await clickOn(await comparisonReport.filterBtn);
    await navigatorPage.waitForPageLoad(
      comparisonReport.upcText,
      navigatorData.comparisonReport_UPC
    );
    await clickOn(await comparisonReport.upcDropDown);
    await comparisonReport.clickAndGetUPCDropdownList();
    await clickOn(comparisonReport.exportExcelBtn);
    await navigatorPage.waitBrowserToLoadPage(6000);
    excelData = await excelReader.readExcelFile();
  });

  it.skip("validate excel data to db data for SFP table when multiple UPCs are selected", async () => {
    let excelSFPData = await comparisonReport.getSystemData(
      excelData,
      navigatorData.systemData_SFP
    );
    let excelSFPDataClean =
      await navigatorPage.removeEmptyStringsFromNestedArray(await excelSFPData);
    let excelSFPDataCleanSort = await comparisonReport.sortNestedArrayByIndex(
      excelSFPDataClean,
      7
    );
    await navigatorPage.waitBrowserToLoadPage(3000);
    let dbSFPData = await result.getResult(
      queryString.comparisonReportSFPData
        .replace("@upcList", await upcList.toString())
        .replace("@str", await storeValueForQuery)
        .replace("@div", await divValueForQuery),
      DBNames.SFP_STAGE
    );
    // db is coming empty
    if ((await dbSFPData.length) > 1) {
      let dbSFPDataSort = await comparisonReport.sortNestedArrayByIndex(
        dbSFPData,
        7
      );
      let compare = await excelReader.dataCompare(
        excelSFPDataCleanSort,
        dbSFPDataSort,
        excelSFPDataCleanSort.length
      );
      await expect(compare).toEqual(true);
    } else {
      await assert.fail(0, 1, "Error: Db data is empty !");
    }
  });

  it("validate ILP data comparison report Tab Excel to DB", async () => {
    let excelILPData = await comparisonReport.getSystemData(
      excelData,
      navigatorData.systemData_ILP
    );

    await navigatorPage.waitBrowserToLoadPage(3000);
    let ilpTableDBData = await result.getResult(
      queryString.ilpComparisonNew
        .replace("@upcList", await upcList)
        .replace("@str", await '3726')
        .replace("@div", await divValueForQuery),
      DBNames.ILP_STAGE
    );
    if (
      (await ilpTableDBData.length) > 1
    ) {
      let compareFirstSubArrayExcelToDb = await excelReader.dataCompare(
        ilpTableDBData,
        excelILPData,
        excelILPData.length
      );
      await expect(compareFirstSubArrayExcelToDb).toEqual(true);

    } else {
      await assert.fail(0, 1, "Error: Db data is empty !");
    }
  });
});
