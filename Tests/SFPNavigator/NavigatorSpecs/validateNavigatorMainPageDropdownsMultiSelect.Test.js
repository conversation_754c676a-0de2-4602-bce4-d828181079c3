const navigatorPage = require("../NavigatorPO/navigator.page");
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const {assert} = require("chai");
const assertTrue = require('chai').assert;

describe('validateNavigatorMainPageDropdownsMultiSelect: Validate Navigator main page dropdowns can select multiple values ', () => {
    const division = navigatorData.divisionListFP
    const department = navigatorData.departmentList;
      const subDepartment = navigatorData.subDepartmentList;
      const commodity = navigatorData.filterCommodityList;
      const commodityGroup = navigatorData.commodityGroupList;
    it('Validate Navigator main page dropdowns can select multiple values ', async () => {

        await navigatorPage.open("navigator");
        await navigatorPage.selectMultipleDropdowns([
            { id: navigatorData.divisionDropdown, values: division },
        ]);
        await navigatorPage.validateDropdownValue('Division', await ((division).length) + ' Items Selected');
        await navigatorPage.waitBrowserToLoadPage(2000);
        let departmentList = await navigatorPage.getDropDownList('Department', 'dept');
        if(departmentList.length >1){
             await navigatorPage.selectMultipleDropdowns([
                { id: navigatorData.departmentDropdown, values: department },
                { id: navigatorData.subDeptDropdown, values: subDepartment },
                { id: navigatorData.commodityDropdown, values: commodity },
                { id: navigatorData.commGroupDropdown, values: commodityGroup },
            ]);
            await navigatorPage.validateDropdownValue( 'SubDept',await ((subDepartment).length) + ' Items Selected');
            await assertTrue.isAbove((subDepartment).length, 1, 'SubDept dropdown does not have value')
            await navigatorPage.validateDropdownValue( 'Commodity',await ((commodity).length ) + ' Items Selected');
            await assertTrue.isAbove((commodity).length, 1, 'Commodity dropdown does not have value')
            await navigatorPage.validateDropdownValue( 'CommodityGroup',await ((commodityGroup).length ) + ' Items Selected');
            await assertTrue.isAbove((commodityGroup).length, 1, 'CommodityGroup dropdown does not have value')
        } else{
            await assert.fail(0, 1, "Error: Department dropdown cannot be clickable !");
        }
    })

    it('Validate Store dropdown values are sorted in ascending order when Division dropdown gets selected twice randomly  ', async () => {

        await navigatorPage.open("navigator");
        await navigatorPage.selectDropDownValues('Division', 'divisionMultiSelect',2,3);
        await navigatorPage.selectDropDownValues('Stores', 'store', 0,2);
        await navigatorPage.selectDropDownValues('Division', 'divisionMultiSelect',0,2);
        let storeDropdownList = await navigatorPage.getDropDownList('Stores', 'store');
        let extractedDivList = await navigatorPage.extractSubArraysByIndex(storeDropdownList, 3);
        await navigatorPage.waitBrowserToLoadPage(3000);
        const extractedDivListUnique = await navigatorPage.getUniqueUnsortedArrayValue(await extractedDivList);
        let extractedDivListUniqueAscending = await navigatorPage.isAscending(await extractedDivListUnique);
        await expect(extractedDivListUniqueAscending).toEqual(true);
    })

})
