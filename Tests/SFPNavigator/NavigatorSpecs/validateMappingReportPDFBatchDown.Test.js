const navigatorPage = require("../NavigatorPO/navigator.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const {
  getFilesCount,
  extractFile,
  getExtractedFilesCount,
} = require("../../../util/fileExtractor");
const { assert } = require("chai");
const mappingReportPage = require("../NavigatorPO/mappingReport.page");

let pogDbKeyListExtractedUniqueLength;

describe("validateMappingReportPDFBatchDown: Validate Batch download of PDF files on mapping report tab", () => {
  const division = navigatorData.div_011_Atlanta_Division;
  const store = navigatorData.store_011_00025;
  it("Validate Batch download of PDF files on MappingReport Tab when one, three and five rows are selected", async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
    ]);
    await navigatorPage.goTo("Mapping Report");
    await navigatorPage.waitForPageLoad(
      await navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
    await navigatorPage.waitForTableExist(await mappingReportPage.table);
    let lengthOfTableRow = await navigatorPage.tableRowList.length;
    if (lengthOfTableRow >= 5) {
      let numOfSelectedCheckBoxes = [
        lengthOfTableRow - 1,
        lengthOfTableRow - 3,
        lengthOfTableRow - 5,
      ];
      let numOfSelectedPogDbKey = [1, 3, 5];
      for (let index = 1; index < numOfSelectedCheckBoxes.length; index++) {
        await navigatorPage.clickColumnCheckBox(
          await mappingReportPage.mappingReportTableValue,
          navigatorData.mappingpdf_text
        );
        let poGDbKeyList = await navigatorPage.getColumnArrayList(
          navigatorData.pog_DBKey_text
        );
        let poGDbKeyListExtracted = await navigatorPage.getNumberArrayElements(
          poGDbKeyList,
          numOfSelectedPogDbKey[index]
        );
        pogDbKeyListExtractedUniqueLength = await (
          await navigatorPage.getUniqueArrayValue(poGDbKeyListExtracted)
        ).length;
        let initialFilesCount = getFilesCount();
        await navigatorPage.clickRowCheckBox(
          await mappingReportPage.mappingReportTableValue,
          numOfSelectedCheckBoxes[index]
        );
        await navigatorPage.batchDownload();
        await navigatorPage.waitBrowserToLoadPage(5000);
        let finalFilesCount = getFilesCount();
        await expect(finalFilesCount - initialFilesCount).toEqual(1);
        await extractFile();
        await navigatorPage.waitBrowserToLoadPage(3000);
      }
      const extractedFileNumber = await getExtractedFilesCount();
      await expect(await extractedFileNumber).toEqual(
        await pogDbKeyListExtractedUniqueLength
      );
    } else {
      await assert.fail(
        0,
        1,
        "Error: Row numbers must be bigger than 5! Update the input data !"
      );
    }
  });
});
