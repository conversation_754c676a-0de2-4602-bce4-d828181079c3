const navigatorPage = require("../NavigatorPO/navigator.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const { excelDataCompareAllRow } = require("../../../util/excelReader");
const qrString = require("../../../SQLConnection/queryString");
const { DBNames } = require("../../../envConfig.js");

describe("validateMappingReportMultiCombinationExcelToDB: Validate download excel and compare data to db on Mapping Report tab", () => {
  const division = navigatorData.select_3_divisions;
  const store = navigatorData.select_3_stores;
  const planogram = navigatorData.planogramStatusLive;
  const floorplan = navigatorData.floorPlanStatusLive;
  it("validate download excel and compare data to db on Mapping Report tab with different Div and Stores  ", async () => {
    let divisionListMainPage = await navigatorPage.extractArrayValuesAsArray(
      navigatorData.divisionList2,
      0
    );
    let storeListMainPage = await navigatorPage.extractArrayValuesAsArray(
      navigatorData.storeList2,
      1
    );
    let length = navigatorData.divisionList2.length;
    for (let i = 0; i < length; i++) {
      await navigatorPage.open("navigator");
      await navigatorPage.selectMultipleDropdowns([
        { id: navigatorData.divisionDropdown, values: division[i] },
        { id: navigatorData.storeDropdown, values: store[i] },
        { id: navigatorData.planogramDropdown, values: planogram },
        { id: navigatorData.floorplanDropdown, values: floorplan },
      ]);
      await navigatorPage.goTo("Mapping Report");
      await navigatorPage.exportToExcel();
      let data = await excelDataCompareAllRow(
        qrString.mappingReportDivStoreQuery
          .replace("?", await divisionListMainPage[i])
          .replace("$", await storeListMainPage[i]),
        DBNames.SFP_STAGE
      );
      await expect(data).toEqual(true);
    }
  });
});
