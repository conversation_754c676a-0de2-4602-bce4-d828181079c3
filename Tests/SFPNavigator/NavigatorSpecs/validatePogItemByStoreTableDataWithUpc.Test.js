const navigatorPage = require('../NavigatorPO/navigator.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const comparisonReport = require('../NavigatorPO/comparisonReport.page');
import result from '../../../SQLConnection/server.js.ts';
const queryString = require('../../../SQLConnection/queryString');
import {dataCompare} from '../../../util/excelReader';
const chaiAssert = require('chai').expect;
const { DBNames } = require('../../../envConfig.js');
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");

let upcList, divNumber,storeNumber;
describe('validatePogItemByStoreTableDataWithUpc: Validate data on Store Planogram Item tab', () => {

    it('Validate UPC data on Store Planogram Item tab', async () => {

        divNumber =  navigatorData.div_016_Columbus_Division.substring(navigatorData.subString_0,navigatorData.subString_3);
        storeNumber =  navigatorData.store_016_00506.split('- ')[1];
        await navigatorPage.waitBrowserToLoadPage(3000);
await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000);
        upcList =  (await (await (await result.getResult(dynamicQuery.upcListForPogItemByStore, DBNames.SFP_STAGE))).flat());
        await navigatorPage.searchByValue('UPC', upcList.join("\n"));
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_016_Columbus_Division);
        await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_016_00506);
        await navigatorPage.goTo(navigatorData.POG_Item_by_Store);
        await navigatorPage.btnPageOne.waitForClickable({ timeout: 59000 }, 'Error: tab did not open before time out')
        let UPC = await navigatorPage.getLinkText(navigatorData.col_UPC)
        let uiUPCSummary = await comparisonReport.getAllSystemComparisonReportUIData();
        await chaiAssert(uiUPCSummary.flat()).to.be.an('array').that.includes(UPC);
        let dbUPCSummary = await result.getResult(queryString.comparisonViewUPCSummary.replace('@upc', await upcList.join(",")).replace('@str', await storeNumber).replace('@div', await divNumber), DBNames.SFP_STAGE);
        if(dbUPCSummary.length > 0){
            let data = await dataCompare(await dbUPCSummary,uiUPCSummary);
            await expect(data).toEqual(true);
        }
    })
})
