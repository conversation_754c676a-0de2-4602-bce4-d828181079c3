const navigatorPage = require("../NavigatorPO/navigator.page");
const itemByPOGPage = require("../NavigatorPO/itemByPOG.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const { assert } = require("chai");
const result = require("../../../SQLConnection/server.js.ts");
const qrString = require("../../../SQLConnection/queryString");
const { DBNames } = require("../../../envConfig.js");
const compareAllRow = require("../../../util/excelReader.js");
const queryData = require("../../../TestData/SFPNavigatorData/navigatorQueryData.json");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");

let uiDataMerchAssortDefault,
  originalUiData,
  merchAssortEntryString,
  queryCondition,
  pogKey,
  dbResultIntToEmptyString;

describe("validateMerchandisedAssortmentButton: Validate MerchandisedAssortment button on Item by POG tab", () => {
  const planogram = navigatorData.planogramStatusLive;
  const floorplan = navigatorData.floorPlanStatusLive;
  before(async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.planogramDropdown, values: planogram },
      { id: navigatorData.floorplanDropdown, values: floorplan },
    ]);

    pogKey = await (
      await (
        await await result.getResult(
          dynamicQuery.pogKeyForItemByPogAssortmentButtons,
          DBNames.SFP_STAGE
        )
      ).flat()
    ).toString();
    await navigatorPage.searchByValue("POG Key", pogKey);
    await navigatorPage.goTo("Item by POG");
    await navigatorPage.waitBrowserToLoadPage(3000);
    queryCondition = [
      "REPLACE(REPLACE(REPLACE( prd.NAME, ' ', '{}'), '}{', ''), '{}', ' ')",
    ];
  });

  it("Validate MerchandisedAssortment button exists on ItemByPog tab", async () => {
    const itemByPogsFilterList = await itemByPOGPage.getArrayElementsText(
      await itemByPOGPage.filterBtnList
    );
    let buttonExist = await itemByPOGPage.isElementExist(
      "Merchandised Assortment",
      itemByPogsFilterList
    );
    await expect(await buttonExist).toEqual(true);
  });

  it('Validate ItemByPog table data is the same before and after clicking the "Merch Assortment" button, then compare UI table data to db', async () => {
    originalUiData = await await navigatorPage.getAllUIData();
    await itemByPOGPage.clickFilterTab(
      navigatorData.Merchandised_Assortment_text
    );
    merchAssortEntryString = await itemByPOGPage.entryNumberString.getText();
    await navigatorPage.waitBrowserToLoadPage(3000);
    uiDataMerchAssortDefault = await await navigatorPage.getAllUIData();
    await navigatorPage.waitBrowserToLoadPage(5000);
    let compare = await compareAllRow.dataCompare(
      await originalUiData,
      await uiDataMerchAssortDefault,
      (await originalUiData.length) - 1
    );
    await expect(compare).toEqual(true);
    let dbResult = await result.getResult(
      await qrString.ItemByPOGQueryPOGDBKeyFilter.replace(
        /productNameCondition/g,
        queryCondition[0]
      )
        .replace(/[?]/g, pogKey)
        .replace(
          /FILTER_CONDITION/g,
          queryData.Merchandise_Assortment_condition
        ),
      DBNames.SFP_STAGE
    );
    await navigatorPage.waitBrowserToLoadPage(5000);
    let dbResultTreated = dbResult.map((arr) =>
      arr.map((val) => (val === "null" ? "" : val))
    );
    await navigatorPage.waitBrowserToLoadPage(5000);
    let dbResultIntToString = dbResultTreated.map((arr) =>
      arr.map((val) => (typeof val === "number" ? val.toString() : val))
    );
    dbResultIntToEmptyString = dbResultIntToString.map((arr) =>
      arr.map((val) => (val === null ? "" : val))
    );
    await navigatorPage.waitBrowserToLoadPage(5000);
    if (
      (await uiDataMerchAssortDefault.length) > 0 &&
      (await dbResultIntToString.length) > 0
    ) {
      let originalUiDataClean =
        await navigatorPage.convertNullZeroToEmptyString(await originalUiData);
      let dbResultIntToEmptyStringClean =
        await navigatorPage.convertNullZeroToEmptyString(
          await dbResultIntToEmptyString
        );
      let compareUiToDb = await compareAllRow.dataCompare(
        await navigatorPage.removeDoubleSpaces(await originalUiDataClean),
        await navigatorPage.removeDoubleSpaces(
          await dbResultIntToEmptyStringClean
        ),
        (await originalUiData.length) - 1
      );
      await navigatorPage.waitBrowserToLoadPage(3000);
      await expect(await compareUiToDb).toEqual(true);
    } else {
      await assert.fail(0, 1, "Error: UI table does not have data !");
    }
  });

  it("Validate Merchandised Assortment result excludes deletes column", async () => {
    await itemByPOGPage.clickFilterTab(navigatorData.Full_Assortment);
    await navigatorPage.waitBrowserToLoadPage(3000);
    let fullAssortEntryString = await itemByPOGPage.entryNumberString.getText();
    let uiDataFullAssort = await navigatorPage.getAllUIData();
    let deleteColumnIndex = await itemByPOGPage.findIndexNumFromArray(
      await itemByPOGPage.itemByPogHeaders,
      navigatorData.Delete_Flag
    );
    let deleteColumnFullAssort = await itemByPOGPage.extractArrayFromArrays(
      uiDataFullAssort,
      deleteColumnIndex
    );
    let deleteColumnFullAssortUnique = await itemByPOGPage.getUniqueArrayValue(
      deleteColumnFullAssort
    );
    let deleteColumnFullAssortUniqueTreat =
      await itemByPOGPage.removeValueFromArray(
        deleteColumnFullAssortUnique,
        "0"
      );
    await expect(deleteColumnFullAssortUniqueTreat.toString()).toEqual("1");
    let deleteColumnMerchAssort = await itemByPOGPage.extractArrayFromArrays(
      await uiDataMerchAssortDefault,
      deleteColumnIndex
    );
    let deleteColumnMerchAssortUnique = await itemByPOGPage.getUniqueArrayValue(
      deleteColumnMerchAssort
    );
    await assert.notEqual(
      deleteColumnFullAssortUniqueTreat.toString(),
      deleteColumnFullAssortUnique.toString()
    );
    await assert.notEqual(fullAssortEntryString, merchAssortEntryString);
    await navigatorPage.waitBrowserToLoadPage(3000);
    await assert.notEqual(deleteColumnMerchAssortUnique.toString(), "1");
    await expect(deleteColumnMerchAssortUnique.toString()).toEqual("0");
  });
});
