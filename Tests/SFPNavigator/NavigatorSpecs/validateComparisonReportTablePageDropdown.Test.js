const navigatorPage = require("../NavigatorPO/navigator.page");
const comparisonReport = require("../NavigatorPO/comparisonReport.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const result = require("../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
const { DBNames } = require("../../../envConfig.js");

describe("validateComparisonReportTablePageDropdown:validate ComparisonReport tab ILP and SFP tables page dropdowns when pogDbKey is used ", () => {
  const division = navigatorData.div_016_Columbus_Division;
  const store = navigatorData.store_016_00506;
  it("validate ILP and SFP table default page dropdown when pogDbKey is used", async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
    ]);
    let pogKey = await (
      await (
        await await result.getResult(
          dynamicQuery.pogKeyForComparisonReport,
          DBNames.SFP_STAGE
        )
      ).flat()
    ).toString();
    await navigatorPage.searchByValue("POG Key", pogKey);
    await navigatorPage.goTo("POG & Item by Store");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.getLinkText("UPC");
    await navigatorPage.waitForPageLoad(
      comparisonReport.exportExcelBtn,
      navigatorData.Export_To_Excel_text
    );
    await navigatorPage.waitBrowserToLoadPage(5000);
    let tableNames = navigatorData.comparisonReport_TableNames;
    for (let tableIndex = 0; tableIndex < tableNames.length; tableIndex++) {
      await navigatorPage.waitBrowserToLoadPage(2000);
      let eachTable = await $(
        await comparisonReport.comparisonReportILPSFPPageDropdown.replace(
          "TableName",
          await tableNames[tableIndex]
        )
      );
      await comparisonReport.clickOn(eachTable);
      await navigatorPage.waitBrowserToLoadPage(2000);
      let pageNumbers = await navigatorPage.getArrayElementsText(
        await navigatorPage.pageDropdownList
      );
      let rowCountBefore = await navigatorPage.getRowCount();
      for (let index = 0; index < pageNumbers.length; index++) {
        await navigatorPage.selectPageNumber(await pageNumbers[index]);
        await navigatorPage.waitBrowserToLoadPage(2000);
        let rowCountAfter = await navigatorPage.getRowCount();
        await expect(rowCountBefore).toEqual(rowCountAfter);
      }
    }
  });
});
