const navigatorPage = require("../NavigatorPO/navigator.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const pogItemByStore = require("../NavigatorPO/storePlanogramItem.page");
const result = require("../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
const { DBNames } = require("../../../envConfig.js");

describe("validateFooterStringOnPogItemByStore: Verify Pog & Item by Store pagination label text after default sort  ", () => {
  const division = navigatorData.div_016_Columbus_Division;
  const store = navigatorData.store_016_00506;
  it("validatePaginationLabel: Verify Pog & Item by Store pagination label text after default sort ", async () => {
    // Note: Skipping because of existing defect: /ERSD-25280; footerLabelValidation function does not work for single page; need to update the function
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
    ]);
    const pogKey = await (
      await (
        await await result.getResult(
          dynamicQuery.pogKeyForComparisonReport,
          DBNames.SFP_STAGE
        )
      ).flat()
    ).toString();
    await navigatorPage.searchByValue("POG Key", await pogKey);
    await navigatorPage.goTo(navigatorData.POG_Item_by_Store);
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.waitForPageLoad(
      pogItemByStore.searchCriteriaBtn,
      navigatorData.pogItemByStore_SearchCriteria
    );
    await expect(await navigatorPage.moduleLink.getText()).toEqual(
      navigatorData.POG_Item_by_Store
    );
    await pogItemByStore.clickOn(await pogItemByStore.defaultSortBtn);
    await expect(await navigatorPage.footerLabelValidation()).toEqual(true);
  });
});
