const navigatorPage = require("../NavigatorPO/navigator.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const {getFilesCount} = require("../../../util/pdfReader");
const {assert} = require("chai");
const mappingReportPage = require("../NavigatorPO/mappingReport.page");
const floorPlanPage = require("../NavigatorPO/floorPlan.page");
const {extractFile, getExtractedFilesCount} = require("../../../util/fileExtractor");
const result = require("../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
const {DBNames} = require("../../../envConfig.js");
const {dbKeyListForPdfPsaDownloadPogsTab} = require("../../../SQLConnection/dynamicQueryNav");

let poGDbKeyListExtractedUniqueLength, countSelectedCheckBox, countUnselectedCheckBox;
describe('validatePDFDownloadPlanogramsTab: Validate PDF download on Store Planogram Item tab', () => {

it('PlanogramsTab: Validate the PDF column header checkbox is clickable and PDFs are downloadable when header column checkbox is clicked ', async () => {
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.open('navigator');
    await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_018_Michigan_Division);
    await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_018_00038);
    await navigatorPage.selectSingleValue('PlanogramStatus', 'pog-status', navigatorData.planogramStatusLive);
    await navigatorPage.goTo('POGs');
    await navigatorPage.waitBrowserToLoadPage(3000);
    let initialFilesCount = getFilesCount();
    await navigatorPage.selectColumnCheckBox('PDF');
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.batchDownload();
    await navigatorPage.waitBrowserToLoadPage(5000);
    let finalFilesCount = getFilesCount();
    if(finalFilesCount > initialFilesCount) {
        await expect(finalFilesCount-initialFilesCount).toEqual(1);
    } else {
        await assert.fail(0, 1, "PDF is NOT downloaded !");
    }
      })

    it('When division and pogKeys are selected, validate multiple PDFs are downloaded when 2, 3 and 4 PDFs are checked', async () => {

        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.selectMultipleValues('Division', 'divisionMultiSelect',   navigatorData.divisionListPogPSA);
        // Note: Data generated by query has only one row but test needs at least 4 rows;
        //const pogKeyList =  (await (await (await result.getResult(dynamicQuery.dbKeyListForPdfPsaDownloadPogsTab, DBNames.SFP_STAGE))).flat()).toString();
        const pogKeyList = ["7076251,6949466,6949255,7039760,6925854" ];
        await navigatorPage.searchByValue('POG Key', await pogKeyList);
        await navigatorPage.selectSingleValue('PlanogramStatus', 'pog-status', navigatorData.planogramStatusLive);
        await navigatorPage.goTo('POGs');
        await navigatorPage.waitBrowserToLoadPage(7000);
        await navigatorPage.waitForPageLoad(await navigatorPage.btnExportToExcel, navigatorData.Export_To_Excel_text);
        await navigatorPage.waitBrowserToLoadPage(3000);
        let totalPageRowCount = await (await navigatorPage.getColumnArrayList('POG Type')).length;
        if (totalPageRowCount >= 4) {
            let numOfSelectedCheckBoxes = [totalPageRowCount - 2, totalPageRowCount - 3, totalPageRowCount - 4];
            let numOfSelectedPogDbKey = [2, 3, 4];
            for (let index = 0; index <= numOfSelectedCheckBoxes.length -1; index++) {
                await navigatorPage.clickColumnCheckBox(await mappingReportPage.table, 'planogrampdf');
                let pogDBKeyList = await navigatorPage.getColumnArrayList('DBKey');
                let poGDbKeyListExtracted = await navigatorPage.getNumberArrayElements(pogDBKeyList, numOfSelectedPogDbKey[index]);
                poGDbKeyListExtractedUniqueLength = await (await navigatorPage.getUniqueArrayValue(poGDbKeyListExtracted)).length;
                let initialFilesCount = getFilesCount();
                await navigatorPage.clickRowCheckBox(await mappingReportPage.table, numOfSelectedCheckBoxes[index]);
                await navigatorPage.waitBrowserToLoadPage(3000);
                countSelectedCheckBox = await navigatorPage.returnNumOfCheckBox(await floorPlanPage.floorplansTable);
                await navigatorPage.batchDownload();
                await navigatorPage.waitBrowserToLoadPage(5000);
                countUnselectedCheckBox = await navigatorPage.returnNumOfCheckBox(await floorPlanPage.floorplansTable);
                await expect(await countUnselectedCheckBox).toEqual(0);
                await navigatorPage.validateColumnCheckboxCleared(await mappingReportPage.table, 'planogrampdf');
                let finalFilesCount = getFilesCount();
                await navigatorPage.waitBrowserToLoadPage(3000);
                await expect(finalFilesCount - initialFilesCount).toEqual(1);
                await extractFile();
                await navigatorPage.waitBrowserToLoadPage(3000);
            }
            if (getFilesCount() !== 0) {
                let extractedFileCount = await getExtractedFilesCount();
                await navigatorPage.waitBrowserToLoadPage(2000);
                await expect(await extractedFileCount).toEqual(poGDbKeyListExtractedUniqueLength);
            } else {
                await assert.fail(0, 1, "Downloaded PDf number does not match with selected PDA number")
            }
        }else {
            await assert.fail(0, 1, "Table row count must be more than 4 !")
        }
           })
               })
