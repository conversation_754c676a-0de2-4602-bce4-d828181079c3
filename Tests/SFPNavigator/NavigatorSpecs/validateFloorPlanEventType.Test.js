const navigatorPage = require("../NavigatorPO/navigator.page");
const floorPlanPage = require("../NavigatorPO/floorPlan.page");
const mappingReportPage = require("../NavigatorPO/mappingReport.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const navigatorDBData = require("../../../TestData/SFPNavigatorData/navigatorQueryData.json");
const qrString = require("../../../SQLConnection/queryString");
const { DBNames } = require("../../../envConfig");
const result = require("../../../SQLConnection/server.js.ts");

describe("validateFloorPlanEventType: Choose one Event Type at a time, go to Floorplans tab, validate UI data with DB data ", () => {
  const division = navigatorData.div_011_Atlanta_Division;

  it("ValidateEventTypes: Choose one Event Type at a time, go to Floorplans tab, validate UI data with DB data, validate Event Type displays in UI (KOM, SPE, NOK, REM, SEA) ", async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
    ]);
    for (let index = 0; index < navigatorData.eventType.length - 1; index++) {
      await navigatorPage.selectSingleValue(
        "EventType",
        "event-type",
        navigatorData.eventType[index]
      );
      await navigatorPage.goTo("Floorplans");
      await mappingReportPage.waitForTableExist(
        await floorPlanPage.floorplansTable
      );
      await navigatorPage.waitBrowserToLoadPage(3000);
      let uiData = await navigatorPage.getColumnArrayListMultiplePages(
        floorPlanPage.floorplansTable,
        "Event Type",
        1
      );
      let sql = qrString.FloorplansQueryDbKey.replace("@dbkey", "%")
        .replace("@Event_Type", navigatorData.eventType[index])
        .replace(
          "@Div",
          navigatorData.div_011_Atlanta_Division.match(/(\d[\d]*)/g)[0]
        )
        .replace("SELECT", navigatorDBData.SQL_TOP_120);
      let dbData = await result.getResult(sql, DBNames.SFP_STAGE);
      let uiListUniqueValue = await navigatorPage.getUniqueArrayValue(uiData);
      await expect(uiListUniqueValue.length).toEqual(navigatorData.subString_1);
      await expect(
        uiListUniqueValue[Number(navigatorData.subString_0)]
      ).toEqual(navigatorData.eventType[index]);
      let dbListUniqueValue = await navigatorPage.getUniqueArrayValue(
        await navigatorPage.extractArrayFromArrays(
          dbData,
          navigatorData.subString_4
        )
      );
      await expect(dbListUniqueValue.length).toEqual(navigatorData.subString_1);
      await expect(
        dbListUniqueValue[Number(navigatorData.subString_0)]
      ).toEqual(navigatorData.eventType[index]);
      await navigatorPage.clickOn(await navigatorPage.navigatorTabBtn);
    }
  });
});
