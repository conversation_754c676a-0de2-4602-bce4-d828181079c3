const {DBNames} = require("../../../envConfig.js");
const navigatorPage = require('../NavigatorPO/navigator.page');
const storePage = require('../NavigatorPO/store.page');
const navigatorQueryData = require('../../../TestData/SFPNavigatorData/navigatorQueryData.json');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const qrString = require('../../../SQLConnection/queryString');
const excelProcess = require('../../../util/excelReader');
const chai = require('chai');
const assert = chai.expect;

describe('ValidateStoreTabExcelToDBToUI: Validate store tab data - compare excel to ui, excel to db, validate search criteria, and check UI store and UI Div are match expected values. ', () => {

    it("Validate store tab data with Excel, DB.  Also, Validate UI with store division list, and search criteria ", async () => {

        let divisionListMainPage = await navigatorPage.extractArrayValuesAsArray(navigatorData.divisionListStoreTab,0);
        let storeListMainPage = await navigatorPage.extractArrayValuesAsArray(navigatorData.storeList_StoreTab,1);
        await navigatorPage.waitBrowserToLoadPage(3000);
await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(4000);
        await navigatorPage.selectMultipleValues("Division", 'divisionMultiSelect',  await navigatorData.divisionListStoreTab);
        let selectedDivisionMainPage = await navigatorPage.getTextOfElement(await navigatorPage.divisionDropdown);
        await navigatorPage.selectMultipleValues('Stores', 'store',   await navigatorData.storeList_StoreTab);
        let selectedStoreMainPage = await navigatorPage.getTextOfElement(await navigatorPage.storeDropdown);
        await storePage.goTo('Store');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.waitForPageLoad(navigatorPage.btnExportToExcel, navigatorData.Export_To_Excel_text);
        await navigatorPage.exportToExcel();
        let dataUIStore = await navigatorPage.getAllUIData();
        dataUIStore = await navigatorPage.blankToNullStrToIntArrayOfArrays(dataUIStore);
        await expect(await excelProcess.excelUiDataCompareAllRows(dataUIStore)).toEqual(true);
        let dataStoreDB = await excelProcess.excelDataCompareAllRow(qrString.storeTabWithDivStoreQuery.replace('@DBKeyList',  navigatorQueryData.storeList_StoreTab_DBKey), DBNames.SFP_STAGE);
        await expect(dataStoreDB).toEqual(true);
        let extractedDivList = await navigatorPage.getColumnArrayByIndex(dataUIStore,0);
        let extractedDivListUnique = await navigatorPage.getUniqueArrayValue(extractedDivList);
        let extractedStoreList = await navigatorPage.getColumnArrayByIndex(dataUIStore,1);
        let extractedStoreListUnique = await navigatorPage.getUniqueArrayValue(extractedStoreList);
        await assert(extractedDivListUnique).to.deep.equal(divisionListMainPage.sort());
        await assert(extractedStoreListUnique).to.deep.equal(storeListMainPage.sort());
        await navigatorPage.searchCriteria();
        await navigatorPage.waitBrowserToLoadPage(2000);
        let selectedDivisionSearchCriteria = await navigatorPage.getSearchCriteriaDropdownText('div');
        let selectedStoreSearchCriteria = await navigatorPage.getSearchCriteriaDropdownText('store');
        await expect(selectedDivisionSearchCriteria).toEqual(selectedDivisionMainPage);
        await expect(selectedStoreSearchCriteria).toEqual(selectedStoreMainPage);
    })

})
