const navigatorPage = require("../NavigatorPO/navigator.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const pogItemByStore = require("../NavigatorPO/storePlanogramItem.page");
const result = require("../../../SQLConnection/server.js.ts");
const qrString = require("../../../SQLConnection/queryString");
const { DBNames } = require("../../../envConfig.js");
const excelReader = require("../../../util/excelReader");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
const { assert } = require("chai");

let upcList, divisionList, storeNumber, upcListForQuery;
describe("validateDefaultSortOnPogItemByStore: validate Default Sort functionality both on UI and DB ", async () => {
  const division = navigatorData.defaultSortDivisionList;
  const store = navigatorData.defaultSortStoreList;
  before(async () => {
    upcList = await await (
      await await result.getResult(
        dynamicQuery.upcForPogItemByStore,
        DBNames.SFP_STAGE
      )
    ).flat();
    upcListForQuery = await await upcList.join("','");
    divisionList = await (
      await navigatorPage.extractArrayValuesAsArray(
        navigatorData.defaultSortDivisionList,
        0
      )
    ).join("','");
    storeNumber = await (
      await navigatorPage.extractArrayValuesAsArray(
        navigatorData.defaultSortStoreList,
        1
      )
    ).join("','");
  });

  beforeEach(async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
    ]);
    await navigatorPage.searchByValue("UPC", upcList.join("\n"));
    await navigatorPage.goTo(navigatorData.POG_Item_by_Store);
    await navigatorPage.waitBrowserToLoadPage(3000);
    await expect(await navigatorPage.moduleLink.getText()).toEqual(
      navigatorData.POG_Item_by_Store
    );
  });

  it("validateDefaultSortOnUI: Verify DefaultSort function on div, store columns when multiple divisions, stores and a UPC are selected", async () => {
    const divColumnListDesc = await pogItemByStore.getColumnArrayList("Div");
    let validateDescendingOrderDiv = await pogItemByStore.isDescending(
      divColumnListDesc
    );
    await expect(validateDescendingOrderDiv).toEqual(true);
    const storeColumnListDesc = await pogItemByStore.getColumnArrayList(
      "Store"
    );
    let validateDescendingOrderStore = await pogItemByStore.isDescending(
      storeColumnListDesc
    );
    await expect(validateDescendingOrderStore).toEqual(true);
    await pogItemByStore.clickOn(await pogItemByStore.defaultSortBtn);
    const divColumnListAsc = await pogItemByStore.getColumnArrayList("Div");
    let validateAscendingOrderDiv = await pogItemByStore.isAscending(
      divColumnListAsc
    );
    await expect(validateAscendingOrderDiv).toEqual(true);
    const storeColumnListAsc = await pogItemByStore.getColumnArrayList("Store");
    let validateAscendingOrderStore = await pogItemByStore.isAscending(
      storeColumnListAsc
    );
    await expect(validateAscendingOrderStore).toEqual(true);
  });

  it("validateDefaultSortOnDB: Verify DefaultSort function on DB when multiple divisions, stores and a UPC are selected", async () => {
    await navigatorPage.waitForPageLoad(
      pogItemByStore.searchCriteriaBtn,
      navigatorData.pogItemByStore_SearchCriteria
    );
    await expect(await navigatorPage.moduleLink.getText()).toEqual(
      navigatorData.POG_Item_by_Store
    );
    let dbDataPogItemByStore = await result.getResult(
      qrString.pogItemByStoreQuery_multipleInputs
        .replace("@divList", divisionList)
        .replace("@storeList", storeNumber)
        .replace("@upc", upcListForQuery),
      DBNames.SFP_STAGE
    );
    let uiDataPogItemByStore = await navigatorPage.getAllUIData();
    let uiDataPogItemByStoreRemovedEmptySpace =
      await navigatorPage.removeDoubleSpaces(await uiDataPogItemByStore);
    let uiDataPogItemByStoreRemovedEmptySpaceRemovedZero =
      await navigatorPage.removeZerosFromArrays(
        await uiDataPogItemByStoreRemovedEmptySpace
      );
    await navigatorPage.waitBrowserToLoadPage(3000);
    if (
      (await dbDataPogItemByStore.length) > 0 &&
      dbDataPogItemByStore.length === uiDataPogItemByStore.length
    ) {
      let dbDataPogItemByStoreNullToEmptyString =
        await navigatorPage.convertNullZeroToEmptyString(
          await dbDataPogItemByStore
        );
      let dbDataPogItemByStoreNullToEmptyStringRemoveNull =
        await navigatorPage.removeSpaces(
          await dbDataPogItemByStoreNullToEmptyString
        );
      let compare = await excelReader.dataCompare(
        await dbDataPogItemByStoreNullToEmptyStringRemoveNull,
        await uiDataPogItemByStoreRemovedEmptySpaceRemovedZero
      );
      await expect(compare).toEqual(true);
    } else {
      await assert.fail(0, 1, "Error: UI table does not have data !");
    }
  });
});
