const navigatorPage = require('../NavigatorPO/navigator.page');
const storePage = require('../NavigatorPO/store.page');
const compositePage = require('../NavigatorPO/composite.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const result = require("../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
const {DBNames} = require("../../../envConfig.js");


describe('validateSingleSearchCriteria: Validate Search Criteria on different tabs', () => {

    before(async () => {
        await navigatorPage.waitBrowserToLoadPage(3000);
await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000);
        const upc =  (await (await (await result.getResult(dynamicQuery.upcListForPogItemByStore, DBNames.SFP_STAGE))).flat())[0];
        await navigatorPage.searchByValue('UPC', upc);
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_016_Columbus_Division);
        await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_016_00506);
        await navigatorPage.selectSingleValue('Department', 'dept', navigatorData.dept_06_PHARMACY);
        await navigatorPage.selectSingleValue('SubDept', 'sub-dept', navigatorData.PHARMACY_65_RX_OTC);
        await navigatorPage.selectSingleValue('Commodity', 'commodity', navigatorData.commGrp_65_RX_OTC_251_APOTHECARY);
        await navigatorPage.selectSingleValue('CommodityGroup', 'comm-group', navigatorData.commGrp_251_APOTHECARY_251_APOTHECARY);
        await navigatorPage.selectSingleValue('PlanogramStatus','pog-status', navigatorData.planogramStatusLive);
        await navigatorPage.selectSingleValue('FloorplanStatus','floorplan', navigatorData.floorPlanStatusLive);
    })

    afterEach(async () => {
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.searchCriteria();
        const selectedDropdownNames = ['div','store','dept','sub-dept','commodity','comm-group','pog-status','floorplan'];
        await navigatorPage.waitBrowserToLoadPage(2000);
        for(let index= 0; index < selectedDropdownNames.length -1; index++){
            let selectedSearchCriteriaDropdown = await navigatorPage.getSearchCriteriaDropdownText(selectedDropdownNames[index]);
            await navigatorPage.waitBrowserToLoadPage(2000);
            await expect(selectedSearchCriteriaDropdown).toEqual(navigatorData.one_Item_Selected);
        }
        await navigatorPage.closeSearchCriteria();
    })


    it('should validate search criteria on Store tab for Single data selection from Navigator page', async () => {

        await storePage.selectStoreTab();
        await navigatorPage.waitBrowserToLoadPage(3000);
        let moduleLinkText = await (await navigatorPage.getTextOfElement(await navigatorPage.moduleLink));
        await expect(moduleLinkText).toEqual("Store")
    })

    it('should validate search criteria on Composite tab for Single data selection from Navigator page', async () => {

        await compositePage.selectCompositeTab()
        await navigatorPage.waitBrowserToLoadPage(3000);
        let moduleLinkText = await (await navigatorPage.getTextOfElement(await navigatorPage.moduleLink));
        await expect(moduleLinkText).toEqual("Composite")
    })

    it('should validate search criteria on Store Planogram Item tab for Single data selection from Navigator page', async () => {

        await navigatorPage.goTo(navigatorData.POG_Item_by_Store);
        await navigatorPage.waitBrowserToLoadPage(3000);
        let moduleLinkText = await (await navigatorPage.getTextOfElement(await navigatorPage.moduleLink));
        await expect(moduleLinkText).toEqual("POG & Item by Store")
    })

    it('should validate search criteria on Floorplan tab for Single data selection from Navigator page ', async () => {

        await navigatorPage.goTo('Floorplans')
        await navigatorPage.waitBrowserToLoadPage(15000);
        let moduleLinkText = await (await navigatorPage.getTextOfElement(await navigatorPage.moduleLink));
        await expect(moduleLinkText).toEqual("Floorplans")
    })

})
