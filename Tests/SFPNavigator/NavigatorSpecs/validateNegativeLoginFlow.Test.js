const navigatorPage = require('../NavigatorPO/navigator.page');
const loginPage = require("../../../GlobalObjectRepository/login.page");


describe('validateNegativeLoginFlow: Validate negative sign in flows', () => {
let userNamePasswordIncorrectMessage = 'The username/password combination you entered is incorrect. Please try again or contact the Help Desk for assistance.';
let errorMessagePromptedByBlankUserName = 'A valid EUID or Email Address is required.';
const btnSignOutArrow = $('div[class="logBox"]>div>div>span>kds-icon-chevron-down> kds-icon>svg')
const btnSignOut = $('#headerWrapper > app-header-user > div > div.ers-context-container > ul > li > a')
    before(async () => {
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.clickOn(await btnSignOutArrow)
        await navigatorPage.clickOn(await btnSignOut)
        await navigatorPage.waitBrowserToLoadPage(7000);
        await browser.url('https://sfpappsstage.kroger.com/');
        await navigatorPage.waitBrowserToLoadPage(7000);
    })

    after(async () => {
        await browser.deleteSession();
    })

    it('Validate invalid user and invalid password - gets error message and blanks out user name field after clicking "Cancel" button ', async () => {

        await loginPage.loginBasic( await navigatorPage.userNamePasswordGenerator(8));
        await navigatorPage.userNamePasswordGenerator(9);
        await navigatorPage.waitBrowserToLoadPage(3000);
        let warningMessage = await navigatorPage.getTextOfElement(await navigatorPage.incorrectUserAndPasswordText);
        if(warningMessage === userNamePasswordIncorrectMessage){
         await navigatorPage.loginCancelButton.click();
         await (await loginPage.userNameInput).waitForDisplayed({ timeout: 9000 });
            const inputBoxValue = await loginPage.userNameInput.getValue();
            await expect(inputBoxValue).toEqual("");
        }
    })

    it('Validate error messages when blank userName and blank password are entered ', async () => {

        await loginPage.btnSubmit.click();
        let errorMessagePromptedByBlankUserName = await navigatorPage.getTextOfElement(await navigatorPage.incorrectUserAndPasswordText);
        if(errorMessagePromptedByBlankUserName === 'A valid EUID or Email Address is required.'){
            let userName = await navigatorPage.userNamePasswordGenerator(15);
            await navigatorPage.waitBrowserToLoadPage(2000);
            await loginPage.loginBasic( await userName, "");
        }
        for(let index=0;index<2;index++){
            await loginPage.clickOn(await loginPage.btnContinue);
            await navigatorPage.waitBrowserToLoadPage(3000);
        }
        let errorMessagePromptedByBlankPassword = await navigatorPage.getTextOfElement(await navigatorPage.incorrectUserAndPasswordText);
        if(errorMessagePromptedByBlankPassword === 'Enter your password.'){
            let password = await navigatorPage.userNamePasswordGenerator(15);
            await (await loginPage.passwordValue).waitForDisplayed({ timeout: 2000 });
            await loginPage.passwordValue.setValue(password);
        }
        for(let index=0;index<2;index++){
            await loginPage.clickOn(await loginPage.btnSubmit);
            await navigatorPage.waitBrowserToLoadPage(3000);
        }
        let errorMessagePromptedByIncorrectPassword = await navigatorPage.getTextOfElement(await navigatorPage.incorrectUserAndPasswordText);
        await expect(errorMessagePromptedByIncorrectPassword).toEqual('The username/password combination you entered is incorrect. Please try again or contact the Help Desk for assistance.');
        await navigatorPage.loginCancelButton.click();
        await navigatorPage.waitBrowserToLoadPage(2000);
    })

})
