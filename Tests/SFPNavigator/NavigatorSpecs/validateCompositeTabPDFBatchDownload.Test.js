const navigatorPage = require("../NavigatorPO/navigator.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const {
  getFilesCount,
  extractFile,
  getExtractedFilesCount,
} = require("../../../util/fileExtractor");
const { assert } = require("chai");
const compositePage = require("../NavigatorPO/composite.page");
const { clickOn } = require("../NavigatorPO/navigator.page");

describe("validateCompositeTabPDFBatchDownload: Validate Batch download of PDF files on Composite tab", () => {
  const division = navigatorData.div_708_Food_4_Less_Midwest_Division;
  beforeEach(async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
    ]);
  });

  it("Validate Batch download of PDF files on Composite Tab when one, three and five rows are selected", async () => {
    await navigatorPage.selectSelectAllValue(
      "Stores",
      "store",
      navigatorData.SelectAll
    );
    await navigatorPage.goTo("Composite");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.waitForPageLoad(
      await navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
    for (let countNum = 0; countNum < 2; countNum++) {
      await clickOn(await compositePage.numOfPogColumn);
      await navigatorPage.waitBrowserToLoadPage(2000);
    }
    let lengthOfTableRow = await navigatorPage.tableRowList.length;
    const numOfCheckBoxes = [1, 3, 5];
    if (lengthOfTableRow >= 5) {
      let numOfSelectedCheckBoxes = [
        lengthOfTableRow - numOfCheckBoxes[0],
        lengthOfTableRow - numOfCheckBoxes[1],
        lengthOfTableRow - numOfCheckBoxes[2],
      ];
      for (let index = 0; index < numOfSelectedCheckBoxes.length; index++) {
        let initialFilesCount = getFilesCount();
        await navigatorPage.clickRowCheckBox(
          await navigatorPage.tableValue,
          numOfSelectedCheckBoxes[index]
        );
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.batchDownload();
        const downloadComplete = await navigatorPage.waitForDownload(
          initialFilesCount
        );
        if (downloadComplete) {
          const finalFilesCount = getFilesCount();
          expect(finalFilesCount - initialFilesCount).toEqual(1);

          await extractFile();
          await navigatorPage.waitBrowserToLoadPage(2000);

          let extractedFileCount = await getExtractedFilesCount();
          await expect(await extractedFileCount).toEqual(
            await numOfCheckBoxes[index]
          );
        } else {
          throw new Error("PDF didn't get to download!");
        }
      }
    } else {
      await assert.fail(
        0,
        1,
        "Row numbers must be bigger than 5! Update the input data !"
      );
    }
  });

  it("Validate Batch download error messages when did not click checkboxes ", async () => {
    await navigatorPage.selectSelectAllValue(
      "Stores",
      "store",
      navigatorData.SelectAll
    );
    await navigatorPage.goTo("Composite");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.waitForPageLoad(
      await navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
    await clickOn(await navigatorPage.btnBatchDownload);
    await navigatorPage.waitBrowserToLoadPage(2000);
    let checkBoxNotClickedErrorMessage = await compositePage.getTextOfElement(
      await compositePage.batchDownloadErrorMsg
    );
    await expect(await checkBoxNotClickedErrorMessage).toEqual(
      navigatorData.please_select_record_message
    );
    await navigatorPage.waitBrowserToLoadPage(3000);
    await compositePage.clickConfirmButton("Confirm");
    await navigatorPage.waitForPageLoad(
      await navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
  });
});
