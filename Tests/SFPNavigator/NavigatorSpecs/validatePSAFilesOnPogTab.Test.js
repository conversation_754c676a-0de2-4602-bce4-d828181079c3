const {extractFile, getExtractedFilesCount} = require('../../../util/fileExtractor')
const navigatorPage = require('../NavigatorPO/navigator.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const pdfReader = require('../../../util/pdfReader')
const {assert} = require("chai");
const mappingReportPage = require('../NavigatorPO/mappingReport.page');
const floorPlanPage = require('../NavigatorPO/floorPlan.page');
const result = require('../../../SQLConnection/server.js.ts');
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
const {DBNames} = require("../../../envConfig.js");


let countSelectedCheckBox, countUnselectedCheckBox, pogKeyList;

describe('validatePSAFilesOnPogTab: Validate PSA BatchDownload ', () => {

    beforeEach( async() =>{
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000);
      //  pogKeyList = (await (await (await result.getResult(dynamicQuery.dbKeyListForPSAPogsTab2, DBNames.SFP_STAGE))).flat());
        pogKeyList =  [];
    })

    it('When division and pogKeys are selected, validate SINGLE  PSA is downloaded for the top row: @Need to run this test individually in local ', async () => {

        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_014_Cincinnati_Division);
        await navigatorPage.searchByValue('POG Key', navigatorData.pogKey_List_Pog_PSA);
        await navigatorPage.selectSingleValue('PlanogramStatus', 'pog-status', navigatorData.planogramStatusLive);
        await navigatorPage.goTo('POGs');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.waitForPageLoad(await navigatorPage.btnExportToExcel, navigatorData.Export_To_Excel_text);
        let initialFilesCount = pdfReader.getFilesCount();
        await navigatorPage.waitBrowserToLoadPage(10000);
        await navigatorPage.getLinkText('PSA');
        await navigatorPage.waitBrowserToLoadPage(6000);
        let finalFilesCount = pdfReader.getFilesCount();
        await navigatorPage.waitBrowserToLoadPage(6000);
        if(finalFilesCount > initialFilesCount){
            await expect(finalFilesCount-initialFilesCount).toEqual(1);
        }else {
            await assert.fail(0, 1, "PSA didn't get to download !");
        }
    })

    it('When division and pogKeys are selected, validate multiple PSAs are downloaded when 2, 3 and 4 PSAs are checked', async () => {

        await navigatorPage.selectMultipleValues('Division', 'divisionMultiSelect',   navigatorData.divisionListPogPSA);
        await navigatorPage.searchByValue('POG Key', navigatorData.pogKey_List_Pog_PSA );
        await navigatorPage.selectSingleValue('PlanogramStatus', 'pog-status', navigatorData.planogramStatusLive);
        await navigatorPage.goTo('POGs');
        await navigatorPage.waitBrowserToLoadPage(5000);
        await navigatorPage.waitForPageLoad(await navigatorPage.btnExportToExcel, navigatorData.Export_To_Excel_text);
        await navigatorPage.waitBrowserToLoadPage(3000);
        let totalPageRowCount = await (await navigatorPage.getColumnArrayList('POG Type')).length;
        if (totalPageRowCount >= 4) {
            let numOfSelectedCheckBoxes = [totalPageRowCount-2, totalPageRowCount-3, totalPageRowCount-4];
            let numOfSelectedPogDbKey = [2, 3, 4];
            for (let index = 0; index <= numOfSelectedCheckBoxes.length -1; index++) {
                await navigatorPage.clickColumnCheckBox(await mappingReportPage.table, 'planogrampsa');
                let pogDBKeyList = await navigatorPage.getColumnArrayList('DBKey');
                let poGDbKeyListExtracted = await navigatorPage.getNumberArrayElements(pogDBKeyList, numOfSelectedPogDbKey[index]);
                let poGDbKeyListExtractedUniqueLength = await (await navigatorPage.getUniqueArrayValue(await poGDbKeyListExtracted)).length;
                let initialFilesCount = pdfReader.getFilesCount();
                await navigatorPage.clickRowCheckBox(await mappingReportPage.table, numOfSelectedCheckBoxes[index]);
                await navigatorPage.waitBrowserToLoadPage(3000);
                countSelectedCheckBox = await navigatorPage.returnNumOfCheckBox(await floorPlanPage.floorplansTable);
                await expect(countSelectedCheckBox).toEqual(numOfSelectedPogDbKey[index]);
                await navigatorPage.batchDownload();
                await navigatorPage.waitBrowserToLoadPage(5000);
                countUnselectedCheckBox = await navigatorPage.returnNumOfCheckBox(await floorPlanPage.floorplansTable);
                await expect(await countUnselectedCheckBox).toEqual(0);
                await navigatorPage.validateColumnCheckboxCleared(await mappingReportPage.table, 'planogrampsa');
                let finalFilesCount = pdfReader.getFilesCount();
                await navigatorPage.waitBrowserToLoadPage(3000);
                await expect(finalFilesCount - initialFilesCount).toEqual(1);
                await extractFile();
                await navigatorPage.waitBrowserToLoadPage(3000);
                if (await pdfReader.getFilesCount() !== 0) {
                    let extractedFileCount = await getExtractedFilesCount();
                    await navigatorPage.waitBrowserToLoadPage(2000);
                    await expect(await extractedFileCount).toEqual(await poGDbKeyListExtractedUniqueLength);
                } else {
                    await assert.fail(0, 1, "Downloaded PSA number does not match with table PSA number")
                }
            }
        } else {
            await assert.fail(0, 1, "Table row count must be more than 3 !")
        }
    })

    it('When division and pogKeys are selected, validate all PSAs are downloaded when all check boxes are clicked', async () => {

        await navigatorPage.selectMultipleValues('Division', 'divisionMultiSelect',   navigatorData.divisionListPogPSA);
        await navigatorPage.searchByValue('POG Key', navigatorData.pogKey_List_Pog_PSA);
        await navigatorPage.selectSingleValue('PlanogramStatus', 'pog-status', navigatorData.planogramStatusLive);
        await navigatorPage.goTo('POGs');
        await navigatorPage.waitBrowserToLoadPage(5000);
        await navigatorPage.waitForPageLoad(await navigatorPage.btnExportToExcel, navigatorData.Export_To_Excel_text);
        await navigatorPage.topCheckBox.click();
        await navigatorPage.waitBrowserToLoadPage(3000);
        let totalRowCount = await (await navigatorPage.getColumnArrayList('POG Type')).length;
        let initialFilesCount = pdfReader.getFilesCount();
        await navigatorPage.selectColumnCheckBox('PSA');
        await navigatorPage.batchDownload();
        await navigatorPage.waitBrowserToLoadPage(5000);
        let finalFilesCount = pdfReader.getFilesCount();
        await navigatorPage.waitBrowserToLoadPage(3000);
        await expect(await finalFilesCount - await initialFilesCount).toEqual(1);
        await extractFile();
        await navigatorPage.waitBrowserToLoadPage(3000);
        if (await pdfReader.getFilesCount() !== 0) {
            let extractAllFileCount = await getExtractedFilesCount();
            await navigatorPage.waitBrowserToLoadPage(2000);
            await expect(await extractAllFileCount).toEqual(await totalRowCount);
        } else {
            await assert.fail(0, 1, "Downloaded PSA number does not match with table PSA number")
        }
    })

    })


