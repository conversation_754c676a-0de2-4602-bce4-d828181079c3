const navigatorPage = require("../NavigatorPO/navigator.page");
const mappingReportPage = require("../NavigatorPO/mappingReport.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const qrString = require("../../../SQLConnection/queryString");
const excelReader = require("../../../util/excelReader");
const result = require("../../../SQLConnection/server.js.ts");
const { DBNames } = require("../../../envConfig.js");

let div, storeList, storeListDb, storeListDbClean, ssn;
describe("validateMappingReportBySSN: Validate UI data has no duplicates, excel data matches to db and verify SSN column values on MappingReport tab ", () => {
  const division = navigatorData.div_014_Cincinnati_Division;
  const store = navigatorData.storeList_Multiple_MappingReport;
  const planogram = navigatorData.planogramStatusLive;
  const floorplan = navigatorData.floorPlanStatusLive;
  before(async () => {
    div = division.match(/(\d[\d]*)/g)[0];
    storeList = store;
    storeListDb = await navigatorPage.extractArrayValuesAsArray(storeList, 1);
    storeListDbClean = "'" + storeListDb.join("', '") + "'";
    storeListDbClean = storeListDbClean.slice(1, -1);
    ssn = navigatorData.SSN_D014_L00000_D01_C113_V100_F016_MX;
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
      { id: navigatorData.planogramDropdown, values: planogram },
      { id: navigatorData.floorplanDropdown, values: floorplan },
    ]);
    await navigatorPage.searchByValue("SSN", ssn);
    await navigatorPage.goTo("Mapping Report");
    await navigatorPage.waitBrowserToLoadPage(3000);
  });

  it("Mapping Report:  when one division and multiple stores are selected, then downloaded excel file and, compare all rows between excel and  DB data", async () => {
    await navigatorPage.exportToExcel();
    await navigatorPage.waitBrowserToLoadPage(3000);
    let excelData = await excelReader.readExcelFile();
    let dataData = await result.getResult(
      qrString.mappingReportSsnQuery
        .replace("@Div", div)
        .replace("@StoreList", storeListDbClean)
        .replace("@SsnValue", ssn),
      DBNames.SFP_STAGE
    );
    for (let index = 0; index < excelData.length - 1; index++) {
      await navigatorPage.waitBrowserToLoadPage(2000);
      await expect(excelData[index]).toEqual(dataData[index]);
    }
  });

  it("Mapping Report: should validate duplicates on excel data from Mapping Report ", async () => {
    await navigatorPage.exportToExcel();
    //compares excel rows and confirms no duplicates
    await expect(await excelReader.validateDuplicates()).toEqual(true);
  });

  it("Mapping Report: should Validate Space Station Column values from UI results match the input SSN value", async () => {
    let spaceStationRowValues = await mappingReportPage.getAllColumnValues(
      navigatorData.space_Station_Name_MR
    );
    await navigatorPage.waitBrowserToLoadPage(3000);
    spaceStationRowValues.forEach((item) => {
      expect(item).toEqual(navigatorData.SSN_D014_L00000_D01_C113_V100_F016_MX);
    });
  });
});
