const navigatorPage = require('../../NavigatorPO/navigator.page');
const navigatorData = require('../../../../TestData/SFPNavigatorData/navigatorUIData.json');
const envVariables = require('../../../../envConfig');
const ENV = process.env.ENV
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');

before(async () => {

    await testSetup(await envVariables.storeUser[ENV], await envVariables.storePassword[ENV]);
})

describe('validateStoreUserTabHighlight: Validate default tabs are highlighted for StoreUser and validate rest of tabs are highlighted when pogKey and UPC are used ', () => {

    it('Validate tabs are highlighted when pogKey and UPCs are used  ', async () => {

        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000)
        const navBarHeaderListDefault = await navigatorPage.getEnabledHeaders(await navigatorPage.navBarHeaderList);
        await expect(navBarHeaderListDefault).toEqual(await navigatorData.navBarList_first_4);
        await navigatorPage.goTo('Mapping Report');
        await navigatorPage.waitBrowserToLoadPage(3000)
        const pogDbKey = await navigatorPage.getLinkText("POG DBKey");
        await navigatorPage.getLinkText("Space Station Name");
        await navigatorPage.waitBrowserToLoadPage(3000)
        let itemByPogTab = await navigatorPage.getTextOfElement(await navigatorPage.breadCrumb);
        await expect(await itemByPogTab).toEqual('Item By POG');
        const upc = await navigatorPage.getLinkText("UPC");
        await navigatorPage.navigatorTabBtn.click();
        await navigatorPage.waitBrowserToLoadPage(3000)
        if(await pogDbKey.length > 0 && await upc.length > 0 ){
            await navigatorPage.searchByValue('POG Key', await pogDbKey);
            const navBarHeaderListPogKey = await navigatorPage.getEnabledHeaders(await navigatorPage.navBarHeaderList);
            await expect(navBarHeaderListPogKey).toEqual(navigatorData.navBarList_first_7);
            await navigatorPage.clearAllButton.click();
            await navigatorPage.waitBrowserToLoadPage(3000)
            await navigatorPage.searchByValue('UPC', await upc);
            const navBarHeaderListUpc = await navigatorPage.getEnabledHeaders(await navigatorPage.navBarHeaderList);
            await expect(navBarHeaderListUpc).toEqual(navigatorData.navBarList_first_8);
        }
    })
})
