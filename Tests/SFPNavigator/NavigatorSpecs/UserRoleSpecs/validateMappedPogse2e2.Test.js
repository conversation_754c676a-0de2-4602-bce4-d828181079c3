const navigatorPage = require('../../NavigatorPO/navigator.page');
const navigatorData = require('../../../../TestData/SFPNavigatorData/navigatorUIData.json');
const envVariables = require('../../../../envConfig');
const ENV = process.env.ENV
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const planogramsPage = require("../../NavigatorPO/planograms.page");
const mappingReportPage = require('../../NavigatorPO/mappingReport.page');
const itemByPOGPage = require('../../NavigatorPO/itemByPOG.page');
const assert = require('chai').assert;

let storeCountValue, numOfEntries;

before(async () => {
    await testSetup(envVariables.divisionalUser[ENV], envVariables.divisionalPassword[ENV]);
})
describe('validateMappedPogsForDivisionalUser: Validate mapped pog data when OnlyMappedPogs button clicked', () => {


    beforeEach(async () => {

        await expect(browser).toHaveUrlContaining(envVariables.url[ENV]);
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(2000);
        let divisionDropdownText = await navigatorPage.getTextOfElement(await navigatorPage.divisionDropdownDefaultSelectedText);
        await navigatorPage.validateDropdownValue('Division', divisionDropdownText === '1 Item Selected' ? navigatorData.one_Item_Selected : 'Select Division');
        await navigatorPage.selectSingleValue('Department', 'dept', navigatorData.dept_06_PHARMACY);
        await navigatorPage.selectDropDownValues('CommodityGroup', 'comm-group',0,2);
        await navigatorPage.goTo('POGs');
        await navigatorPage.waitBrowserToLoadPage(5000);
        let buttonsExist = await planogramsPage.validateButtonsExist(await planogramsPage.pogFilterListBtn);
        await expect(buttonsExist).toEqual(true);
        await planogramsPage.clickOn(await planogramsPage.mappedPogButton)
        await navigatorPage.waitBrowserToLoadPage(3000);
    })

    it('Validate numberOfItem value and hyperlink on POGs tab when onlyMappedPOGs is clicked ', async () => {

        let numOfItems = await navigatorPage.getLinkText(navigatorData.number_of_Items);
        let itemByPogFilterList = await planogramsPage.validateButtonsExist(await itemByPOGPage.filterBtnList);
        await expect(itemByPogFilterList).toEqual(true);
        await navigatorPage.waitBrowserToLoadPage(3000);
        numOfEntries = await (await navigatorPage.GetText(await navigatorPage.footerEntryString)).split(" ")[5];
        await navigatorPage.waitBrowserToLoadPage(3000);
        await assert.isAtLeast(parseInt(numOfEntries), parseInt(numOfItems), 'Footer entry number should be equal or bigger than number of items column value');
        let rowCountNum = await (await navigatorPage.getRowCount());
        let lengthOfTableRow = await planogramsPage.sumArrayElements(rowCountNum, rowCountNum.length);
        // eslint-disable-next-line no-constant-condition
        if(lengthOfTableRow.toString() !== null || true){
            await navigatorPage.waitBrowserToLoadPage(3000);
            await expect(lengthOfTableRow === 1 ? '0' : lengthOfTableRow.toString()).toEqual(numOfEntries);
        }
    })

    it('Validate numberOfItem value and hyperlink on mappingReport tab when onlyMappedPOGs is clicked', async () => {

        for(let index=0; index<=1; index++){
            await navigatorPage.clickColumnHeader(await navigatorPage.tableHeaders, navigatorData.store_Count);
            await navigatorPage.waitBrowserToLoadPage(2000);
        }
        await navigatorPage.waitBrowserToLoadPage(2000);
        storeCountValue = await navigatorPage.getLinkText(navigatorData.store_Count);
        await navigatorPage.waitBrowserToLoadPage(5000);
        let numOfStoresUi = await mappingReportPage.getAllColumnValues(navigatorData.col_noOfStores);
        await navigatorPage.waitBrowserToLoadPage(5000);
        let numOfFooterEntryMappingReport = await (await navigatorPage.GetText(await navigatorPage.footerEntryString)).split(" ")[5];
        await expect(numOfStoresUi.length.toString()).toEqual(storeCountValue);
        await expect(numOfFooterEntryMappingReport).toEqual(await (await planogramsPage.getUniqueArrayValue(numOfStoresUi)).toString() );
        await navigatorPage.waitBrowserToLoadPage(4000);
        let numOfItems = await navigatorPage.getLinkText(navigatorData.col_noOfItems);
        await navigatorPage.waitBrowserToLoadPage(15000);
        let itemByPogFilterList = await planogramsPage.validateButtonsExist(await itemByPOGPage.filterBtnList);
        await expect(itemByPogFilterList).toEqual(true);
        let itemByPogFullAssortmentFilter = await planogramsPage.validateSingleButtonExist(await itemByPOGPage.filterBtnList, navigatorData.Full_Assortment);
        await expect(itemByPogFullAssortmentFilter).toEqual(true);
        await itemByPOGPage.clickFilterTab('Merchandised Assortment')
        await navigatorPage.waitBrowserToLoadPage(5000)
        let numOfFooterEntryItemByPog = await (await navigatorPage.GetText(await navigatorPage.footerEntryString)).split(" ")[5];
        // Note: confirmed with Chandru; This is a data issue.
        //await expect(parseInt(numOfItems)).toEqual(numOfFooterEntryItemByPog !== numOfStoresUi ? parseInt(numOfFooterEntryItemByPog) -1 : parseInt(numOfFooterEntryItemByPog));
        await navigatorPage.waitBrowserToLoadPage(3000);
        let pageNumbers = await navigatorPage.getPageNumberList();
        for (let i = 0; i < pageNumbers.length-1; i++) {
            await navigatorPage.waitBrowserToLoadPage(2000)
            await navigatorPage.selectPageNumber(await pageNumbers[i]);
            await navigatorPage.waitBrowserToLoadPage(2000)
            let rowCount = await navigatorPage.divColumnList.length;
            await assert.isAtLeast(parseInt(pageNumbers[i]), parseInt(rowCount), 'PageNumber index value should be equal or bigger than table row number');
        }
    })

    it('Validate ssn value and hyperlink on mappingReport tab when onlyMappedPOGs is clicked ', async () => {

        await navigatorPage.getLinkText(navigatorData.store_Count);
        let mappingReportSsnColumnList = await mappingReportPage.getAllColumnValues(navigatorData.space_Station_Name_MR);
        let mappingReportSsnColumnListUnique = await planogramsPage.getUniqueArrayValue(mappingReportSsnColumnList)
        if(mappingReportSsnColumnListUnique.length === 1){
            await navigatorPage.waitBrowserToLoadPage(3000);
            let ssnSingleTopValue = await navigatorPage.getLinkText(navigatorData.space_Station_Name_MR);
            await navigatorPage.waitBrowserToLoadPage(5000);
            await itemByPOGPage.clickFilterTab(navigatorData.Merchandised_Assortment_text);
            let itemByPogSsnColumnListUnique = await planogramsPage.getUniqueArrayValue(await mappingReportPage.getAllColumnValues(navigatorData.space_Station_Name_MR));
            await expect(mappingReportSsnColumnListUnique).toEqual(itemByPogSsnColumnListUnique);
            await expect(itemByPogSsnColumnListUnique.toString()).toEqual(ssnSingleTopValue)
        }
    })

    it('Validate ssn value and hyperlink on POGS tab when onlyMappedPOGs is clicked ', async () => {

        // Note: SSN could not navigate to ItemByPog;
        let ssnOnPogs =  await navigatorPage.getLinkText(navigatorData.space_Station_Name_MR);
        await navigatorPage.waitBrowserToLoadPage(20000);
        if(ssnOnPogs !== null){
            let itemByPogSsnColumnListUnique = await planogramsPage.getUniqueArrayValue(await mappingReportPage.getAllColumnValues(navigatorData.space_Station_Name_MR));
            await expect(ssnOnPogs).toEqual(itemByPogSsnColumnListUnique.toString());
        }
    })

})
