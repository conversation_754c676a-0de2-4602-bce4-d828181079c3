const navigatorPage = require('../../NavigatorPO/navigator.page');
const navigatorData = require('../../../../TestData/SFPNavigatorData/navigatorUIData.json');
const envVariables = require('../../../../envConfig');
const ENV = process.env.ENV
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const planogramsPage = require("../../NavigatorPO/planograms.page");
const mappingReportPage = require('../../NavigatorPO/mappingReport.page');
const excelReader = require('../../../../util/excelReader.js');
const {assert} = require("chai");
const result = require("../../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../../SQLConnection/dynamicQueryNav");
const {DBNames} = require("../../../../envConfig.js");

let storeCountValue, numOfEntries;

before(async () => {
    await testSetup(await envVariables.divisionalUser[ENV], await envVariables.divisionalPassword[ENV]);
})
describe('validateDisabledPogFilterBtnsDivisionalUser: Validate disabled buttons on Pogs and storeCount column value', () => {

    beforeEach(async () => {
        
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000);
    })

    afterEach(async () => {

        await navigatorPage.goTo('POGs');
        await navigatorPage.waitBrowserToLoadPage(5000);
        let tableRowCount = await planogramsPage.getCurrentPageRowLength(await planogramsPage.table, await planogramsPage.tableRowList);
        if(await tableRowCount >= 1){
            const disabledButtons = ["All POGs", "Only Unmapped POGs"]
            for(let index=0; index < disabledButtons.length; index++){
                let buttonDisabled = await planogramsPage.validateDisabledButton(disabledButtons[index])
                await expect(buttonDisabled ).toEqual(true);
            }
            let tableExist = await planogramsPage.tableIsNotEmpty(await planogramsPage.table);
            await expect(tableExist).toEqual(true)
            await navigatorPage.waitBrowserToLoadPage(3000);
            numOfEntries = await (await navigatorPage.GetText(await navigatorPage.footerEntryString)).split(" ")[5];
            await navigatorPage.waitBrowserToLoadPage(2000);
            if(numOfEntries !== '0') {
                await navigatorPage.waitBrowserToLoadPage(3000);
                let uiPogData = await navigatorPage.getAllUIData();
                await navigatorPage.exportToExcel();
                await navigatorPage.waitBrowserToLoadPage(3000);
                let excelData = await excelReader.readExcelFile();
                let cleanExcelData = await planogramsPage.removeExtraSpaces(excelData)
                await navigatorPage.waitBrowserToLoadPage(6000)
                let cleanUiPogData = await navigatorPage.removeElementsFromArrayOfArrays(uiPogData, 'PDF', 'PSA');
                let excelDataCompareUi = await excelReader.dataCompare(cleanExcelData, cleanUiPogData, cleanUiPogData.length);
                await expect(excelDataCompareUi).toEqual(true);
                let dbKeyFromPogs = await navigatorPage.getLinkText('DBKey');
                await navigatorPage.waitBrowserToLoadPage(3000);
                storeCountValue = await navigatorPage.getLinkText(navigatorData.store_Count);
                await navigatorPage.waitBrowserToLoadPage(2000);
                numOfEntries = await (await navigatorPage.GetText(await navigatorPage.footerEntryString)).split(" ")[5];
                await navigatorPage.waitBrowserToLoadPage(3000);
                if (parseInt(await numOfEntries) > 0 && await (parseInt(await storeCountValue)) >= await (parseInt(await numOfEntries)) ) {
                    let numOfSelectedPogOnMappingReport = await navigatorPage.extractDigits(await mappingReportPage.pogDbKeyFilterString.getText());
                    let dbKeyFromMappingReport = await mappingReportPage.getAllColumnValues(navigatorData.pog_DBKey_text);
                    await expect(await (await navigatorPage.getUniqueArrayValue(dbKeyFromMappingReport)).toString()).toEqual(dbKeyFromPogs);
                    await expect(numOfSelectedPogOnMappingReport).toEqual(dbKeyFromPogs);
                } else {
                    await assert.fail(0, 1, "Error: if condition did not get executed !");
                }
            } else{
                await assert.fail(0, 1, "Error: Table is empty !");
            }
        } else{
            await assert.fail(0, 1, "Error: POGs Table is empty !");
        }

    })

    it('Validate disabled buttons when pogKey and Stores are selected', async () => {


        const pogKey = await (await (await (await result.getResult(dynamicQuery.dbKeyForDisablePogFilter, DBNames.SFP_STAGE)))).flat().toString();
        await navigatorPage.searchByValue('POG Key', await pogKey);
        await navigatorPage.selectDropDownValues('Stores', 'store', 1,4);
    })

    it('Validate disabled buttons when UPC, Store, Department and SubDept are selected ', async () => {

        let upc = await (await (await (await result.getResult(dynamicQuery.upcForDisablePogFilter1, DBNames.SFP_STAGE)))).flat().toString();
        await navigatorPage.searchByValue('UPC', await upc);
        await navigatorPage.selectSelectAllValue('Stores', 'store', 'Select All');
        await navigatorPage.selectDropDownValues('Department','dept', 0,1);
        await navigatorPage.selectDropDownValues('SubDept', 'sub-dept',0,1);
    })

    it('Validate disabled buttons when UPC, Store and PlanogramStatus is pending ', async () => {

        // Note: upc generate from query has data issue
        //let upc = await (await (await (await result.getResult(dynamicQuery.upcForDisablePogFilter2, DBNames.SFP_STAGE)))).flat().toString();
        await navigatorPage.searchByValue('UPC', '0070189011854');
        await navigatorPage.selectDropDownValues('Stores', 'store',1,8);
    })

})
