const navigatorPage = require('../../NavigatorPO/navigator.page');
const navigatorData = require('../../../../TestData/SFPNavigatorData/navigatorUIData.json');
const envVariables = require('../../../../envConfig');
const ENV = process.env.ENV
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const comparisonReport = require("../../NavigatorPO/comparisonReport.page.js");
const itemByPOGPage = require("../../NavigatorPO/itemByPOG.page.js");
const {assert} = require("chai");

let defaultDiv, defaultStore, pogDbKey, upc, ssnMappingReport;

before(async () => {
    await testSetup(await envVariables.storeUser[ENV], await envVariables.storePassword[ENV]);
    defaultDiv = navigatorData.div_014_Cincinnati_Division.match(/(\d[\d]*)/g)[0];
    defaultStore = navigatorData.store_014_00359.split(' - ')[1];
})

describe('validateStoreUserInEachTab: Validate Store, Composite, Floorplans and Mappping Report tabs are highlighted for StoreUser and each of those tabs', () => {

    beforeEach(async () => {
        await expect(browser).toHaveUrlContaining(await envVariables.url[ENV]);
        await navigatorPage.waitBrowserToLoadPage(3000);
await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000)
        const navBarHeaderList = await navigatorPage.getEnabledHeaders(await navigatorPage.navBarHeaderList);
        await expect(navBarHeaderList).toEqual(await navigatorData.navBarList_first_4);
    })

    afterEach(async () => {
        await navigatorPage.waitBrowserToLoadPage(5000)
        let breadCrumbText = await navigatorPage.getTextOfElement(await navigatorPage.breadCrumb);
        if(await breadCrumbText === 'Store' || await breadCrumbText === 'Composite' || await breadCrumbText === 'Floorplans' || await breadCrumbText === 'Mapping Report'){
            const divColumnList = await navigatorPage.getColumnArrayList('Div');
            const divColumnListUnique = await navigatorPage.getUniqueArrayValue(divColumnList);
            await expect(await defaultDiv).toEqual(divColumnListUnique.toString())
            const storeColumnList = await navigatorPage.getColumnArrayList('Store');
            const storeColumnListUnique = await navigatorPage.getUniqueArrayValue(storeColumnList);
            await expect(defaultStore).toEqual(storeColumnListUnique.toString())
            const rowCount = await navigatorPage.getTotalRowCountNum();
            const footerStringRowValue = await (await navigatorPage.GetText(await navigatorPage.footerEntryString)).split(" ")[5];
            if(await footerStringRowValue.length > 0){
                await expect(rowCount.toString()).toEqual(await footerStringRowValue);
            } else{
                await assert.fail(0, 1, "Error: Footer Entry string did not appear !")
            }
        }
        if(await breadCrumbText === 'Mapping Report'){
            pogDbKey = await navigatorPage.getLinkText("POG DBKey");
            ssnMappingReport = await navigatorPage.getLinkText("Space Station Name");
            await navigatorPage.waitBrowserToLoadPage(3000)
            let itemByPogTab = await navigatorPage.getTextOfElement(await navigatorPage.breadCrumb);
            await expect(await itemByPogTab).toEqual('Item By POG');
            upc = await navigatorPage.getLinkText("UPC");
            const ssnListItemByPog = await navigatorPage.getColumnArrayList('Space Station Name');
            const ssnListItemByPogUnique = await navigatorPage.getUniqueArrayValue(ssnListItemByPog);
            await expect(await ssnMappingReport).toEqual(ssnListItemByPogUnique.toString());
            let buttonList = await itemByPOGPage.getArrayElementsText(await itemByPOGPage.filterBtnList);
            await expect(buttonList).toEqual(await navigatorData.button_List);
        } else if(await breadCrumbText === 'POG & Item by Store'){
            const upcList = await navigatorPage.getColumnArrayList('UPC');
            const upcListUnique = await navigatorPage.getUniqueArrayValue(upcList);
            await expect(upc).toEqual(upcListUnique.toString());
            await navigatorPage.getLinkText("UPC");
            await navigatorPage.waitBrowserToLoadPage(3000)
            let itemByPogTab = await navigatorPage.getTextOfElement(await navigatorPage.breadCrumb);
            await expect(await itemByPogTab).toEqual('Comparison Report');
            let comparisonViewTableDiv = await comparisonReport.getColumnArrayList('DIV');
            const comparisonViewTableDivUnique = await navigatorPage.getUniqueArrayValue(comparisonViewTableDiv);
            await navigatorPage.waitBrowserToLoadPage(2000)
            let comparisonViewTableStore = await comparisonReport.getColumnArrayList('Store');
            const comparisonViewTableStoreUnique = await navigatorPage.getUniqueArrayValue(comparisonViewTableStore);
            await expect(await defaultDiv).toEqual(comparisonViewTableDivUnique.toString());
            await expect(await defaultStore).toEqual(comparisonViewTableStoreUnique.toString());
        } else if (await breadCrumbText === 'POGs'){
            let dbKey = await navigatorPage.getLinkText(" DBKey");
            await expect(await pogDbKey).toEqual(await dbKey);
        }
    })

    it('Validate Store tab', async () => {

        await navigatorPage.goTo('Store');
    })

    it('Validate Composite tab ', async () => {

        await navigatorPage.goTo('Composite');
    })

    it('Validate Floorplans tab ', async () => {

        await navigatorPage.goTo('Floorplans');
    })

    it('Validate MappingReport tab ', async () => {

        await navigatorPage.goTo('Mapping Report');
    })

    it('Validate POGs tab ', async () => {

        await navigatorPage.waitBrowserToLoadPage(3000)
        await navigatorPage.searchByValue('POG Key', await pogDbKey);
        await navigatorPage.goTo('POGs');
    })
    it('Validate POG & Item by Store tab ', async () => {

        await navigatorPage.waitBrowserToLoadPage(3000)
        await navigatorPage.searchByValue('UPC', await upc);
        await navigatorPage.goTo('POG & Item by Store');
    })

})
