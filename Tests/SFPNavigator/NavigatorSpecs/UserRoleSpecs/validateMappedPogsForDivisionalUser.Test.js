const navigatorPage = require('../../NavigatorPO/navigator.page');
const navigatorData = require('../../../../TestData/SFPNavigatorData/navigatorUIData.json');
const envVariables = require('../../../../envConfig');
const ENV = process.env.ENV
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const planogramsPage = require("../../NavigatorPO/planograms.page");
const mappingReportPage = require('../../NavigatorPO/mappingReport.page');
const itemByPOGPage = require('../../NavigatorPO/itemByPOG.page');
const excelReader = require('../../../../util/excelReader.js');
const {assert} = require("chai");
const result = require("../../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../../SQLConnection/dynamicQueryNav");
const {DBNames} = require("../../../../envConfig.js");

let storeCountValue, numOfEntries;

before(async () => {
    await testSetup(envVariables.divisionalUser[ENV], envVariables.divisionalPassword[ENV]);
})
describe('validateMappedPogsForDivisionalUser: Validate mapped pog data when OnlyMappedPogs button clicked', () => {

    beforeEach(async () => {

        await expect(browser).toHaveUrlContaining(envVariables.url[ENV]);
        await navigatorPage.waitBrowserToLoadPage(3000);
await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000);
    })

    afterEach(async () => {

        await navigatorPage.goTo('POGs');
        await navigatorPage.waitBrowserToLoadPage(5000);
        let buttonsExist = await planogramsPage.validateButtonsExist(await planogramsPage.pogFilterListBtn);
        await expect(buttonsExist).toEqual(true);
        let numOfEntryList = await planogramsPage.returnFooterEntryNumLisOfFilters(await planogramsPage.pogFilterListBtn, await navigatorPage.footerEntryString);
        let sumOfEntries = await planogramsPage.addArrayElementsByIndex(numOfEntryList, 1,2)
        await navigatorPage.waitBrowserToLoadPage(3000);
        await expect(await (parseInt(numOfEntryList[0])) ).toEqual(sumOfEntries);
        await navigatorPage.waitBrowserToLoadPage(3000);
        await itemByPOGPage.clickFilterTab(navigatorData.Only_Mapped_POGs);
        numOfEntries = await (await navigatorPage.GetText(await navigatorPage.footerEntryString)).split(" ")[5];
        if(numOfEntries !== '0') {
            await navigatorPage.waitBrowserToLoadPage(3000);
            let uiPogData = await navigatorPage.getAllUIData();
            await navigatorPage.exportToExcel();
            await navigatorPage.waitBrowserToLoadPage(3000);
            let excelData = await excelReader.readExcelFile();
            let cleanExcelData = await planogramsPage.removeExtraSpaces(excelData)
            await navigatorPage.waitBrowserToLoadPage(6000)
            let cleanUiPogData = await navigatorPage.removeElementsFromArrayOfArrays(uiPogData, 'PDF', 'PSA');
            let excelDataCompareUi = await excelReader.dataCompare(cleanExcelData, cleanUiPogData, cleanUiPogData.length);
            await expect(excelDataCompareUi).toEqual(true);
            await navigatorPage.waitBrowserToLoadPage(3000);
            storeCountValue = await navigatorPage.getLinkText(navigatorData.store_Count);
            numOfEntries = await (await navigatorPage.GetText(await navigatorPage.footerEntryString)).split(" ")[5];
            await navigatorPage.waitBrowserToLoadPage(3000);
            if (parseInt(numOfEntries) > 0 && parseInt(storeCountValue) >= parseInt(numOfEntries)) {
                let numOfSelectedPogOnMappingReport = await navigatorPage.extractDigits(await mappingReportPage.pogDbKeyFilterString.getText());
                let storeTypeTableUi = await mappingReportPage.getAllColumnValues(navigatorData.pog_DBKey_text);
                await expect(numOfSelectedPogOnMappingReport).toEqual(await (await navigatorPage.getUniqueArrayValue(storeTypeTableUi)).toString());
            } else {
                await assert.fail(0, 1, "Error: If condition is not executed !");
            }
        } else{
            await expect(numOfEntries.toString()).toEqual('0');
        }
    })

    it('Validate pog filters when pogKey is used ', async () => {

        const pogKey = await (await (await (await result.getResult(dynamicQuery.dbKeyForDisablePogFilter, DBNames.SFP_STAGE)))).flat().toString();
        await navigatorPage.searchByValue('POG Key', await pogKey);
    })

    it('Validate pog filters when CommodityGroup is used ', async () => {

        await navigatorPage.selectDropDownValues('CommodityGroup', 'comm-group',1,4);
    })

    it('Validate pog filters when UPC, Depart and SubDepart are selected  ', async () => {

        let upc = await (await (await (await result.getResult(dynamicQuery.upcForMappedPogs, DBNames.SFP_STAGE)))).flat().toString();
        await navigatorPage.searchByValue('UPC', upc);
        await navigatorPage.selectSelectAllValue('Department', 'dept', navigatorData.SelectAll);
        await navigatorPage.selectSelectAllValue('SubDept', 'sub-dept', navigatorData.SelectAll);
    })

    it('Validate pog filters when UPC, Store and PlanogramStatus are used ', async () => {

        let upc = await (await (await (await result.getResult(dynamicQuery.upcForMappedPogs, DBNames.SFP_STAGE)))).flat().toString();
        await navigatorPage.searchByValue('UPC', upc);
        await navigatorPage.selectSingleValue('PlanogramStatus', 'pog-status', navigatorData.planogramStatusLive);
    })

})
