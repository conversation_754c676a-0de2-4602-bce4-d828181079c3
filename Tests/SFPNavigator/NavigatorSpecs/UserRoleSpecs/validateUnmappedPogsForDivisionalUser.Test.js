const navigatorPage = require('../../NavigatorPO/navigator.page');
const navigatorData = require('../../../../TestData/SFPNavigatorData/navigatorUIData.json');
const envVariables = require('../../../../envConfig');
const ENV = process.env.ENV
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const planogramsPage = require("../../NavigatorPO/planograms.page");
const itemByPOGPage = require('../../NavigatorPO/itemByPOG.page');
const excelReader = require('../../../../util/excelReader.js');
const result = require("../../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../../SQLConnection/dynamicQueryNav");
const {DBNames} = require("../../../../envConfig.js");

let storeCountValue, numOfItemValue, upcList;

before(async () => {
    await testSetup(envVariables.divisionalUser[ENV], envVariables.divisionalPassword[ENV]);
    upcList = await (await (await (await result.getResult(dynamicQuery.upcListForForUnMappedPog, DBNames.SFP_STAGE)))).flat();
})

describe('validateUnmappedPogsForDivisionalUser: Validate unmapped pog data when OnlyUnmappedPOGS button is selected', () => {

    beforeEach(async () => {
        await expect(browser).toHaveUrlContaining(envVariables.url[ENV]);
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(5000);
        let divisionDropdownText = await navigatorPage.getTextOfElement(await navigatorPage.divisionDropdownDefaultSelectedText);
        await navigatorPage.validateDropdownValue('Division', divisionDropdownText === '1 Item Selected' ? navigatorData.one_Item_Selected : 'Select Division');
        await navigatorPage.waitBrowserToLoadPage(3000);
    })

    afterEach(async () => {

        await navigatorPage.goTo('POGs');
        await navigatorPage.waitBrowserToLoadPage(7000);
        let numOfEntryList = await planogramsPage.returnFooterEntryNumLisOfFilters(await planogramsPage.pogFilterListBtn, await navigatorPage.footerEntryString);
        let sumOfEntries = await planogramsPage.addArrayElementsByIndex(numOfEntryList, 1,2)
        await expect(parseInt(numOfEntryList[0]) ).toEqual(sumOfEntries)
        await itemByPOGPage.clickFilterTab(navigatorData.Only_Unmapped_POGs);
        await navigatorPage.waitBrowserToLoadPage(3000);
        let uiPogData = await navigatorPage.getAllUIData();
        await navigatorPage.exportToExcel();
        let excelData = await excelReader.readExcelFile();
        let cleanExcelData = await planogramsPage.removeExtraSpaces(excelData)
        await navigatorPage.waitBrowserToLoadPage(3000)
        let cleanUiPogData = await navigatorPage.removeElementsFromArrayOfArrays(uiPogData,'PDF','PSA');
        let excelDataCompareUi = await excelReader.dataCompare( cleanExcelData,cleanUiPogData, cleanUiPogData.length);
        await expect(excelDataCompareUi).toEqual(true);
        for (let index=0; index<= 1; index++){
            await navigatorPage.clickColumnHeader(await navigatorPage.tableHeaders, navigatorData.store_Count);
            await navigatorPage.waitBrowserToLoadPage(3000);
        }
        let firstElementOfColumn = await planogramsPage.getCellValueWithColumnIndex(await planogramsPage.pogTabHeaders, navigatorData.store_Count, await navigatorPage.columnText);
        if(firstElementOfColumn === '0') {
            await navigatorPage.waitBrowserToLoadPage(3000);
            numOfItemValue = await navigatorPage.getLinkText(navigatorData.number_of_Items);
            await expect(numOfItemValue).not.toEqual('0')
            let numOfEntries = await (await navigatorPage.GetText(await navigatorPage.footerEntryString)).split(" ")[5];
            let rowNumber = await navigatorPage.getTotalRowCountNum();
            // Note: Need confirm logic of following scenario with devs: Spike - ERSD-23931
            await expect(numOfEntries).toEqual(numOfEntries >= '0' ? rowNumber.toString(): '0');
        }
        else{
            await navigatorPage.waitBrowserToLoadPage(3000);
            storeCountValue = await navigatorPage.getLinkText(navigatorData.store_Count);
            await expect(storeCountValue).not.toEqual('0')
            let numOfEntries = await (await navigatorPage.GetText(await navigatorPage.footerEntryString)).split(" ")[5];
            await expect(numOfEntries).toEqual('0');
        }
    })

    it('Validate unmapped pog filter when commodity dropdown is selected with specific index', async () => {

        await navigatorPage.selectDropDownValues('Commodity', 'commodity',1,4);
    })

    it('Validate unmapped pog filter with POG key ', async () => {
        let unmappedPog = await (await (await (await result.getResult(dynamicQuery.dbKeyForUnMappedPog, DBNames.SFP_STAGE)))).flat();
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.searchByValue('POG Key', await unmappedPog);
    })

    it('Validate unmapped pog filter when UPC is selected', async () => {

        await navigatorPage.searchByValue('UPC', upcList[0]);
    })

    it('Validate unmapped pog filter when Department and commodity dropdown values are selected', async () => {

        await navigatorPage.selectSingleValue('Department', 'dept', navigatorData.dept_87_Gm);
        await navigatorPage.selectDropDownValues('Commodity', 'commodity',0,2);
    })

    it('Validate unmapped pog filter when either of UPC/Department/SubDept are selected ', async () => {

        await navigatorPage.searchByValue('UPC', await upcList[1]);
        await navigatorPage.selectSingleValue('Department', 'dept', navigatorData.dept_03_HBC);
        await navigatorPage.selectDropDownValues('SubDept', 'sub-dept',1,4);
    })

})
