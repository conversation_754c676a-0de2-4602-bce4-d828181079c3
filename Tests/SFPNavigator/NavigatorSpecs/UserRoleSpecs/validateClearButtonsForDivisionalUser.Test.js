const navigatorPage = require('../../NavigatorPO/navigator.page');
const navigatorData = require('../../../../TestData/SFPNavigatorData/navigatorUIData.json');
const envVariables = require('../../../../envConfig');
const ENV = process.env.ENV
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const result = require("../../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../../SQLConnection/dynamicQueryNav");
const {DBNames} = require("../../../../envConfig.js");


let clearDropDownList,dropdownNameList,selectedValueList,upcNumber;

before(async () => {
    await testSetup(await envVariables.divisionalUser[ENV], await envVariables.divisionalPassword[ENV]);
})

describe('validateClearButtonsForDivisionalUser: Verify clear options functionality', () => {

    beforeEach(async () => {

        dropdownNameList = ['Division','Stores','Department','SubDept','PlanogramStatus','Commodity','CommodityGroup','FloorplanStatus','EventType','EventName'];
        selectedValueList = [navigatorData.one_Item_Selected,navigatorData.select_Store,navigatorData.select_Department,navigatorData.select_Sub_Department,
            navigatorData.select_Planogram_Status,navigatorData.select_Commodity,navigatorData.select_Commodity_Group,navigatorData.select_Floorplan_Status,
            navigatorData.select_Event_Type,navigatorData.select_Event_Name];
        upcNumber = await (await (await (await result.getResult(dynamicQuery.upcForClearButtons, DBNames.SFP_STAGE))).flat()).toString();
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(5000);
        await navigatorPage.validateDropdownValue('Division', navigatorData.one_Item_Selected);
        await navigatorPage.searchByValue('UPC', upcNumber);
        await navigatorPage.selectSelectAllValue('Stores', 'store', 'Select All');
        await navigatorPage.selectSingleValue('Department', 'dept', navigatorData.dept_07_PACKAGE_PRODUCE);
        await navigatorPage.selectSingleValue('SubDept', 'sub-dept',navigatorData.subDept_07_PACKAGE_PRODUCE_07_PACKAGE_PRODUCE);
        await navigatorPage.selectSingleValue('Commodity', 'commodity', navigatorData.comm_07_PACKAGE_PRODUCE_642_PROCESSED);
        await navigatorPage.selectSingleValue('CommodityGroup', 'comm-group',  navigatorData.commGrp_642_PROCESSED_642_PROCESSED);
        await navigatorPage.selectSingleValue('PlanogramStatus', 'pog-status', navigatorData.planogramStatusLive);
        await navigatorPage.selectSingleValue('FloorplanStatus', 'floorplan', navigatorData.floorPlanStatusLive);
        await navigatorPage.selectSingleValue('EventType', 'event-type', navigatorData.eventType_KOM);
        await navigatorPage.selectSingleValue('EventName', 'event-name', navigatorData.eventName_SPE);
        clearDropDownList = await navigatorPage.getArrayElementsText(await navigatorPage.clearButtonList);
    })

    afterEach(async () => {

        await navigatorPage.validateEmptySelectList();
        let divisionDropdownText = await navigatorPage.getTextOfElement(await navigatorPage.divisionDropdownDefaultSelectedText);
        await navigatorPage.validateDropdownValue('Division', divisionDropdownText === '1 Item Selected' ? navigatorData.one_Item_Selected : 'Select Division');
    })

    it('Verify Clear List function with Single data selection on Navigator page for divisional user', async () => {

        await navigatorPage.genericClearOptions(clearDropDownList[0]);
    })

    it('Verify Clear Dropdown function with Single data selection on Navigator page for divisional user', async () => {

        await navigatorPage.genericClearOptions(clearDropDownList[1]);
        for(let index=0;index<dropdownNameList.length;index++){
            await navigatorPage.validateDropdownValue(dropdownNameList[index], selectedValueList[index]);
        }
        await expect (await navigatorPage.enterValue.getValue()).toEqual(upcNumber);
    })

    it('Verify Clear All function with Single data selection on Navigator page for divisional user', async () => {

        await navigatorPage.genericClearOptions(clearDropDownList[2]);
        for(let index=0;index<dropdownNameList.length;index++){
            await navigatorPage.validateDropdownValue(dropdownNameList[index], selectedValueList[index]);
        }
          })

      })

