const navigatorPage = require('../../NavigatorPO/navigator.page');
const navigatorData = require('../../../../TestData/SFPNavigatorData/navigatorUIData.json');
const envVariables = require('../../../../envConfig');
const ENV = process.env.ENV
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const {assert} = require("chai");

before(async () => {
    await testSetup(await envVariables.divisionalUser[ENV], await envVariables.divisionalPassword[ENV]);
})

describe('validateDivisionalUserTabHighlight: Validate default tabs are highlighted for DivisionalUser and validate rest of tabs are highlighted when pogKey and UPC are used ', () => {


    it('Validate tabs are highlighted when pogKey and UPCs are used  ', async () => {

        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000)
        const navBarHeaderListDefault = await navigatorPage.getEnabledHeaders(await navigatorPage.navBarHeaderList);
        await expect(navBarHeaderListDefault).toEqual(await navigatorData.navBarList_3_DivUser);
        await navigatorPage.selectDropDownValues('Stores', 'store',0,2);
        const navBarHeaderListDefaultIncludeStore = await navigatorPage.getEnabledHeaders(await navigatorPage.navBarHeaderList);
        await expect(navBarHeaderListDefaultIncludeStore).toEqual(await navigatorData.navBarList_first_4);
        await navigatorPage.goTo('Mapping Report');
        await navigatorPage.waitBrowserToLoadPage(3000)
        const pogDbKey = await navigatorPage.getLinkText("POG DBKey");
        await navigatorPage.getLinkText("Space Station Name");
        await navigatorPage.waitBrowserToLoadPage(4000)
        let itemByPogTab = await navigatorPage.getTextOfElement(await navigatorPage.breadCrumb);
        await expect(await itemByPogTab).toEqual('Item By POG');
        const upc = await navigatorPage.getLinkText("UPC");
        await navigatorPage.navigatorTabBtn.click();
        await navigatorPage.waitBrowserToLoadPage(3000)
        if(await (pogDbKey.length) > 0 && await (upc.length > 0) ){
            await navigatorPage.searchByValue('POG Key', await pogDbKey);
            const navBarHeaderListPogKey = await navigatorPage.getEnabledHeaders(await navigatorPage.navBarHeaderList);
            await expect(navBarHeaderListPogKey).toEqual(navigatorData.navBarList_first_7);
            await navigatorPage.clearListButton.click();
            await navigatorPage.waitBrowserToLoadPage(3000)
            await navigatorPage.searchByValue('UPC', await upc);
            const navBarHeaderListUpc = await navigatorPage.getEnabledHeaders(await navigatorPage.navBarHeaderList);
            await expect(navBarHeaderListUpc).toEqual(navigatorData.navBarList_first_8);
        }else{
            await assert.fail(0, 1, "Error: Did not get to retrieve DbKey or UPC ! ");
        }
    })
})
