const navigatorPage = require('../../NavigatorPO/navigator.page');
const navigatorData = require('../../../../TestData/SFPNavigatorData/navigatorUIData.json');
const envVariables = require('../../../../envConfig');
const ENV = process.env.ENV
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const result = require("../../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../../SQLConnection/dynamicQueryNav");
const {DBNames} = require("../../../../envConfig.js");

let clearDropDownList, upcNumber;

before(async () => {
    await testSetup(envVariables.storeUser[ENV], envVariables.storePassword[ENV]);
})

describe('validateClearButtonsForStoreUser: Verify clear options functionality for Store User', () => {


    beforeEach(async () => {

        await expect(browser).toHaveUrlContaining(envVariables.url[ENV]);
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        upcNumber = await (await (await (await result.getResult(dynamicQuery.upcForClearButtons, DBNames.SFP_STAGE))).flat()).toString();
        await navigatorPage.searchByValue('UPC', upcNumber);
        let divisionDropdownText = await navigatorPage.getTextOfElement(await navigatorPage.divisionDropdownDefaultSelectedText);
        let storeDropdownText = await navigatorPage.getTextOfElement(await navigatorPage.storeDropdownDefaultSelectedText);
        await navigatorPage.validateDropdownValue('Division', divisionDropdownText === '1 Item Selected' ? navigatorData.one_Item_Selected : 'Select Division');
        await navigatorPage.validateDropdownValue('Stores', storeDropdownText === '1 Item Selected' ? navigatorData.one_Item_Selected : 'Select Store');
        let divLocatorDisabled = await navigatorPage.locatorDisabled(await navigatorPage.divisionDropdown);
        await expect(divLocatorDisabled).toEqual(true);
        let storeLocatorDisabled = await navigatorPage.locatorDisabled(await navigatorPage.storeDropdown);
        await expect(storeLocatorDisabled).toEqual(true);
        await navigatorPage.selectSingleValue('Department','dept', navigatorData.dept_06_PHARMACY);
        await navigatorPage.selectSingleValue('SubDept', 'sub-dept', navigatorData.PHARMACY_65_RX_OTC);
        await navigatorPage.selectSingleValue('Commodity','commodity',  navigatorData.commGrp_65_RX_OTC_251_APOTHECARY);
        await navigatorPage.selectSingleValue('CommodityGroup', 'comm-group', navigatorData.commGrp_251_APOTHECARY_251_APOTHECARY);
        await navigatorPage.selectSingleValue('PlanogramStatus','pog-status', navigatorData.planogramStatusLive);
        await navigatorPage.selectSingleValue('FloorplanStatus', 'floorplan', navigatorData.floorPlanStatusLive);
        await navigatorPage.selectSingleValue('EventType', 'event-type', navigatorData.eventType_KOM);
        await navigatorPage.selectSingleValue('EventName', 'event-name', navigatorData.eventName_SPE);
        clearDropDownList = await navigatorPage.getArrayElementsText(await navigatorPage.clearButtonList);
    })

    it('Verify Clear List function with Single data selection on Navigator page for store user', async () => {

        await navigatorPage.genericClearOptions(clearDropDownList[0]);
        await navigatorPage.waitBrowserToLoadPage(5000)
        await navigatorPage.validateEmptySelectList();
        let divisionDropdownText = await navigatorPage.getTextOfElement(await navigatorPage.divisionDropdownDefaultSelectedText);
        let storeDropdownText = await navigatorPage.getTextOfElement(await navigatorPage.storeDropdownDefaultSelectedText);
        await navigatorPage.validateDropdownValue('Division', divisionDropdownText === '1 Item Selected' ? navigatorData.one_Item_Selected : 'Select Division');
        await navigatorPage.validateDropdownValue('Stores', storeDropdownText === '1 Item Selected' ? navigatorData.one_Item_Selected : 'Select Store');
        await navigatorPage.validateDropdownValue( 'Department', navigatorData.one_Item_Selected);
        await navigatorPage.validateDropdownValue( 'SubDept', navigatorData.one_Item_Selected);
        await navigatorPage.validateDropdownValue( 'Commodity', navigatorData.one_Item_Selected);
        await navigatorPage.validateDropdownValue( 'CommodityGroup', navigatorData.one_Item_Selected);
        await navigatorPage.validateDropdownValue( 'PlanogramStatus', navigatorData.one_Item_Selected);
    })

    it('Verify Clear Dropdown function with Single data selection on Navigator page for store user', async () => {

        await navigatorPage.genericClearOptions(clearDropDownList[1]);
        await navigatorPage.waitBrowserToLoadPage(5000)
        await navigatorPage.validateDropdownValue('Division', navigatorData.one_Item_Selected);
        await navigatorPage.validateDropdownValue('Stores', navigatorData.one_Item_Selected);
        await navigatorPage.validateDropdownValue( 'Department', navigatorData.select_Department);
        await navigatorPage.validateDropdownValue( 'SubDept', navigatorData.select_Sub_Department);
        await navigatorPage.validateDropdownValue( 'PlanogramStatus', navigatorData.select_Planogram_Status);
        await navigatorPage.validateDropdownValue( 'Commodity', navigatorData.select_Commodity);
        await navigatorPage.validateDropdownValue( 'CommodityGroup', navigatorData.select_Commodity_Group);
        await expect (await navigatorPage.enterValue.getValue()).toEqual(await upcNumber);
    })

    it('Verify Clear All function with Single data selection on Navigator page for store user', async () => {

        await navigatorPage.genericClearOptions(clearDropDownList[2]);
        await navigatorPage.waitBrowserToLoadPage(5000)
        await navigatorPage.validateDropdownValue('Division', navigatorData.one_Item_Selected);
        await navigatorPage.validateDropdownValue('Stores', navigatorData.one_Item_Selected);
        await navigatorPage.validateDropdownValue( 'Department',  navigatorData.select_Department);
        await navigatorPage.validateDropdownValue( 'SubDept', navigatorData.select_Sub_Department);
        await navigatorPage.validateDropdownValue( 'PlanogramStatus',  navigatorData.select_Planogram_Status);
        await navigatorPage.validateDropdownValue( 'Commodity',  navigatorData.select_Commodity);
        await navigatorPage.validateDropdownValue( 'CommodityGroup',  navigatorData.select_Commodity_Group);
          })

        })
