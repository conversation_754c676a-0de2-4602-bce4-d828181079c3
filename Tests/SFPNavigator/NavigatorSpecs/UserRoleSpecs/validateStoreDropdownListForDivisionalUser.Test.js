const navigatorPage = require('../../NavigatorPO/navigator.page');
const navigatorData = require('../../../../TestData/SFPNavigatorData/navigatorUIData.json');
const envVariables = require('../../../../envConfig');
const ENV = process.env.ENV
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');

let defaultDivNumber;

before(async () => {

    await testSetup(envVariables.divisionalUser[ENV], envVariables.divisionalPassword[ENV]);
    defaultDivNumber =  navigatorData.div_016_Columbus_Division.match(/(\d[\d]*)/g)[0];
})
describe('validateStoreDropdownListForDivisionalUser: Validate store dropdown only contains values related to default-selected division value', () => {

    it('Validate division dropdown is disabled and store dropdown list only contains values related to default-selected Div value ', async () => {

        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000);
        let divisionDropdownText = await navigatorPage.getTextOfElement(await navigatorPage.divisionDropdownDefaultSelectedText);
        await navigatorPage.validateDropdownValue('Division', divisionDropdownText === '1 Item Selected' ? navigatorData.one_Item_Selected : 'Select Division');
        let divLocatorDisabled = await navigatorPage.locatorDisabled(await navigatorPage.divisionDropdown);
        await expect(divLocatorDisabled).toEqual(true);
        let storeDropdownList = await navigatorPage.getDropDownList('Stores', 'store');
        let extractedDivList = await navigatorPage.extractSubArraysByIndex(storeDropdownList, 3);
        await navigatorPage.waitBrowserToLoadPage(3000);
        const extractedDivListUnique = await navigatorPage.getUniqueArrayValue(await extractedDivList);
        await expect(extractedDivListUnique.toString()).toEqual(await defaultDivNumber);
    })
})
