const navigatorPage = require('../../NavigatorPO/navigator.page');
const envVariables = require('../../../../envConfig');
const ENV = process.env.ENV
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const {assert} = require("chai");

before(async () => {

    await testSetup(await envVariables.multiDivisionalUser[ENV], await envVariables.multiDivisionalPassword[ENV]);
})
describe('validateMultiDivisionalUserDropdown: Validate Division and Store dropdown values', () => {

    it('Validate division dropdown only has two values and validate Store dropdown only shows that are related to Division dropdown value,' +
        'Then go to Floorplans tab and validate Division column list only shows values related to main page division dropdown', async () => {

        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(5000);
        let divDropdownList = await navigatorPage.getDropDownList('Division', 'divisionMultiSelect');
        if(divDropdownList.length === 2){
            let extractedDivListFromDivDropdown = await navigatorPage.extractSubArraysByIndex(divDropdownList, 3);
            await navigatorPage.selectSelectAllValue('Division', 'divisionMultiSelect',  'Select All');
            let storeDropdownList = await navigatorPage.getDropDownList('Stores', 'store');
            let extractedDivListFromStoreDropdown = await navigatorPage.extractSubArraysByIndex(storeDropdownList, 3);
            await navigatorPage.waitBrowserToLoadPage(3000);
            const extractedDivListFromStoreDropdownUnique = await navigatorPage.getUniqueArrayValue(await extractedDivListFromStoreDropdown);
            await expect(extractedDivListFromStoreDropdownUnique).toEqual(await extractedDivListFromDivDropdown);
            await navigatorPage.goTo('Floorplans');
            await navigatorPage.waitBrowserToLoadPage(5000);
            let divColumnListBefore = await navigatorPage.getColumnArrayList('Div');
            await navigatorPage.paginationLastPage.click();
            await navigatorPage.waitBrowserToLoadPage(5000);
            let divColumnListAfter = await navigatorPage.getColumnArrayList('Div');
            await expect(await (await navigatorPage.getUniqueArrayValue(divColumnListBefore)).toString()).toEqual(await extractedDivListFromDivDropdown[0]);
            await expect(await (await navigatorPage.getUniqueArrayValue(divColumnListAfter)).toString()).toEqual(await extractedDivListFromDivDropdown[1]);
        }else{
            await assert.fail(0, 1, "Error: Division dropdown has more or less than 2 values !");
        }
    })
})
