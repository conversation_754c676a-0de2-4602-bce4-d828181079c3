const navigatorPage = require('../../NavigatorPO/navigator.page');
const envVariables = require('../../../../envConfig');
const ENV = process.env.ENV
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');
const navigatorData = require('../../../../TestData/SFPNavigatorData/navigatorUIData.json');
const {assert} = require("chai");

before(async () => {
    await testSetup(await envVariables.corpUser[ENV], await envVariables.corpPassword[ENV]);
})

describe('validateCorpUserAccessToNavigator: Validate Corporate User only access to Navigator tab ', () => {

    it('Validate navBarList ONLY has Navigator tab ', async () => {

        await navigatorPage.waitBrowserToLoadPage(3000)
        await navigatorPage.navBarHeader.click();
        await navigatorPage.waitBrowserToLoadPage(3000)
        let navBarTextList = await navigatorPage.getArrayElementsText(await navigatorPage.navBarList);
        let navBarTextClean = await navigatorPage.removeValueFromArray(navBarTextList, '');
        if(await navBarTextClean.length === 1){
            await expect(navBarTextClean.toString()).toEqual(navigatorData.sfp_Navigator_Text);
        } else {
            await assert.fail(0, 1, "Error: Nav bar list has other values which should not be there !");
        }
    })

    it(' Validate: 1) division dropdown is enabled for CorpUser and clickable; 2) Store dropdown only displays values corresponding to selected division dropdown value', async () => {

        let endIndex = 3;
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(5000);
        await expect(await navigatorPage.locatorDisabled(await navigatorPage.divisionDropdown)).toEqual(false);
        await expect(await navigatorPage.elementIsClickable(await navigatorPage.divisionDropdown)).toEqual(true);
        let divDropdownList = await navigatorPage.getDropDownList('Division', 'divisionMultiSelect');
        if(divDropdownList.length > 25){
           let extractedDivListFromDivDropdown = await navigatorPage.extractSubArraysByIndex(divDropdownList, 3);
            await navigatorPage.selectDropDownValues('Division', 'divisionMultiSelect',2,endIndex);
            let storeDropdownList = await navigatorPage.getDropDownList('Stores', 'store');
            let extractedDivListFromStoreDropdown = await navigatorPage.extractSubArraysByIndex(storeDropdownList, 3);
            await navigatorPage.waitBrowserToLoadPage(3000);
            const extractedDivListFromStoreDropdownUnique = await navigatorPage.getUniqueArrayValue(await extractedDivListFromStoreDropdown);
            await navigatorPage.waitBrowserToLoadPage(2000);
            await expect(await (await extractedDivListFromStoreDropdownUnique).toString()).toEqual(await extractedDivListFromDivDropdown[endIndex-1]);
        }else{
            await assert.fail(0, 1, "Error: Division dropdown has less than 25 values !");
         }
    })

})


