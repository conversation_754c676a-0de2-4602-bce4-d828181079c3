const navigatorPage = require('../../NavigatorPO/navigator.page');
const navigatorData = require('../../../../TestData/SFPNavigatorData/navigatorUIData.json');
const envVariables = require('../../../../envConfig');
const ENV = process.env.ENV
const { testSetup } = require('../../../../helpers/testSetupTeardown.js');

before(async () => {
    await testSetup(await envVariables.storeUser[ENV], await envVariables.storePassword[ENV]);
})
describe('validateDefaultStoreValueNotClearedForStoreUser: Store dropdown value should not be cleared after clicking DropDowns and All buttons', () => {


    it('Validating Default Division and Store values are not cleared after clicking clear-dropdown buttons ', async () => {

        await expect(browser).toHaveUrlContaining(await envVariables.url[ENV]);
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(2000);
        const clearDropDownList = await navigatorPage.getArrayElementsText(await navigatorPage.clearButtonList);
        for(let clearBtnIndex=0;clearBtnIndex<clearDropDownList.length;clearBtnIndex++){
            await navigatorPage.genericClearOptions(await clearDropDownList[clearBtnIndex]);
            let divisionDropdownText = await navigatorPage.getTextOfElement(await navigatorPage.divisionDropdownDefaultSelectedText);
            let storeDropdownText = await navigatorPage.getTextOfElement(await navigatorPage.storeDropdownDefaultSelectedText);
            await navigatorPage.validateDropdownValue('Division', divisionDropdownText === '1 Item Selected' ? navigatorData.one_Item_Selected : 'Select Division');
            await navigatorPage.validateDropdownValue('Stores', storeDropdownText === '1 Item Selected' ? navigatorData.one_Item_Selected : 'Select Store');
        }
    })
})
