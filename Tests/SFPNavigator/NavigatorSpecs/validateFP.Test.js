const navigatorPage = require("../NavigatorPO/navigator.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const qrString = require("../../../SQLConnection/queryString");
const excelReader = require("../../../util/excelReader");
const mappingReportPage = require("../NavigatorPO/mappingReport.page");
const result = require("../../../SQLConnection/server.js.ts");
const floorPlanPage = require("../NavigatorPO/floorPlan.page");
const { DBNames } = require("../../../envConfig.js");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
const { assert } = require("chai");

let divNumber, upc, floorPlanKey;
beforeEach(async () => {
  await navigatorPage.open("navigator");
  divNumber = navigatorData.div_016_Columbus_Division.match(/(\d[\d]*)/g)[0];
  await navigatorPage.waitBrowserToLoadPage(3000);
});

describe("validateFP: Validate functionality and data on Floorplans", () => {
  const division = navigatorData.div_016_Columbus_Division;
  const store = navigatorData.store_016_00506;
  const floorplan = navigatorData.floorPlanStatusLive;
  const storeValueForQuery = store.split(" - ")[1];
  it("Validate search by Floor Key and and compare FP Department dropdown values to UI column data ", async () => {
    floorPlanKey = await (
      await (
        await await result.getResult(
          dynamicQuery.floorKeyForFpDepart,
          DBNames.SFP_STAGE
        )
      ).flat()
    ).toString();
    await navigatorPage.searchByValue("Floor Key", await floorPlanKey);
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
    ]);
    await expect(await navigatorPage.validateTab("Floorplans")).toBeClickable();
    await navigatorPage.goTo("Floorplans");
    await navigatorPage.waitForPageLoad(
      await navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
    let fpDepartmentDropdownList = (
      await mappingReportPage.getFilterDropdownList(
        "FloorplanDepartment",
        "fp-department"
      )
    ).filter((item) => !item.includes(navigatorData.SelectAll));
    await navigatorPage.waitBrowserToLoadPage(3000);
    let tableRowCount = (await navigatorPage.tableRows).length;
    if (tableRowCount > 0) {
      let fpDepartmentUiColumnList = await navigatorPage.getColumnArrayList(
        "Floorplan Department"
      );
      let fpDepartmentUiColumnListUnique =
        await navigatorPage.getUniqueArrayValue(fpDepartmentUiColumnList);
      await expect(fpDepartmentDropdownList).toEqual(
        fpDepartmentUiColumnListUnique
      );
    } else {
      await assert.fail(0, 1, "Error: Table has no data !");
    }
  });

  it("When div, store and floorplan states are selected, validate download excel file and compare the excel data with DB data", async () => {
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
      { id: navigatorData.floorplanDropdown, values: floorplan },
    ]);
    await navigatorPage.goTo("Floorplans");
    await navigatorPage.waitBrowserToLoadPage(2000);
    await navigatorPage.waitForPageLoad(
      await navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
    await navigatorPage.exportToExcel();
    let excelData = await excelReader.readExcelFile();
    await navigatorPage.waitBrowserToLoadPage(6000);
    let dataCompare = await excelReader.excelDataCompareAllRow(
      qrString.FloorplansQuery.replace("@div", divNumber).replace(
        "@store",
        storeValueForQuery
      ),
      excelData.length,
      DBNames.SFP_STAGE
    );
    await expect(dataCompare).toEqual(true);
  });

  it("Data validation Floorplans Tab: validate download excel file and compare the excel data with DB data", async () => {
    floorPlanKey = await (
      await (
        await await result.getResult(
          dynamicQuery.floorKeyForFpDepart,
          DBNames.SFP_STAGE
        )
      ).flat()
    ).toString();
    await navigatorPage.searchByValue("Floor Key", floorPlanKey);
    await navigatorPage.waitBrowserToLoadPage(2000);
    await navigatorPage.goTo("Floorplans");
    await navigatorPage.waitForPageLoad(
      await navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
    await navigatorPage.exportToExcel();
    await navigatorPage.waitBrowserToLoadPage(6000);
    let data = await excelReader.excelDataCompareAllRow(
      qrString.FloorplansQueryDbKey.replace("@dbkey", await floorPlanKey)
        .replace("@Event_Type", "%")
        .replace("@Div", "%"),
      1,
      DBNames.SFP_STAGE
    );
    await expect(data).toEqual(true);
  });

  it("Floorplan should display when Division & Store & UPC are selected; validate UI data ", async () => {
    upc = await (
      await (
        await await result.getResult(
          dynamicQuery.upcForFpDepart,
          DBNames.SFP_STAGE
        )
      ).flat()
    ).toString();
    await navigatorPage.selectMultipleDropdowns([
      {
        id: navigatorData.divisionDropdown,
        values: navigatorData.divisionListFP,
      },
      { id: navigatorData.storeDropdown, values: store },
    ]);
    await navigatorPage.searchByValue("UPC", upc);
    await navigatorPage.waitBrowserToLoadPage(2000);
    await navigatorPage.goTo("Floorplans");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await (
      await floorPlanPage.floorplansTableValue
    ).waitForExist;
    await navigatorPage.exportToExcel();
    await navigatorPage.waitBrowserToLoadPage(5000);
    let uiData = await navigatorPage.getAllUIData();
    let filteredFpUiData = await navigatorPage.removeElementsFromArrayOfArrays(
      uiData,
      "PDF",
      "Planograms"
    );
    let compareData = await excelReader.excelUiDataCompareAllRows(
      filteredFpUiData
    );
    await expect(compareData).toEqual(true);
  });

  it("Validate Store list in Floorplan tab ", async () => {
    await navigatorPage.selectMultipleDropdowns([
      {
        id: navigatorData.divisionDropdown,
        values: navigatorData.div_014_Cincinnati_Division,
      },
      {
        id: navigatorData.storeDropdown,
        values: navigatorData.store_014_00335,
      },
    ]);
    await navigatorPage.goTo("Floorplans");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.exportToExcel();
    let excelFpStores = await excelReader.getExcelFPStoreValues();
    let distinctExcelFpStores = await mappingReportPage.getUniqueArrayValue(
      excelFpStores
    );
    let distinctDbStores = await (
      await result.getResult(qrString.storeListQuery, DBNames.SFP_STAGE)
    ).flat();
    const isSubset = distinctExcelFpStores.every((val) =>
      distinctDbStores.includes(val)
    );
    await expect(isSubset).toEqual(true);
  });
});
