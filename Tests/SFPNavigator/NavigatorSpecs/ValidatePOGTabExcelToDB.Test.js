import { excelDataCompareAllRow } from '../../../util/excelReader';
const navigatorPage = require('../NavigatorPO/navigator.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const qrString = require('../../../SQLConnection/queryString');
const {DBNames} = require("../../../envConfig.js");

describe('ValidatePOGTabExcelToDB: Download exel file, read contents and compare with DB data', () => {

  it('POG Tab:Validate the downloaded excel file and compare the contents with DB data for Allrows', async () => {

    await navigatorPage.waitBrowserToLoadPage(3000);
await navigatorPage.open('navigator');
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_018_Michigan_Division);
    await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_018_00038);
    await navigatorPage.selectSingleValue('PlanogramStatus', 'pog-status', navigatorData.planogramStatusLive);
    await navigatorPage.goTo('POGs')
    await navigatorPage.exportToExcel()
    await navigatorPage.waitBrowserToLoadPage(6000)
    let compareDataAllRow = await excelDataCompareAllRow(qrString.pogTabQueryWithDivStore, DBNames.SFP_STAGE)
    await expect(compareDataAllRow).toEqual(true)
  })
})
