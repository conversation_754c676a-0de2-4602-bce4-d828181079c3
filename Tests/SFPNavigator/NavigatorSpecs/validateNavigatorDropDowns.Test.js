const navigatorPage = require('../NavigatorPO/navigator.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');

describe('validateNavigatorDropDowns: Handling dropdown in Navigator home page', () => {

    before(async () => {
        await navigatorPage.open("navigator");
        await navigatorPage.genericClearOptions('All');
    })
    
    it('Should validate the division dropdown', async () => {

        await navigatorPage.waitBrowserToLoadPage(2000);
        let finalValue = await navigatorPage.getDropdownValue('Division', 'divisionMultiSelect','divisionMultiSelect');
        await navigatorPage.waitBrowserToLoadPage(2000);
        const selectTextDiv = await navigatorPage.divisionDropdown;
        await expect([finalValue].length > 1 ? [finalValue].length + ' Items Selected': [finalValue].length + ' Item Selected' ).toEqual(await selectTextDiv.getText());
    })

    it('Should validate the store dropdown', async () => {

        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_701_Fred_Meyer_Stores);
        await navigatorPage.waitBrowserToLoadPage(2000);
        let finalValue = await navigatorPage.getDropdownValue('Stores', 'store', 'store-select');
        await navigatorPage.waitBrowserToLoadPage(2000);
        const selectTextStore = await navigatorPage.storeDropdown;
        await expect([finalValue].length + ' Item Selected').toEqual(await selectTextStore.getText());
    })

    it('Should validate the department dropdown', async () => {

        let finalValue = await navigatorPage.getDropdownValue('Department', 'dept', 'dept-select');
        await navigatorPage.waitBrowserToLoadPage(2000)
        const selectText = await navigatorPage.deptDropdown;
        await expect([finalValue].length + ' Item Selected').toEqual(await selectText.getText());
    })

    it('Should validate the SubDepartment dropdown', async () => {
        await navigatorPage.selectMultipleDropdowns([
              { id: navigatorData.departmentDropdown, values: navigatorData.dept_03_HBC },
        ]);
        let finalValue = await navigatorPage.getDropdownValue('SubDept', 'sub-dept', 'sub-dept-select');
        await navigatorPage.waitBrowserToLoadPage(2000);
        const selectText = await navigatorPage.subDeptDropdown;
        await navigatorPage.waitBrowserToLoadPage(2000);
        await expect(await([finalValue].length + ' Item Selected')).toEqual(await (selectText.getText()));
    })

    it('Should validate the commodity dropdown', async () => {
        await navigatorPage.selectMultipleDropdowns([
            { id: navigatorData.departmentDropdown, values: navigatorData.dept_06_PHARMACY },
        ]);
        let finalValue = await navigatorPage.getDropdownValue('Commodity','commodity','commodity-select');
        await navigatorPage.waitBrowserToLoadPage(2000);
        const selectText = await navigatorPage.commodityDropdown;
        await expect(await ([finalValue].length + ' Item Selected')).toEqual(await selectText.getText());
    })

    it('Should validate the commodity Group dropdown', async () => {
        await navigatorPage.selectMultipleDropdowns([
            { id: navigatorData.departmentDropdown, values: navigatorData.dept_06_PHARMACY },
        ]);
        let finalValue = await navigatorPage.getDropdownValue('CommodityGroup', 'comm-group','comm-group-select');
        await navigatorPage.waitBrowserToLoadPage(2000)
        const selectText = await navigatorPage.commodityGrpDropdown;
        await expect(await ([finalValue].length + ' Item Selected')).toEqual(await selectText.getText());
    })

    it('Should validate the Planogram Status dropdown', async () => {

        await navigatorPage.waitBrowserToLoadPage(5000);
        let finalValue = await navigatorPage.getDropdownValue('PlanogramStatus', 'pog-status','pog-status-select',)
        await navigatorPage.waitBrowserToLoadPage(2000);
        const selectText = await navigatorPage.planogramStatusDropdown;
        await navigatorPage.waitBrowserToLoadPage(3000);
        const finalValueLength = [finalValue].length;
        await expect(await (finalValueLength+1 + ' Items Selected')).toEqual(await selectText.getText());
    })

    it('Should validate the floor Plan Status dropdown', async () => {

        await navigatorPage.waitBrowserToLoadPage(5000);
        let finalValue = await navigatorPage.getDropdownValue('FloorplanStatus', 'floorplan','floorplan-select',);
        await navigatorPage.waitBrowserToLoadPage(2000)
        const selectText = await navigatorPage.fpStatusDropdown;
        const finalValueLength = [finalValue].length;
        await expect(await (finalValueLength+1 + ' Items Selected')).toEqual(await selectText.getText());
    })

    it('Should validate the eventType dropdown', async () => {

        let finalValue = await navigatorPage.getDropdownValue('EventType','event-type', 'event-type-select');
        await navigatorPage.waitBrowserToLoadPage(2000)
        const selectText = await navigatorPage.eventTypeDropdown;
        await expect(finalValue).toEqual(await selectText.getText());
    })

    it('Should validate the eventName dropdown', async () => {

        let finalValue = await navigatorPage.getDropdownValue('EventName', 'event-name','event-name-select',);
        await navigatorPage.waitBrowserToLoadPage(2000);
        const selectText = await navigatorPage.eventNameDropdown;
        await expect([finalValue].length + ' Item Selected').toEqual(await selectText.getText());
    })

    it('Should validate the commodity dropdown is sorted', async () => {
        await navigatorPage.selectMultipleDropdowns([
            { id: navigatorData.departmentDropdown, values: navigatorData.dept_06_PHARMACY },
        ]);
        let commodityList = (await navigatorPage.getDropDownList('Commodity', 'commodity')).filter(item => !item.includes('Select All'))
        await expect (commodityList).toEqual(commodityList.sort());
    })
})
