const navigatorPage = require("../NavigatorPO/navigator.page");
const mappingReportPage = require("../NavigatorPO/mappingReport.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const { assert } = require("chai");

describe("validateHeaderSortButtonsOnMappingReport: validate Sort By Aisle button on mapping report ", () => {
  const division = navigatorData.div_021_Central_Division;
  const store = navigatorData.store_021_00016;
  const planogram = navigatorData.planogramStatusLive;
  beforeEach(async () => {
    await navigatorPage.open("navigator");
  });

  it("Verify SortByAisle functionality with 1 division & 1 store when results are ordered in desc order on Aisle column", async () => {
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
      { id: navigatorData.planogramDropdown, values: planogram },
    ]);
    await navigatorPage.goTo("Mapping Report");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.waitForPageLoad(
      await mappingReportPage.filtersBtn,
      "Filters"
    );
    await mappingReportPage.clickOn(await navigatorPage.moduleLink);
    for (let loopNum = 0; loopNum <= 1; loopNum++) {
      await navigatorPage.clickColumnHeader(
        await navigatorPage.tableHeaders,
        "Aisle #"
      );
      await navigatorPage.waitBrowserToLoadPage(5000);
    }
    await navigatorPage.waitBrowserToLoadPage(4000);
    const descendingOrder = await navigatorPage.getColumnArrayList(
      navigatorData.mappingReport_AisleColumn
    );
    let validateDescendingOrder = await mappingReportPage.isDescending(
      descendingOrder
    );
    await navigatorPage.waitBrowserToLoadPage(2000);
    await expect(validateDescendingOrder).toEqual(true);
    await navigatorPage.clickColumnHeader(
      await navigatorPage.tableHeaders,
      "Aisle Desc"
    );
    await navigatorPage.waitBrowserToLoadPage(5000);
    const ascendingOrder = await navigatorPage.getColumnArrayList(
      navigatorData.mappingReport_AisleColumn
    );
    let validateAscendingOrder = await mappingReportPage.isAscending(
      ascendingOrder
    );
    await expect(validateAscendingOrder).toEqual(true);
    await navigatorPage.waitBrowserToLoadPage(2000);
    await assert.notEqual(
      descendingOrder,
      ascendingOrder,
      "Error:sortByAisle could not reset results to default ordering"
    );
  });

  it("Validate Division column sort function with multiple divisions for ascending and descending data order", async () => {
    await navigatorPage.selectMultipleDropdowns([
      {
        id: navigatorData.divisionDropdown,
        values: navigatorData.divisionList_SortByAisle,
      },
      {
        id: navigatorData.planogramDropdown,
        values: navigatorData.planogramStatusPending,
      },
    ]);
    await navigatorPage.goTo("Mapping Report");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.waitForPageLoad(
      await mappingReportPage.filtersBtn,
      navigatorData.mappingReport_Filters
    );
    await mappingReportPage.clickOn(await navigatorPage.moduleLink);
    await navigatorPage.waitBrowserToLoadPage(2000);
    const divAscendingOrder = await navigatorPage.getColumnArrayList(
      navigatorData.mappingReport_Div_Column
    );
    let validateAscendingOrder = await mappingReportPage.isAscending(
      divAscendingOrder
    );
    await expect(validateAscendingOrder).toEqual(true);
    for (let loopNum = 0; loopNum <= 1; loopNum++) {
      await navigatorPage.clickColumnHeader(
        await navigatorPage.tableHeaders,
        "Div"
      );
      await navigatorPage.waitBrowserToLoadPage(5000);
    }
    const divDescendingOrder = await navigatorPage.getColumnArrayList(
      navigatorData.mappingReport_Div_Column
    );
    let validateDescendingOrder = await mappingReportPage.isDescending(
      divDescendingOrder
    );
    await navigatorPage.waitBrowserToLoadPage(2000);
    await expect(validateDescendingOrder).toEqual(true);
    await navigatorPage.waitBrowserToLoadPage(2000);
    await assert.notEqual(
      divDescendingOrder,
      divAscendingOrder,
      "Error: Div column sort button could not reset results for descending and ascending order !"
    );
  });
});
