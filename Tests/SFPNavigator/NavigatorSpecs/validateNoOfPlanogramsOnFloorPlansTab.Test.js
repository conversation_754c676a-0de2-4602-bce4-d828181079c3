const navigatorPage = require('../NavigatorPO/navigator.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const mappingReportPage = require('../NavigatorPO/mappingReport.page');
const qrString = require('../../../SQLConnection/queryString');
const {excelDataCompareAllRow} = require('../../../util/excelReader');
const {DBNames} = require("../../../envConfig.js");

describe('validateNoOfPlanogramsOnFloorPlansTab: Validate No of Planograms link functionality and data on Floorplans', () => {

    it('FPStatusLive:Validate No of Planograms link functionality and data on Floorplans with FP status LIVE', async () => {

        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',  navigatorData.div_016_Columbus_Division);
        await navigatorPage.selectSingleValue('FloorplanStatus', 'floorplan', navigatorData.floorPlanStatusLive);
        await navigatorPage.goTo('Floorplans');
        await navigatorPage.waitForPageLoad(navigatorPage.btnExportToExcel, navigatorData.Export_To_Excel_text);
        let floorKey = await navigatorPage.getLinkText('DBKey');
        await navigatorPage.getLinkText('# of Planograms');
        await mappingReportPage.textSelectedFloorplanKey.waitForDisplayed({timeout:3000});
        let selectedPOGKey = (await mappingReportPage.textSelectedFloorplanKey.getText()).match(/(\d[\d]*)/g)[0];
        await expect(selectedPOGKey).toEqual(floorKey);
        await navigatorPage.exportToExcel();
        await navigatorPage.waitBrowserToLoadPage(6000);
        let data = await excelDataCompareAllRow(qrString.mappingReportQueryFloorKey.replace('@FPKey', floorKey.toString()).replace('@status', '1'), DBNames.SFP_STAGE);
        await expect(data).toEqual(true);
    })

    it('FPStatusPending: Validate No of Planograms link functionality and data on Floorplans with FP status PENDING', async () => {

        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_701_Fred_Meyer_Stores);
        // eslint-disable-next-line camelcase
        //comment out following line in the TEST environment
        //await navigatorPage.selectSingleValue('FloorplanStatus', 'floorplan', navigatorData.floorPlanStatusPending);
        await navigatorPage.goTo('Floorplans');
        await navigatorPage.waitForPageLoad(navigatorPage.btnExportToExcel, navigatorData.Export_To_Excel_text);
        let floorKey = await navigatorPage.getLinkText('DBKey');
        await navigatorPage.getLinkText('# of Planograms');
        await mappingReportPage.textSelectedFloorplanKey.waitForDisplayed({timeout:3000});
        let selectedFloorplanPOGKey = (await mappingReportPage.textSelectedFloorplanKey.getText()).match(/(\d[\d]*)/g)[0];
        await expect(selectedFloorplanPOGKey).toEqual(floorKey);
        await navigatorPage.exportToExcel();
        let data = await excelDataCompareAllRow(qrString.mappingReportQueryFloorKey.replace('@FPKey', floorKey.toString()).replace('@status', '2'), DBNames.SFP_STAGE);
        await expect(data).toEqual(true);
    })
})
