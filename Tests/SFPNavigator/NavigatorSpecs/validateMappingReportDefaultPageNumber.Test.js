const navigatorPage = require("../NavigatorPO/navigator.page");
const mappingReportPage = require("../NavigatorPO/mappingReport.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const assert = require("chai").assert;

let setDefaultPageNum;
describe("validateMappingReportDefaultPageNumber: Validate Mapping Report tab default page number dropdown", () => {
  const division = navigatorData.div_014_Cincinnati_Division;
  const store = navigatorData.division_014_multiple;
  it("When NumberOfStores column value is clicked, validate default selected page number and page selection dropdown", async () => {
    setDefaultPageNum = navigatorData.page_row_size_120.toString();
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
    ]);
    await navigatorPage.goTo("Mapping Report");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.waitForPageLoad(
      await navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
    await navigatorPage.getLinkText(navigatorData.col_noOfStores);
    if (
      (await mappingReportPage.pogDbKeyFilterString) !== null &&
      (await navigatorPage.tableRows.length) >= 60
    ) {
      let defaultPageNumText = await navigatorPage.getTextOfElement(
        await mappingReportPage.defaultPageNumBtn
      );
      await expect(setDefaultPageNum).toEqual(defaultPageNumText);
      let firstPageRowNumber = await navigatorPage.getCurrentPageRowLength(
        await mappingReportPage.mappingReportTable,
        await mappingReportPage.tableRowList
      );
      await navigatorPage.waitBrowserToLoadPage(4000);
      await assert.isAtLeast(
        parseInt(defaultPageNumText),
        parseInt(firstPageRowNumber),
        "DefaultPageNumber should be equal or bigger than table row number"
      );
      let pageNumbers = await navigatorPage.getPageNumberList();
      for (let index = 0; index < pageNumbers.length; index++) {
        await navigatorPage.selectPageNumber(await pageNumbers[index]);
        await navigatorPage.waitBrowserToLoadPage(2000);
        const firstPage = navigatorPage.previousButtonClass;
        if (await firstPage.isEnabled()) {
          await navigatorPage.clickOn(await firstPage);
        }
        await navigatorPage.waitBrowserToLoadPage(2000);
        let rowCount = await navigatorPage.getRowCount();
        for (let rowNum = 0; rowNum < rowCount.length - 1; rowNum++) {
          await expect(rowCount[rowNum].toString()).toEqual(pageNumbers[index]);
        }
      }
    } else {
      await assert.fail(
        0,
        1,
        "Error: Table did not have enough data. Table must have over 60 rows !"
      );
    }
  });
});
