import {excelDataCompareAllRow, getExcelDataCount} from '../../../util/excelReader';
import {DBNames} from "../../../envConfig.js";
import result from "../../../SQLConnection/server.js.ts";
const navigatorPage = require('../NavigatorPO/navigator.page');
const storePage = require('../NavigatorPO/store.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const qrString = require('../../../SQLConnection/queryString');

let divValue;
describe('ValidateStoreTabExcelToDBDivOnly: Only use Divisional value and navigate to Store tab. Then Download exel file, read contents and compare it with DB data', () => {

    before(async ()=>{
        let divisionValue = navigatorData.div_708_Food_4_Less_Midwest_Division
        await navigatorPage.waitBrowserToLoadPage(3000);
await navigatorPage.open('navigator');
        divValue = await divisionValue.match(/(\d[\d]*)/g)[0];
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',  await divisionValue);
        await storePage.goTo('Store');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.waitForPageLoad(navigatorPage.btnExportToExcel, navigatorData.Export_To_Excel_text);
        await navigatorPage.exportToExcel();
        await navigatorPage.waitBrowserToLoadPage(3000);
    })

    it('Store Tab: Validate the downloaded excel file and compare the UI data to DB for no of rows', async () => {

        let data = await excelDataCompareAllRow(qrString.storeTabQuery.replace('@Div', await divValue),5, DBNames.SFP_STAGE);
        await expect(data).toEqual(true);
    })

    it('Store Tab: Validate the downloaded excel file and compare the UI data to DB', async () => {

        let data = await excelDataCompareAllRow(qrString.storeTabQuery.replace('@Div', await divValue), await (await getExcelDataCount() -1), DBNames.SFP_STAGE);
        await expect(data).toEqual(true)                                                      
    })

})
