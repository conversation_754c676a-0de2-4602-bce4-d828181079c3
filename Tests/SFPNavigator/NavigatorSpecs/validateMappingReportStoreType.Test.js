const navigatorPage = require("../NavigatorPO/navigator.page");
const mappingReportPage = require("../NavigatorPO/mappingReport.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const result = require("../../../SQLConnection/server.js.ts");
const underscorelib = require("underscore");
const fetchQuery = require("../../../SQLConnection/queryString");
const assert = require("chai").assert;
const { DBNames } = require("../../../envConfig.js");

let storeNum00053, storeList;
describe("validateMappingReportStoreType: validate StoreType dropdown list both on UI and db", () => {
  storeNum00053 = navigatorData.store_014_00053.split("- ")[1];
  storeList = navigatorPage.extractArrayValuesAsString(
    navigatorData.storeList_StoreType,
    1
  );
  const division = navigatorData.div_014_Cincinnati_Division;
  const store = navigatorData.store_014_00053;
  const planogram = navigatorData.planogramStatusLive;
  const floorplan = navigatorData.floorPlanStatusLive;
  before(async () => {});

  beforeEach(async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
      { id: navigatorData.planogramDropdown, values: planogram },
      { id: navigatorData.floorplanDropdown, values: floorplan },
    ]);

    await navigatorPage.selectSingleValue(
      "Division",
      "divisionMultiSelect",
      navigatorData.div_014_Cincinnati_Division
    );
  });
  it("validateSingleDropdownOptionFromStoreType: validate single StoreType dropdown value both on UI and db when single Div/Store is selected on Navigator", async () => {
    await navigatorPage.goTo("Mapping Report");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.waitForPageLoad(
      await mappingReportPage.filtersBtn,
      navigatorData.mappingReport_Filters
    );
    await navigatorPage.clickOn(await mappingReportPage.filtersBtn);
    let storeTypeUi = await mappingReportPage.getFilterDropdownList(
      navigatorData.mappingReport_StoreType,
      "store-type"
    );
    let filteredStoreTypeUi = storeTypeUi.filter(
      (item) => !item.includes(navigatorData.mappingReport_Select_All)
    );
    let storeTypeDb = await (
      await result.getResult(
        fetchQuery.mappingReport_StoreTypeDropdown.replace(
          "@storeMR",
          storeNum00053
        ),
        DBNames.SFP_STAGE
      )
    ).flat();
    await expect(underscorelib.isEqual(filteredStoreTypeUi, storeTypeDb)).toBe(
      true
    );
    let storeTypeTableUi = await mappingReportPage.getAllColumnValues(
      navigatorData.mappingReport_Store_Type
    );
    let uniqueStoreTypeTableUi = await mappingReportPage.getUniqueArrayValue(
      storeTypeTableUi
    );
    await expect(filteredStoreTypeUi.toString()).toEqual(
      uniqueStoreTypeTableUi.toString()
    );
    await expect(
      await mappingReportPage.storeTypeDropdownText.getText()
    ).toEqual(navigatorData.mappingReport_Select_Store_Type);
  });

  it("validateMultipleDropdownOptionsFromStoreType: validate multiple StoreType dropdown options both on UI and db when multiple stores are selected on Navigator", async () => {
    await navigatorPage.selectMultipleDropdowns([
      {
        id: navigatorData.storeDropdown,
        values: navigatorData.storeList_StoreType,
      },
    ]);
    await navigatorPage.goTo("Mapping Report");
    await navigatorPage.waitForPageLoad(
      await mappingReportPage.filtersBtn,
      navigatorData.mappingReport_Filters
    );
    await navigatorPage.clickOn(await mappingReportPage.filtersBtn);
    let storeTypeUi = await mappingReportPage.getFilterDropdownList(
      navigatorData.mappingReport_StoreType,
      "store-type"
    );
    let filteredStoreTypeUi = storeTypeUi.filter(
      (item) => !item.includes(navigatorData.mappingReport_Select_All)
    );
    let storeTypeDb = await (
      await result.getResult(
        fetchQuery.mappingReport_StoreTypeDropdown.replace(
          "@storeMR",
          await storeList
        ),
        DBNames.SFP_STAGE
      )
    ).flat();
    await expect(
      underscorelib.isEqual(
        filteredStoreTypeUi.sort(),
        await storeTypeDb.sort()
      )
    ).toBe(true);
    for (let index = 0; index < filteredStoreTypeUi.length; index++) {
      await mappingReportPage.selectFilterValue(
        navigatorData.mappingReport_StoreType,
        "store-type",
        filteredStoreTypeUi[index]
      );
      await mappingReportPage.selectFilterValue(
        navigatorData.mappingReport_PogType,
        navigatorData.mappingReport_Display_Space
      );
      let storeTypeColumnList = await mappingReportPage.getAllColumnValues(
        navigatorData.mappingReport_Store_Type
      );
      await navigatorPage.waitBrowserToLoadPage(3000);
      storeTypeColumnList = await navigatorPage.getUniqueArrayValue(
        storeTypeColumnList
      );
      await expect(storeTypeColumnList.toString()).toEqual(
        await filteredStoreTypeUi[index]
      );
      await mappingReportPage.clearFilterButton("store-type");
      await navigatorPage.waitBrowserToLoadPage(2000);
      await mappingReportPage.selectFilterValue(
        navigatorData.mappingReport_PogType,
        "pog-type",
        navigatorData.mappingReport_Display_Space
      );
    }
    await expect(
      await await mappingReportPage.storeTypeDropdownText.getText()
    ).toEqual(navigatorData.mappingReport_Select_Store_Type);
  });

  it("validateFilteringStoreTypeColumnWithPogTypeDropdown: validate filtering StoreType column values with FloorPlanDBKey when PogType dropdown is selected", async () => {
    await navigatorPage.selectMultipleDropdowns([
      {
        id: navigatorData.storeDropdown,
        values: navigatorData.store_014_00335,
      },
    ]);
    await navigatorPage.goTo("Mapping Report");
    await navigatorPage.waitForPageLoad(
      await mappingReportPage.filtersBtn,
      navigatorData.mappingReport_Filters
    );
    await navigatorPage.clickOn(await mappingReportPage.filtersBtn);
    await mappingReportPage.selectFilterValue(
      navigatorData.mappingReport_StoreType,
      "store-type",
      navigatorData.mappingReport_Food_Store
    );
    await navigatorPage.waitBrowserToLoadPage(3000);
    let storeTypeTableUi = await mappingReportPage.getAllColumnValues(
      navigatorData.mappingReport_Store_Type
    );
    await mappingReportPage.selectFilterValue(
      navigatorData.mappingReport_PogType,
      "pog-type",
      navigatorData.mappingReport_Basic
    );
    await navigatorPage.waitBrowserToLoadPage(2000);
    let storeTypeFilterByPogTypeUi = await mappingReportPage.getAllColumnValues(
      navigatorData.mappingReport_Store_Type
    );
    await assert.isAtLeast(
      await (storeTypeTableUi.length - storeTypeFilterByPogTypeUi.length),
      navigatorData.integer_1,
      "PogType filter did not work"
    );
  });
});
