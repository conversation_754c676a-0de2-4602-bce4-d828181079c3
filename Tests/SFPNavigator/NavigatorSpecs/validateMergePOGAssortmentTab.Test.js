const navigatorPage = require("../NavigatorPO/navigator.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const { readExcelFile } = require("../../../util/excelReader");
const qrString = require("../../../SQLConnection/queryString");
const result = require("../../../SQLConnection/server.js.ts");
const { DBNames } = require("../../../envConfig.js");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
const excelReader = require("../../../util/excelReader");
const assert = require("chai").assert;

let pogKey;
beforeEach(async () => {
  await navigatorPage.open("navigator");

  pogKey = (
    await (
      await await result.getResult(
        dynamicQuery.pogKeyForItemByPog,
        DBNames.SFP_STAGE
      )
    ).flat()
  ).toString();
});

describe("validateMergePOGAssortmentTab: Validate Item by POG tab for merging Planogram Assortment tab changes", () => {
  it("Validate Item by POG tab is enabled on UPC", async () => {
    let upc = await (
      await (
        await await result.getResult(
          dynamicQuery.upcForMergedPogAssort,
          DBNames.SFP_STAGE
        )
      ).flat()
    ).toString();
    await navigatorPage.searchByValue("UPC", await upc);
    await navigatorPage.validateTab("Item by POG");
  });

  it("Validate Item by POG tab is enabled on POG Key", async () => {
    await navigatorPage.searchByValue("POG Key", pogKey);
    await navigatorPage.validateTab("Item by POG");
  });

  it("Validate New columns added in Item by POG tab", async () => {
    await navigatorPage.searchByValue("POG Key", pogKey);
    await navigatorPage.goTo("Item by POG");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.waitForTableExist(await navigatorPage.tableValue);
    let headers = await navigatorPage.getTableHeaderNames(
      await navigatorPage.tableHeaders
    );
    const newHeaders = ["FW", "FH", "C", "SRP"];
    const containsAll = newHeaders.every((element) => {
      return headers.includes(element);
    });
    await expect(containsAll).toEqual(true);
  });

  it("Validate DB to Excel data on Item by POG tab", async () => {
    await navigatorPage.searchByValue("POG Key", pogKey);
    await navigatorPage.goTo("Item by POG");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.exportToExcel();
    let excelData = await readExcelFile();
    let excelDataString = await navigatorPage.convertToStrings(excelData);
    let excelDataStringTreated = await navigatorPage.convertNullToZero(
      excelDataString
    );
    let dbData = await await result.getResult(
      qrString.ItemByPOGQuery_POGDBKey.replace(/[?]/g, pogKey),
      DBNames.SFP_STAGE
    );
    let dbDataString = await navigatorPage.convertToStrings(dbData);
    let dbDataStringTreated = await navigatorPage.convertNullToZero(
      dbDataString
    );
    if ((await dbData.length) > 0) {
      let dataCompare = await excelReader.dataCompare(
        await excelDataStringTreated,
        dbDataStringTreated,
        dbData.length
      );
      await navigatorPage.waitBrowserToLoadPage(3000);
      await expect(dataCompare).toEqual(true);
    } else {
      await assert.fail(
        0,
        1,
        "Error: Db did not print any data; update the pogKey !"
      );
    }
  });

  it("should validate dropdown page numbers against column number on Item by POG", async () => {
    await navigatorPage.selectMultipleDropdowns([
      {
        id: navigatorData.divisionDropdown,
        values: navigatorData.div_016_Columbus_Division,
      },
      {
        id: navigatorData.storeDropdown,
        values: navigatorData.store_016_00506,
      },
      {
        id: navigatorData.planogramDropdown,
        values: navigatorData.planogramStatusLive,
      },
      {
        id: navigatorData.floorplanDropdown,
        values: navigatorData.floorPlanStatusLive,
      },
    ]);
    await navigatorPage.goTo("Mapping Report");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.waitForPageLoad(
      await navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
    await navigatorPage.getLinkText("Space Station Name");
    await navigatorPage.waitBrowserToLoadPage(3000);
    let pageNumbers = await navigatorPage.getPageNumberList();
    for (let index = 0; index < pageNumbers.length; index++) {
      await navigatorPage.selectPageNumber(await pageNumbers[index]);
      await navigatorPage.waitBrowserToLoadPage(2000);
      await navigatorPage.btnPageOne.waitForClickable(
        { timeout: 59000 },
        "Error: Page one did not appear before timeout"
      );
      let rowCount = await navigatorPage.upcColumnList.length;
      await assert.isAtLeast(
        parseInt(pageNumbers[index]),
        parseInt(rowCount),
        "PageNumber index value should be equal or bigger than table row number"
      );
    }
  });
});
