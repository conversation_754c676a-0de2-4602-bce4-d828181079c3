const navigatorPage = require('../NavigatorPO/navigator.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');

describe('validatePageFooter: Validate page footer numbers are correctly displaying when page number 15, 30 and 60 are selected until page 4', () => {

    beforeEach(async ()=>{
        await navigatorPage.waitBrowserToLoadPage(6000);
        await navigatorPage.open('navigator');
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_014_Cincinnati_Division);
        await navigatorPage.selectMultipleValues('Stores', 'store', navigatorData.storeList_MappingReport);
    })

    it('Validate page footer numbers in FloorPlans tab by paginating', async () => {
        await navigatorPage.goTo('Floorplans');
    })

    it('Validate page footer numbers in MappingReport by paginating', async () => {
        await navigatorPage.goTo('Mapping Report');
    })

    afterEach(async ()=>{
        await navigatorPage.waitBrowserToLoadPage(3000)
        let pageNumbers = await navigatorPage.getPageNumberList();
        for (let i = 0; i < pageNumbers.length; i++) {
            await navigatorPage.selectPageNumber(pageNumbers[i])
            await navigatorPage.waitBrowserToLoadPage(3000)
            await navigatorPage.validateFooterEntries(pageNumbers[i], '3');
        }

    })

})

