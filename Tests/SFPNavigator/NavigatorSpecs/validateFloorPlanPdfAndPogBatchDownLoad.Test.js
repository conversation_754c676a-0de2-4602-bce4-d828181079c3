const navigatorPage = require("../NavigatorPO/navigator.page");
const floorPlanPage = require("../NavigatorPO/floorPlan.page");
const mappingReportPage = require("../NavigatorPO/mappingReport.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const {
  getFilesCount,
  extractFile,
  getExtractedFilesCount,
} = require("../../../util/fileExtractor");
const { clickOn } = require("../../../GlobalObjectRepository/login.page");
const { assert } = require("chai");

let countSelectedCheckBox, lengthOfTableRow;
let requiredRowNum = 5;

describe("validateFloorPlanPdfAndPogBatchDownLoad: Validate Batch download of PDF and Planograms files on floorPlans tab - @Need to run this test individually in local ", () => {
  const division = navigatorData.div_014_Cincinnati_Division;
  const store = navigatorData.store_014_00429;
  before(async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
    ]);
    await navigatorPage.goTo("Floorplans");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await navigatorPage.waitForPageLoad(
      await navigatorPage.btnExportToExcel,
      navigatorData.Export_To_Excel_text
    );
    lengthOfTableRow = await (await navigatorPage.divColumnList).length;
  });

  it("Validate Batch download of PDF and planograms files on Floorplans Tab when one, three and five rows are selected", async () => {
    if (lengthOfTableRow < requiredRowNum) {
      await assert.fail(
        0,
        1,
        "Row numbers must be bigger than 5! Update the input data !"
      );
    } else {
      let numOfSelectedCheckBoxes = [
        lengthOfTableRow - 1,
        lengthOfTableRow - 3,
        lengthOfTableRow - 5,
      ];
      let checkBoxColumnList = [
        navigatorData.FP_Layout_text,
        navigatorData.planogram_list_text,
      ];
      for (
        let checkBoxColumnIndex = 0;
        checkBoxColumnIndex < checkBoxColumnList.length;
        checkBoxColumnIndex++
      ) {
        let numOfElClickable = await navigatorPage.sumOfElementsClickable(
          checkBoxColumnList[checkBoxColumnIndex],
          5
        );
        assert.equal(
          requiredRowNum,
          numOfElClickable,
          "Top 5 PDF and Planogram values are not clickable! Update input data!"
        );
      }
      for (
        let checkBoxIndex = 0;
        checkBoxIndex < numOfSelectedCheckBoxes.length;
        checkBoxIndex++
      ) {
        let checkBoxColumns = [
          navigatorData.floorplancheck_text,
          navigatorData.planogramcheck_text,
        ];
        for (
          let columnIndex = 0;
          columnIndex < checkBoxColumns.length;
          columnIndex++
        ) {
          await navigatorPage.clickColumnCheckBox(
            await mappingReportPage.mappingReportTableValue,
            checkBoxColumns[columnIndex]
          );
          let initialFilesCount = getFilesCount();
          await navigatorPage.clickRowCheckBox(
            await floorPlanPage.floorplansTable,
            numOfSelectedCheckBoxes[checkBoxIndex]
          );
          countSelectedCheckBox = await navigatorPage.returnNumOfCheckBox(
            await floorPlanPage.floorplansTable
          );
          await navigatorPage.batchDownload();
          // Wait for files to download

          const downloadComplete = await navigatorPage.waitForDownload(
            initialFilesCount
          );
          if (downloadComplete) {
            const finalFilesCount = getFilesCount();
            expect(finalFilesCount - initialFilesCount).toEqual(1);

            await extractFile();
            await navigatorPage.waitBrowserToLoadPage(2000);
          } else {
            throw new Error("PDF didn't get to download!");
          }
        }
        const extractedFileCount = await getExtractedFilesCount();
        await expect(await extractedFileCount).toEqual(
          (await countSelectedCheckBox) + (await countSelectedCheckBox)
        );
      }
    }
  });

  it("Validate Batch Download of PDF and planograms files on Floorplans Tab when all CheckBox column values are selected", async () => {
    requiredRowNum = lengthOfTableRow;
    let numOfSelectedCheckBoxes = [lengthOfTableRow];
    let checkBoxColumnList = [
      navigatorData.FP_Layout_text,
      navigatorData.planogram_list_text,
    ];
    for (
      let checkBoxColumnIndex = 0;
      checkBoxColumnIndex < checkBoxColumnList.length;
      checkBoxColumnIndex++
    ) {
      let numOfElClickable = await navigatorPage.sumOfElementsClickable(
        checkBoxColumnList[checkBoxColumnIndex],
        numOfSelectedCheckBoxes
      );
      assert.equal(
        requiredRowNum,
        numOfElClickable,
        "PDF and Planogram values are not clickable! Update input data!"
      );
    }
    for (
      let selectedBoxIndex = 0;
      selectedBoxIndex < numOfSelectedCheckBoxes.length;
      selectedBoxIndex++
    ) {
      let checkBoxColumns = [
        navigatorData.floorplancheck_text,
        navigatorData.planogramcheck_text,
      ];
      for (
        let checkBoxIndex = 0;
        checkBoxIndex < checkBoxColumns.length;
        checkBoxIndex++
      ) {
        await clickOn(await navigatorPage.checkBoxHeaderColumn);
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.clickElement(
          await floorPlanPage.floorplansTable,
          checkBoxColumns[checkBoxIndex]
        );
        let initialFilesCount = getFilesCount();
        await navigatorPage.clickRowCheckBox(
          await floorPlanPage.floorplansTable,
          numOfSelectedCheckBoxes[selectedBoxIndex]
        );
        countSelectedCheckBox = await navigatorPage.returnNumOfCheckBox(
          await floorPlanPage.floorplansTable
        );
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.batchDownload();
        const downloadComplete = await navigatorPage.waitForDownload(
          initialFilesCount
        );
        if (downloadComplete) {
          const finalFilesCount = getFilesCount();
          expect(finalFilesCount - initialFilesCount).toEqual(1);
          await extractFile();
          await navigatorPage.waitBrowserToLoadPage(2000);
        } else {
          throw new Error("PDF didn't get to download!");
        }
      }
      const extractedFileCount = await getExtractedFilesCount();
      await expect(await extractedFileCount).toEqual(
        (await getExtractedFilesCount()) === countSelectedCheckBox
          ? countSelectedCheckBox
          : countSelectedCheckBox + countSelectedCheckBox
      );
    }
  });
});
