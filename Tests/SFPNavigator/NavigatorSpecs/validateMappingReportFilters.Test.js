const navigatorPage = require("../NavigatorPO/navigator.page");
const mappingReportPage = require("../NavigatorPO/mappingReport.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const qrString = require("../../../SQLConnection/queryString");
const result = require("../../../SQLConnection/server.js.ts");
const underscorelib = require("underscore");
const { DBNames } = require("../../../envConfig.js");

let divNum = navigatorData.div_018_Michigan_Division.match(/(\d[\d]*)/g)[0];
let storeNum = navigatorData.store_018_00074.match(/(\d[\d]*)/g)[1];
let allFpDeparts;
describe("validateMappingReportFilters: Validate data on Mapping Report tab", () => {
    const division = navigatorData.div_018_Michigan_Division
    const store = navigatorData.store_018_00074
  beforeEach(async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
    ]);
    await navigatorPage.goTo("Mapping Report");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await mappingReportPage.clickOn(await mappingReportPage.filtersBtn);
  });

  it("should validate the UI data based on Select POG type dropdown selection", async () => {
    let pogValuesUi = await mappingReportPage.getFilterDropdownList(
      "PogType",
      "pog-type"
    );
    let filteredPogValuesUi = pogValuesUi.filter(
      (item) => !item.includes("Select All")
    );
    let pogValuesDb = await result.getResult(
      qrString.POGTypeQuery.replace("@Div", divNum).replace("@Store", storeNum),
      DBNames.SFP_STAGE
    );
    await expect(
      await underscorelib.isEqual(
        filteredPogValuesUi.sort(),
        pogValuesDb.flat().sort()
      )
    ).toBe(true);
    for (let index = 0; index < filteredPogValuesUi.length; index++) {
      await mappingReportPage.selectFilterValue(
        "PogType",
        "pog-type",
        filteredPogValuesUi[index]
      );
      await navigatorPage.waitBrowserToLoadPage(6000);
      let allPogTypes = await mappingReportPage.getAllColumnValues("POG Type");
      await Promise.all(
        allPogTypes.map((item) => {
          expect(item).toEqual(filteredPogValuesUi[index]);
        })
      );
      await navigatorPage.waitBrowserToLoadPage(2000);
      await mappingReportPage.clearFilterButton("pog-type");
    }
  });

  it("Select FP Department (dropdown):  compare dropdown options to UI Dept column data", async () => {
    let fpValuesUi = await mappingReportPage.getFilterDropdownList(
      "FloorplanDepartment",
      "fp-department"
    );
    let filteredFpValuesUi = fpValuesUi.filter(
      (item) => !item.includes("Select All")
    );
    let fpValuesDb = await result.getResult(
      qrString.FpDepartmentMR.replace("@Div", divNum).replace(
        "@Store",
        storeNum
      ),
      DBNames.SFP_STAGE
    );
    let fpValuesDbArray = fpValuesDb.flat();
    await expect(
      await underscorelib.isEqual(filteredFpValuesUi, fpValuesDbArray)
    ).toBe(true);
    for (let index = 0; index < filteredFpValuesUi.length; index++) {
      await mappingReportPage.selectFilterValue(
        "FloorplanDepartment",
        "fp-department",
        filteredFpValuesUi[index]
      );
      await navigatorPage.waitBrowserToLoadPage(6000);
      allFpDeparts = await mappingReportPage.getAllColumnValues("Dept");
      await Promise.all(
        allFpDeparts.map((item) => {
          navigatorPage.waitBrowserToLoadPage(2000);
          expect(item).toEqual(filteredFpValuesUi[index]);
        })
      );
      await navigatorPage.waitBrowserToLoadPage(2000);
      await mappingReportPage.clearFilterButton("fp-department");
      await navigatorPage.waitBrowserToLoadPage(6000);
      await expect(
        await mappingReportPage.selectFPDeptDropDown.getText()
      ).toEqual(navigatorData.SelectFPDepartment);
    }
  });
});
