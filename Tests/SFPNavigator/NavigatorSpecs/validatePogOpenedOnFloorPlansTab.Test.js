const navigatorPage = require('../NavigatorPO/navigator.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const planogramsPage = require('../NavigatorPO/planograms.page');

describe('validatePogOpenedOnFloorPlansTab: Validate PDF opened in new tab for POG Tab', () => {
    const division = navigatorData.div_014_Cincinnati_Division
    const store = navigatorData.store_014_00335
    it('Validate user is able to open Planogram in new tab @UI', async () => {
        await navigatorPage.open('navigator');
        await navigatorPage.selectMultipleDropdowns([
            { id: navigatorData.divisionDropdown, values: division },
            { id: navigatorData.storeDropdown, values: store },  
        ]);                                                        
        await navigatorPage.goTo('Floorplans');
        await navigatorPage.waitBrowserToLoadPage(5000)
        let parentGuId = await browser.getWindowHandle()                                                        
        await navigatorPage.getLinkText('Planogram List')                                                                          
        await navigatorPage.waitBrowserToLoadPage(5000)                                                                    
        await planogramsPage.validateNewTab(parentGuId)
        await expect(browser).toHaveTitleContaining('Planograms')
    })
})
