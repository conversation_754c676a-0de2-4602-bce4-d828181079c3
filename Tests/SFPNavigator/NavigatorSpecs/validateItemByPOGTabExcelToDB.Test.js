import {excelDataCompareAllRow} from '../../../util/excelReader';
import result from "../../../SQLConnection/server.js.ts";
import {DBNames} from "../../../envConfig.js";
import {assert} from "chai";
const navigatorPage = require('../NavigatorPO/navigator.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const qrString = require('../../../SQLConnection/queryString');
const readExcel = require("../../../util/excelReader");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");

let upcList,upcListFirstValue,pogKey ;
describe('validateItemByPOGTabExcelToDB: Download exel file, read contents and compare with DB data', () => {
    const division = navigatorData.div_016_Columbus_Division
    beforeEach(async () =>{
        await navigatorPage.open("navigator");
        upcList = await (await (await (await result.getResult(dynamicQuery.upcListForItemByPog, DBNames.SFP_STAGE))).flat());
        upcListFirstValue = await upcList[0].toString();
        await navigatorPage.waitBrowserToLoadPage(3000);
    })

    it('Item by POG Tab: Validate the downloaded excel file and compare the data with DB for one row', async () => {
        await navigatorPage.selectMultipleDropdowns([
              { id: navigatorData.divisionDropdown, values: division }
        ]);
        await navigatorPage.searchByValue('UPC', await upcListFirstValue);
        await navigatorPage.goTo('Item by POG');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.waitForPageLoad(await navigatorPage.btnExportToExcel, navigatorData.Export_To_Excel_text);
        await(await  navigatorPage.btnExportToExcel).waitForClickable();
        await navigatorPage.exportToExcel();
        await navigatorPage.waitForPageLoad(await navigatorPage.btnExportToExcel, navigatorData.Export_To_Excel_text);
        await navigatorPage.waitBrowserToLoadPage(6000);
        let dataCompare = await excelDataCompareAllRow(qrString.ItemByPOGQuery.replace(/[?]/g, await upcListFirstValue), 1, DBNames.SFP_STAGE);
        await expect(dataCompare).toEqual(true);
    })

    it('Item by POG Tab: Validate the downloaded excel file and compare the data with DB for all rows', async () => {

        await navigatorPage.searchByValue('UPC', await upcListFirstValue);
        await navigatorPage.goTo('Item by POG');
        await navigatorPage.waitBrowserToLoadPage(3000)
        await navigatorPage.waitForPageLoad(await navigatorPage.btnExportToExcel, navigatorData.Export_To_Excel_text);
        await(await  navigatorPage.btnExportToExcel).waitForClickable();
        await navigatorPage.exportToExcel();
        let dataCompare = await excelDataCompareAllRow(await qrString.ItemByPOGQuery.replace(/[?]/g, await upcListFirstValue), DBNames.SFP_STAGE);
        await expect(dataCompare).toEqual(true);
    })

    it('Item by POG Tab: Validate the downloaded excel file and compare the data with DB for multiple UPCs for all rows', async () => {

        await navigatorPage.searchByValue('UPC',await upcList.join('\n'));
        await navigatorPage.goTo('Item by POG');
        await navigatorPage.waitBrowserToLoadPage(3000)
        await navigatorPage.waitForPageLoad(await navigatorPage.btnExportToExcel, navigatorData.Export_To_Excel_text);
        await(await  navigatorPage.btnExportToExcel).waitForClickable();
        await navigatorPage.exportToExcel();
        await navigatorPage.waitBrowserToLoadPage(6000);
        let dataCompare = await excelDataCompareAllRow(qrString.ItemByPOGQuery.replace(/[?]/g, await upcList.toString()), DBNames.SFP_STAGE);
        await expect(dataCompare).toEqual(true);
    })

    it('Item by POG Tab: Validate the downloaded excel file and compare the data with DB for multiple POG DBKeys', async () => {

       pogKey = await (await (await (await result.getResult(dynamicQuery.pogKeyForItemByPog, DBNames.SFP_STAGE))).flat()).toString();
        await navigatorPage.searchByValue('POG Key', await pogKey);
        await navigatorPage.goTo('Item by POG');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.waitForPageLoad(await navigatorPage.btnExportToExcel, navigatorData.Export_To_Excel_text);
        await navigatorPage.exportToExcel();
        let excelData = await readExcel.readExcelFile();
        let excelDataTreated = excelData.map(arr =>arr.map(val => val === null ? 'null': val));
        let dbResult = await result.getResult(qrString.ItemByPOGQuery_POGDBKey.replace(/[?]/g, await pogKey), DBNames.SFP_STAGE);
        if(await dbResult.length > 0){
            let dbResultTreated = dbResult.map(arr => arr.map(val => val === null ? 'null': val));
            let compareArrays = await readExcel.dataCompare(excelDataTreated.sort(), dbResultTreated.sort(), dbResultTreated.length);
            await expect(compareArrays).toEqual(true);
        } else{
            await assert.fail(0, 1, "Error: Table has no data !");
        }
    })

})
