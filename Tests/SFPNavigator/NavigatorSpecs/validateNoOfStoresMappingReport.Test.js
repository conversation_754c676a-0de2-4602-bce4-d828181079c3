const navigatorPage = require('../NavigatorPO/navigator.page');
const mappingReportPage = require('../NavigatorPO/mappingReport.page');
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const excelReader = require("../../../util/excelReader");
const qrString = require("../../../SQLConnection/queryString");
const result = require("../../../SQLConnection/server.js.ts");
const { DBNames } = require('../../../envConfig.js');
const {assert} = require("chai");

let div,store,divNumber,storeNumber;
describe('validateNoOfStoresMappingReport: Validate No of Stores link and data on Mapping Report tab', () => {

    beforeEach(async ()=>{
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
         div = navigatorData.div_014_Cincinnati_Division;
         divNumber = div.match(/(\d[\d]*)/g)[0];
         store=navigatorData.store_014_00353;
         storeNumber = store.split('- ')[1];
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',  await div);
        await navigatorPage.selectSingleValue('Stores', 'store', await store);
        // eslint-disable-next-line
        //Note:  un-comment following lines to run in STAGE and un-comment Query as well with LIVE table.
        // await navigatorPage.selectSingleValue('PlanogramStatus', 'pog-status',  navigatorData.planogramStatusLive);
        // await navigatorPage.selectSingleValue('FloorplanStatus', 'floorplans', navigatorData.floorPlanStatusLive);
        await navigatorPage.waitBrowserToLoadPage(3000);
    })

    it('Validate No of Stores link and compare db data to exel data on Mapping Report tab for multiple stores', async ()=> {

        // Note: need to update query
        // let pogKey = await (await result.getResult(qrString.getMappingReportPOGKey.replace('@div',  divNumber).replace('@store', storeNumber), DBNames.SFP_STAGE)).flat();
        let pogKey = '7073048';
        await navigatorPage.searchByValue('POG Key', await pogKey);
        await navigatorPage.goTo('Mapping Report');
        await navigatorPage.waitForPageLoad(await navigatorPage.btnExportToExcel, navigatorData.Export_To_Excel_text);
        await navigatorPage.getLinkText(navigatorData.col_noOfStores);
        await mappingReportPage.selectedPOGKeyString.waitForDisplayed({timeout:8000});
        let selectedPOGKey = (await mappingReportPage.selectedPOGKeyString.getText()).match(/(\d[\d]*)/g)[0];
        await expect (selectedPOGKey).toEqual(pogKey.toString());
        await expect (selectedPOGKey).toEqual(pogKey.toString());
        await navigatorPage.waitForPageLoad(navigatorPage.btnExportToExcel, navigatorData.Export_To_Excel_text);
        await navigatorPage.exportToExcel();
        await navigatorPage.waitBrowserToLoadPage(3000);
        let excelData = await excelReader.readExcelFile();
        let dbData = await (await result.getResult(qrString.mappingReportNoOfStores.replace('@pogKey', await pogKey.toString()), DBNames.SFP_STAGE) ) ;
        await navigatorPage.waitBrowserToLoadPage(3000);
        if(dbData !== null) {
        await expect(await excelData).toEqual(dbData);
        } else {
            await assert.fail(0, 1, "Error: Db did not print any data !");
        }
    })

    it('Validate CLear filter on No of Stores link on Mapping Report tab ', async ()=> {

        await navigatorPage.waitBrowserToLoadPage(2000);
        let pogKey= await (await result.getResult(qrString.getMappingReportPOGKey.replace('@div', divNumber).replace('@store',  storeNumber), DBNames.SFP_STAGE)).flat();
        await navigatorPage.searchByValue('POG Key',pogKey);
        await navigatorPage.goTo('Mapping Report');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.waitForPageLoad(await navigatorPage.btnExportToExcel, navigatorData.Export_To_Excel_text);
        await navigatorPage.waitBrowserToLoadPage(6000);
        await navigatorPage.getLinkText(navigatorData.col_noOfStores);
        await mappingReportPage.selectedPOGKeyString.waitForDisplayed({timeout:8000});
        let selectedPogKey = (await mappingReportPage.selectedPOGKeyString.getText()).match(/(\d[\d]*)/g)[0];
        await expect (selectedPogKey).toEqual(pogKey.toString());
        await mappingReportPage.clickOn(await mappingReportPage.filtersBtn);
        await navigatorPage.waitBrowserToLoadPage(6000);
        await navigatorPage.exportToExcel();
        let data = await excelReader.excelDataCompareAllRow(qrString.mappingReportQuery.replace("@Div",divNumber).replace("@Store",storeNumber).replace('@pogKey',pogKey.toString()),DBNames.SFP_STAGE);
        await expect(data).toEqual(true);
    })

    it('Validate FP Department Dropdown on No of Stores link on Mapping Report tab ', async ()=> {
        let pogKey= await (await result.getResult(qrString.getMappingReportPOGKey.replace('@div', divNumber).replace('@store', storeNumber), DBNames.SFP_STAGE)).flat();
        await navigatorPage.searchByValue('POG Key',pogKey);
        await navigatorPage.goTo('Mapping Report');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.waitForPageLoad(navigatorPage.btnExportToExcel, navigatorData.Export_To_Excel_text);
        await navigatorPage.getLinkText(navigatorData.col_noOfStores);
        await mappingReportPage.selectedPOGKeyString.waitForDisplayed({timeout:8000});
        let selectedPogKey = (await mappingReportPage.getTextOfElement(await mappingReportPage.selectedPOGKeyString)).match(/(\d[\d]*)/g)[0];
        await expect (selectedPogKey).toEqual(pogKey.toString());
        await mappingReportPage.clickOn(await mappingReportPage.filtersBtn);
        let fpValuesUi = await mappingReportPage.getFilterDropdownList('FloorplanDepartment','fp-department');
        let fpValuesDb = await ( await result.getResult( qrString.FPDeptMRStores.replace('@div', divNumber).replace('@pogKey', pogKey.toString()), DBNames.SFP_STAGE) ).flat();
        let fpValuesDbUnique = await mappingReportPage.getUniqueArrayValue(fpValuesDb);
        await expect(fpValuesUi.filter(val => val !== 'Select All')).toEqual(fpValuesDbUnique);
    })

    it('Validate POG Type Dropdown on No of Stores link on Mapping Report tab ', async ()=> {
        await navigatorPage.waitBrowserToLoadPage(2000);
        let pogKey= await (await result.getResult(qrString.getMappingReportPOGKey.replace('@div', divNumber).replace('@store', storeNumber), DBNames.SFP_STAGE)).flat();
        await navigatorPage.searchByValue('POG Key',pogKey);
        await navigatorPage.goTo('Mapping Report');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.getLinkText(navigatorData.col_noOfStores);
        await mappingReportPage.selectedPOGKeyString.waitForDisplayed({timeout:8000});
        let selectedPogKey = (await mappingReportPage.getTextOfElement(await mappingReportPage.selectedPOGKeyString)).match(/(\d[\d]*)/g)[0];
        await expect (selectedPogKey).toEqual(pogKey.toString());
        await mappingReportPage.clickOn(await mappingReportPage.filtersBtn);
        let pogValuesUi = await mappingReportPage.getFilterDropdownList('PogType', 'pog-type');
        let pogValuesDb = await (await result.getResult(qrString.POGTypeMRStores.replace('@div', divNumber).replace('@store', storeNumber).replace('@pogKey',pogKey.toString()), DBNames.SFP_STAGE)).flat();
        let pogValuesDbUnique = await mappingReportPage.getUniqueArrayValue(pogValuesDb);
        await expect(pogValuesUi.filter(val => val !== 'Select All')).toEqual( pogValuesDbUnique);
    })

})
