const navigatorPage = require('../../NavigatorPO/navigator.page');
const navigatorData = require('../../../../TestData/SFPNavigatorData/navigatorUIData.json');
const qrString = require('../../../../SQLConnection/queryString')
const {DBNames} = require("../../../../envConfig.js");
const excelReader = require("../../../../util/excelReader");
const mappingReportPage = require("../../NavigatorPO/mappingReport.page");
const {assert} = require("chai");
let divNumber, storeNumber, pogStatusDatabaseMappedValue;

describe('e2eValidateCompositeFlowToMappingExcel: Validate composite tab link # of Planograms link goes to Mapping Tab and validate Excel to DB', () => {

        beforeEach(async ()=>{

            await navigatorPage.waitBrowserToLoadPage(3000);
            await navigatorPage.open('navigator');
            await navigatorPage.waitBrowserToLoadPage(5000);
        })

        it('Validate multiple divisions and stores with composite tab # of Planograms link goes to Mapping Tab and validate Excel to DB ', async () => {

            divNumber = navigatorData.divisionList_011T014;
            storeNumber = navigatorData.dbkey_stores_011_00002_014_00335;
            pogStatusDatabaseMappedValue = navigatorData.db_POG_Status_Default;
            await navigatorPage.selectMultipleValues("Division", 'divisionMultiSelect', await navigatorData.divisionListCompositeMapping);
            await navigatorPage.selectMultipleValues('Stores', 'store', await navigatorData.storeListStoreTabCompositeMapping);
            await navigatorPage.waitBrowserToLoadPage(3000);
        })

        it('Validate composite tab # of Planograms link goes to Mapping Tab and validate Excel to DB. Validates page footer label ', async () => {

            divNumber = navigatorData.div_018_Michigan_Division.match(/(\d[\d]*)/g)[0];
            storeNumber = navigatorData.dbkey_store_018_00074;
            pogStatusDatabaseMappedValue = navigatorData.db_POG_Status_Default;
            await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',  navigatorData.div_018_Michigan_Division);
            await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_018_00074);
            await navigatorPage.waitBrowserToLoadPage(3000);
        })

        it('Validate composite tab with POG pending status - link goes to Mapping Tab and validate empty set ', async () => {

            divNumber = navigatorData.div_018_Michigan_Division.match(/(\d[\d]*)/g)[0];
            storeNumber = navigatorData.dbkey_store_018_00074;
            pogStatusDatabaseMappedValue = navigatorData.db_POG_Status_Pending;
            await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',  navigatorData.div_018_Michigan_Division);
            await navigatorPage.selectSingleValue("Stores", 'store', navigatorData.store_018_00074);
            await navigatorPage.selectSingleValue('PlanogramStatus', 'pog-status', navigatorData.planogramStatusPending);
            await navigatorPage.waitBrowserToLoadPage(3000);
        })


        afterEach(async ()=>{

            await navigatorPage.goTo('Composite');
            await navigatorPage.waitBrowserToLoadPage(5000);
            await navigatorPage.getLinkText(navigatorData.num_Of_Planograms);
            await navigatorPage.waitForPageLoad(await mappingReportPage.filtersBtn, navigatorData.mappingReport_Filters);
            await(await  navigatorPage.btnExportToExcel).waitForClickable();
            if(await pogStatusDatabaseMappedValue !== navigatorData.db_POG_Status_Pending) {
                await navigatorPage.exportToExcel();
                await navigatorPage.waitBrowserToLoadPage(5000);
            }
            let databaseExcelCompareResults;
            if(await pogStatusDatabaseMappedValue === navigatorData.db_POG_Status_Pending || await pogStatusDatabaseMappedValue === navigatorData.db_POG_Status_Default) {
                let tableText = await navigatorPage.GetText(await navigatorPage.tableFirstRowData)
                if(tableText === navigatorData.no_records_found){
                    await expect(await tableText).toEqual(navigatorData.no_records_found);
                } else{
                    await navigatorPage.exportToExcel();
                    await navigatorPage.waitBrowserToLoadPage(5000);
                    databaseExcelCompareResults = await excelReader.excelDataCompareAllRow(qrString.mappingReportMultipleStoreDivQuery.replace('@div', divNumber).replace('@Store', storeNumber), DBNames.SFP_STAGE);
                    await expect(databaseExcelCompareResults).toEqual(true);
                }
            } else {
                await assert.fail(0, 1, "Error: Compare Database query result to Excel did not contain any data !");
            }
            if(await storeNumber === navigatorData.dbkey_store_018_00074 && pogStatusDatabaseMappedValue !== navigatorData.db_POG_Status_Pending){
                await expect(await navigatorPage.footerLabelValidation()).toEqual(true);
            }
        })

})

