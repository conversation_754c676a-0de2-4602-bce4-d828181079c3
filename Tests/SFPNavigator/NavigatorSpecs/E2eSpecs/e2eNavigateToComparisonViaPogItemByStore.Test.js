const navigatorPage = require('../../NavigatorPO/navigator.page');
const navigatorData = require("../../../../TestData/SFPNavigatorData/navigatorUIData.json");
const comparisonReport = require("../../NavigatorPO/comparisonReport.page");

describe('e2eNavigateToComparisonViaPogItemByStore: Validate all tables are visible when navigating to ComparisonReport through PogItemByStore tab', () => {

    it('Validate ils, ilp and sfp tables are visible', async () => {

        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_014_Cincinnati_Division);
        await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_014_00335);
        await navigatorPage.goTo('Mapping Report');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await(await  navigatorPage.btnExportToExcel).waitForClickable();
        let pogKey = await navigatorPage.getLinkText(navigatorData.col_POGDBKey);
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.clickOn(await navigatorPage.navigatorTabBtn)
        await navigatorPage.searchByValue('POG Key', pogKey)
        await navigatorPage.goTo('POG & Item by Store');
        await(await  navigatorPage.btnExportToExcel).waitForClickable();
        let upcPogItemByStore = await navigatorPage.getLinkText('UPC');
        await comparisonReport.waitForPageLoad(await comparisonReport.exportExcelBtn, navigatorData.Export_To_Excel_text);
        let upcTable = await comparisonReport.getColumnArrayList('UPC');
       await expect(upcPogItemByStore).toEqual(upcTable.toString());
        let tableNames = navigatorData.comparisonReport_TableNames;
        for(let index=0; index<tableNames.length; index++){
            let tableExist = await comparisonReport.validateComparisonTablesVisible(tableNames[index])
            await navigatorPage.waitBrowserToLoadPage(2000);
            await expect(tableExist).toEqual(true);
        }

    })
})
