const navigatorData = require('../../../../TestData/SFPNavigatorData/navigatorUIData.json');
const navigatorPage = require('../../NavigatorPO/navigator.page');
const itemByPOGPage = require("../../NavigatorPO/itemByPOG.page");
const floorPlanPage = require("../../NavigatorPO/floorPlan.page");
const mappingPage = require('../../NavigatorPO/mappingReport.page');

let originalSSNValue;
describe('e2eValidatePOGTabLinkToItemByPogToMapping: validate three POG tabs links to ItemByPog and Mapping tabs - Links: SSN, Number of items, Store Count', () => {

    beforeEach(  async ()=> {

        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',  navigatorData.div_016_Columbus_Division);
        await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_016_00506);
        await navigatorPage.goTo('Mapping Report');
        await navigatorPage.waitBrowserToLoadPage(3000)
        originalSSNValue = await navigatorPage.getLinkText("Space Station Name");
        await navigatorPage.waitBrowserToLoadPage(2000)
        await navigatorPage.navigatorTabBtn.click();
        await navigatorPage.waitBrowserToLoadPage(2000)
        await navigatorPage.searchByValue('SSN', await originalSSNValue);
        await navigatorPage.goTo('POGs');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await(await  navigatorPage.btnExportToExcel).waitForClickable();
    })

    it('validate POG tabs link (SSN) goes to ItemByPog - validate SSN', async () => {

        await navigatorPage.getLinkText(navigatorData.space_station_name);
        await navigatorPage.waitBrowserToLoadPage(2000);
        await expect(await itemByPOGPage.moduleLink.getText()).toEqual(navigatorData.Item_By_POG);
        let dropDownIndex;
        for(dropDownIndex = 0; dropDownIndex < 3; dropDownIndex++) {
            await expect(await navigatorPage.matchPageRowCountWithDropDown(await dropDownIndex)).toEqual(true);
        }
    })

    it('validate POG tabs links (number of items) goes to ItemByPog - validate number of item ', async () => {

        let numberOfItems =  await navigatorPage.getLinkText(navigatorData.number_of_Items);
        await navigatorPage.waitBrowserToLoadPage(2000);
        await expect(await itemByPOGPage.moduleLink.getText()).toEqual(navigatorData.Item_By_POG);
        await expect(await numberOfItems).toEqual((await navigatorPage.GetText(await navigatorPage.footerEntryString)).split(" ")[5]);
        let ssnColumnList = await mappingPage.getAllColumnValues(navigatorData.space_station_name);
        await expect(Number(numberOfItems)).toEqual(await ssnColumnList.length);
    })

    it('validate POG tabs links (number of stores) goes to Mapping tabs - validate store count, SSN', async () => {

        let pogStoreCount = await navigatorPage.getLinkText(navigatorData.store_Count);
        let pogNumberOfItems  = await navigatorPage.getFirstRowColumnText(navigatorData.col_noOfItems);
        await navigatorPage.waitBrowserToLoadPage(2000);
        await expect(await navigatorPage.moduleLink.getText()).toEqual(navigatorData.mapping_Report);
        await expect(pogStoreCount).toEqual((await navigatorPage.GetText(await navigatorPage.footerEntryString)).split(" ")[5]);
        let uiMappingSSN = await navigatorPage.getColumnArrayListMultiplePages(await mappingPage.mappingReportTable, navigatorData.space_station_name, 1);
        await expect(await navigatorPage.allArrayElementsMatchValue(uiMappingSSN, originalSSNValue)).toEqual(true);
        let uiMappingNumOfStores = await navigatorPage.getColumnArrayListMultiplePages(await mappingPage.mappingReportTable, navigatorData.col_noOfStores, 4);
        await expect(await navigatorPage.allArrayElementsMatchValue(uiMappingNumOfStores, pogStoreCount)).toEqual(true);
        await expect(uiMappingNumOfStores.length).toEqual(Number(pogStoreCount));
        let uiMappingNumOfItems = await navigatorPage.getColumnArrayListMultiplePages(floorPlanPage.floorplansTable, navigatorData.col_noOfItems, 1);
        await expect(await navigatorPage.allArrayElementsMatchValue(uiMappingNumOfItems, pogNumberOfItems)).toEqual(true);
    })

})
