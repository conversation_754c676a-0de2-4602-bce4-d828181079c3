const navigatorPage = require('../../NavigatorPO/navigator.page');
const navigatorData = require('../../../../TestData/SFPNavigatorData/navigatorUIData.json');
const navigatorQueryData = require('../../../../TestData/SFPNavigatorData/navigatorQueryData.json');
const excelReader = require('../../../../util/excelReader');
const qrString = require('../../../../SQLConnection/queryString');
const dynamicQuery = require('../../../../SQLConnection/dynamicQueryNav');
const compositePage = require('../../NavigatorPO/composite.page');
const comparisonReport = require("../../NavigatorPO/comparisonReport.page");
const {DBNames} = require("../../../../envConfig.js");
const {excelDataCompareAllRow} = require("../../../../util/excelReader");
const {assert} = require("chai");
const result = require("../../../../SQLConnection/server.js.ts");


let divNumber, storeNumber, queryByUPC, upcNumber, pogKeyNumber, conditionPogKeyNumber, conditionUpcNumber;
describe('e2eValidateNumOfItemsOnCompositeTabClickable: Validate flow - Navigator > Composite > ItemByStore > Comparison tab', async () => {

    divNumber = navigatorData.div_014_Cincinnati_Division.match(/(\d[\d]*)/g)[0];
    storeNumber = navigatorData.store_014_00353.split('- ')[1];

    beforeEach(async () => {
        pogKeyNumber = await (await(await (await result.getResult(dynamicQuery.pogKeyForComposite, DBNames.SFP_STAGE))).flat()).toString();
        upcNumber = await (await (await (await result.getResult(dynamicQuery.upcForComposite, DBNames.SFP_STAGE))).flat()).toString();
        conditionPogKeyNumber = navigatorQueryData.conditionPogItemByStore_PogKey.replace("@pogDbKey", pogKeyNumber);
        conditionUpcNumber = navigatorQueryData.conditionPogItemByStore_UPC.replace("@upc", upcNumber);
        await navigatorPage.waitBrowserToLoadPage(3000);
    })

    it('Using PogKey - compositeTab is clickable and UPC from pogItemByStore matches with UPC on comparison report ', async () => {

        for (let testIndex = 0; testIndex < 2; testIndex++) {
            await navigatorPage.waitBrowserToLoadPage(3000);
            await navigatorPage.open('navigator');
            await navigatorPage.waitBrowserToLoadPage(3000);
            await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',  navigatorData.div_016_Columbus_Division);
            await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_016_00506);
            if (testIndex === 0) {
                await navigatorPage.searchByValue('POG Key', pogKeyNumber);
                queryByUPC = false;
            }
            if (testIndex === 1) {
                await navigatorPage.searchByValue("UPC", upcNumber);
                queryByUPC = true;
            }
            await navigatorPage.goTo("Composite");
            await navigatorPage.waitBrowserToLoadPage(3000);
            await navigatorPage.exportToExcel();
            await navigatorPage.waitBrowserToLoadPage(6000);
            await expect(await navigatorPage.elementIsClickable(compositePage.numOfItemsEntryLinkFirst)).toEqual(true);
            let uiComposite = await navigatorPage.getAllUIData();
            uiComposite = await navigatorPage.removeElementsFromArrayOfArrays(uiComposite, 'PDF', 'PSA')
            let uiCompareExcelComposite = await excelReader.excelUiDataCompareAllRows(uiComposite);
            await expect(uiCompareExcelComposite).toEqual(true);
            await navigatorPage.getLinkText(navigatorData.col_noOfItems);
            await navigatorPage.waitBrowserToLoadPage(6000);
            const pogItemByStoreBreadCrumb = await navigatorPage.breadCrumb.getText();
            await expect(pogItemByStoreBreadCrumb).toEqual('POG & Item by Store');
            await navigatorPage.exportToExcel();
            let dbCompareExcelPogItemByStore = await excelDataCompareAllRow(qrString.pogItemByStoreQuery.replace('@Div', divNumber).replace('@Store', storeNumber).replace('\'@Condition\'', queryByUPC ? conditionUpcNumber : conditionPogKeyNumber), DBNames.SFP_STAGE);
            await expect(dbCompareExcelPogItemByStore).toEqual(true);
            await navigatorPage.waitBrowserToLoadPage(3000);
            const numOfFooterEntries = await (await navigatorPage.GetText(await navigatorPage.footerEntryString)).split(" ")[5];
            const totalRowCount = await navigatorPage.getTotalRowCountNum();
            await expect(numOfFooterEntries).toEqual(totalRowCount.toString());
            let upcPOGItemByStore = await navigatorPage.getLinkText(navigatorData.col_UPC);
            if (await numOfFooterEntries.length > 0) {
                const comparisonReportBreadCrumb = await navigatorPage.breadCrumb.getText();
                await expect(comparisonReportBreadCrumb).toEqual('Comparison Report');
                await navigatorPage.waitForPageLoad(comparisonReport.exportExcelBtn, navigatorData.Export_To_Excel_text);
                await navigatorPage.elementIsClickable(comparisonReport.exportExcelBtn);
                let upcItemLocationComparisonView = await comparisonReport.getColumnArrayList('UPC');
                const upcItemLocationComparisonViewUnique = await comparisonReport.getUniqueArrayValue(upcItemLocationComparisonView);
                await expect(upcItemLocationComparisonViewUnique.toString()).toEqual(await upcPOGItemByStore);
                let tableNames = navigatorData.comparisonReport_TableNames;
                for (let index = 0; index < tableNames.length; index++) {
                    let tableExist = await comparisonReport.validateComparisonTablesVisible(tableNames[index])
                    await navigatorPage.waitBrowserToLoadPage(2000);
                    await expect(await tableExist).toEqual(true);
                }
            } else {
                await assert.fail(0, 1, "Error: PogItemByStore table is empty !");
            }
        }
    })

})
