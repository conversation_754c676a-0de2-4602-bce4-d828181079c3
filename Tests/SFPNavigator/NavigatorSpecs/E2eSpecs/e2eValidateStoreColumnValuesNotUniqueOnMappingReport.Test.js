const navigatorPage = require('../../NavigatorPO/navigator.page');
const navigatorData = require("../../../../TestData/SFPNavigatorData/navigatorUIData.json");
const mappingReportPage = require("../../NavigatorPO/mappingReport.page");
const result = require("../../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../../SQLConnection/dynamicQueryNav");
const {DBNames} = require("../../../../envConfig.js");
const chaiAssert = require('chai').assert;

let pogKeyNumber;
describe('e2eValidateStoreColumnValuesNotUniqueOnMappingReport: Validate Store column values on MappingReport tab are not unique', () => {

    beforeEach( async() => {
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_014_Cincinnati_Division);
        await navigatorPage.waitBrowserToLoadPage(3000);
        pogKeyNumber = await (await(await (await result.getResult(dynamicQuery.pogKeyMappingReport, DBNames.SFP_STAGE))).flat()).toString();
    })

    it('Validate MappingReport tab Store column values are not unique when single division and pogKeys are selected', async () => {

        await navigatorPage.searchByValue('POG Key', pogKeyNumber);
    })

    it('Validate MappingReport tab Store column values are not unique when Live status is selected from Planogram dropdown', async () => {

        await navigatorPage.selectSingleValue('PlanogramStatus', 'pog-status', navigatorData.planogramStatusLive);
    })

    it('Validate MappingReport tab Store column values are not unique when pogKey and Live status is selected from Floorplan dropdown', async () => {

        await navigatorPage.searchByValue('POG Key', pogKeyNumber);
        await navigatorPage.selectSingleValue('FloorplanStatus', 'floorplan', navigatorData.floorPlanStatusLive);
    })

    afterEach(async()=> {

        await navigatorPage.goTo('Mapping Report');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.waitForPageLoad(navigatorPage.btnExportToExcel, navigatorData.Export_To_Excel_text);
        await navigatorPage.waitBrowserToLoadPage(1000);
        let element = await mappingReportPage.elementExists(await mappingReportPage.emptyTableText)
        if(element === true){
            let noRecordsFoundText = await mappingReportPage.emptyTableText;
            await expect(noRecordsFoundText).toHaveText(navigatorData.no_records_found);
        } else {
            await navigatorPage.getLinkText(navigatorData.col_noOfStores);
            await navigatorPage.waitBrowserToLoadPage(2000);
            let storeColumnList = await navigatorPage.getColumnArrayList('Store');
            let storeColumnListUnique = await navigatorPage.getUniqueArrayValue(storeColumnList);
            if (await storeColumnListUnique.length >= 2) {
                await chaiAssert.notEqual(storeColumnListUnique.length, '1', 'StoreColumnList values are not unique');
            } else {
                await chaiAssert.fail(0, 1, 'Table must have more than 2 rows !');
            }
        }
    })

})
