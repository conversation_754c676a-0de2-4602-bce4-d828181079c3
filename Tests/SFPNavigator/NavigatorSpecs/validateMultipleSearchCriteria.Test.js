const navigatorPage = require("../NavigatorPO/navigator.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");

let pogKey;
describe("validateMultipleSearchCriteria: Validate Multiple Search Criteria on different tabs", () => {
  const division = navigatorData.select_3_divisions;
  const store = navigatorData.store_016_multiple;
  const department = navigatorData.departmentList;
  const subDepartment = navigatorData.subDepartmentList;
  const commodity = navigatorData.filterCommodityList;
  const commodityGroup = navigatorData.commodityGroupList;
  const planogram = navigatorData.planogramStatusLive;
  before(async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
      { id: navigatorData.departmentDropdown, values: department },
      { id: navigatorData.subDeptDropdown, values: subDepartment },
      { id: navigatorData.commodityDropdown, values: commodity },
      { id: navigatorData.commGroupDropdown, values: commodityGroup },
      { id: navigatorData.planogramDropdown, values: planogram },
    ]);
    await navigatorPage.goTo("Mapping Report");
    await navigatorPage.waitBrowserToLoadPage(3000);
    await (await navigatorPage.btnExportToExcel).waitForClickable();
    pogKey = await navigatorPage.getLinkText(navigatorData.col_POGDBKey);
    await navigatorPage.navigatorTabBtn.click();
    await navigatorPage.waitBrowserToLoadPage(3000);
  });

  it("Should validate search criteria on Store Planogram Item tab for Multiple data selection from Navigator page", async () => {
    await navigatorPage.searchByValue("POG Key", await pogKey);
    await navigatorPage.goTo(navigatorData.POG_Item_by_Store);
    await navigatorPage.waitBrowserToLoadPage(2000);
  });

  it("Should validate search criteria on Floorplan tab for Multiple data selection from Navigator page", async () => {
    await navigatorPage.goTo("Floorplans");
  });

  it("Should validate search criteria on Composite tab for Multiple data selection from Navigator page", async () => {
    await navigatorPage.goTo("Composite");
    await navigatorPage.waitBrowserToLoadPage(5000);
  });

  afterEach(async () => {
    await navigatorPage.waitBrowserToLoadPage(2000);
    await navigatorPage.searchCriteria();
    await navigatorPage.waitBrowserToLoadPage(2000);
    await navigatorPage.validateSearchCriteriaDropdownValue(
      "div",
      (await division.length) + " Items Selected"
    );
    await navigatorPage.validateSearchCriteriaDropdownValue(
      "store",
      (await store.length) + " Items Selected"
    );
    await navigatorPage.validateSearchCriteriaDropdownValue(
      "dept",
      (await department.length) + " Items Selected"
    );
    await navigatorPage.validateSearchCriteriaDropdownValue(
      "sub-dept",
      (await subDepartment.length) + " Items Selected"
    );
    await navigatorPage.validateSearchCriteriaDropdownValue(
      "pog-status",
      (await planogram.length) + " Item Selected"
    );
    await navigatorPage.validateSearchCriteriaDropdownValue(
      "commodity",
      (await commodity.length) + " Items Selected"
    );
    await navigatorPage.validateSearchCriteriaDropdownValue(
      "comm-group",
      (await commodityGroup.length) + " Items Selected"
    );
    await navigatorPage.closeSearchCriteria();
    await navigatorPage.waitBrowserToLoadPage(2000);
  });
});
