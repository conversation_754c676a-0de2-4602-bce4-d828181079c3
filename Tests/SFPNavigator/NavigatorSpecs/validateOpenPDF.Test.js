const navigatorPage = require('../NavigatorPO/navigator.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const floorplanPage = require('../NavigatorPO/floorPlan.page');
const result = require("../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
const {DBNames} = require("../../../envConfig.js");

describe('validateOpenPDF: Validate PDF opened in new tab for POG Tab', () => {

    beforeEach(async () => {

        await navigatorPage.waitBrowserToLoadPage(3000);
await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(2000)
    })

    afterEach(async () => {

        await navigatorPage.waitBrowserToLoadPage(10000)
        let newWindowUrl= await browser.getUrl();
        await navigatorPage.waitBrowserToLoadPage(5000);
        const hasBlob = await newWindowUrl.includes('blob');
        await navigatorPage.waitBrowserToLoadPage(5000)
        await expect(hasBlob).toEqual(true)
    })


    it('Validate PDF opens in new tab for POG tab ', async () => {
        const upc =  (await (await (await result.getResult(dynamicQuery.upcForOpenPogPdf, DBNames.SFP_STAGE))).flat()).toString();
        await navigatorPage.searchByValue('UPC', await upc);
        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect', navigatorData.div_016_Columbus_Division);
        await navigatorPage.selectSingleValue('PlanogramStatus', 'pog-status', navigatorData.planogramStatusLive);
        await navigatorPage.goTo('POGs')
        await navigatorPage.waitBrowserToLoadPage(3000)
        let parentGuId = browser.getWindowHandle();
        await navigatorPage.getLinkText('PDF');
        await navigatorPage.waitBrowserToLoadPage(5000)
        await floorplanPage.validateNewTab(parentGuId)
   })

   it('should validate PDF open in new tab from Floorplans ', async () => {

        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_014_Cincinnati_Division);
        await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_014_00353);
        await navigatorPage.selectSingleValue('Department', 'dept', navigatorData.dept_06_PHARMACY);
        await navigatorPage.goTo('Floorplans')
        await navigatorPage.waitBrowserToLoadPage(3000)
        let parentGuId = browser.getWindowHandle()
        await navigatorPage.getLinkText(navigatorData.FP_Layout_text)
        await navigatorPage.waitBrowserToLoadPage(5000)
        await floorplanPage.validateNewTab(parentGuId)
    })

    it('should validate PDF open in new tab from Floorplans when FloorplanStatus is pending ', async () => {

        await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',   navigatorData.div_701_Fred_Meyer_Stores);
        await navigatorPage.selectSingleValue('FloorplanStatus', 'floorplan', navigatorData.floorPlanStatusPending);
        await navigatorPage.goTo('Floorplans')
        await navigatorPage.waitBrowserToLoadPage(3000)
        let parentGuId = browser.getWindowHandle()
        await navigatorPage.getLinkText(navigatorData.FP_Layout_text)
        await navigatorPage.waitBrowserToLoadPage(5000)
        await floorplanPage.validateNewTab(parentGuId)
    })

})
