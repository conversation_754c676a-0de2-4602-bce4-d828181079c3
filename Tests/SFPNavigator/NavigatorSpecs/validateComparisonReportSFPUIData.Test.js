const result = require("../../../SQLConnection/server.js.ts");
const navigatorPage = require("../NavigatorPO/navigator.page");
const comparisonReport = require("../NavigatorPO/comparisonReport.page");
const navigatorData = require("../../../TestData/SFPNavigatorData/navigatorUIData.json");
const excelReader = require("../../../util/excelReader");
const queryString = require("../../../SQLConnection/queryString");
const { DBNames } = require("../../../envConfig.js");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
const { assert } = require("chai");

let uiSfpData, upcListFirstValue;
describe("validateComparisonReportSFPUIData:validate SFP data in comparison report Tab", () => {
  const division = navigatorData.div_016_Columbus_Division;
  const divValueForQuery = division.split(" - ")[0];
  const store = navigatorData.store_016_00506;
  const storeValueForQuery = store.split(" - ")[1];
  beforeEach(async () => {
    await navigatorPage.open("navigator");
    await navigatorPage.selectMultipleDropdowns([
      { id: navigatorData.divisionDropdown, values: division },
      { id: navigatorData.storeDropdown, values: store },
    ]);
    // await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect',  navigatorData.div_016_Columbus_Division);
    // await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_016_00506);
    let upcList = await await (
      await await result.getResult(
        dynamicQuery.upcListForILPComparisonTab,
        DBNames.SFP_STAGE
      )
    ).flat();
    upcListFirstValue = await upcList[0];
    await navigatorPage.searchByValue("UPC", await upcListFirstValue);
    await navigatorPage.goTo("Comparison Report");
    await navigatorPage.waitForPageLoad(
      comparisonReport.exportExcelBtn,
      navigatorData.Export_To_Excel_text
    );
    await navigatorPage.waitBrowserToLoadPage(5000);
    uiSfpData = await comparisonReport.getAllSystemUIData("sfp");
  });

  it("validate SFP data comparison report Tab for KOM completion status null", async () => {
    let uiSfpDataFlatArray = uiSfpData.flat();
    let uiSfpDataFlatArrayTreated = navigatorPage.replaceArrayElement(
      uiSfpDataFlatArray,
      "",
      null
    );
    let dbSfpData = await result.getResult(
      queryString.comparisonReportSFPUIData
        .replace("@upc", upcListFirstValue.toString())
        .replace("@str", storeValueForQuery)
        .replace("@div", divValueForQuery),
      DBNames.SFP_STAGE
    );
    if (dbSfpData.length > 0) {
      let compare = await excelReader.dataCompare(
        dbSfpData,
        uiSfpDataFlatArrayTreated
      );
      await expect(compare).toEqual(true);
    } else {
      await assert.fail(0, 1, "Error: Db result is empty; update inputs !");
    }
  });

  it("validate SFP data comparison report Tab for KOM completion status Y", async () => {
    // Note: Completion status is not Y
    let dbSfpData = await result.getResult(
      queryString.comparisonReportSFPUIData
        .replace("@upc", upcListFirstValue.toString())
        .replace("@str", storeValueForQuery)
        .replace("@div", divValueForQuery),
      DBNames.SFP_STAGE
    );
    if (dbSfpData.length > 0) {
      let compare = await excelReader.dataCompare(dbSfpData, uiSfpData);
      await expect(compare).toEqual(true);
    } else {
      await assert.fail(0, 1, "Error: Db result is empty; update inputs !");
    }
  });
});
