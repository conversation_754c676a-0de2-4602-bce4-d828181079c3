const navigatorPage = require('../NavigatorPO/navigator.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const {getFilesCount, extractFile, getExtractedFilesCount, zipCount} = require("../../../util/fileExtractor");
const {assert} = require('chai');
const mappingReportPage = require("../NavigatorPO/mappingReport.page");
const planogramPage = require("../NavigatorPO/mappingReport.page");
const result = require("../../../SQLConnection/server.js.ts");
const qrString = require("../../../SQLConnection/queryString");
const {DBNames} = require("../../../envConfig.js");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
const {dbKeyListForPdfPsaDownloadPogsTab} = require("../../../SQLConnection/dynamicQueryNav");

let countSelectedCheckBox, inactivePogs;
describe('validatePDFandPSABatchDownloadOnPogs: Validate Batch download of PSA and PDF files at the same time on POGs tab - need to run in local ', () => {

    beforeEach(async () => {

        await navigatorPage.waitBrowserToLoadPage(3000);
await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000);
    })

    it('Validate Batch Download of PDF and PSA files on POGS Tab when both PDF and PSA are selected at the same time', async () => {
        //Note: Query didn't work for this test.

        // const pogKeyList =  (await (await (await result.getResult(dynamicQuery.dbKeyListForPdfPsaDownloadPogsTab, DBNames.SFP_STAGE))).flat()).toString();
        await navigatorPage.searchByValue('POG Key', navigatorData.pogKey_List_Pog_PSA);
        await navigatorPage.selectSingleValue('PlanogramStatus', 'pog-status', navigatorData.planogramStatusLive);
        await navigatorPage.goTo('POGs');
        await navigatorPage.waitBrowserToLoadPage(5000);
        await navigatorPage.waitForPageLoad(await navigatorPage.btnExportToExcel, navigatorData.Export_To_Excel_text);
        await navigatorPage.waitBrowserToLoadPage(3000);
        let totalPageRowCount = await (await navigatorPage.getColumnArrayList('POG Type')).length;
        if (totalPageRowCount >= 3) {
            let numOfSelectedCheckBoxes = [totalPageRowCount-1, totalPageRowCount-2, totalPageRowCount-3];
            let numOfSelectedPogDbKey = [1, 2, 3];
            for (let index = 0; index <= numOfSelectedCheckBoxes.length -1; index++) {
                await navigatorPage.clickColumnCheckBox(await mappingReportPage.table, 'planogrampdf');
                await navigatorPage.clickColumnCheckBox(await mappingReportPage.table, 'planogrampsa');
                let pogDBKeyList = await navigatorPage.getColumnArrayList('DBKey');
                let poGDbKeyListExtracted = await navigatorPage.getNumberArrayElements(pogDBKeyList, numOfSelectedPogDbKey[index]);
                let poGDbKeyListExtractedUniqueLength = await (await navigatorPage.getUniqueArrayValue(await poGDbKeyListExtracted)).length;
                await navigatorPage.waitBrowserToLoadPage(2000);
                let initialFilesCount = getFilesCount();
                await navigatorPage.clickRowCheckBox(await mappingReportPage.table, numOfSelectedCheckBoxes[index]);
                await navigatorPage.waitBrowserToLoadPage(3000);
                countSelectedCheckBox = await navigatorPage.returnNumOfCheckBox(await planogramPage.table);
                await expect(await countSelectedCheckBox).toEqual(numOfSelectedPogDbKey[index]);
                await navigatorPage.batchDownload();
                await navigatorPage.waitBrowserToLoadPage(5000);
                let countUnselectedCheckBox = await navigatorPage.returnNumOfCheckBox(await planogramPage.table);
                await expect(await countUnselectedCheckBox).toEqual(0);
                await navigatorPage.validateColumnCheckboxCleared(await mappingReportPage.table, 'planogrampdf');
                await navigatorPage.validateColumnCheckboxCleared(await mappingReportPage.table, 'planogrampsa');
                let finalFilesCount = getFilesCount();
                await navigatorPage.waitBrowserToLoadPage(3000);
                await expect(finalFilesCount - initialFilesCount).toEqual(1);
                await extractFile();
                await navigatorPage.waitBrowserToLoadPage(3000);
                if (getFilesCount() !== 0) {
                    let extractedFileCount = await getExtractedFilesCount();
                    await navigatorPage.waitBrowserToLoadPage(3000);
                    await expect(await extractedFileCount).toEqual(await (poGDbKeyListExtractedUniqueLength * 2) );
                } else {
                    await assert.fail(0, 1, "Downloaded PDF/PSA number does not match with selected PDF/PSA number")
                }
            }
        } else {
            await assert.fail(0, 1, "Table row count must be more than 3 !")
        }
    })

    it('Validate Batch download all PSA files on Planograms Tab when there are only inactive PSA Links', async () => {

        inactivePogs = await ( await result.getResult(qrString.selectInactivePDFPOGKeys, DBNames.SFP_STAGE)).flat();
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.searchByValue('POG Key', await (inactivePogs.toString().replace(/[,]/g,'\n')) );
        await navigatorPage.goTo('POGs');
        await navigatorPage.waitBrowserToLoadPage(3000);
        let initialFilesCount = getFilesCount();
        await navigatorPage.topCheckBox.click();
        await navigatorPage.selectColumnCheckBox('PSA');
        let countSelectedCheckBox = await navigatorPage.returnNumOfCheckBox(await planogramPage.table);
        if(countSelectedCheckBox >= 1){
            await navigatorPage.batchDownload();
            await navigatorPage.waitBrowserToLoadPage(5000);
            let finalFilesCount = getFilesCount();
            await navigatorPage.waitBrowserToLoadPage(3000);
            if(finalFilesCount > initialFilesCount) {
                await expect(finalFilesCount-initialFilesCount).toEqual(1);
                await extractFile();
                await navigatorPage.waitBrowserToLoadPage(2000);
                await expect(await zipCount()).toEqual(0);
            } else{
                await assert.fail(0, 1, "PSA didn't get to download !");
            }
        } else{
            await assert.fail(0, 1, "Top left side column checkbox didn't get to select ! ");
        }
    })

})


