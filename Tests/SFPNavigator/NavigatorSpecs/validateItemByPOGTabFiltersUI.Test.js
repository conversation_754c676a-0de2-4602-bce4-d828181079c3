const navigatorPage = require('../NavigatorPO/navigator.page');
const itemByPOGPage = require('../NavigatorPO/itemByPOG.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const {assert} = require("chai");
const result = require("../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
const {DBNames} = require("../../../envConfig");

let headers;
describe('validateItemByPOGTabFiltersUI: Validate ItemByPog tab filter when Division, store and PogKeys are selected ', () => {
    const division = navigatorData.div_016_Columbus_Division
    const store = navigatorData.store_016_00506
    before(async () => {
        await navigatorPage.open('navigator');
        await navigatorPage.selectMultipleDropdowns([
            { id: navigatorData.divisionDropdown, values: division },
            { id: navigatorData.storeDropdown, values: store }
        ]);     
        await navigatorPage.searchByValue('POG Key', '7472074');
        await navigatorPage.goTo('Item by POG');
        await navigatorPage.waitBrowserToLoadPage(3000);
        await navigatorPage.waitForTableExist(await navigatorPage.tableValue);
        if(await navigatorPage.tableRowList.length >= 1){
            headers = await navigatorPage.getTableHeaderNames(await navigatorPage.tableHeaders);
        } else{
            await assert.fail(0, 1, "Error: Table is empty; update pogKey !");
        }
    })

    it('Validate NII info filter functionality on Item by POG tab', async () => {

        await itemByPOGPage.clickFilterTab('NII Info')
        let firstRecord=await navigatorPage.GetText(await navigatorPage.tableFirstData)
        if( !firstRecord.includes('No records found')) {
            let uiData = await (await navigatorPage.getAllUIData());
            for (let index = 0; index < uiData.length; index++) {
                await expect(parseInt(uiData[index][headers.indexOf('New Flag')]) || parseInt(uiData[index][headers.indexOf('Move\nFlag')])).toEqual(1)
            }
        }
    })

    it('Validate Pusher - Tray filter functionality on Item by POG tab', async () => {
        await itemByPOGPage.clickFilterTab('Pusher - Tray')
        let firstRecord=await navigatorPage.GetText(await navigatorPage.tableFirstData)
        if( !firstRecord.includes('No records found')) {
            let uiData = await (await navigatorPage.getAllUIData())
            let flag=false
            for (let index = 0; index < uiData.length; index++) {
                if((uiData[index][headers.indexOf('Pusher')] !== 0 && uiData[index][headers.indexOf('Pusher')] !== null) || (uiData[index][headers.indexOf('Tray')] !== 0 && uiData[index][headers.indexOf('Tray')] !== null)){
                    flag=true
                }
                await expect(flag).toEqual(true)
            }
        }
    })

    it('Validate "Merchandised Assortment", "Deletes", "Move - Adjustments" and "Adds" filters functionality on Item by POG tab', async () => {
        let filterNameList = ['Deletes','Move - Adjustments','Adds'];
        let filterFlagList =['Delete Flag','Move Flag','New Flag'];
        for(let filterName=0; filterName<filterNameList.length; filterName++){
            await itemByPOGPage.clickFilterTab(filterNameList[filterName])
            let firstRecord=await navigatorPage.GetText(await navigatorPage.tableFirstData)
            if( !firstRecord.includes('No records found')) {
                let uiData = await (await navigatorPage.getAllUIData())
                for (let index = 0; index < uiData.length; index++) {
                    await expect(parseInt(uiData[index][headers.indexOf(filterFlagList[filterName])])).toEqual(1)
                }
            }
        }

    })

})
