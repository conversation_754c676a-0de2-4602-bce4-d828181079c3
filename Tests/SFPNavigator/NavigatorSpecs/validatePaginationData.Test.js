const navigatorPage = require('../NavigatorPO/navigator.page');
const navigatorData = require('../../../TestData/SFPNavigatorData/navigatorUIData.json');
const getData = require("../../../util/genericGetData");
const {assert} = require("chai");
const result = require("../../../SQLConnection/server.js.ts");
const dynamicQuery = require("../../../SQLConnection/dynamicQueryNav");
const {DBNames} = require("../../../envConfig.js");

describe('validatePaginationData: Validate data on all tabs with pagination', () => {

    beforeEach(async () =>{
        await navigatorPage.waitBrowserToLoadPage(3000);
await navigatorPage.open('navigator');
        await navigatorPage.waitBrowserToLoadPage(3000);
    })

    afterEach(async () =>{
        await navigatorPage.waitBrowserToLoadPage(5000);
        let uiData=await navigatorPage.getNPagesUIData("2");
        await navigatorPage.waitBrowserToLoadPage(2000);
        if(await uiData.length >= 1){
            let isDuplicate=await navigatorPage.checkDuplicate(uiData);
            await expect(isDuplicate).toEqual(false);
        }else{
            await assert.fail(0, 1, "Error: Table data is empty !");
        }
    })

    it('validateTableDataUniqueWithPaginationOnMappingReport: validate UI data on mappingReport is unique based on Pagination', async () => {

        await getData.selectSingleDivisionWithQuery(navigatorData.div018_Index_3);
        await navigatorPage.selectSingleValue('Stores', 'store',  navigatorData.store_018_00038);
        await navigatorPage.goTo('Mapping Report');
    })

    it('validateTableDataUniqueWithPaginationOnStoreTab: validate UI data is unique on Store tab based on Pagination', async () => {

        await getData.selectSingleDivisionWithQuery(navigatorData.div018_Index_3);
        await navigatorPage.selectSelectAllValue('Stores', 'store', navigatorData.SelectAll);
        await navigatorPage.goTo('Store');
    })

    it('validateTableDataUniqueWithPaginationOnCompositeTab:validate the UI data is unique in Composite tab based on Pagination', async () => {

        await navigatorPage.selectMultipleValues('Division', 'divisionMultiSelect', navigatorData.divisionListStoreTab);
        await navigatorPage.selectMultipleValues('Stores','store', navigatorData.storelist);
        await navigatorPage.goTo('Composite');
    })

    it('validateTableDataUniqueWithPaginationOnFloorplans:validate the UI data is unique in Floorplans tab based on Pagination', async () => {

        await getData.selectSingleDivisionWithQuery(navigatorData.div090_Index_11);
        await navigatorPage.selectSelectAllValue('Stores', 'store', navigatorData.SelectAll);
        await navigatorPage.goTo('Floorplans');
    })

    it('validateTableDataUniqueWithPaginationOnPogItemByStore: validate the UI data is unique in POG & Item by Store tab based on Pagination', async () => {

        await getData.selectSingleDivisionWithQuery('2');
        await navigatorPage.selectMultipleValues('Stores', 'store', navigatorData.storeList_StoreType_POG);
        await navigatorPage.waitBrowserToLoadPage(4000);
        const upc =  (await (await (await result.getResult(dynamicQuery.upcListForPageNumbers, DBNames.SFP_STAGE))).flat())[0];
        await navigatorPage.searchByValue('UPC', upc);
        await navigatorPage.goTo(navigatorData.POG_Item_by_Store);
        await navigatorPage.waitBrowserToLoadPage(4000);
    })


})
