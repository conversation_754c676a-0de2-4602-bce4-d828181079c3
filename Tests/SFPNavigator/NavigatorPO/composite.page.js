const Page = require('../../../GlobalObjectRepository/page');
/**
 * sub page containing specific selectors and methods for Composite Tab
 */
class CompositePage extends Page {

    get tabHeaders()                            { return $$('div.navbar.sfpnavigator>div>ul>kds-tooltippable>li>a')}
    get moduleLink()                            { return $('#page > app-breadcrumb > div > ol > li:nth-child(3) >a')}
    get numOfItemsEntryLinkFirst()              { return $('table>tbody>tr:nth-child(1)>td:nth-child(9)>a')}
    get batchDownloadErrorMsg()                 { return $('mx-modal-body[id="mx-modal-id"]>div>p')}
    get errorMessageConfirmBtn()                { return $$('div[id="mx-Modal"] mx-modal-footer>div>div+div>kds-button+kds-button>button')}
    get checkBoxColumn()                        { return $('tr[class="ng-star-inserted"]>th >p-tableheadercheckbox>div>div.p-checkbox-box')}
    get numOfPogColumn()                        { return $('tr>th[id="num_of_pogs"]')}
    get tableValue()                            {return $('div#page tbody')}
    get firstRowCheckBox()                      {return $('tbody>tr:nth-child(1)>td.p-element.p-frozen-column>p-tablecheckbox>div>div.p-checkbox-box.p-component')}


    async selectCompositeTab() {
        let tab = await this.tabHeaders
        for (let index = 0; index < tab.length; index++) {
            let appList = await tab[index]
            if (await tab[index].getText() === 'Composite') {
                await appList.click();
                break;
            }
        }
       await super.clickOn(await this.moduleLink)
    }

    async clickConfirmButton(text) {
        const buttons = await this.errorMessageConfirmBtn;
        await browser.pause(3000)
        for (let i = 0; i < buttons.length; i++) {
            await browser.pause(1000)
            const buttonText = await buttons[i].getText();
            if (await buttonText === await text) {
                await buttons[i].click();
                break;
            }
        }
    }


}

module.exports = new CompositePage();
