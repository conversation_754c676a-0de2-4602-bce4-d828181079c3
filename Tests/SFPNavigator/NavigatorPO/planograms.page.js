const Page = require('../../../GlobalObjectRepository/page');

let col, row;
/**
 * sub page containing specific selectors and methods for Planograms tab
 */
class PlanogramsPage extends Page {

    get table()                           { return $('div#page tbody')}
    get nextButtonPagination()            { return $('p-paginator>div>button:nth-child(5)') }
    get valueColumnsLink()                { return $$(`tbody td:nth-child(${col})>div>a`)}
    get valueColumnsLink2()                { return $$('tbody td:nth-child(7)>div>a')}
    get btnPageOne()                      { return $('span[class="p-paginator-pages ng-star-inserted"]>button:nth-child(1)')}
    get pogTabHeaders()                   { return $$('div#page thead th')}
    get totalSelectedDisplayString()      { return $('div[class="planogramReport"]>div>aside')}
    get checkBoxHeader()                  { return $('thead[class="p-datatable-thead"]>tr>th.p-element.p-frozen-column')}
    get selectedPogInfo()                 { return $('app-planogram[class="ng-star-inserted"]>div>div>aside>span:last-child')}
    get displayMappingPogString()         { return $('kds-button[id="mapping-filter-button"]+div')}
    get columnCellText()                  { return $(`table>tbody>tr:nth-child(${row})>td:nth-child(${col})`)}
    get columnCellLink()                  { return $(`table>tbody>tr:nth-child(${row})>td:nth-child(${col})>a`)}
    get tableRowList()                    { return $$('p-table>div>div>table>tbody>tr') }
    get copySelectedPogBtn()              { return $('button[id="copy-to-clipboard-button"]>kds-icon-copy>kds-icon>svg')}
    get pageEntryCountLabel()             { return $('p-paginator>div>span.p-paginator-current.ng-star-inserted')}
    get pogFilterListBtn()                { return $$('kds-buttongroup:nth-child(2)>kds-buttongroup-member')}
    get allPogsDisableBtn()               { return $('kds-buttongroup-member[id="all-pogs-button"]')}
    get pogFilterBtn()                    { return ('kds-buttongroup-member[text-label="@filter"]')}
    get disabledFilterBtn()               { return ('kds-buttongroup-member[text-label="@filterName"]>label')}
    get mappedPogButton()                 { return $('kds-buttongroup-member[id="mapped-pogs-button"]>label>span')}

    /*clicking FP department values one by one and clicking each page after each dropdown value is selected*/
    async getActivePDFCount(key){
        let count=0
        let table = await this.table;
        await table.waitForExist({timeout:50000},'Err: did not appear before time out')
        await expect(table).toBeDisplayedInViewport()
        let nextButton = await this.nextButtonPagination;
        const headerData = await this.pogTabHeaders;
        for(let index=0; index<headerData.length;index++) {
            if ((await headerData[index].getText()) === (key)) {
                col = index + 1;
                let value =  await this.valueColumnsLink;
                count = count + value.length;
                break
            }
        }
        let nextBtnClassName = await this.nextButtonPagination.getAttribute("class");
            while (!nextBtnClassName.includes('p-disabled')){
            let value =  await this.valueColumnsLink;
            count = count + value.length;
            await super.clickOn(await nextButton);
            await super.waitBrowserToLoadPage(6000)
            if(nextBtnClassName.includes("p-disabled") ) {
                break;
            }
        }
        return count;
    }

    async returnFooterEntryNumLisOfFilters(filterLocator, footerStringLocator) {
    let buttonList = await this.getArrayElementsText(filterLocator);
    let numOfEntryList = [];
    for(let index =0; index< buttonList.length; index++){
        let filterName = await buttonList[index]
        await super.waitBrowserToLoadPage(3000);
        let filterTab = await $((this.pogFilterBtn).replace('@filter', filterName))
        await super.clickOn(filterTab)
        await super.waitBrowserToLoadPage(3000);
    let numOfEntries = await (await footerStringLocator.getText()).split(" ")[5];
    await super.waitBrowserToLoadPage(3000);
    numOfEntryList.push( await numOfEntries)
            }
        return numOfEntryList;
     }

    async validateDisabledButton(filterName) {
        let flag;
        let selector = await $((this.disabledFilterBtn).replace('@filterName', filterName))
        await selector.waitForExist({timeout: 30000, timeoutMsg: 'Error: did not appear before time out'})
        let isDisabled = await selector.isEnabled();
        if (isDisabled) {
            flag = true;
        } else {
            console.error(`Error while validating disabled button with selector ${selector}:`);
            flag = false;
        }
        return flag;
    }

}

module.exports = new PlanogramsPage();
