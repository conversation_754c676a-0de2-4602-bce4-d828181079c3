const Page = require('../../../GlobalObjectRepository/page.js');

let colIndex,filter, pogRowIndex,colCount;
let rowIndex = 0;
/**
 * sub page containing specific selectors and methods for MappingReport tab
 */
class MappingReportPage extends Page {

    get mappingReportTableValue()           { return $('div#page tbody') }
    get searchPopup()                       { return $('mx-modal[id="search-modal"] * mx-modal-header')}
    get moduleLink()                        { return $('#page > app-breadcrumb > div > ol > li:nth-child(3) >a')}
    get mrTabHeaders()                      { return $$('div#page thead th')}
    get selectAisleDropDown()               { return $('mx-multi-select[id="select-aisle-dropdown"]>div>div>span')}
    get sortByAisleBtn()                    { return  $('app-mapping>div>div>div>div>div:nth-child(1)>a:nth-child(2)')}
    get clearAisleBtn()                     { return  $('mx-multi-select[id="select-aisle-dropdown"]>div>div>kds-icon-close>kds-icon')}
    get clearFPDeptBtn()                    { return  $(`mx-multi-select[id="fp-department-select"]>div>div>kds-icon-close>kds-icon`)}
    get clearPOGTypeBtn()                   { return  $(`mx-multi-select[id="pog-type-select"]>div>div>kds-icon-close>kds-icon`)}
    get clearBtn()                          { return  (`mx-multi-select[id="buttonName-select"]>div>div>kds-icon-close>kds-icon`)}
    get selectFPDeptDropDown()              { return $('mx-multi-select[id="fp-department-select"]>div>div>span')}
    get selectFilterDropDown()              { return $('mx-multi-select[name="selected'+filter+'"]>div>div>span')}
    get columnOne()                         { return $('div#page tbody tr:nth-child(1) td:nth-child(1)')}
    get fpDepartDropDownList()              { return $$('mx-multi-select[id="fp-department-select"] * mx-menu-item>li')}
    get pogTypeDownList()                   { return $$('mx-multi-select[id="pog-type-select"] * mx-menu-item>li')}
    get dropDownList()                      { return $$('mx-multi-select[id="dropdownIDName-select"] * mx-menu-item>li')}
    get filterDropDownList()                { return ('mx-multi-select[id="dropdownIDName-select"] * mx-menu-item>li')}
    get valueNColumns()                     { return $$(`div#page tbody td:nth-child(${colIndex})`)}
    get nextButtonPagination()              { return $('p-paginator>div>button.p-ripple.p-element.p-paginator-next') }
    get table()                             { return $('div#page tbody')}
    get btnPageOne()                        { return $('span[class="p-paginator-pages ng-star-inserted"]>button:nth-child(1)')}
    get textSelectedPOGKey()                { return $('div[class="ng-star-inserted"]>aside')}
    get textSelectedFloorplanKey()          { return $('kds-text[name="filter"]>span')}
    get selectedPOGKeyString()              { return $('div[class="ng-star-inserted"]>aside')}
    get aisleNumColumnHeader()              { return $('div#page thead th:nth-child(10)')}
    get divisionColumnHeader()              { return $('div#page thead th:nth-child(2)')}
    get pageThreePagination()               { return $('p-paginator>div>span>a:nth-child(3)')}
    get paginationRow()                     { return $('p-paginator>div>span')}
    get paginationRowList()                 { return $$('p-paginator>div>span>a')}
    get filtersBtn()                        { return $('kds-button[id="mapping-filter-button"]>button')}
    get storeTypeDropdownText()             { return $('mx-multi-select[id="store-type-select"]>div>div>span')}
    get clearStoreTypeBtn()                 { return $(`mx-multi-select[id="store-type-select"]>div>div>kds-icon-close>kds-icon>svg`)}
    get paginationPageNumberTwo()           { return $(`p-paginator[class="p-element ng-star-inserted"]>div>span>button:nth-child(2)`)}
    get displayEntryString()                { return $(`p-paginator[class="p-element ng-star-inserted"]>div>span.p-paginator-current`)}
    get backToStartPaginationBtn()          { return $(`p-paginator[class="p-element ng-star-inserted"]>div>button.p-ripple.p-element.p-paginator-first`)}
    get editPogListBtn()                    { return $(`button[id="filter-modal-open-button"]>kds-icon-edit>kds-icon>svg`)}
    get pogDbKeyFilterString()              { return $(`kds-button[id="mapping-filter-button"]+div>aside`)}
    get pogInfoTable()                      { return $(`mx-modal[id="edit-pog-modal"]>div>div>mx-modal-body * p-table`)}
    get pogInfoTableApplyBtn()              { return $(`//mx-modal[@id="edit-pog-modal"]//button[contains(@class, 'interactive palette-brand')]`)}
    get pogInfoTableCancelBtn()             { return $(`//mx-modal[@id="edit-pog-modal"]//button[contains(@class, 'interactive palette-neutral')]`)}
    get pogInfoTableDeleteFirstRow()        { return $(`(//mx-modal[@id="edit-pog-modal"]//button[@class="ml-4"])[1]`)}
    get pogInfoTableHeader()                { return $$(`p-table[id="pogInfoTable"]>div>div>table>thead>tr>th`)}
    get pogInfoTableRows()                  { return $$(`p-table[id="pogInfoTable"]>div>div>table>tbody>tr`)}
    get pogInfoTableDeleteList()            { return $$(`p-table[id="pogInfoTable"]>div>div>table>tbody>tr * svg`)}
    get pogInfoTableNthRowList()            { return $$(`p-table[id="pogInfoTable"]>div>div>table>tbody>tr:nth-child(${pogRowIndex})>td`)}
    get cancelCircleButton()                { return $('aside > button')}
    get mappingReportTable()                { return $('div#page tbody') }
    get defaultPageNumBtn()                 { return $('div[class="p-paginator-rpp-options p-dropdown p-component"]>div+span') }
    get tableRowList()                      { return $$('app-mapping>div>div>p-table>div>div>table>tbody>tr')}
    get emptyTableText()                    { return $('p-table>div>div>table>tbody>tr>td>span') }

    async getFilterDropdownList(filterKey, dropdownListLocatorName){
        filter=filterKey
        let filterSelector = await this.selectFilterDropDown;
        await filterSelector.waitForExist({timeout: 59000}, 'Error: Floor plan page did not appear before timeout')
        await super.clickOn(filterSelector)
        await super.waitBrowserToLoadPage(2000)
        let text = await $$((this.filterDropDownList).replace('dropdownIDName', dropdownListLocatorName));
        let filterList = [];
        for (let index = 0; index < text.length; index++) {
            filterList.push(await (await text[index]).getText())
            await super.waitBrowserToLoadPage(1000)
        }
        await browser.keys('Escape');
        return filterList;
    }

    async selectFilterValue(filterKey, dropdownListLocatorName, filteredValuesUI) {
        filter=filterKey
        let filterSelector = await this.selectFilterDropDown;
        await this.isDisplayed('Filter dropdown selector', filterSelector)
        await super.clickOn(filterSelector)
        await super.waitBrowserToLoadPage(2000)
        let text = await $$((this.filterDropDownList).replace('dropdownIDName', dropdownListLocatorName));
        for (let index = 0; index < text.length; index++) {
            let appList = text[index];
            if (await text[index].getText() === filteredValuesUI) {
                await text[index].scrollIntoView();
                await super.waitBrowserToLoadPage(1000);
                await appList.click();
                break;
            }
        }
        await browser.keys('Escape')
    }

    async clearFilterButton(clearButtonName) {
        let clearBtn = await $((this.clearBtn).replace('buttonName', clearButtonName));
        await clearBtn.waitForClickable({timeout: 59000}, 'Error: Floor plan page did not appear before timeout')
        await expect(clearBtn).toBeClickable()
        await super.clickOn(clearBtn)
    }

    /*clicking FP department values one by one and clicking each page after each dropdown value is selected*/
    async getAllColumnValues(key){
        let table = await this.mappingReportTableValue;
        await table.waitForExist({timeout:59000},'Error: Mapping Report Table did not appear before time out')
        let columnValues = [];
        let nextButton = await this.nextButtonPagination;
        let headerData = await this.mrTabHeaders;
        await super.waitBrowserToLoadPage(2000)
        for(let dataCount=0; dataCount < headerData.length; dataCount++) {
            if ((await headerData[dataCount].getText()) === (key)) {
                await super.waitBrowserToLoadPage(1000)
                colIndex = dataCount + 1;
                break;
            }
        }
        let nextBtnClassName =  await this.nextButtonPagination.getAttribute("class");
        while (nextBtnClassName){
            let value =  await this.valueNColumns;
            for( let index =0; index < value.length; index++){
                columnValues.push(await value[index].getText() )
            }
            await super.waitBrowserToLoadPage(1000)
            nextBtnClassName =  await this.nextButtonPagination.getAttribute("class");
            if(await nextButton.isClickable()){
                await super.clickOn(await nextButton)
            }
            await super.waitBrowserToLoadPage(1000)
            if(await this.btnPageOne.isClickable()){
                await this.btnPageOne.waitForClickable({timeout: 59000}, 'Error: Next button did not appear clickable before timeout')
            }
            await super.waitBrowserToLoadPage(1000)
            if(nextBtnClassName.includes("p-disabled") ) {
                break;
            }
        }
        return columnValues;
    }

    async clickElement(elementLocator) {
        let clearBtn = await elementLocator;
        await clearBtn.waitForClickable({timeout: 6000}, 'Error: Floor plan page did not appear before timeout')
        await expect(clearBtn).toBeClickable()
        await super.clickOn(clearBtn)
    }

    async getPageNumber(){
        let pagination = await this.paginationRow;
        await this.isDisplayed('Mapping report pagination row', pagination)
        let pageNumber = [];
        let paginationNum = await this.paginationRowList;
        for(let index = 0; index < paginationNum.length; index++){
            let getAttribute =  await paginationNum[index].getAttribute('class')
            if(getAttribute.includes('ui-state-active') ){
                pageNumber.push(await paginationNum[index].getText())
            }
        }
        return pageNumber;
    }

    async returnUniqueStringByArrayIndex(arrayList, index){
        let specificColumnArray = await this.getColumnArrayByIndex(arrayList, index)
        let specificColumnArrayUnique = await this.getUniqueArrayValue(specificColumnArray)
        return specificColumnArrayUnique.toString();
    }

    async validatePogEdit(actionName, tableName){
        await super.clickOn(await this.editPogListBtn);
        await this.waitForTableExist(tableName)
        let rowNumBeforeDelete =  (await this.pogInfoTableRows).length;
        let deleteRow = await this.pogInfoTableDeleteFirstRow;
        await super.clickOn(deleteRow)
        await super.waitBrowserToLoadPage(2000)
        let rowNumAfterDelete =  (await this.pogInfoTableRows).length;
        await super.waitBrowserToLoadPage(2000)
        if( (rowNumBeforeDelete-1) === rowNumAfterDelete) {
            if(actionName === 'Cancel'){
                await super.clickOn(await this.pogInfoTableCancelBtn)
                await super.waitBrowserToLoadPage(2000)
                let numOfSelectedPogOnMappingReport = await this.extractDigits(await this.pogDbKeyFilterString.getText())
                await expect(numOfSelectedPogOnMappingReport).toEqual((rowNumAfterDelete + 1).toString());
            }else{
                await console.error("Error: Cancel button did not work")
            }
            if(actionName === 'Apply') {
                await super.clickOn(await this.pogInfoTableApplyBtn);
                await super.waitBrowserToLoadPage(2000)
                let numOfSelectedPogOnMappingReport = await this.extractDigits(await this.pogDbKeyFilterString.getText())
                await expect(numOfSelectedPogOnMappingReport).toEqual(rowNumAfterDelete.toString());
            }else{
                await console.error("Error: Apply button did not work")
            }
        }
    }

    async clickDeleteRow(tableName, rowList, rowIndex){
        await this.waitForTableExist(tableName)
        let deleteRowList = await rowList;
        for(let index=1; index< (deleteRowList - (deleteRowList - rowIndex)); index++){
            await super.clickOn(deleteRowList[index])
            await super.clickOn(await this.pogInfoTableApplyBtn)
        }
    }

   // Table does not have pagination
    async getAllUIData(tableName) {
        await this.waitForTableExist(tableName)
        let uiData = []
        let tableRows = await this.pogInfoTableRows;
        for (rowIndex = 1; rowIndex <=  tableRows.length; rowIndex++) {
            pogRowIndex = rowIndex
            let uiRowData = []
            let columns = await this.pogInfoTableNthRowList;
            for (colCount = 0; colCount < columns.length; colCount++) {
                let data = await this.pogInfoTableNthRowList[colCount]
                uiRowData.push(await data.getText())
            }
            uiData.push(uiRowData)
        }
        return uiData
    }

    async getAllColumnValuesWithDynamicIndex(key, dynamicIndex){
        let table = await this.mappingReportTableValue;
        await table.waitForExist({timeout:59000},'Error: Mapping Report Table did not appear before time out');
        let columnValues = [];
        let nextButton = await this.nextButtonPagination
        let headerData = await this.mrTabHeaders;
        await super.waitBrowserToLoadPage(2000)
        for(let dataCount=0; dataCount<headerData.length;dataCount++) {
            if ((await headerData[dataCount].getText()) === (key)) {
                await super.waitBrowserToLoadPage(1000)
                colIndex = dataCount + dynamicIndex;
                break
            }
        }
        while (nextButton.isEnabled()){
            let nextBtnClassName =  await this.nextButtonPagination.getAttribute("class")
            let value =  await this.valueNColumns;
            for( let index =0; index < value.length; index++){
                columnValues.push(await value[index].getText() )
            }
            await super.clickOn(await nextButton)
            await super.waitBrowserToLoadPage(1000)
            await this.btnPageOne.waitForClickable({timeout: 7000}, 'Error: Next button did not appear clickable before timeout')
            await super.waitBrowserToLoadPage(1000)
            if(nextBtnClassName.includes("p-disabled") ) {
                break;
            }
        }
        return columnValues
    }



}
module.exports = new MappingReportPage();
