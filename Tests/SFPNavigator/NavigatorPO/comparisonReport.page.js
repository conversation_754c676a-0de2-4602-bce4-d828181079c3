const Page = require('../../../GlobalObjectRepository/page.js');
let divIndex;
let rowIndex = 0;
let rowCount, colCount;
class ComparisonReportPage extends Page{
        get itemLocComparisonViewTable()                      { return $('app-comparison-upc-summary>aside')}
        get exportExcelBtn()                                  { return $('kds-button[id="exportDataButton"]>button')}
        get filterBtn()                                       { return $('kds-sliding-panel[aria-label="Filter"]>header>kds-button')}
        get upcDropDown()                                     { return $('div[aria-label="UPC:"]')}
        get upcText()                                         { return $('mx-multi-select[label="UPC:"]>div>kds-label')}
        get upcDropDownInput()                                { return $('mx-search-input[class="filter-search hydrated"]>div>div>input')}
        get itemLocComparisonViewTxt()                        { return $('div[class="ilpReport report"]>div>div>label')}
        get selectAllUpcCheckBox()                            { return $('#upc-select>div>div>div>div>mx-multi-select-list>ul>mx-menu-item>li>div>div.w-full>kds-label>label')}
        get upcDropDownCheckBoxList()                         { return $$('ers-menu-item>li>div>kds-label>label>kds-checkbox>input')}
        get upcDropDownList()                                 { return $$('#upc-select > div > div:nth-child(3) > div > div > mx-multi-select-list > ul > div > mx-menu-item> li > div > div.w-full > kds-label > label')}
        get itemLocComparisonViewTableNthRowList()            { return $$(`app-comparison-upc-summary>aside>div:nth-child(${divIndex})>div>p:nth-child(2)`)}
        get itemLocComparisonViewTableNthColumnList()         { return $$(`app-comparison-upc-summary>aside>div>div:nth-child(${divIndex})>p:nth-child(2)`)}
        get itemLocComparisonViewTableFirstRowHeaders()       { return $$('app-comparison-upc-summary>aside>div:nth-child(1)>div>p:nth-child(1)')}
        get tableValue()                                      { return (`label +app-comparison-name-report-table>div`) }
        get tableColumns()                                    { return (`div>app-comparison-name-report-table>div>p-table>div>div>table>tbody>tr:nth-child(1)>td`) }
        get nextButtonPagination()                            { return (`app-comparison-name-report-table * button:nth-child(5)`) }
        get tableRows()                                       { return (`div>app-comparison-name-report-table>div>p-table>div>div>table>tbody>tr`) }
        get tableNthColumns()                                 { return (`div>app-comparison-name-report-table>div>p-table>div>div>table>tbody>tr:nth-child(${rowIndex}) > td`) }
        get btnPageOne()                                      { return (`app-comparison-name-report-table * button+span>button`) }
        get comparisonViewTableColumns()                      { return $$("app-comparison-upc-summary > aside > div >div p+p") }
        get comparisonViewTableRows()                         { return $$(`app-comparison-upc-summary > aside > div`) }
        get comparisonViewNthColumn ()                        { return $$('div > app-comparison-upc-summary >aside>div>div p+p')}
        get tableIlComparisonView()                           { return $("app-comparison-upc-summary > aside") }
        get comparisonTable()                                 { return ('div[class="tableNameTableReport"]>p-table>div>div>table>tbody') }
        get comparisonReportILPSFPPageDropdown()              { return (`div[class="TableNameTableReport"]>p-table>div>p-paginator>div>p-dropdown>div`) }


       //dynamically finds column tile and loops the column values => specific for Pog&ItemByStore
        async getColumnArrayList(header){
                let table = await this.itemLocComparisonViewTable;
                await table.waitForExist({ timeout: 50000 }, 'Error: UI table did not appear before time out')
                await expect(await table).toBeDisplayedInViewport();
                let headerData = await this.itemLocComparisonViewTableFirstRowHeaders;
                await super.waitBrowserToLoadPage(2000);
                let arrayList = await this.getArrayElementsText(headerData);
                let headerIndex = arrayList.indexOf(header);
                divIndex = headerIndex + 1;
                let columnList = await this.itemLocComparisonViewTableNthColumnList;
                let columnTextList = await this.getArrayElementsText(columnList);
                return columnTextList;
        }

        //clicks all upc check boxes and prints UPCs text into an array
        async clickAndGetUPCDropdownList() {
                let selectAll = await this.selectAllUpcCheckBox;
                await selectAll.waitForExist({ timeout: 4000 }, 'UPC dropdown did not appear before timeout !');
                await expect(selectAll).toBeDisplayedInViewport();
                for(let countNum=0; countNum<2; countNum++){
                        await super.clickOn(selectAll);
                        await super.waitBrowserToLoadPage(2000)
                }
                await super.waitBrowserToLoadPage(2000)
                let upcDropdownText = await this.upcDropDownList;
                let upcList = [];
                for (let index = 0; index < upcDropdownText.length; index++) {
                        await super.clickOn(await upcDropdownText[index])
                        await super.waitBrowserToLoadPage(1000)
                        upcList.push(await upcDropdownText[index].getText())
                }
                await browser.keys('Escape')
                return upcList
        }

        async getSystemData(excelData, system) {
                let systemData = [];
                try {
                    for (let index = 0; index < excelData.length; index++) {
                        if (excelData[index][5] === system) {
                            let row = [...excelData[index]]; // Copy row
                            row.splice(4, 1); // Remove 5th element
                            systemData.push(row);
                        }
                    }
                } catch (error) {
                    console.error("No Data Found");
                    return error.message;
                }
                return systemData;
            }

        async getAllSystemUIData(tableName) {
                let table = await $((this.tableValue).replace('name', tableName));
                await table.waitForExist({ timeout: 59000 }, 'Error: UI table of data did not appear before timeout')
                await table.scrollIntoView()
                await expect(table).toBeDisplayedInViewport()
                let columns = await $$((this.tableColumns).replace('name', tableName));
                let uiData = []
                let nextButton = await $((this.nextButtonPagination).replace('name', tableName));
                await super.waitBrowserToLoadPage(3000)
                let btnPageOne = await $((this.btnPageOne).replace('name', tableName))
                await $((this.btnPageOne).replace('name', tableName)).waitForClickable({ timeout: 59000 }, 'Error: SFP table did not appear before timeout')
                while (await nextButton.isEnabled()) {
                        let nextButtonClassName = await nextButton.getAttribute("class")
                        let tableRowsOfTableNames = await $$((this.tableRows).replace('name', tableName))
                        for (rowCount = 1; rowCount <= await tableRowsOfTableNames.length; rowCount++) {
                                rowIndex = rowCount
                                let uiRowData = []
                                for (colCount = 0; colCount < columns.length; colCount++) {
                                        let data = $$(await this.tableNthColumns.replace('name', tableName))[colCount]
                                        uiRowData.push(await data.getText())
                                }
                                uiData.push(uiRowData)
                        }

                        await super.clickOn(nextButton)
                        await super.waitBrowserToLoadPage(1000)
                        await btnPageOne.waitForClickable({ timeout: 59000 }, 'Error: Page one did not appear before timeout')
                        if (nextButtonClassName.includes("p-disabled")) {
                                break
                        }
                }
                return uiData
        }

        async getAllSystemComparisonReportUIData() {
                await super.waitBrowserToLoadPage(4000)
                let table = await (this.tableIlComparisonView)
                await table.waitForExist({ timeout: 59000 }, 'Error: UI table of data did not appear before timeout')
                await table.scrollIntoView()
                let uiData = []
                        let tableRowsOfTableNames = await this.comparisonViewTableRows
                        for (rowIndex = 1; rowIndex <= await tableRowsOfTableNames.length; rowIndex++) {
                                divIndex = rowIndex
                                let uiRowData = []
                                let columns = await this.itemLocComparisonViewTableNthRowList
                                for (colCount = 0; colCount < columns.length; colCount++) {
                                        let data = await this.itemLocComparisonViewTableNthRowList[colCount]
                                        uiRowData.push(await data.getText())
                                }
                                uiData.push(uiRowData)    
                         }    
                return uiData
        }

        async validateComparisonTablesVisible(tableLocatorName) {
                let eachTable = await $(this.comparisonTable.replace('tableName', tableLocatorName))
                await this.waitForTableExist(eachTable);
                if(await eachTable.isDisplayed()){
                        return true
                }else{
                        return 'Comparison report tables are not visible'
                }
        }

}

module.exports = new ComparisonReportPage();
