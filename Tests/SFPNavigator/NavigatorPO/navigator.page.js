const { select } = require('underscore');
const Page = require('../../../GlobalObjectRepository/page');
const {getFilesCount} = require("../../../util/fileExtractor");

let childIndex = 0;
let rowIndexNum = 0;
let rowIndex, colIndex, childIndexNumber;
/**
 * sub page containing specific selectors and methods for a specific page
 */

class NavigatorPage extends Page {

    get enterValue() {return $('mx-single-select[id="list-filter-criteria-dropdown"]+textarea')}
    get optionDropdown() {return $('mx-single-select[name="selectedFilter"]>div>kds-label+div')}
    get selectOption() {return $$('mx-single-select-list>ul>div>mx-single-select-list-item>div>li')}
    get optionText() {return $('mx-single-select[name="selectedFilter"]>div>kds-label+div>span')}
    get inputFilterName() {return ('mx-multi-select[name="selected?DropDown"]>div>div>div>mx-search-input>div>div>input')}
    get divisionDropdown() {return $('mx-multi-select[name="selectedDivision"]>div>kds-label+div>span')}
    get searchCriteriaDivisionDropdown() {return $('mx-multi-select[name="selectedDivisions"]>div>kds-label+div>span')}
    get searchCriteriaDropdown() {return $('mx-multi-select[name="selectedDropdownName"]>div>kds-label+div>span')}
    get storeDropdown() {return $('mx-multi-select[name="selectedStores"]>div>kds-label+div>span')}
    get deptDropdown() {return $('mx-multi-select[name="selectedDepartment"]>div>kds-label+div>span')}
    get subDeptDropdown() {return $('mx-multi-select[name="selectedSubDept"]>div>kds-label +div>span')}
    get commodityDropdown() {return $('mx-multi-select[name="selectedCommodity"]>div>kds-label +div>span')}
    get commodityGrpDropdown() {return $('mx-multi-select[name="selectedCommodityGroup"]>div>kds-label+div>span')}
    get planogramStatusDropdown() {return $('mx-multi-select[name="selectedPlanogramStatus"]>div>kds-label+div>span')}
    get fpStatusDropdown() {return $('mx-multi-select[name="selectedFloorplanStatus"]>div>kds-label+div>span')}
    get eventTypeDropdown() {return $('mx-single-select[name="selectedEventType"]>div>kds-label+div>span')}
    get eventNameDropdown() {return $('mx-multi-select[name="selectedEventName"]>div>kds-label+div>span')}
    get selectDropdown() {return ('mx-multi-select[name="selected?DropDown"]>div>kds-label+div>span')}
    get selectDropdownEventType() {return ('mx-single-select[name="selected?DropDown"]>div>kds-label+div')}
    get searchCriteriaSelectDropdown()           {return ('#dropdownName-select>div>div.mx-dropdown-input-wrapper.disabled.compact')}
    get selectDropdownSingleSelect() {return ('mx-single-select[name="selected?DropDown"]>div>kds-label+div')}
    get selectEventTypeDropdown() {return $('mx-single-select[id="event-type-select"]>div>kds-label+div')}
    get selectAisleDropdown() {return $('mx-multi-select[name="selectedAisle"]>div>div')}

    get dropdownList()                   {return ('#dropDownName-select>div>div>div>div>mx-multi-select-list>ul>div>mx-menu-item>li>div>div.w-full')}
    get selectAllValue() {return ('#dropDownName-select>div>div>div>div>mx-multi-select-list>ul>mx-menu-item>li>div>div.w-full>kds-label>label')}
    get aisleDropdownList() {return $$('#aisle-select>div>div>div>div>mx-multi-select-list>ul>div>mx-menu-item>li>div>div.w-full>kds-label>label')}
    get eventTypeList() {return $$('#event-type-select>div>div>mx-single-select-list>ul>div>mx-single-select-list-item>div>li')}
    get btnSearchCriteria() {return $('kds-buttongroup-member[id="search-criteria-button"]>label>span')}
    get searchPopup() {return $('#searchPopup')}
    get btnClosePopUp() {return $('mx-modal-body[id="mx-modal-id"] + mx-modal-footer > div > div.mx-modal-footer-container-right > kds-button:nth-child(2) > button')}
    get dropButtons() {return ('kds-buttongroup-member[reader-label="?item"]')}
    get tableValue() {return $('div#page tbody')}
    get tableRows() {return $$('div#page tbody tr')}
    get tableColumns() {return $$('div#page tbody tr:nth-child(1) td')}
    get tableNthColumns() {return $$('div#page tbody tr:nth-child(' + rowIndex + ') td')}
    get nextButtonPagination(){return $('p-paginator > div > button.p-ripple.p-element.p-paginator-next.p-paginator-element.p-link')}
    get columnNth() {return $$('table>tbody>tr>td:nth-child(2)')}
    get previousButtonClass() {return $('//button[contains(@class, "p-paginator-first")]')}
    get spinner() {return $('ui-table-loading-icon pi-spin pi pi-spinner')}
    get moduleLink() {return $('#page > app-breadcrumb > div > ol > li:nth-child(3) >a')}
    get btnExportToExcel()            {return $('kds-buttongroup>kds-buttongroup-member[id="export-to-excel-button"]>label')}
    get paginationLine()               {return $('span[class="ui-paginator-pages"]')}
    get pageNumbers()                  {return $$('span[class="ui-paginator-pages"]>a:nth-child("+i+")')}
    get divColumnList()                 {return $$('tr[class="ng-star-inserted"]>td:nth-child(2)')}
    get pageDropdown()                 {return $('p-paginator > div > p-dropdown > div')}
    get pageDropdownText()           {return $('p-dropdown[styleclass="p-paginator-rpp-options"]>div>span')}
    get upcColumnList()                 {return $$('tr[class="ng-star-inserted"]>td:nth-child(1)')}
    get pageDropdownList()             {return $$('ul[class="p-dropdown-items ng-star-inserted"]>p-dropdownitem>li')}
    get backButtonPagination()             {return $('p-paginator>div>button.p-ripple.p-element.p-paginator-first.p-paginator-element>angledoublelefticon')}
    get btnPageOne()                  {return $('p-paginator>div>button+span>button:nth-child(1)')}
    get tableHeaders()                {return $$('table[class="p-datatable-table p-datatable-scrollable-table p-datatable-resizable-table p-datatable-resizable-table-fit ng-star-inserted"]>thead>tr>th')}
    get tableCheckBox() {return $('div#page thead >tr >th >p-tableheadercheckbox')}
    get tableFirstData() {return $('div#page tbody tr:nth-child(1)>td:nth-child(1)')}
    get tableFirstRowData() {return $('div#page tbody tr:nth-child(1)')}
    get columnLink() {return $(`div#page tbody td:nth-child(${childIndex}) a`)}
    get columnText() {return $(`table > tbody > tr:nth-child(1) > td:nth-child(${childIndex})`)}
    get columnCheckbox() {return $(`div#page thead > tr > th:nth-child(${childIndex}) > input`)}
    get selectAisleDropDown() {return $('div>[name="selectedAisle"]>div>div>span')}
    get nthColumnList() {return $$(`div#page tbody>tr>td:nth-child(${childIndexNumber})`)}
    get btnBatchDownload() {return $('kds-buttongroup-member[id="batch-download-button"]>label')}
    get activePaginationNum() {return $('p-paginator>div>span.p-paginator-pages.ng-star-inserted>button.p-ripple.p-element.p-paginator-page.p-paginator-element.p-link.p-highlight.ng-star-inserted')}
    get columnCheckboxes()         {return ('input[name="checkBoxName"]')}
    get rowCheckBoxList()          {return $$('table>tbody>tr>td>p-tablecheckbox>div')}
    get checkBoxList()                {return $$('tbody>tr>td.p-element.p-frozen-column>p-tablecheckbox>div>div.p-checkbox-box.p-component')}
    get tableRowList() {return $$('p-table>div>div>table>tbody>tr')}
    get tabDropdowns() {return ('mx-multi-select[name="selected?DropDown"]>div')}
    get footerEntryString() {return $('p-paginator[class="p-element ng-star-inserted"]>div>span.p-paginator-current.ng-star-inserted')}
    get navigatorTabBtn() {return $('app-navigator>div>div>ul>li:nth-child(1)>a')}
    get selectAllOption() {return $('div>ers-multi-select-list>ul>ers-menu-item>li>div>kds-label>label')}
    get closeBtn() {return $('ers-button[aria-label="Close"]')}
    get divisionErrorMessage() {return $('#ers-basic-modal>div:nth-child(3)>span')}
    get filterCriteriaText() {return $('mx-single-select[id="list-filter-criteria-dropdown"]>div>kds-label>label')}
    get storeCountCheckbox() {return $(`div#page thead > tr > th:nth-child(9) > input`)}
    get clickableColumnElements() {return $(`table > tbody > tr:nth-child(${rowIndexNum}) > td:nth-child(${childIndex}) > div > a`)}
    get checkBoxHeaderColumn() {return $(`p-tableheadercheckbox > div > div.p-checkbox-box`)}
    get cancelDropdownBtn() {return $(`button > kds-icon-close > kds-icon > svg`)}
    get criteriaSearchDivision() {return $('mx-multi-select[id="div-select"]>div>div.mx-dropdown-input-wrapper.disabled>span')}
    get criteriaSearchDropdown() {return ('mx-multi-select[id="name-select"]>div>div.mx-dropdown-input-wrapper.disabled')}
    get columnHeader() {return $(`div#page thead th:nth-child(${childIndex})`)}
    get footerString() {return $('span.ui-paginator-current.ng-star-inserted')}
    get paginationBtn()                                {return $('p-paginator>div>p-dropdown>div>label')}
    get searchFirstDropDownItem() {return ('#dropdown-select * kds-checkbox > input')}
    get clearButtonList() {return $$('kds-buttongroup>kds-buttongroup-member')}
    get clearAllButton() {return $('kds-buttonGroup-member[id="clear-all-button"]>label')}
    get clearListButton() {return $('kds-buttongroup-member[id="clear-list-button"]>label')}
    get topCheckBox() {return $('div[class="p-datatable-wrapper"]>table>thead>tr>th>p-tableheadercheckbox')}
    get navBarHeader() {return $('a[id="menu"]')}
    get navBarList() {return $$('#headerWrapper>app-header-nav>div>div>ul>li>ul')}
    get paginationLastPage() {return $('p-paginator>div>button.p-ripple.p-element.p-paginator-last.p-paginator-element.p-link.ng-star-inserted')}
    get navBarHeaderList() {return $$('div[class="navbar-header"] +div>ul>kds-tooltippable>li>a')}
    get breadCrumb() {return $('#page>app-breadcrumb>div>ol>li:nth-child(3)>a')}
    get navDropdownCloseBtn() {return ('mx-multi-select[name="selected?Dropdown"]>div>div>kds-icon-close')}
    get leftFirstPage() {return $('button > angledoublelefticon > svg')}
    get applicationsMenu()                      {return $(`li[class="nav-item dropdown multi-level-dropdown"]>#menu`)}
    get spfNavigatorModule()                    {return $$(`#headerWrapper > * a[href='/navigator']`)}
    get spfNavigatorModule2()                   {return $$(`ul > li.nav-item.dropdown.multi-level-dropdown.open > ul > li > a`)}
    get incorrectUserAndPasswordText()          {return $('div[id="info-container"]>p')}
    get loginCancelButton()                     {return $('button[id="btnCancel"]')}
    get divisionDropdownDefaultSelectedText()                     {return $('#divisionMultiSelect > div > div> span')}
    get storeDropdownDefaultSelectedText()                     {return $('#store-select> div > div> span')}

    async openNavigatorPage(navigatorText){
        await (await this.applicationsMenu).waitForDisplayed({ timeout: 9000 });
        const isMenuDisplayed = await this.applicationsMenu.isDisplayed();
        if (isMenuDisplayed) {
            await super.clickOn(await this.applicationsMenu);
        } else {
            console.error("Applications menu is not displayed !");
        }
        const navigatorModuleList = await this.spfNavigatorModule;
        for(let i=0;i<navigatorModuleList.length;i++){
                if(await navigatorModuleList[i].getText() === navigatorText) {
                    await super.clickOn(await navigatorModuleList[i]);
                }
        }
    }

    async genericClickDropdown(locator) {
        let dropdown;
        if (locator === 'EventType') {
            dropdown = await this.selectEventTypeDropdown;
        } else if(locator === 'Aisle'){
            dropdown = await this.selectAisleDropdown;
        } else {
            await super.waitBrowserToLoadPage(2000);
            dropdown = await $(this.selectDropdown.replace('?DropDown', locator));
        }
        await dropdown.waitForClickable({timeout: 15000})
        await super.clickOn(await dropdown);
    }

    async setValues(Locator, dropdownListLocatorName, selectValue) {
        for (let len = 0; len < selectValue.length - 1; len++) {
            await this.genericClickDropdown(Locator);
            let setText = $((this.inputFilterName).replace('?DropDown', Locator))
            await super.waitBrowserToLoadPage(1000)
            await setText.setValue(await selectValue[len])
            let text;
            if (Locator === 'Division') {
                text = await $$((this.dropdownList).replace('dropDownName-select', dropdownListLocatorName));
            } else {
                text = await $$((this.dropdownList).replace('dropDownName', dropdownListLocatorName));
            }
            await super.waitBrowserToLoadPage(2000)
            for (let valueLength = 0; valueLength < selectValue.length - 1; valueLength++) {
                for (let index = 0; index < text.length; index++) {
                    let appList = await text[index]
                    if (await text[index].getText() === await selectValue[valueLength]) {
                        await appList.click();
                        break;
                    }
                }
            }
        }
        await browser.keys('Escape')
        !expect(super.doIsDisplay(this.dropdownList))
    }

    async searchByValue(option, value) {
        const optionDropdown = await this.optionDropdown;
        await optionDropdown.waitForClickable({timeout: 15000});
        await optionDropdown.waitForDisplayed({
            timeout: 15000,
            timeoutMsg: 'Error: Search page did not appear before timeout'
        })
        if (option !== 'UPC') {
            await optionDropdown.click();
            const selectOption = await this.selectOption;
            for (let index = 0; index < selectOption.length; index++) {
                let dropdownList = selectOption[index]
                if (await selectOption[index].getText() === option) {
                    await selectOption[index].scrollIntoView()
                    await dropdownList.click();
                    break;
                }
            }
        }
        await this.optionText.waitForExist({timeout: 4000, timeoutMsg: 'not displaying in given time'})
        let text = await this.optionText.getText();
        await super.waitBrowserToLoadPage(2000)
        await expect(text).toEqual(await option);
        const valueField = await this.enterValue;
        await valueField.waitForExist({timeout: 2000, timeoutMsg: 'not displaying in given time'})
        await valueField.setValue(value);
    }

    async clickOnChkBox(index) {
        await this.selectCheckBox(index).waitForDisplayed();
        await this.selectCheckBox(index).click()
    }

    async selectCheckBox(index, divisionName) {
        let box = await $((`#? * mx-menu-item:nth-child(${index}) > li > div > div.w-full > kds-label > label > kds-checkbox > input`).replace('?', divisionName));
        await box.scrollIntoView({behavior: 'smooth'});
        await box.click();
        await browser.keys('Escape');
    }

    async selectEvent(index) {
        let box = await $(`#event-type-select > div > div.mx-single-select-container > mx-single-select-list > ul > div > mx-single-select-list-item:nth-child(${index}) > div > li`)
        await box.scrollIntoView({behavior: 'smooth'})
        await box.click()
        await browser.keys('Escape')
    }

    async genericClearOptions(locator) {
        let clearOptions = $(this.dropButtons.replace('?item', locator))
        await super.clickOn(clearOptions);
    }

    async selectMultipleValues(dropdownName, dropdownListLocatorName, selectValue) {
        await super.waitBrowserToLoadPage(2000)
        await this.genericClickDropdown(dropdownName);
        await super.waitBrowserToLoadPage(1000)
        let text;
        if (dropdownName === 'Division') {
            text = await $$((this.dropdownList).replace('dropDownName-select', dropdownListLocatorName));
        } else {
            text = await $$((this.dropdownList).replace('dropDownName', dropdownListLocatorName));
        }
        for (let len = 0; len < await selectValue.length; len++) {
            for (let index = 0; index < text.length; index++) {
                let appList = await text[index]
                let textList = await text[index].getText();
                    if ( await (await selectValue[len]).includes(await textList)) {
                    await appList.click();
                    break;
                }
            }
        }
        await browser.keys('Escape')
        !expect(super.doIsDisplay(await $$((this.dropdownList).replace('dropDownName', dropdownListLocatorName))));
    }

    async getDropdownValue(Locator, dropdownListLocatorName, singleDropdownValueName) {
        await this.genericClickDropdown(Locator);
        await super.waitBrowserToLoadPage(5000)
        let text;
        if (Locator === 'Division') {
            text = await $$((this.dropdownList).replace('dropDownName-select', dropdownListLocatorName));
        } else if (Locator === 'EventType') {
            text = await this.eventTypeList;
        } else {
            text = await $$((this.dropdownList).replace('dropDownName', dropdownListLocatorName));
        }
        let appList = []
        for (let index = 0; index < text.length; index++) {
            appList.push(await text[index].getText())
        }
        let finalValue;
        if (Locator === 'Division') {
            finalValue = await appList[appList.length - 5]
        } else {
            finalValue = await appList[appList.length]
        }
        await expect(text).toBeElementsArrayOfSize(appList.length)
        if (Locator === 'EventType') {
            await this.selectEvent(appList.length - 1)
            await super.waitBrowserToLoadPage(3000)
            return 'SEA'
        } else if (Locator === 'Division') {
            await this.selectCheckBox(appList.length - 5, singleDropdownValueName);
        } else {
            await this.selectCheckBox(appList.length - 1, singleDropdownValueName);
        }
        return finalValue
    }

    async selectSingleValue(dropdownName, dropdownListLocatorName, selectValue) {
        await super.waitBrowserToLoadPage(1000);
        await this.genericClickDropdown(dropdownName);
        await super.waitBrowserToLoadPage(1000);
        let dropdownItems;
        if (dropdownName === 'Division') {
            await super.waitBrowserToLoadPage(1000);
            dropdownItems = await $$(this.dropdownList.replace('dropDownName-select', await dropdownListLocatorName));
        } else if (dropdownName === 'EventType') {
            dropdownItems = await this.eventTypeList;
        } else {
            dropdownItems = await $$(this.dropdownList.replace('dropDownName', await dropdownListLocatorName));
        }
        if (!dropdownItems.length) {
            throw new Error(`No dropdown items found for "${dropdownName}".`);
        }
        // Normalize `selectValue` for safer matching
        let normalizedSelectValue = await (await selectValue); 
        // Find and click the matching dropdown value
        let matchingItem = null;
        for (let item of dropdownItems) {
            let itemText = await(await item.getText()); 
            if (itemText === normalizedSelectValue) {
                matchingItem = item;
            }
        }
        await super.waitBrowserToLoadPage(2000);
        if (matchingItem) {
            await matchingItem.scrollIntoView();
            await matchingItem.waitForClickable({timeout:15000})
            await matchingItem.click();
        } 
        else {
            throw new Error(`Value "${selectValue}" not found in dropdown "${dropdownName}".`);
        }
        await browser.keys('Escape');
    }

    async searchCriteria() {
        let searchBtn = await this.btnSearchCriteria;
        await super.clickOn(await searchBtn)
    }

    async closeSearchCriteria() {
        let closeBtn = await this.btnClosePopUp;
        await super.clickOn(closeBtn)
    }

    async validateDropdownValue(dropdownName, value) {
        let elLocator;
        if (dropdownName === 'EventType') {
            elLocator = await $(this.selectDropdownEventType.replace('?DropDown', dropdownName));
        } else {
            elLocator = await $(this.selectDropdown.replace('?DropDown', dropdownName));
        }
        await elLocator.waitForExist({timeout: 3000, timeoutMsg: "Element did not display before timeout"});
        let elLocatorText = await elLocator.getText();
        if (await elLocatorText.length > 0) {
            await expect(await elLocatorText).toEqual(value);
        } else {
            console.error(`No option found in the dropdown with name '${dropdownName}'`);
        }
    }

    async validateSearchCriteriaDropdownValue(dropdownLocator, value) {
        let elLocator;
        elLocator = await $(this.searchCriteriaSelectDropdown.replace('dropdownName', dropdownLocator))
        await elLocator.waitForExist({timeout: 3000, timeoutMsg: "Element did not display before timeout"})
        let elLocatorText = await elLocator.getText();
        if (elLocatorText !== null) {
            await expect(await elLocatorText).toEqual(await value);
        }
    }

    async validateEmptySelectList() {
        await expect(await this.enterValue.getText()).toEqual("")
    }

    async pagination(pageNum) {
        let nextButtonClass = await this.nextButtonPagination
        let spinner = await this.spinner
        // let counter = 0;
        let columnData = []
        let columnRows = await this.columnNth
        let initialArraySize = columnRows.length
        while (await nextButtonClass.isEnabled()) {
            let getAttrContains = await this.nextButtonPagination.getAttribute("class")
            let ColumnRows = await this.columnNth
            let arrayLength = ColumnRows.length
            for (let index = 0; index < arrayLength; index++) {
                columnData.push(await ColumnRows[index].getText())
            }
            if (arrayLength >= 1) {
                await nextButtonClass.click()
            } else {
                break;
            }
            if (await spinner.isExisting()) {
                await browser.waitUntil(
                    async () => {
                        return (!await spinner.isExisting(),
                                {
                                    timeout: 59000,
                                    timeoutMsg: 'expected spinner to be removed '
                                }
                        );
                    }
                );
            }
            await super.waitBrowserToLoadPage(2000)
            if (getAttrContains.includes("p-disabled") || await this.activePaginationNum.getText() === pageNum) {
                break;
            }
            //  counter++
        }
        //  eslint-disable-next-line
        // validates going to first page and count no. of rows
        const firstPage = await this.previousButtonClass
        await super.clickOn(firstPage)
        await super.waitBrowserToLoadPage(3000)
        let finalArraySize = columnRows.length
        expect(initialArraySize).toBeElementsArrayOfSize(finalArraySize);
        return columnData.length;
    }

    async getPageNumberList() {
        let chevDropdown = await this.pageDropdown;
        await chevDropdown.waitForExist({
            timeout: 9000,
            timeoutMsg: 'Error: page numbers did not appear before timeout'
        })
        await super.clickOn(chevDropdown)
        await super.waitBrowserToLoadPage(3000)
        let chevDropdownListTxt = await this.pageDropdownList;
        let dropdownList = [];
        await super.waitBrowserToLoadPage(2000)
        for (let index = 0; index < await chevDropdownListTxt.length; index++) {
            dropdownList.push(await chevDropdownListTxt[index].getText());
        }
        await browser.keys('Escape');
        return dropdownList;
    }

    async selectPageNumber(columnCount) {
        await super.waitBrowserToLoadPage(2000)
        let pageNumberLocator = await this.pageDropdown;
       if(await pageNumberLocator.isClickable()){
           await pageNumberLocator.click();
       }
        await super.waitBrowserToLoadPage(3000)
        let pageDropdownTxt = await this.pageDropdownList;
        for (let index = 0; index < pageDropdownTxt.length; index++) {
            if (await pageDropdownTxt[index].getText() === columnCount) {
                await pageDropdownTxt[index].scrollIntoView();
                await super.waitBrowserToLoadPage(3000)
                 await (await pageDropdownTxt[index]).click();
                break;
            }
        }
        await browser.keys('Escape');
    }

    async waitForTableLoad() {
        let table = await this.tableValue;
        await table.waitForDisplayed({timeout: 10000, timeoutMsg: 'Err: Table did not appear before time out'})
        await expect(table).toBeDisplayedInViewport()
    }

    async getRowCount() {
        await this.waitForTableLoad()
        let columnValues = [];
        let nextButton = await this.nextButtonPagination;
        let nextBtnClassName = await this.nextButtonPagination.getAttribute("class");
        let column = await this.divColumnList;
        columnValues.push(column.length);
        while (nextBtnClassName) {
            await nextButton.scrollIntoView();
            if( await nextButton.isEnabled() && await nextButton.isClickable()){
                await nextButton.click();
                await super.waitBrowserToLoadPage(3000)
                column = await this.divColumnList
                columnValues.push(column.length)
            }
            await super.waitBrowserToLoadPage(2000)
            nextBtnClassName = await this.nextButtonPagination.getAttribute("class");
            if (nextBtnClassName.includes("disabled") ) {
                break;
            }
        }
        return columnValues;
    }

    async getTotalRowCountNum() {
        await this.waitForTableLoad();
        let columnValues = [];
        let nextButton = await this.nextButtonPagination;
        while (nextButton.isEnabled()) {
            let nextBtnClassName = await this.nextButtonPagination.getAttribute("class");
            let column = await this.divColumnList;
            columnValues.push(column.length);
            await nextButton.scrollIntoView();
            await super.clickOn(nextButton);
            await super.waitBrowserToLoadPage(3000);
            if (nextBtnClassName.includes("p-disabled")) {
                break;
            }
        }
        return columnValues.reduce((accumulator, currentValue) => {
            return accumulator + currentValue;
        }, 0);
    }

    async exportToExcel() {
        (await this.moduleLink).waitForClickable({timeout: 10000})
        await super.clickOn(await this.moduleLink)
        let exportBtn = await this.btnExportToExcel;
        await super.clickOn(await exportBtn)
        await super.waitBrowserToLoadPage(2000)
    }

    async getAllUIData() {
        await super.waitBrowserToLoadPage(2000);
        const columns = await this.tableColumns;
        const columnCount = columns.length;
        const hasCheckbox = await this.tableCheckBox.isExisting();
        const startColIndex = hasCheckbox ? 1 : 0;
        let uiData = [];
        const nextButton = $('//button[contains(@class, "p-paginator-next")]');

        let nextButtonClassName;
         nextButtonClassName = await this.nextButtonPagination.getAttribute("class")
        await super.waitBrowserToLoadPage(1000)
        while (nextButtonClassName) {
            const rows = await this.tableRows;
            const rowCount = rows.length;
            for (let rowIndex = 1; rowIndex <= rowCount; rowIndex++) {
                let rowData = [];
                for (let colIndex = startColIndex; colIndex < columnCount; colIndex++) {
                    const cellSelector = `//table//tr[${rowIndex}]//td[${colIndex + 1}]`;
                    const cellElement = await $(cellSelector);
                    const cellText = await cellElement.getText();
                    rowData.push(cellText);
                }
                uiData.push(rowData);
            }
            if(await nextButton.isClickable()){
                await super.clickOn(nextButton)
            }
            else {
                break;
            }

        }
        return uiData;
    }

    async getTableHeaderNames(tableHeaders) {
        let tableHeaderValues = [];
        let headers = await tableHeaders;
        let startValue
        if (await this.tableCheckBox.isExisting()) {
            startValue = 1
        } else {
            startValue = 0
        }
        for (let i = startValue; i <= headers.length - 1; i++) {
            tableHeaderValues.push(await headers[i].getText())
        }
        return tableHeaderValues
    }

    async getDropDownList(LocatorName, dropdownListLocatorName) {
        await this.genericClickDropdown(LocatorName);
        let text;
        if (await LocatorName === 'Division') {
            text = await $$((this.dropdownList).replace('dropDownName-select', await dropdownListLocatorName));
        } else {
            text = await $$((this.dropdownList).replace('dropDownName', await dropdownListLocatorName));
        }
        let appList = []
        for (let index = 0; index < await text.length; index++) {
            appList.push(await text[index].getText())
        }
        return appList
    }

    // clicking on # of Stores link in Mapping report UI column
    async getLinkText(key) {
        await this.waitForTableLoad()
        const headerData = await this.tableHeaders;
        await super.waitBrowserToLoadPage(2000)
        let linkText
        for (let index = 0; index < headerData.length; index++) {
            if ((await headerData[index].getText()) === (key)) {
                childIndex = index + 1;
                await super.waitBrowserToLoadPage(4000)
                linkText = await (await this.columnText.getText());
                if (await this.columnText.isClickable()) {
                    const columnEl = await this.columnLink;
                    if (await columnEl.isClickable()){
                        await super.clickOn(await this.columnLink);
                        await super.waitBrowserToLoadPage(2000)
                }
                }
                break
            }
        }
        return linkText
    }

    /*clicking on # of Stores link in Mapping report UI column*/
    async selectColumnCheckBox(key) {
        await this.waitForTableLoad()
        const headerData = await this.tableHeaders;
        await super.waitBrowserToLoadPage(2000)
        for (let j = 0; j < headerData.length; j++) {
            if ((await headerData[j].getText()) === (key)) {
                childIndex = j + 1;
                await super.clickOn(await this.columnCheckbox)
                break
            }
        }
    }

    async validateNewTab(parentGUID) {
        let allGUID = await browser.getWindowHandles()
        for (let i = 0; i < await allGUID.length; i++) {
            if (await allGUID[i] !== parentGUID) {
                await browser.switchToWindow(await allGUID[i])
                break;
            }
        }
    }

    /*dynamically finds column tile and loops the column values*/
    async getColumnArrayList(header) {
        await super.waitBrowserToLoadPage(3000)
        const headerData = await this.tableHeaders;
        await super.waitBrowserToLoadPage(2000)
        let arrayList = await this.getArrayElementsText(headerData);
        let headerIndex = await arrayList.indexOf(header);
        await super.waitBrowserToLoadPage(2000)
        childIndexNumber = headerIndex + 1;
        let columnList = await this.nthColumnList;
        let columnTextList = await this.getArrayElementsText(columnList);
        return columnTextList;
    }

    async batchDownload() {
        let batchDownloadBtn = await this.btnBatchDownload;
        await browser.waitUntil(() => batchDownloadBtn.isDisplayed({
            timeout: 15000,
            timeoutMsg: 'Error: button did not display before timeout'
        }))
        await browser.waitUntil(() => batchDownloadBtn.isClickable({
            timeout: 15000,
            timeoutMsg: 'Error: button is not clickable before timeout'
        }))
        await super.clickOn(batchDownloadBtn)
        // await super.waitBrowserToLoadPage(3000)
    }

    async getNPagesUIData(pageNum) {
        let table = await this.tableValue;
        await table.waitForExist({timeout: 5900, timeoutMsg: 'Error: UI table of data did not appear before timeout'})
        await expect(table).toBeDisplayedInViewport()
        let columns = await this.tableColumns;
        let uiData = []
        let nextButton = await this.nextButtonPagination
        let startValue
        if (await this.tableCheckBox.isExisting()) {
            startValue = 1
        } else {
            startValue = 0
        }
        let nextButtonClassName = await this.nextButtonPagination.getAttribute("class")
        while (await nextButtonClassName) {
            for (rowIndex = 1; rowIndex <= await this.tableRows.length; rowIndex++) {
                let uiRowData = []
                for (colIndex = startValue; colIndex < columns.length; colIndex++) {
                    let data = await this.tableNthColumns[colIndex]
                    uiRowData.push(await data.getText())
                }
                uiData.push(uiRowData)
            }
            if(await nextButton.isClickable()){
                await super.clickOn(nextButton)
                await super.waitBrowserToLoadPage(1000)
                await this.btnPageOne.waitForClickable({
                    timeout: 5900,
                    timeoutMsg: 'Error: Page one did not appear before timeout'
                })
            }

            if (nextButtonClassName.includes("p-disabled") || await this.activePaginationNum.getText() === pageNum) {
                break;
            }
            await browser.keys('Escape');
        }
        return uiData
    }

    async clickColumnCheckBox(tableLocatorName, locator) {
        await this.waitForTableExist(tableLocatorName);
        let checkBox = await $(this.columnCheckboxes.replace('checkBoxName', locator));
        await super.clickOn(checkBox);
       let checkBoxSelected = await checkBox.isSelected();
        await expect(await checkBoxSelected).toEqual(true);
    }

    async validateColumnCheckboxCleared(tableLocatorName, locator) {
        await this.waitForTableExist(tableLocatorName);
        let checkBox = await $(this.columnCheckboxes.replace('checkBoxName', locator));
        await super.waitBrowserToLoadPage(2000);
        let checkBoxUnSelected = !(await checkBox.isSelected());
        await expect(checkBoxUnSelected).toEqual(true);
    }

    async validateCheckboxesUnselected(tableLocatorName, locator) {
        await this.waitForTableExist(tableLocatorName);
        let checkBox = await $(this.columnCheckboxes.replace('checkBoxName', locator));
        await super.waitBrowserToLoadPage(2000);
        let checkBoxSelected = await checkBox.getAttribute('class');
        if(checkBoxSelected.includes('ng-untouched')){
            await expect(checkBoxSelected).toEqual(true);
        }
    }

    async clickRowCheckBox(tableLocatorName, checkBoxIndex) {
        await this.waitForTableExist(tableLocatorName);
        let checkBox;
        let checkBoxList = await this.rowCheckBoxList;
        for (let index = 0; index < checkBoxList.length - checkBoxIndex; index++) {
            checkBox = await checkBoxList[index];
            await super.clickOn(checkBox);
        }
    }

    async returnNumOfCheckBox(tableLocatorName) {
        await this.waitForTableExist(tableLocatorName);
        let checkList = await this.checkBoxList;
        let count = 0;
        for (let i = 0; i < checkList.length; i++) {
            if (await ((await checkList[i].getAttribute("class")).includes('p-highlight')) === true)
                count++;
        }
        return count;
    }

    async clickTabDropdown(locatorName) {
        let dropdown = $(this.tabDropdowns.replace('?DropDown', locatorName));
        await super.waitBrowserToLoadPage(6000);
        await super.clickOn(dropdown);
    }

    async clickSelectAllBtn(dropdownLocator, errorMessage) {
        await this.genericClickDropdown(dropdownLocator)
        let selectAllBtn = await this.selectAllOption
        let closeBtn = await this.closeBtn
        await super.waitBrowserToLoadPage(3000);
        await super.clickOn(selectAllBtn);
        await expect(await this.divisionErrorMessage.getText()).toEqual(errorMessage);
        await super.waitBrowserToLoadPage(3000);
        await super.clickOn(closeBtn);
    }

    async clickFilterCriteria(option) {
        await super.waitBrowserToLoadPage(2000)
        const optionDropdown = await this.optionDropdown;
        await optionDropdown.waitForDisplayed({timeout: 3000})
        if (option !== 'UPC') {
            await super.clickOn(optionDropdown);
            const selectOption = await this.selectOption;
            for (let index = 0; index < selectOption.length; index++) {
                let dropdownList = selectOption[index]
                if (await selectOption[index].getText() === option) {
                    await selectOption[index].scrollIntoView()
                    await super.clickOn(dropdownList);
                    const valueField = await this.enterValue;
                    await super.clickOn(valueField);
                    break;
                }
            }
        } else {
            await console.error('Error: DbKeys are not pasted successfully')
        }
    }

    async unclickColumnCheckBox(tableLocatorName, locator) {
        await this.waitForTableExist(tableLocatorName);
        let checkBox = $(this.columnCheckboxes.replace('checkBoxName', locator));
        let checkBoxSelected = await checkBox.isSelected();
        await expect(checkBoxSelected).toEqual(true);
        await super.clickOn(checkBox);
        await super.waitBrowserToLoadPage(2000);
        checkBoxSelected = await checkBox.isSelected();
        await expect(checkBoxSelected).toEqual(false);
    }

    async columnCheckBoxIsClickable(tableLocatorName, locator) {
        await this.waitForTableExist(tableLocatorName);
        let checkBox = $(this.columnCheckboxes.replace('checkBoxName', locator));
        let flag = false;
        if (await checkBox.waitForClickable({timeout: 6000, timeoutMsg: "Element is not clickable in given time"})) {
            flag = true;
        }
        return flag;
    }

    async clickElement(tableLocatorName, locator) {
        await this.waitForTableExist(tableLocatorName);
        let firstCheckBox;
        if (locator !== '') {
            firstCheckBox = await $(this.columnCheckboxes.replace('checkBoxName', locator));
        }
        if(firstCheckBox){
            await firstCheckBox.click();
            await super.waitBrowserToLoadPage(2000);
        }
        }

    async clickElements(tableLocatorName, locatorOne, locatorTwo) {
        await this.waitForTableExist(tableLocatorName);
        let firstCheckBox, secondCheckBox;
        if (locatorOne === '') {
            firstCheckBox = null;
        } else {
            firstCheckBox = await $(this.columnCheckboxes.replace('checkBoxName', locatorOne));
        }
        secondCheckBox = await $(this.columnCheckboxes.replace('checkBoxName', locatorTwo));
        if (firstCheckBox) {
            await firstCheckBox.click();
            await super.waitBrowserToLoadPage(2000);
        }
        if(secondCheckBox){
            await secondCheckBox.click();
            await super.waitBrowserToLoadPage(2000);
        }

    }

    async sumOfElementsClickable(columnName, rowLength) {
        await super.waitBrowserToLoadPage(2000)
        const headerData = await this.tableHeaders;
        await super.waitBrowserToLoadPage(2000)
        let arrayList = await this.getArrayElementsText(headerData);
        let headerIndex = arrayList.indexOf(columnName);
        await super.waitBrowserToLoadPage(2000);
        let sumOfClickableEls = 0;
        for (let index = 1; index <= rowLength; index++) {
            childIndex = headerIndex + 1;
            rowIndexNum = [index]
            let columnList = await this.clickableColumnElements;
            await super.waitBrowserToLoadPage(2000);
            if (await columnList.isClickable()) {
                sumOfClickableEls += 1
            }
        }
        return sumOfClickableEls;
    }

    async matchDataListToTableColumn(dataList, arrayOfArrayData, arrayOfArrayDataColumnName) {
        let tableDataIndex = 0;
        let arrayOfArrayDataColumn = await this.findIndexNumFromArray(await this.tableHeaders, arrayOfArrayDataColumnName);
        if (dataList.length === arrayOfArrayData.length) {
            for (tableDataIndex = 0; tableDataIndex < arrayOfArrayData.length; tableDataIndex++) {
                for (let storeListIndex = 0; storeListIndex < dataList.length; storeListIndex++) {
                    if (arrayOfArrayData[tableDataIndex][arrayOfArrayDataColumn] === dataList[storeListIndex]) {
                        dataList.splice(storeListIndex, 1);
                        break;
                    }
                }
            }
        }
        return await (tableDataIndex === arrayOfArrayData.length) && (dataList.length === 0);
    }

    async clickColumnHeader(tableHeaders, singleHeaderName) {
        let headerIndex = await this.findIndexNumFromArray(await tableHeaders, singleHeaderName)
        childIndex = headerIndex + 1;
        let headerEl = await this.columnHeader;
        await super.clickOn(headerEl);
    }

    // This function does not work for single page; need to update the function
    async footerLabelValidation() {
        let isLabelMatch = true;
        let tableSizes = await this.getRowCount();
        const paginationLabels = [].concat(await this.footerRowCalculator(tableSizes));
        const firstPage = this.previousButtonClass
        if (await firstPage.isEnabled()){
            await this.clickOn(await firstPage);
        }
        await this.waitBrowserToLoadPage(2000);

        let pageIndex = 0;

    while (true) {
        const paginationText = await (await this.footerEntryString).getText();

        try {
            expect(paginationText).toEqual(paginationLabels[pageIndex]);
        } catch (error) {
            isLabelMatch = false;
            break;
        }

        const nextBtnClassName = await this.nextButtonPagination.getAttribute("class");

        if (nextBtnClassName.includes("p-disabled")) {
            break; // Last page reached
        }

        await this.clickOn(await this.nextButtonPagination);
        await this.waitBrowserToLoadPage(4000);
        pageIndex++;
    }

    return isLabelMatch;
}

    async footerRowCalculator(pageRowCountArray) {
        let grandTotal = 0;
        await pageRowCountArray.forEach(num => {
            grandTotal += num
        });
        let paginationLabels = [];
        for (let index = 0, runningTotal = 0; index < pageRowCountArray.length; index++) {
            let paginationLabel = "Showing " + (runningTotal + 1) + " to ";
            runningTotal += pageRowCountArray[index];
            paginationLabel += runningTotal + " of " + grandTotal + " entries";
            paginationLabels.push(paginationLabel);
        }
        return paginationLabels;
    }

    async nextPage() {
        let isNext = true;
        let nextButton = await this.nextButtonPagination;
        let getAttrContains = await this.nextButtonPagination.getAttribute("class")
        if (getAttrContains.includes("disabled")) {
            isNext = false;
        }
        if (await nextButton.isEnabled()) {
            await this.clickOn(await nextButton);
        }
        return isNext;
    }

    async getColumnArrayListMultiplePages(table, columnHeader, numberOfPages) {
        let columnAllData = [];
        for (let pageIndex = 0; pageIndex < numberOfPages; pageIndex++) {
            await this.waitForTableExist(await table);
            await this.waitBrowserToLoadPage(3000);
            let columnPageData = await this.getColumnArrayList(columnHeader);
            for (let columnPageDataIndex = 0; columnPageDataIndex < columnPageData.length; columnPageDataIndex++) {
                columnAllData.push(await columnPageData[columnPageDataIndex]);
            }
            if (pageIndex < numberOfPages - 1) {
                if (!await this.nextPage()) {
                    break;
                }
            }
        }
        return columnAllData;
    }

    async dropDownSelectBySearchFunction(dropDownName, dropdownListName, dropDownElement) {
        await super.waitBrowserToLoadPage(3000);
        await this.genericClickDropdown(dropDownName);
        await super.waitBrowserToLoadPage(3000);
        for (let index = 0; index < dropDownElement.length - 1; index++) {
            let setText = await $((this.inputFilterName).replace('?DropDown', dropDownName));
            await setText.setValue(await dropDownElement[index]);
            if (dropDownName === 'Division') {
                await (await $((this.searchFirstDropDownItem.replace('dropdown-select', dropdownListName))).click());
            } else {
                await super.waitBrowserToLoadPage(2000);
                await this.clickOn(await $(this.searchFirstDropDownItem.replace('dropdown-select', dropdownListName)));
                await super.waitBrowserToLoadPage(1000);
            }
        }
        await browser.keys('Escape');
    }

    async validateFooterEntries(selectedPageNum, pageNum) {
        await super.waitBrowserToLoadPage(2000)
        let nextButton = await this.nextButtonPagination
        while (await nextButton.isEnabled()) {
            let nextBtnClassName = await nextButton.getAttribute("class")
            let footerString = (await this.footerEntryString.getText()).split(' ');
            let startingRowNum = footerString[1];
            let endingRowNum = footerString[3];
            let diff = (parseInt(endingRowNum) - parseInt(startingRowNum)) + 1;
            let rowCount = (await (this.getColumnArrayList('Div'))).length;
            await expect(rowCount).toEqual(diff === selectedPageNum ? selectedPageNum : diff)
            await expect(diff).toEqual(diff === selectedPageNum ? selectedPageNum : diff)
            await super.waitBrowserToLoadPage(2000)
            await super.clickOn(nextButton)
            await super.waitBrowserToLoadPage(2000)
            if (nextBtnClassName.includes("p-disabled") || await this.activePaginationNum.getText() === pageNum) {
                break;
            }
        }
        let backBtn = await this.backButtonPagination;
        await this.clickOn(backBtn)
    }

    async searchCriteriaCompare(navigatorSelections) {
        let isMatch = true;
        let selections = Array();
        selections = await this.searchCriteriaDropDownListText();
        for (let index = 0; index < selections.length; index++) {
            if (!selections[index] === navigatorSelections[index]) {
                isMatch = false;
            }
        }
        return isMatch;
    }

    // This function should be deleted or needs to be re-write because it is wrong in every metrics
    async searchCriteriaDropDownListText() {
        let selections
         selections = [];
        selections.push(await this.getTextOfElement(this.searchCriteriaDivisionDropdown));
        selections.push(await this.getTextOfElement(this.storeDropdown));
        selections.push(await this.getTextOfElement(this.deptDropdown));
        selections.push(await this.getTextOfElement(this.subDeptDropdown));
        selections.push(await this.getTextOfElement(this.commodityDropdown));
        selections.push(await this.getTextOfElement(this.commodityGrpDropdown));
        selections.push(await this.getTextOfElement(this.planogramStatusDropdown));
        selections.push(await this.getTextOfElement(this.fpStatusDropdown));
        selections.push(await this.getTextOfElement(this.eventTypeDropdown));
        selections.push(await this.getTextOfElement(this.eventNameDropdown));

        return selections;
    }

    async selectDropDownValues(locator, dropdownListLocatorName, startIndex, endingIndex) {
        await this.genericClickDropdown(locator);
        let dropdown;
        if (locator === 'Division') {
            dropdown = await $$((this.dropdownList).replace('dropDownName-select', dropdownListLocatorName));
        } else {
            dropdown = await $$((this.dropdownList).replace('dropDownName', dropdownListLocatorName));
        }
        if (await dropdown.length >= await endingIndex) {
            for (let index = startIndex; index <= endingIndex-1; index++) {
                await dropdown[index].click();
            }
        } else {
            console.error("Dropdown does not have enough options")
        }
        await browser.keys('Escape');
    }

    async selectAndGetDropDownValues(locator, dropdownListLocatorName, startIndex, endingIndex) {
        await this.genericClickDropdown(locator);
        await super.waitBrowserToLoadPage(2000)
        let dropdown;
        let dropdownList = [];
        if (locator === 'Division') {
            dropdown = await $$((this.dropdownList).replace('dropDownName-select', dropdownListLocatorName));
        } else {
            dropdown = await $$((this.dropdownList).replace('dropDownName', dropdownListLocatorName));
        }
        if (await dropdown.length >= await endingIndex) {
            for (let index = startIndex; index <= endingIndex-1; index++) {
                await super.waitBrowserToLoadPage(2000)
                await dropdown[index].click();
                dropdownList.push( await (await dropdown[index]).getText())
            }
        } else {
            console.error("Dropdown does not have enough options")
        }
        return dropdownList;
    }

    async getFirstRowColumnText(columnName) {
        await this.waitForTableLoad()
        const headerData = await this.tableHeaders;
        await super.waitBrowserToLoadPage(2000)
        let linkText
        for (let index = 0; index < headerData.length; index++) {
            if ((await headerData[index].getText()) === (columnName)) {
                childIndex = index + 1;
                await super.waitBrowserToLoadPage(4000)
                linkText = await (await this.columnText.getText());
                break
            }
        }
        return linkText
    }

    async matchPageRowCountWithDropDown(RowCountIndex) {
        let isMatch = false;
        let pageRowCounts = [];
        await this.clickOn(await this.pageDropdownText);
        let pageDropdownWebElements = await this.pageDropdownList;
        for (let index = 0; index < pageDropdownWebElements.length; index++) {
            pageRowCounts.push(Number(await this.GetText(pageDropdownWebElements[index])));
        }
        let pageRowCount = pageRowCounts[RowCountIndex];
        await this.waitBrowserToLoadPage(3000)
        await (await this.pageDropdownList[await RowCountIndex]).click();
        await this.waitBrowserToLoadPage(4000);
        let TableRowCount = (await this.tableRows).length;
        await this.waitBrowserToLoadPage(2000)
        let dropdownRowCount = Number(await this.pageDropdownText.getText());
        let pageFooterLabel = (await this.footerEntryString.getText()).split(' ');
        let startingRowNum = pageFooterLabel[1];
        let endingRowNum = pageFooterLabel[3];
        if ((pageRowCount === dropdownRowCount && dropdownRowCount === pageRowCount) || (pageRowCount > TableRowCount && dropdownRowCount > endingRowNum - startingRowNum + 1)) {
            isMatch = true;
        }
        return isMatch;
    }

    async getSearchCriteriaDropdownText(dropdownName) {
        let locator = await $((this.criteriaSearchDropdown).replace('name', dropdownName));
        let text = locator.getText();
        return text;
    }

    async selectSelectAllValue(dropdownName, dropdownListLocatorName, selectValue) {
        await super.waitBrowserToLoadPage(4000);
        await this.genericClickDropdown(dropdownName);
        await super.waitBrowserToLoadPage(2000);
        let textLocator;
        if (await dropdownName === 'Division') {
            textLocator = $(this.selectAllValue.replace('dropDownName-select', dropdownListLocatorName));
        } else {
            textLocator = $(this.selectAllValue.replace('dropDownName', dropdownListLocatorName));
        }
        const text = await textLocator.getText();
        if (await text === selectValue) {
            await textLocator.click();
            await super.waitBrowserToLoadPage(2000);
        }
        await browser.keys('Escape');
    }

    async clickNavDropdownCloseBtn(locatorName) {
        let closeOption = $(this.navDropdownCloseBtn.replace('?Dropdown', locatorName));
        await super.waitBrowserToLoadPage(2000)
        await super.clickOn(closeOption);
        await super.waitBrowserToLoadPage(2000)
    }

    async clickSSNRowNumber (index) {
        const spaceStationNameColumn = $('//th[@id="space-station-name"]');
        await spaceStationNameColumn.waitForClickable({timeout: 10000});
        const spaceStationRowValues = await this.getColumnArrayList('Space Station Name');
        const elementToClick = await $(`//a[normalize-space()='${spaceStationRowValues[index]}']`);
        await elementToClick.waitForClickable({ timeout: 10000 });
        await elementToClick.click();
    }
    async selectDropdownValueById(dropdownId, values) {
        // Convert single value to an array for consistency
        values = Array.isArray(values) ? values : [values];
    
        // Dynamically build the dropdown locator
        let selectLocator = await `//*[@id='${dropdownId}']`;
        let filterLocator = await `//mx-multi-select[@name='${dropdownId}']`;
        
        // Wait for either the ID-based or name-based locator to exist
        await browser.waitUntil(async () => {
        return (await $(selectLocator).isExisting()) || (await $(filterLocator).isExisting());
         }, {
        timeout: 20000,
        timeoutMsg: `Neither ${selectLocator} nor ${filterLocator} dropdown found`
        });

        let selectElement = await $(selectLocator);
        if (!(await selectElement.isExisting())) {
            selectLocator = filterLocator;
            selectElement = await $(selectLocator);
        }
   
        // Click the dropdown first
        await selectElement.waitForClickable({timeout: 20000})
        await selectElement.click();
        await super.waitBrowserToLoadPage(700)
    
        // Loop through each value in the array and select them
        for (const value of values) {
            let valueLocator = ''
            if (value === 'Select All'){
            valueLocator = `${selectLocator}//div[contains(text(),"Select All")]`;  
            }
            else if (value === 'Empty'){
            valueLocator = `(${selectLocator}//div[@title=''])[2]`;
            }
            else {
                valueLocator = `${selectLocator}//div[@title='${value}']`;
            }
            const valueElement = await $(valueLocator);
    
            await valueElement.waitForDisplayed({ timeout: 15000 });
            await valueElement.scrollIntoView();
            await valueElement.click();
            await super.waitBrowserToLoadPage(700)
        }
    
        // Close dropdown
        await browser.keys('Escape');
    }
    async selectMultipleDropdowns(dropdownSelections = []) {
        for (const { id, values } of dropdownSelections) {
            await this.selectDropdownValueById(id, values);
        }
    }

    async clearMultipleDropdowns(dropdowns = []) {
        for (const { id } of dropdowns) {
            await this.clearDropdown(id);
        }
    }
    
    async clearDropdown(id) {
        const closeIcon = await $(`//*[@id='${id}']//kds-icon-close`);
        await closeIcon.waitForDisplayed({ timeout: 15000 });
        await closeIcon.scrollIntoView();
        await closeIcon.click();
    }

     // Wait for files to download
        async waitForDownload (initialFilesCount, timeout = 20000, interval = 500){
            const start = Date.now();
            while ((Date.now() - start) < timeout) {
                const newCount = getFilesCount();
                if (newCount > initialFilesCount) return true;
                await new Promise(resolve => setTimeout(resolve, interval));
            }
            return false;
        }

}
module.exports = new NavigatorPage();
