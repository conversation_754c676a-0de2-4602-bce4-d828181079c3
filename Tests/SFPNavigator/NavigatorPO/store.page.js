const Page = require('../../../GlobalObjectRepository/page.js');

/**
 * sub page containing specific selectors and methods for Store tab
 */

class StorePage extends Page {

    get tabHeaders()                    { return $$('kds-tooltippable[class="kds-Tooltippable hydrated"]>li>a')}
    get moduleLink()                    { return $('#page > app-breadcrumb > div > ol > li:nth-child(3) >a')}

    async selectStoreTab(){
        let tab = await this.tabHeaders
        for (let index = 0; index < tab.length; index++) {
            let appList = await tab[index]
            if (await tab[index].getText() === 'Store') {
                await appList.click();
                break;
            }
        }
        await super.clickOn(await this.moduleLink)
    }

}
module.exports = new StorePage();
