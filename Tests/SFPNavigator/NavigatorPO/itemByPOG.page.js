const Page = require('../../../GlobalObjectRepository/page.js');

/**
 * sub page containing specific selectors and methods for Item By POG tab
 */
class ItemByPOGPage extends Page {

    get moduleLink()                        { return $('#page>app-breadcrumb>div>ol>li:nth-child(3)>a')}
    get searchPopupTable()                  { return $('div[id="searchPopup"]>div>div>div.modal-body>div>div')}
    get searchPopupDivision()               { return $('mx-multi-select[id="div-select"]>div>div.mx-dropdown-input-wrapper.disabled>span')}
    get searchPopupStore()                  { return $('mx-multi-select[id="store-select"]>div>div.mx-dropdown-input-wrapper.disabled>span')}
    get searchPopupPogStatus()              { return $('mx-multi-select[id="pog-status-select"]>div>div.mx-dropdown-input-wrapper.disabled>span')}
    get searchPopupFPStatus()               { return $('mx-multi-select[id="floorplan-select"]>div>div.mx-dropdown-input-wrapper.disabled>span')}
    get moveFlagNColumns()                  { return $$('div#page tbody td:nth-child(15)')}
    get btnFilter()                         { return ('kds-buttongroup-member[text-label="@filter"]>label')}
    get filterBtnList()                     { return $$('div>div>div>kds-buttongroup>kds-buttongroup-member')}
    get itemByPogHeaders()                  { return $$('p-table>div>div>table>thead>tr>th')}
    get entryNumberString()                 { return $('p-paginator>div>span.p-paginator-current')}

    async clickFilterTab(filter) {
        let filterTab=$((this.btnFilter).replace('@filter',filter))
        await super.clickOn(filterTab)
        await super.waitBrowserToLoadPage(1000)
    }

}

module.exports = new ItemByPOGPage();
