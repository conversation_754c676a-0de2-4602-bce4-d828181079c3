const Page = require('../../../GlobalObjectRepository/page.js');

/**
 * sub page containing specific selectors and methods for Floorplans tab
 */
class FloorPlanPage extends Page {

    get floorplansTableValue()              { return $('div#page tbody') }
    get tabHeaders()                        { return $$('kds-tooltippable[class="kds-Tooltippable hydrated"]>li>a') }
    get moduleLink()                        { return $('div[class="row ng-star-inserted"]>ol>li:nth-child(3)>a')}
    get floorplanElementLocator ()          { return $('id="floorplans-tab"') }
    get noOfItemsColumnOne()                { return $('div#page tbody tr:nth-child(1) td:nth-child(12) a')}
    get selectFPDeptDropDown()              { return $('mx-multi-select[id="fp-department-select"]>div>div>span')}
    get floorplanFpDepartDropdownList()     { return $$('mx-multi-select[id="fp-department-select"] * kds-label>label')}
    get nextButtonPagination()              { return $('p-paginator>div>button.p-ripple.p-element.p-paginator-next') }
    get floorplansDeptColumnValues()        { return $$('div#page tbody > tr > td:nth-child(13)')}
    get clearFPDepartBtn()                  { return $('mx-multi-select[id="fp-department-select"] kds-icon-close>kds-icon>svg') }
    get floorplansTable()                   { return $('p-table>div>div>table') }
    get storeSelectionCustomStoreGroup()    { return $('#custom-store-group-selector > div > div.mx-dropdown-input-wrapper.compact > span') }
    get storeSelectionSchedule()            { return $('#custom-store-group-schedule-selector > div > div> span') }

    async selectFloorplansTab(){
        await this.floorplanElementLocator.waitForEnabled({timeout:3000},'Error: time out before enabled')
        let tab = await this.tabHeaders
        for(let index = 0; index< tab.length; index++){
            let appList = await tab[index]
            if(await tab[index].getText() === 'Floorplans' ) {
                await appList.click();
                break;
            }
        }
        await super.clickOn(await this.moduleLink)
    }

    async getAllDepartColumnValues(){
        let departValues = [];
        let nextButton = await this.nextButtonPagination;
        let nextBtnClassName =  await this.nextButtonPagination.getAttribute("class");
        while (await nextBtnClassName){
            let departColumnList =  await this.floorplansDeptColumnValues;
            for( let index =0; index < await departColumnList.length; index++){
                await super.waitBrowserToLoadPage(2000)
                departValues.push(await departColumnList[index].getText() )
            }
            if(await nextButton.isClickable()){
                await super.clickOn(await nextButton)
            }
            await super.waitBrowserToLoadPage(1000)
            if(nextBtnClassName.includes("p-disabled") ) {
                break;
            }
        }
        return departValues;
    }

    /*store FP Department dropdown elements into an array*/
    async getFPDepartDropdownList(){
        let fpDepartSelector = await this.selectFPDeptDropDown;
        await fpDepartSelector.waitForExist({timeout:5000}, 'Err: FP dropdown did not appear before timeout')
        //await expect(fpDepartSelector).toBeDisplayedInViewport()
        await super.clickOn(fpDepartSelector);
        await super.waitBrowserToLoadPage(3000)
        let departDropdownTxt = await this.floorplanFpDepartDropdownList;
        let fpDeptList = [];
        for(let index = 0; index < departDropdownTxt.length; index++){
            fpDeptList.push(await departDropdownTxt[index].getText())
        }
        await browser.keys('Escape')
        return fpDeptList
    }

}

module.exports = new FloorPlanPage();
