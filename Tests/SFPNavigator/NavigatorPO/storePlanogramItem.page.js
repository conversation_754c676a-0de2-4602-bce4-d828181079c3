const Page = require('../../../GlobalObjectRepository/page');

/**
 * sub page containing specific selectors and methods for a specific page
 */
let col;
class StorePlanogramItemPage extends Page {

    get storePlanogramTableValue()                { return $('div#page tbody') }
    get storePlanogramColumns()                   { return $$('tbody>tr:nth-child(2)>td')}
    get tabHeaders()                              { return $$('kds-tooltippable[class="kds-Tooltippable hydrated"]>li>a') }
    get searchCriteriaBtn()                       { return $('kds-buttongroup-member[id="search-criteria-button"]>label')}
    get searchPopup()                             { return $('mx-modal[id="search-modal"] * mx-modal-header')}
    get btnClosePopUp()                           { return $('mx-modal[id="search-modal"] * mx-modal-footer>div>div>kds-button>button.variant-fill')}
    get tableHeaders()                            { return $$('div>table>thead>tr>th')}
    get tablePogItem()                            { return $('div#page tbody') }
    get nthColumnListPogItem()                    { return $$(`div#page tbody>tr>td:nth-child(${col})`)}
    get defaultSortBtn()                          { return $('kds-buttongroup-member[id="sort-by-button"]>label')}


    //dynamically finds index of column header => specific for Pog&ItemByStore
    async getColumnArrayList(header){
        let table = await this.tablePogItem;
        await table.waitForExist({timeout:50000},'Error: UI table did not appear before time out')
        await expect(table).toBeDisplayedInViewport()
        const headerData = await this.tableHeaders;
        await super.waitBrowserToLoadPage(2000)
        let arrayList = await this.getArrayElementsText(headerData);
        let headerIndex =  arrayList.indexOf(header);
        col =  headerIndex + 1;
        let columnList = await this.nthColumnListPogItem;
        return await this.getArrayElementsText(columnList);
    }

}
module.exports = new StorePlanogramItemPage();
