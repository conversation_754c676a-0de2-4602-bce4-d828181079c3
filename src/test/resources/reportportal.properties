# Report Portal Configuration for SFP Apps UI Automation
# WebDriverIO Framework Integration

# Report Portal endpoint URL
rp.endpoint=http://reportportal.kroger.com:8080

# API key for authentication - replace with actual key from CoE SME
rp.api.key=[REACH_OUT_TO_CoE_SME_IN_YOUR_PILLAR]

# Project name in Report Portal
rp.project=project-name

# Launch name for test execution
rp.launch=sfp-apps-ui-automation

# Enable/disable Report Portal reporting
# Can be overridden via environment variable: -Dreportportal=true
rp.enable=${reportportal:false}

# Launch description
rp.description=SFP Apps UI Automation Test Execution

# Launch attributes/tags
rp.attributes=framework:webdriverio;automation:ui;application:sfp-apps

# Reporting mode: DEFAULT, DEBUG
rp.mode=DEFAULT

# Skip issues analysis
rp.skipped.issue=true

# Batch size for logs
rp.batch.size.logs=20

# Convert image to base64
rp.convertimage=true

# Launch UUID (optional - for rerun scenarios)
# rp.launch.uuid=

# Client join mode
rp.client.join=false

# Async reporting
rp.async=true
