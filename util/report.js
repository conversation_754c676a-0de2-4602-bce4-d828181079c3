
//import { execSync } from 'child_process';
const execSync = require('child_process').execSync;
const allure = require('allure-commandline')

export async function generateReport() {
    let reportError = new Error('Could not generate Allure report');
    let generation = allure(['generate', 'TestReports/allure-results', '--clean']);

    return new Promise((resolve, reject) => {
        let generationTimeout = setTimeout(() => {
            reject(reportError);
        }, 50000);

        generation.on('exit', function (exitCode) {
            clearTimeout(generationTimeout);
            if (exitCode !== 0) {
                reject(reportError);
                return;
            }
            console.log('Allure report successfully generated');
            resolve();
        });
    });
}
        
export  async function openAllure(){
const output = await execSync('npx allure open', { encoding: 'utf-8' });  // the default is 'buffer'
console.log('Output was:\n', output);
}


