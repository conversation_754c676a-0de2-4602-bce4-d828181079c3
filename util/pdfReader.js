const pdfparser = require('pdf-parse')
const dir = global.downloadDir;



let fs = require('fs'),
    path = require('path'),
    _ = require('underscore');
// Return only base file name without dir
export let getMostRecentlyDownloadedPDFFileName = () => {
    let files = fs.readdirSync(dir);

    // use underscore for max()
    return _.max(files, function (f) {
        let fullPath = path.join(dir, f);
        // ctime = creation time is used
        // replace with mtime for modification time
        return fs.statSync(fullPath).ctime;
    });
}

export let getFullPathPDF = (fileName) => {
    if (dir.substring(0, 1) === "/") {
        return dir + `/${fileName}`;
    } else {
        return dir + `\\${fileName}`;
    }
};

export let getFilesCount = () => {
    let files = fs.readdirSync(dir);
    return files.length;
}

export const getPDFText = async () => {
    const pdfFile = getFullPathPDF(getMostRecentlyDownloadedPDFFileName())
    let parsedPDF = ""
    let pdfBuffer = null
    let maxPages=1
    try {
        if (fs.existsSync(pdfFile)) {
            pdfBuffer = await fs.readFileSync(pdfFile)
            if (maxPages) {
                parsedPDF = await pdfparser(pdfBuffer, { max: maxPages })
            } else {
                parsedPDF = await pdfparser(pdfBuffer)
            }
            if (parsedPDF) {
                return await parsedPDF.text
            }
        }
    } catch (err) {
        console.error('Error: ', err.message)
    }
}
