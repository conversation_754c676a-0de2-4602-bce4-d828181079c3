import {DBNames} from "../envConfig";
const navigatorPage = require('../Tests/SFPNavigator/NavigatorPO/navigator.page')
const qrString = require('../SQLConnection/queryString');
const result = require('../SQLConnection/server.js.ts')

/**
 * This class is in experimental stage; some functions are not used in the tests yet.
 */
let divValue,storeValue, divValueList, storeValueList;

export async function selectSingleDivisionWithQuery(divIndex) {
     divValue = await (await result.getResult(qrString.getAllDivisionDropdownQuery, DBNames.SFP_STAGE))[divIndex];
     await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect', await divValue.join(' - '));
    }

export async function selectSingleStoreWithQuery(divValue, storeIndex) {
    storeValue = await (await result.getResult(qrString.getAllStoreDropdownByDivQuery.replace('@Div',divValue), DBNames.SFP_STAGE))[storeIndex];
    await navigatorPage.selectSingleValue('Stores', 'divisionMultiSelect', await storeValue.join(' - '));
}

export async function singleStoreWithQuery(divValue, storeIndex) {
    storeValue = await (await result.getResult(qrString.getAllStoreDropdownByDivQuery.replace('@Div',divValue), DBNames.SFP_STAGE))[storeIndex];
    return await storeValue.join(' - ')
}

export async function getMultipleDivisionsWithQuery(index1,index2,index3) {
    divValueList = await (await result.getResult(qrString.getAllDivisionDropdownQuery, DBNames.SFP_STAGE));
    let indicesToSelectDiv = [index1, index2, index3]
    let selectedDivValues = indicesToSelectDiv.map(index => divValueList[index])
    return selectedDivValues.map(innerArray => innerArray.join(' - '));
}

export async function selectMultipleDivisionsWithQuery(index1,index2,index3) {
    divValueList = await (await result.getResult(qrString.getAllDivisionDropdownQuery, DBNames.SFP_STAGE));
    let indicesToSelectDiv = [index1, index2, index3]
    let selectedDivValues = indicesToSelectDiv.map(index => divValueList[index])
    let combinedValueList = selectedDivValues.map(innerArray => innerArray.join(' - '))
    if (combinedValueList !== null) {
        combinedValueList.push(['Select All'])
        await navigatorPage.selectMultipleValues('Division', 'divisionMultiSelect', combinedValueList);
    }
}

export async function selectMultipleStoresWithDivQuery(divIndex1,divIndex2,divIndex3,storeIndex1,storeIndex2,storeIndex3) {
    let divValueList = await this.getMultipleDivisionsWithQuery(divIndex1,divIndex2,divIndex3)
    let extractedDigits
     extractedDigits = divValueList.map( element => {
        let match = element.match(/\d+/);
        return match ? match[0] : null;
    })
    storeValueList = await (await result.getResult(qrString.getAllStoreDropdownByDivQuery.replace('@Div', extractedDigits.join("', '")), DBNames.SFP_STAGE));
    let indicesToSelectStore = [storeIndex1,storeIndex2,storeIndex3]
    let selectedStoreValues = indicesToSelectStore.map(index => storeValueList[index])
    let combinedValueList = selectedStoreValues.map(innerArray => innerArray.join(' - '))
    if(combinedValueList !== null){
        combinedValueList.push('Select All')
        await navigatorPage.selectMultipleValues('Division', 'divisionMultiSelect', combinedValueList);
    }
}

export async function selectSingleStoreValue(index) {
    divValue = await (await result.getResult(qrString.getAllDivisionDropdownQuery, DBNames.SFP_STAGE))[index];
    await navigatorPage.selectSingleValue('Division', 'divisionMultiSelect', await divValue.join(' - '));
}

