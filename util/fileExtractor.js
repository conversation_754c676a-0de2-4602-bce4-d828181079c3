'use strict';

let fs = require('fs');
let StreamZip = require('node-stream-zip');
const path = require("path");
let _ = require('underscore');
const dir = global.downloadDir;


// Return only base file name without dir
export let getMostRecentlyDownloadedPDFFileName = () => {
    let files = fs.readdirSync(dir);

    // use underscore for max()
    return _.max(files, function (f) {
        let fullpath = path.join(dir, f);
        // ctime = creation time is used
        // replace with mtime for modification time
        return fs.statSync(fullpath).ctime;
    });
}

export let getFullPathPDF = (fileName) => {
    if (dir.substring(0, 1) === "/") {
        return dir + `/${fileName}`;
    } else {
        return dir + `\\${fileName}`;
    }
};

export let getFilesCount = () => {
    let files = fs.readdirSync(dir);
    return files.length;
}

export let getExtractedFilesCount = async () => {
    let files = await fs.readdirSync(dir+'/batchDownloads');
    return await files.length;
}

export let zipCount = async () => {
    let zip = await new StreamZip.async({
        file: getFullPathPDF(await getMostRecentlyDownloadedPDFFileName())
    });
    return await zip.entriesCount
}

export let extractFile = () => {
    let zip = new StreamZip({
        file: getFullPathPDF(getMostRecentlyDownloadedPDFFileName())
        , storeEntries: true
    });
    zip.on('error', function (err) {
        console.error('[ERROR]', err);
    });

    zip.on('ready', function () {
        console.log('All entries read: ' + zip.entriesCount);
    });

    zip.on('entry', function (entry) {

        let pathname = path.resolve(dir + '/batchDownloads', entry.name);
        if (/\.\./.test(path.relative(dir + '/batchDownloads', pathname))) {
            console.warn("[zip warn]: ignoring maliciously crafted paths in zip file:", entry.name);
            return;
        }

        if ('/' === entry.name[entry.name.length - 1]) {
            console.log('[DIR]', entry.name);
            return;
        }

        console.log('[FILE]', entry.name);
        zip.stream(entry.name, function (err, stream) {
            if (err) {
                console.error('Error:', err.toString());
                return;
            }

            stream.on('error', function (err) {
                console.log('[ERROR]', err);
                return;
            });
            fs.mkdir(
                path.dirname(pathname),
                {recursive: true},
                function (err) {
                    stream.pipe(fs.createWriteStream(pathname));
                }
            );
        });
    });
}
