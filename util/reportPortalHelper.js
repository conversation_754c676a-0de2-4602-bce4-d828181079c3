/**
 * Report Portal Helper Utility
 * Provides configuration and utility functions for Report Portal integration
 */

const fs = require('fs');
const path = require('path');

/**
 * Load Report Portal configuration from properties file
 * @param {string} propertiesPath - Path to reportportal.properties file
 * @returns {object} Configuration object
 */
function loadReportPortalConfig(propertiesPath = 'src/test/resources/reportportal.properties') {
    const config = {};
    
    try {
        if (fs.existsSync(propertiesPath)) {
            const content = fs.readFileSync(propertiesPath, 'utf8');
            const lines = content.split('\n');
            
            lines.forEach(line => {
                line = line.trim();
                if (line && !line.startsWith('#')) {
                    const [key, value] = line.split('=');
                    if (key && value) {
                        config[key.trim()] = value.trim();
                    }
                }
            });
        }
    } catch (error) {
        console.warn('Could not load Report Portal properties file:', error.message);
    }
    
    return config;
}

/**
 * Get Report Portal configuration with environment variable overrides
 * @param {string} environment - Current test environment
 * @returns {object} Report Portal configuration
 */
function getReportPortalConfig(environment = 'stage') {
    const propsConfig = loadReportPortalConfig();
    
    return {
        endpoint: process.env.RP_ENDPOINT || propsConfig['rp.endpoint'] || 'http://reportportal.kroger.com:8080',
        token: process.env.RP_API_KEY || propsConfig['rp.api.key'] || '[REACH_OUT_TO_CoE_SME_IN_YOUR_PILLAR]',
        project: process.env.RP_PROJECT || propsConfig['rp.project'] || 'sfp-apps-project',
        launch: process.env.RP_LAUNCH || propsConfig['rp.launch'] || `SFP-Apps-${environment}-${new Date().toISOString().split('T')[0]}`,
        description: process.env.RP_DESCRIPTION || propsConfig['rp.description'] || `SFP Apps UI Automation - Environment: ${environment}`,
        attributes: [
            {
                key: 'framework',
                value: 'webdriverio'
            },
            {
                key: 'environment',
                value: environment
            },
            {
                key: 'application',
                value: 'sfp-apps'
            },
            {
                key: 'automation',
                value: 'ui'
            }
        ],
        mode: propsConfig['rp.mode'] || 'DEFAULT',
        skippedIssue: propsConfig['rp.skipped.issue'] === 'true',
        debug: propsConfig['rp.debug'] === 'true' || false
    };
}

/**
 * Check if Report Portal is enabled
 * @returns {boolean} True if Report Portal should be enabled
 */
function isReportPortalEnabled() {
    const envEnabled = process.env.RP_ENABLE === 'true';
    const propsConfig = loadReportPortalConfig();
    const propsEnabled = propsConfig['rp.enable'] === 'true';
    
    return envEnabled || propsEnabled;
}

/**
 * Create Report Portal reporter configuration for WebDriverIO
 * @param {string} environment - Current test environment
 * @returns {array|null} Reporter configuration or null if disabled
 */
function createReportPortalReporter(environment = 'stage') {
    if (!isReportPortalEnabled()) {
        return null;
    }
    
    const config = getReportPortalConfig(environment);
    
    return ['@reportportal/agent-js-webdriverio', config];
}

/**
 * Log Report Portal configuration (without sensitive data)
 * @param {string} environment - Current test environment
 */
function logReportPortalConfig(environment = 'stage') {
    if (isReportPortalEnabled()) {
        const config = getReportPortalConfig(environment);
        console.log('Report Portal Configuration:');
        console.log(`  Endpoint: ${config.endpoint}`);
        console.log(`  Project: ${config.project}`);
        console.log(`  Launch: ${config.launch}`);
        console.log(`  Environment: ${environment}`);
        console.log(`  Mode: ${config.mode}`);
    } else {
        console.log('Report Portal is disabled');
    }
}

module.exports = {
    loadReportPortalConfig,
    getReportPortalConfig,
    isReportPortalEnabled,
    createReportPortalReporter,
    logReportPortalConfig
};
