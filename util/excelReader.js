import allureReporter from '@wdio/allure-reporter';
import md5 from "md5";
const navigatorPage = require("./../Tests/SFPNavigator/NavigatorPO/navigator.page");

let xlsx = require('xlsx');
const underscoreLib = require('underscore')
const fs = require('fs')
const path = require('path')
const Result = require('./../SQLConnection/server.js.ts');
const dir = global.downloadDir;

export let getFullPath = (fileName) => {
    if (dir.substring(0, 1) === "/") {
        return dir + `/${fileName}`;
    } else {
        return dir + `\\${fileName}`;
    }
};

export let getMostRecentlyDownloadedFileName = () => {
    let files = fs.readdirSync(dir)
    return underscoreLib.max(files, function (f) {
        let fullPath = path.join(dir, f)
        return fs.statSync(fullPath).ctime

    });
};

export const readExcelFile = async () => {
    let workbook = await xlsx.readFile(getFullPath(getMostRecentlyDownloadedFileName()));
    let sheetNameList = workbook.SheetNames;
    /*to get data from excel as values from Object*/
   // let fileContents = xlsx.utils.sheet_to_json(workbook.Sheets[sheetNameList[0]]).map(obj => Object.values(obj));
   // above line skips excel columns if entire column is empty and skipping is not consistent. Thus, made following change
    let fileContents = xlsx.utils.sheet_to_json(await workbook.Sheets[sheetNameList[0]],  {defval: null}).map(obj => Object.values(obj));
    return fileContents
}

export async function dataFromDB(query, DBNames) {
    let dataFromDb = await Result.getResult(query, DBNames);
    return dataFromDb[0]
}

export const getExcelHeaders = async () => {
    let workbook = xlsx.readFile(getFullPath(getMostRecentlyDownloadedFileName()));
    let sheetNameList = workbook.SheetNames;
    let fileContents = xlsx.utils.sheet_to_json(workbook.Sheets[sheetNameList[0]], { header: 1, defval: null })
    return fileContents[0]
}

export async function getExcelDataCount() {
    let excelData = await readExcelFile();
    return excelData.length
}

export async function validateDuplicates() {
    let excelData = await readExcelFile();
    let flag = true
    let hashes = {};

    await excelData.forEach(function (row, idx) {
        let hash = md5(row.join('~~~'));
        if (hash in hashes) {
            hashes[hash].push(idx + 1);
        } else {
            hashes[hash] = [idx + 1];
        }
    })

    Object.keys(hashes).forEach(function (key, idx) {
        let msg = '';
        if (hashes[key].length > 1) {
            msg = 'Rows ' + hashes[key].join(' and ') + ' are duplicate\n';
            console.log(msg);
            flag = false
        }
    })
    return flag

}

export async function excelUiDataCompareAllRows(uiData) {
    let excelData = await readExcelFile();
    return await dataCompare(excelData, uiData)
}

// Note: Need to re-assess this function and tests that are related to this function - might cause false positive
export async function excelDataCompareAllRow(query, rowNum, DBNames) {
    let excelData = await readExcelFile();
    let dbData = await Result.getResult(query, DBNames);
    return await dataCompare(excelData,dbData,rowNum)
}

/**
 * Reads data from a specific sheet in an Excel file, retaining null or empty values.
 * @param {string} selectSheet - The name of the sheet to read.
 * @returns {Promise<Array<Array<any>>>} - A 2D array of the sheet data including null or empty values.
 */
export const readExcelFileMultiSheet = async (selectSheet) => {
    try {
        // Load the workbook from the most recent file
        const filePath = getFullPath(getMostRecentlyDownloadedFileName());
        const workbook = await xlsx.readFile(filePath);

        // Validate if the selected sheet exists
        if (!workbook.Sheets[selectSheet]) {
            throw new Error(`Sheet "${selectSheet}" does not exist in the workbook.`);
        }

        // Convert sheet to JSON format
        const rawSheetData = xlsx.utils.sheet_to_json(workbook.Sheets[selectSheet], { defval: null });

        // Map rows to include all values, including null or empty ones
        const fileContents = rawSheetData.map((row) => Object.values(row));

        return fileContents;
    } catch (error) {
        console.error(`Error reading Excel file: ${error.message}`);
        throw error;
    }
};

export async function validateExcelDataMultiSheet(query, selectSheet, DBNames) {
    let excelData = await readExcelFileMultiSheet(selectSheet);
    let dbData = await Result.getResult(query, DBNames);
    return await dataCompare(await navigatorPage.removeDoubleSpaces(await excelData), await navigatorPage.removeDoubleSpaces(await dbData))
    }

export async function getExcelFPStoreValues() {
    let excelData = await readExcelFile();
    let storeExcel=[]
    for (let index = 0; index < excelData.length; index++) {
        storeExcel.push(excelData[index][1])
        }
    return storeExcel
    }

export async function excelUiDataCompareNthRowCommodityRecap(uiData, numOfRow) {
    // This function is temp solution to handle int data type of last four columns on Commodity recap tab; data type should be string, not int;
    let excelData = await readExcelFile();
    let uiDataSample=[];
    let excelDataSample=[];
    for(let index = 0; index < numOfRow-1; index++){
        uiDataSample.push(uiData[index].map(String))
        excelDataSample.push(excelData[index].map(String))
    }
    return await dataCompare(uiDataSample,excelDataSample,numOfRow)
}

export async function excelDataCompareAllRowString(query, DBNames) {
    let excelData = await readExcelFile();
    let dbData = await Result.getResult(query, DBNames);
    let dbDataString=[];
    let excelDataString=[]
    for(let index = 0; index < excelData.length; index++){
        dbDataString.push(dbData[index].map(String))
        excelDataString.push(excelData[index].map(String))
    }
    return await dataCompare(dbDataString,excelDataString)
}

export async function dataCompare(data1, data2, noOfRows) {
    let flag = true;
    let rowNum = noOfRows !== undefined ? noOfRows - 1 : data2.length;

    for (let index = 0; index < rowNum; index++) {
        const cleanedDBRow = await cleanAndJoinData(data2[index]);

        const matchFound = await Promise.all(data1.map(async row => {
            const cleanedUIRow = await cleanAndJoinData(row);
            return underscoreLib.isEqual(cleanedUIRow, cleanedDBRow);
        })).then(results => results.some(result => result));

        if (!matchFound) {
            allureReporter.addArgument(`No matching UI row found for DB row ${index + 2}`, cleanedDBRow);
            flag = false;
        }
    }

    return flag;
}

function cleanAndJoinData(data) {
    if (!Array.isArray(data)) return [];

    return data
        .map(val => {
            if (val === null || val === undefined) return "";
            return String(val).trim(); // force all values to strings and trim
        })
        .filter(val => val !== "");
}


//validate excelToDBDataIgnoringColumn
export async function validateExcelDataMultiSheetIgnoreColumns( query,selectSheet,DBNames, noOfRows,ignoreColumn) {
    let excelData = await readExcelFileMultiSheet(selectSheet);
    let dbData = await Result.getResult(query, DBNames);
    let flag = true;
    let rowNum = noOfRows ? noOfRows - 1 : dbData.length;
    
    for (let index = 0; index < rowNum; index++) {
      const data1String = excelData[index].map((val, i) => (i === ignoreColumn) ? '' : String(val)).join(",");
      const data2String = dbData[index].map((val, i) => (i === ignoreColumn) ? '' : String(val)).join(",");
      if (data1String !== data2String) {
        allureReporter.addArgument("Data mismatch at row from DB :" + (index + 2), data1String);
        allureReporter.addArgument("Data mismatch at row from UI :" + (index + 2), data2String);
        flag = false;
      }
    }
    return flag;
  }

export async function dataCompareWithNullValue(uiData, excelData) {
    let numOfRow = excelData.length;
    let uiDataSample = [];
    let excelDataSample = [];
    for (let index = 0; index < numOfRow - 1; index++) {
        uiDataSample.push(await uiData[index].map(str => str === null ? '' : str))
        excelDataSample.push(await excelData[index].map(str => str === null ? '' : str))
    }
    return await dataCompare(uiDataSample, excelDataSample)
}

export async function getExcelDataByColumnName(columnName)  {

    let filePath = getFullPath(getMostRecentlyDownloadedFileName());
           if(!fs.existsSync(filePath)){
               throw new Error(`File not found: ${filePath}`)
           }
        const workbook =   await xlsx.readFile(getFullPath(getMostRecentlyDownloadedFileName()));
        const sheetName = workbook.SheetNames[0];
        const sheet = workbook.Sheets[sheetName];
        const data = xlsx.utils.sheet_to_json(sheet);
    return data.map(row => row[columnName]).filter(value => value !== undefined);
}



