export const upcForComposite = `select top 1 UPC
                                from CKB.DBO.IX_SPC_POSITION POS
                                         join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                              on POS.DBParentplanogramkey = FUT.POG_DBKey
                                         join ckb.dbo.ix_spc_product PROD on POS.DBParentproductkey = prod.DBKey
                                where FUT.STR_DBKey = 3726;`

export const pogKeyForComposite = `select top 1 POS.DBParentplanogramkey
                                   from CKB.DBO.IX_SPC_POSITION POS
                                            join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                 on POS.DBParentplanogramkey = FUT.POG_DBKey
                                   where FUT.STR_DBKey = 3726;`

export const ssnPogTab = `select top 1 POG.Desc37
                          from CKB.DBO.IX_SPC_POSITION POS
                                   join ckb.dbo.ix_spc_planogram POG on POG.DBKey = POS.DBParentplanogramkey
                                   join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                        on POS.DBParentplanogramkey = FUT.POG_DBKey
                          where FUT.STR_DBKey = 3726
                            and LEN(POG.Desc37) = 33;`

export const pogKeyMappingReport = `select top 1 POS.DBParentplanogramkey
                                    from CKB.DBO.IX_SPC_POSITION POS
                                             join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                  on POS.DBParentplanogramkey = FUT.POG_DBKey
                                    where FUT.STR_DBKey = 3726;`

export const upcForClearButtons = `select top 1 UPC
                                   from CKB.DBO.IX_SPC_POSITION POS
                                            join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                 on POS.DBParentplanogramkey = FUT.POG_DBKey
                                            join ckb.dbo.ix_spc_product PROD on POS.DBParentproductkey = prod.DBKey
                                   where FUT.STR_DBKey = 3726;`

export const upcForDisablePogFilter1 = `select top 1 UPC
                                        from CKB.DBO.IX_SPC_POSITION POS
                                                 join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                      on POS.DBParentplanogramkey = FUT.POG_DBKey
                                                 join CKB.DBO.ix_spc_planogram POG on POG.DBkey = POS.DBParentplanogramkey
                                                 join ckb.dbo.ix_spc_product PROD on POS.DBParentproductkey = prod.DBKey
                                        where FUT.STR_DBKey = 3726
                                          and POG.Department = '01-GROCERY'
                                          and POG.Desc10 = '01-GROC-ALL OTHER';`

export const upcForDisablePogFilter2 = `select top 1 UPC
                                        from CKB.DBO.IX_SPC_POSITION POS
                                                 join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                      on POS.DBParentplanogramkey = FUT.POG_DBKey
                                                 join
                                             (select top 4 Desc3, DBKey
                                              from ckb.dbo.ix_str_store
                                              where Desc2 = '016' and DBStatus = 1
                                              order by Desc3
                                             ) A on A.DBKey = FUT.STR_DBKey
                                                 join ckb.dbo.ix_spc_product PROD on POS.DBParentproductkey = prod.DBKey;`

export const upcForMappedPogs = `select top 1 UPC
                                 from CKB.DBO.IX_SPC_POSITION POS
                                          join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                               on POS.DBParentplanogramkey = FUT.POG_DBKey
                                          join ckb.dbo.ix_spc_product PROD on POS.DBParentproductkey = prod.DBKey
                                 where FUT.STR_DBKey = 3726;`

export const dbKeyForDisablePogFilter = `select top 1 POS.DBParentplanogramkey
                                         from CKB.DBO.IX_SPC_POSITION POS
                                                  join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                       on POS.DBParentplanogramkey = FUT.POG_DBKey
                                                  join
                                              (select top 4 Desc3, DBKey
                                               from ckb.dbo.ix_str_store
                                               where Desc2 = '016' and DBStatus = 1
                                               order by Desc3
                                              ) A on A.DBKey = FUT.STR_DBKey
                                                  join ckb.dbo.ix_spc_product PROD on POS.DBParentproductkey = prod.DBKey;`

export const dbKeyForUnMappedPog = `Select top 1 A.POG_DBKey
                                    from (select distinct FUT.POG_DBKey
                                          from ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                   join ckb.dbo.ix_spc_planogram POG on POG.DBKey = FUT.POG_DBKey
                                                   join ckb.dbo.ix_str_store STR on STR.DBKey = FUT.STR_DBKey
                                          where STR.Desc2 = '014'
                                            and LEN(POG.Desc37) = 33
                                            and POG.Desc37 like 'D060%') A
                                    where A.POG_DBKey Not in
                                          (select distinct FUT.POG_DBKey
                                           from ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                    join ckb.dbo.ix_spc_planogram POG on POG.DBKey = FUT.POG_DBKey
                                                    join ckb.dbo.ix_str_store STR on STR.DBKey = FUT.STR_DBKey
                                           where STR.Desc2 = '016'
                                             and LEN(POG.Desc37) = 33
                                             and POG.Desc37 like 'D060%');`

export const upcListForForUnMappedPog = `select top 5 UPC
                                         from CKB.DBO.IX_SPC_POSITION POS
                                                  join CKB.DBO.IX_SPC_PRODUCT PROD on PROD.DBKey = POS.DBParentproductkey
                                                  join
                                              (Select top 1 A.POG_DBKey
                                               from (select distinct FUT.POG_DBKey
                                                     from ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                              join ckb.dbo.ix_spc_planogram POG on POG.DBKey = FUT.POG_DBKey
                                                              join ckb.dbo.ix_str_store STR on STR.DBKey = FUT.STR_DBKey
                                                     where STR.Desc2 = '014'
                                                       and LEN(POG.Desc37) = 33
                                                       and POG.Desc37 like 'D060%') A
                                               where A.POG_DBKey Not in
                                                     (select distinct FUT.POG_DBKey
                                                      from ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                               join ckb.dbo.ix_spc_planogram POG on POG.DBKey = FUT.POG_DBKey
                                                               join ckb.dbo.ix_str_store STR on STR.DBKey = FUT.STR_DBKey
                                                      where STR.Desc2 = '016'
                                                        and LEN(POG.Desc37) = 33
                                                        and POG.Desc37 like 'D060%')
                                              ) B on B.POG_DBKey = POS.DBParentplanogramkey;`

export const upcListForComparisonTab = `select top 4 UPC
                                        from CKB.DBO.IX_SPC_POSITION POS
                                                 join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                      on POS.DBParentplanogramkey = FUT.POG_DBKey
                                                 join ckb.dbo.ix_spc_product PROD on POS.DBParentproductkey = prod.DBKey
                                        where FUT.STR_DBKey = 3726;`

export const upcListForILPComparisonTab = `select top 8 UPC
                                           from CKB.DBO.IX_SPC_POSITION POS
                                                    join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                         on POS.DBParentplanogramkey = FUT.POG_DBKey
                                                    join ckb.dbo.ix_spc_product PROD on POS.DBParentproductkey = prod.DBKey
                                           where FUT.STR_DBKey = 3726;`

export const upcForSFPComparisonTab = `select top 1 UPC
                                       from CKB.DBO.IX_SPC_POSITION POS
                                                join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                     on POS.DBParentplanogramkey = FUT.POG_DBKey
                                                join ckb.dbo.ix_spc_product PROD on POS.DBParentproductkey = prod.DBKey
                                       where FUT.STR_DBKey = 3726;`

export const pogKeyForComparisonReport = `select top 1 POS.DBParentplanogramkey
                                          from CKB.DBO.IX_SPC_POSITION POS
                                                   join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                        on POS.DBParentplanogramkey = FUT.POG_DBKey
                                          where FUT.STR_DBKey = 3726;`

export const upcForPogItemByStore = `select top 5 PROD.UPC
                                     from CKB.DBO.ix_spc_position POS
                                              join ckb.dbo.ix_spc_product PROD on POS.DBParentproductkey = PROD.DBkey
                                              join
                                          (select top 1 POG_DBKey
                                           from ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                    join ckb.dbo.ix_str_store STR on STR.DBKey = FUT.STR_DBKey
                                                    join ckb.dbo.ix_spc_planogram POG on POG.DBKey = FUT.POG_DBKey
                                           where POG.Desc37 like 'D060%'
                                             and POG.NumberofProductsAllocated > 10
                                           group by POG_DBKey
                                           having count(distinct STR.Desc2) > 8) A
                                          on A.POG_DBKey = POS.DBParentplanogramkey;`

export const upcForPogItemByStore1 = `select top 10 PROD.UPC
                                      from CKB.DBO.ix_spc_position POS
                                               join ckb.dbo.ix_spc_product PROD on POS.DBParentproductkey = PROD.DBkey
                                               join
                                           (select top 1 POG_DBKey
                                            from ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                     join ckb.dbo.ix_str_store STR on STR.DBKey = FUT.STR_DBKey
                                                     join ckb.dbo.ix_spc_planogram POG on POG.DBKey = FUT.POG_DBKey
                                            where POG.Desc37 like 'D060%'
                                              and POG.NumberofProductsAllocated > 59
                                              and STR.Desc2 in ('016', '014')
                                            group by POG_DBKey
                                            having count(distinct STR.Desc2) = 2) A
                                           on A.POG_DBKey = POS.DBParentplanogramkey;`

export const pogKeyForNonDuplicatedRecords = `select top 1 POS.DBParentplanogramkey
                                              from CKB.DBO.IX_SPC_POSITION POS
                                                       join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                            on POS.DBParentplanogramkey = FUT.POG_DBKey
                                              where FUT.STR_DBKey = 3726;`

export const upcForFpDepart = `select top 1 UPC
                               from CKB.DBO.IX_SPC_POSITION POS
                                        join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                             on POS.DBParentplanogramkey = FUT.POG_DBKey
                                        join ckb.dbo.ix_spc_product PROD on POS.DBParentproductkey = prod.DBKey
                               where FUT.STR_DBKey = 3726;`

export const floorKeyForFpDepart = `select top 1 FP_DBKey
                                    from ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW
                                    where STR_DBKey = 3726
                                      and DISTINCT_PRODUCT_COUNT > 50;`

export const upcListForItemByPog = `select top 6 UPC, count(distinct POS.DBParentplanogramkey)
                                    from CKB.DBO.IX_SPC_POSITION POS
                                             join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                  on POS.DBParentplanogramkey = FUT.POG_DBKey
                                             join ckb.dbo.ix_str_store STR on STR.DBKey = FUT.STR_DBKey
                                             join ckb.dbo.ix_spc_product PROD on POS.DBParentproductkey = prod.DBKey
                                    where STR.Desc2 = '016'
                                    group by UPC
                                    having count(distinct POS.DBParentplanogramkey) between 30 and 40;`

export const pogKeyForItemByPog = `select top 1 POS.DBParentplanogramkey
                                   from CKB.DBO.IX_SPC_POSITION POS
                                            join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                 on POS.DBParentplanogramkey = FUT.POG_DBKey
                                   where FUT.STR_DBKey = 3726;`

export const pogKeyForItemByPogAssortmentButtons = `select top 1 POS.DBParentplanogramkey
                                                    from CKB.DBO.IX_SPC_POSITION POS
                                                             join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                                  on POS.DBParentplanogramkey = FUT.POG_DBKey
                                                             join ckb.dbo.ix_spc_performance PERF
                                                                  on PERF.DBParentplanogramkey = POS.DBParentplanogramkey
                                                    where FUT.STR_DBKey = 3726
                                                      and PERF.Flag3 = 1
                                                      order by POS.DBParentplanogramkey asc;`
                                                      

export const floorKeyForMappingReport = `select top 1 FP_DBKey
                                         from ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW
                                         where STR_DBKey = 3726
                                           and DISTINCT_PRODUCT_COUNT > 50;`

export const upcForMergedPogAssort = `select top 1 UPC
                                      from CKB.DBO.IX_SPC_POSITION POS
                                               join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                    on POS.DBParentplanogramkey = FUT.POG_DBKey
                                               join ckb.dbo.ix_spc_product PROD on POS.DBParentproductkey = prod.DBKey
                                      where FUT.STR_DBKey = 3726;`

export const pogKeyForTabHighLight = `select top 1  POS.DBParentplanogramkey
                                      from CKB.DBO.IX_SPC_POSITION POS
                                               join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                    on POS.DBParentplanogramkey = FUT.POG_DBKey
                                      where FUT.STR_DBKey = 3726;`

export const upcForOpenPogPdf = `select top 1 UPC
                                 from CKB.DBO.IX_SPC_POSITION POS
                                          join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                               on POS.DBParentplanogramkey = FUT.POG_DBKey
                                          join ckb.dbo.ix_spc_product PROD on POS.DBParentproductkey = prod.DBKey
                                 where FUT.STR_DBKey = 3726;`

export const dbKeyListForPdfDownloadPogsTab = `select distinct top 10  POS.DBParentplanogramkey
                                               from CKB.DBO.IX_SPC_POSITION POS
                                                        join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                             on POS.DBParentplanogramkey = FUT.POG_DBKey
                                                        join ckb.dbo.ix_spc_planogram POG on POG.DBKey = POS.DBParentplanogramkey
                                               where FUT.STR_DBKey = 3711
                                                 and POG.Desc6 = 'BASIC'
                                                 and POG.NumberofProductsAllocated > 50`

export const dbKeyListForPdfPsaDownloadPogsTab = `select top 10  POS.DBParentplanogramkey
                                                  from CKB.DBO.IX_SPC_POSITION POS
                                                           join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                                on POS.DBParentplanogramkey = FUT.POG_DBKey
                                                  where FUT.STR_DBKey = 3726;`


export const ssnListForPogsTab = `select top 5 POG.Desc37
                                  from CKB.DBO.IX_SPC_POSITION POS
                                           join ckb.dbo.ix_spc_planogram POG on POG.DBKey = POS.DBParentplanogramkey
                                           join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                on POS.DBParentplanogramkey = FUT.POG_DBKey
                                  where FUT.STR_DBKey = 3726
                                    and LEN(POG.Desc37) = 33;`

export const upcForPogHeaders = `select top 1 UPC
                                 from CKB.DBO.IX_SPC_POSITION POS
                                          join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                               on POS.DBParentplanogramkey = FUT.POG_DBKey
                                          join ckb.dbo.ix_spc_product PROD on POS.DBParentproductkey = prod.DBKey
                                 where FUT.STR_DBKey = 3726;`

export const dbKeyListForPogMappingReport = `select distinct top 8  POG_DBKey
                                             from ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                      join ckb.dbo.ix_str_store STR on STR.DBKey = FUT.STR_DBKey
                                                      join ckb.dbo.ix_spc_planogram POG on POG.DBKey = FUT.POG_DBKey
                                             where POG.NumberofProductsAllocated > 10
                                               and str.desc2 in ('016')
                                               and str.desc3 in ('00251', '00264', '00268', '00273', '00312')
                                             group by POG_DBKey
                                             having count(distinct STR.Desc3) > 4;`

export const dbKeyListForPSAPogsTab = `select top 10  POS.DBParentplanogramkey
                                       from CKB.DBO.IX_SPC_POSITION POS
                                                join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                     on POS.DBParentplanogramkey = FUT.POG_DBKey
                                       where FUT.STR_DBKey = 3726;`

export const dbKeyListForPSAPogsTab2 = `select distinct top 10  POS.DBParentplanogramkey
                                        from CKB.DBO.IX_SPC_POSITION POS
                                                 join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                      on POS.DBParentplanogramkey = FUT.POG_DBKey
                                                 join ckb.dbo.ix_spc_planogram POG on POG.DBKey = POS.DBParentplanogramkey
                                        where FUT.STR_DBKey = 3711
                                          and POG.Desc6 = 'BASIC'
                                          and POG.NumberofProductsAllocated > 50;`

export const upcListForPageNumbers = `select top 10 PROD.UPC
                                      from CKB.DBO.ix_spc_position POS
                                               join ckb.dbo.ix_spc_product PROD on POS.DBParentproductkey = PROD.DBkey
                                               join
                                           (select distinct top 10  POG_DBKey
                                            from ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                     join ckb.dbo.ix_str_store STR on STR.DBKey = FUT.STR_DBKey
                                                     join ckb.dbo.ix_spc_planogram POG on POG.DBKey = FUT.POG_DBKey
                                            where POG.Desc37 like 'D060%'
                                              and POG.NumberofProductsAllocated > 10
                                            group by POG_DBKey
                                            having count(distinct STR.Desc2) > 10) A
                                           on A.POG_DBKey = POS.DBParentplanogramkey;`

export const dbKeyForPogItemByStore = `select top 1 POS.DBParentplanogramkey
                                       from CKB.DBO.IX_SPC_POSITION POS
                                                join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                     on POS.DBParentplanogramkey = FUT.POG_DBKey
                                       where FUT.STR_DBKey = 3726;`

export const upcListForPogItemByStore = `select top 5 UPC
                                         from CKB.DBO.IX_SPC_POSITION POS
                                                  join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                       on POS.DBParentplanogramkey = FUT.POG_DBKey
                                                  join ckb.dbo.ix_spc_product PROD on POS.DBParentproductkey = prod.DBKey
                                         where FUT.STR_DBKey = 3726;`

export const dbKeyForStoreCountPogTab = `select top 1 POS.DBParentplanogramkey
                                         from CKB.DBO.IX_SPC_POSITION POS
                                                  join ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                                       on POS.DBParentplanogramkey = FUT.POG_DBKey
                                         where FUT.STR_DBKey = 3726;`
