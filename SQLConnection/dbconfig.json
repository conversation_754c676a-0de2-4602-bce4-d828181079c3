{"ILP-Stage": {"user": "svcPOGCILP_stg", "password": "D4DaNGrZP7C5dApaKvnLH7V", "server": "N060ILPSQL21.kroger.com", "database": "item_location_processor", "port": 1675, "synchronize": true, "trustServerCertificate": true}, "ILP-Test": {"user": "svcPOGCILP_test", "password": "tYf01UlGSOr2RG9QXWTMUC81", "server": "N060ILPSQL41.kroger.com", "database": "item_location_processor", "port": 1675, "synchronize": true, "trustServerCertificate": true}, "SFP-Stage": {"user": "s5940MerAppJDADBS", "password": "7qtC1UQsauNeRUzAnB00bU0bhNavrG", "server": "N060JDAFSSQL23.kroger.com", "database": "SFPAPPS", "port": 1675, "synchronize": true, "trustServerCertificate": true, "protocol": "tcp"}, "SFP-Test": {"user": "s5940MerAppJDADBT", "password": "B9oyfEzIfLdrB6", "server": "N060JDAFSSQL44.kroger.com", "database": "SFPAPPS", "port": 1675, "synchronize": true, "trustServerCertificate": true}, "CKB-Stage": {"user": "s5940MerAppJDADBS", "password": "7qtC1UQsauNeRUzAnB00bU0bhNavrG", "server": "N060JDAFSSQL21.kroger.com", "database": "CKB", "port": 1675, "synchronize": true, "trustServerCertificate": true}, "CKB-Test": {"user": "s5940MerAppJDADBT", "password": "B9oyfEzIfLdrB6", "server": "N060JDAFSSQL44.kroger.com", "database": "CKB", "port": 1675, "synchronize": true, "trustServerCertificate": true}, "NAV-Stage": {"user": "s5940MerchNavS", "password": "NgGmnFG3jDJeMQQWfHfa1", "server": "N060JDAFSSQL23.kroger.com", "database": "SFPAPPS", "port": 1675, "synchronize": true, "trustServerCertificate": true}, "OSS-Stage": {"user": "s5940MerchOssS", "password": "9Uga05T4VZ0aB8A74OH9ib0R8o", "server": "N060JDAFSSQL23.kroger.com", "database": "SFPAPPS", "port": 1675, "synchronize": true, "trustServerCertificate": true}}