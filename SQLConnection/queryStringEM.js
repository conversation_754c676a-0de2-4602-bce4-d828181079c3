const DataJsonEMQ = require('../TestData/EventManagerData/eventManagerQueryData.json');
//SFP-Stage

export let SfpEvent_Eventid = `SELECT t.event_id As 'Event Id'
                               FROM SFPApps.dbo.sfp_event_header_tbl t
                               WHERE event_id = ${'eventID'}`

export let autoActivationRecap = `SELECT DISTINCT dept,
                                                  commodity,
                                                  error_code,
                                                  error_description,
                                                  upc,
                                                  product_name,
                                                  divisional_count,
                                                  ' ' + divisional_count
                                  FROM (
                                           SELECT concat(desc45, ' - ', desc16)   AS dept,
                                                  concat(desc32, ' - ', category) AS commodity,
                                                  Name                            AS product_name,
                                                  desc32                          AS commodity_code,
                                                  category                        AS commodity_name,
                                                  upc                             AS product_upc
                                           FROM ckb.dbo.ix_spc_product
                                           WHERE DBStatus = 1) AS x
                                           RIGHT JOIN (
                                      SELECT ROW_NUMBER()    over (ORDER BY M.div_no, M.con_upc_no, M.status_cd) AS id, M.div_no AS division,
                                             M.status_cd  AS error_code,
                                             S.status_tx  AS error_description,
                                             M.con_upc_no AS upc,
                                             COUNT(*)     AS divisional_count
                                      FROM auto_activation_error_management_tbl M
                                               LEFT JOIN auto_activation_log_status_tbl S ON M.status_cd = S.status_cd
                                               INNER JOIN fiscal_calendar_tbl FC ON M.original_error_date = FC.date
                                      WHERE 1 = 1
                                        AND M.con_upc_no = '@upc'
                                        AND M.div_no IN ('@div')
                                        AND FC.fiscal_year = '@year'
                                        AND M.status_cd = '@error'
                                        AND deleted IS NULL
                                      GROUP BY M.con_upc_no, M.div_no, m.status_cd, S.status_tx
                                  ) as y ON x.product_upc = y.upc;`

export let autoActivationSummary = `SELECT details.div_no AS division,
                                           IIF(store.DESC4 IS NULL, '', store.Desc4) AS zone,
                                           details.loc_no                                              AS location,
                                           format(MIN(details.activation_date), 'yyyy-MM-ddTHH:mm:ss') AS first_activation_date,
                                           format(MAX(details.activation_date), 'yyyy-MM-ddTHH:mm:ss') AS last_activation_date,
                                           details.con_upc_no                                          AS upc,
                                           product_info.Name                                           AS product_name,
                                           st.status_tx                                                AS error_description,
                                           COUNT(*)                                                    AS error_count
                                    FROM auto_activation_log_detail_tbl details
                                        LEFT JOIN auto_activation_log_status_tbl st
                                    ON details.status_cd = st.status_cd
                                        INNER JOIN fiscal_calendar_tbl fc ON details.activation_date = fc.date,
                                        (
                                        SELECT UPC AS CON_UPC_NO, NAME, DBKEY
                                        FROM ckb.dbo.ix_spc_product
                                        WHERE UPC = '@upc'
                                        ) product_info
                                        INNER JOIN (
                                        SELECT MAX (DBKEY) AS DBKEY
                                        FROM ckb.dbo.ix_spc_product
                                        WHERE UPC = '@upc'
                                        GROUP BY UPC
                                        ) product_dbkey ON product_info.DBKEY = product_dbkey.DBKEY
                                        LEFT JOIN CKB.dbo.ix_spc_planogram pog ON product_dbkey.DBKey = pog.DBKey
                                        LEFT JOIN CKB.jdacustom.RPT_STR_POG_MAP_LIVE_VW F ON F.POG_DBKEY = pog.DBKey
                                        LEFT JOIN CKB.dbo.ix_str_store store ON store.DBKey = F.STR_DBKEY
                                    WHERE details.deleted IS NULL
                                      AND details.con_upc_no = '@upc'
                                      AND details.div_no IN ('@div')
                                      AND FC.fiscal_year = '@year'
                                      AND ST.status_cd='@error'
                                    GROUP BY details.div_no, details.loc_no, details.status_cd, details.con_upc_no,
                                        product_info.Name, product_dbkey.DBKEY,
                                        st.status_tx, store.Desc4
                                    ORDER BY details.div_no, details.loc_no, details.con_upc_no, details.status_cd;`

export let getRandomUPC = `SELECT DISTINCT top 1 percent upc
                           FROM (
                                    SELECT concat(desc45, ' - ', desc16)   AS dept,
                                           concat(desc32, ' - ', category) AS commodity,
                                           Name                            AS product_name,
                                           desc32                          AS commodity_code,
                                           category                        AS commodity_name,
                                           upc                             AS product_upc
                                    FROM ckb.dbo.ix_spc_product
                                    WHERE DBStatus = 1) AS x
                                    RIGHT JOIN (
                               SELECT ROW_NUMBER()    over (ORDER BY M.div_no, M.con_upc_no, M.status_cd) AS id, M.div_no AS division,
                                      M.status_cd  AS error_code,
                                      S.status_tx  AS error_description,
                                      M.con_upc_no AS upc,
                                      COUNT(*)     AS divisional_count
                               FROM auto_activation_error_management_tbl M
                                        LEFT JOIN auto_activation_log_status_tbl S ON M.status_cd = S.status_cd
                                        INNER JOIN fiscal_calendar_tbl FC ON M.original_error_date = FC.date
                               WHERE 1 = 1
                                 AND M.div_no IN ('@div')
                                 AND FC.fiscal_year = '@year'
                                 AND deleted IS NULL
                                 AND M.status_cd = '@error'
                               GROUP BY M.con_upc_no, M.div_no, m.status_cd, S.status_tx
                           ) as y ON x.product_upc = y.upc;
--where x.dept IS NOT NULL and x.commodity is not null and divisional_count between 10 and 50;`

export let getRandomChangeUPC = `SELECT top 1 upc
                                 FROM (
                                          SELECT concat(desc45, ' - ', desc16)   AS dept,
                                                 concat(desc32, ' - ', category) AS commodity,
                                                 Name                            AS product_name,
                                                 desc32                          AS commodity_code,
                                                 category                        AS commodity_name,
                                                 upc                             AS product_upc
                                          FROM ckb.dbo.ix_spc_product
                                          WHERE DBStatus = 1) AS x
                                          RIGHT JOIN (
                                     SELECT ROW_NUMBER()    over (ORDER BY M.div_no, M.con_upc_no, M.status_cd) AS id, M.div_no AS division,
                                            M.status_cd  AS error_code,
                                            S.status_tx  AS error_description,
                                            M.con_upc_no AS upc,
                                            COUNT(*)     AS divisional_count
                                     FROM auto_activation_error_management_tbl M
                                              LEFT JOIN auto_activation_log_status_tbl S ON M.status_cd = S.status_cd
                                              INNER JOIN fiscal_calendar_tbl FC ON M.original_error_date = FC.date
                                     WHERE 1 = 1
                                       AND M.div_no IN ('@div')
                                       AND FC.fiscal_year = '@year'
                                       AND deleted IS NULL
                                       AND M.status_cd = '@error'
                                     GROUP BY M.con_upc_no, M.div_no, m.status_cd, S.status_tx
                                 ) as y ON x.product_upc = y.upc
                                 where x.dept IS NOT NULL
                                   and x.commodity is not null
                                 order by divisional_count;`

export let deletedAutoActivationQuery = `select deleted
                                         from auto_activation_error_management_tbl M
                                                  INNER JOIN fiscal_calendar_tbl FC ON M.original_error_date = FC.date
                                         where con_upc_no = '@upc'
                                           AND M.div_no IN ('@div')
                                           AND FC.fiscal_year = '@year'
                                           and M.status_cd = '@error';`


export let resetCompletionCommodityRecapTab = `SELECT scd.div_no                                                                                                                        AS division,
                                                      COUNT(loc_no)                                                                                                                     AS store_location,
                                                      CONCAT(FC.fiscal_year, '-P', RIGHT('00' + LTRIM(STR(FC.fiscal_period)), 2), '-W', RIGHT('00' + LTRIM(STR(FC.week_of_period)), 2)) AS YPW,
                                                      scd.cpt_dpt_cd                                                                                                                    AS department,
                                                      scd.cpt_com_cd                                                                                                                    AS commodity,
                                                      CAST(SUM(IIF(scd.status = 'Y' AND scd.deleted IS NULL, 1, 0)) AS VARCHAR)                                                         AS completed,
                                                      CAST(SUM(IIF(scd.status = 'N' AND scd.deleted IS NULL, 1, 0)) AS VARCHAR)                                                         AS not_completed,
                                                      CAST(SUM(IIF(ISNULL(scd.status, 'X') = 'X' AND scd.deleted IS NULL, 1, 0)) AS VARCHAR)                                            AS no_response,
                                                      CAST(SUM(IIF(scd.deleted = 'Y', 1, 0)) AS VARCHAR)                                                                                AS deleted
                                               FROM sfp_compliancy_detail_tbl scd
                                                        INNER JOIN fiscal_calendar_tbl fc ON scd.event_date = fc.date
                                                        LEFT JOIN familyTree_commodity_tbl fTct
                                                                  ON scd.cpt_com_cd = fTct.cpt_com_cd AND scd.cpt_dpt_cd = fTct.cpt_dpt_cd
                                               WHERE 1 = 1
                                                 AND scd.div_no IN (SELECT value FROM STRING_SPLIT('${DataJsonEMQ.Division_014}', 'T'))
                                                 AND fc.fiscal_year = '${DataJsonEMQ.Year_2023}'
                                                 -- AND scd.reason_cd <> 'LK'
                                                 AND scd.loc_no = '${DataJsonEMQ.Store_00426}'
                                               GROUP BY scd.div_no,
                                                        fc.fiscal_year,
                                                        fc.fiscal_period,
                                                        fc.fiscal_week,
                                                        fc.week_of_period,
                                                        scd.cpt_com_cd,
                                                        fTct.com_nam_tx,
                                                        scd.cpt_dpt_cd
                                               ORDER BY commodity ASC, department ASC;`

export let resetCompletionStoreReviewTab = `SELECT div_no
                                                 , loc_no
                                                 , ISNULL(vendor_name, '') AS vendor_name
                                                 , reset_type
                                                 , year
                                                 , period
                                                 , week
                                                 -- ,weekOfPeriod
                                                 , reset_date
                                                 , pog_id
                                                 , name
                                                 , shelf_size
                                                 , status
                                                 , CASE WHEN deleted = 'Y' THEN 'Deleted' ELSE ''
END
AS deleted
                                            FROM (SELECT C.flr_key
                                                  , C.event_id       AS event_id
                                                  , C.dbkey          AS dbkey
                                                  , C.rec_id         AS rec_id
                                                  , C.div_no         AS div_no
                                                  , C.loc_no         AS loc_no
                                                  , V.vendor_name    AS vendor_name
                                                  , C.reset_type_cd  AS reset_type
                                                  , CAST(CAST(FC.fiscal_year AS INT) AS VARCHAR)  AS year
                                                  , CAST(CAST(FC.fiscal_period AS INT) AS VARCHAR) AS period
                                                --, FC.fiscal_week   AS week
                                                  , CONCAT(FC.week_of_period ,' (', FC.fiscal_week , ')')   AS week
                                                  , CONVERT(varchar, C.event_date, 1)    AS reset_date
                                                  , C.pog_id         AS pog_id
                                                  , P.Name           AS name
                                                  , CAST(CAST(P.Width AS INT) AS varchar)AS shelf_size
                                                  , CASE C.status WHEN 'Y' THEN 'Yes' WHEN 'N' THEN 'No' ELSE 'No Response' END AS status
                                                  , CASE WHEN C.deleted = 'Y' THEN 'Deleted' ELSE NULL END AS deleted
                                            FROM sfp_event_vendor_tbl V
                                                      INNER JOIN sfp_vendor_ref_tbl VR
                                                                ON V.vendor_cd = VR.vendor_cd
                                                      RIGHT OUTER JOIN sfp_compliancy_detail_tbl C
                                                                      ON VR.cpt_dpt_cd = C.cpt_dpt_cd
                                                                          AND VR.loc_no = C.loc_no
                                                      LEFT OUTER JOIN sfp_event_vendor_tbl a
                                                                      ON a.vendor_cd = c.event_vendor_cd
                                                      LEFT OUTER JOIN sfp_event_reset_types r
                                                                      ON r.reset_type_cd = c.reset_type_cd
                                                      LEFT OUTER JOIN [CKB].[DBO].[IX_SPC_PLANOGRAM] p
                                            ON C.dbkey = p.dbkey
                                                INNER JOIN fiscal_calendar_tbl FC
                                                ON C.event_date = FC.[date]
                                            WHERE 1 = 1
                                              AND C.div_no IN (SELECT value FROM STRING_SPLIT('${DataJsonEMQ.Division_014}', 'T'))
                                              AND FC.fiscal_year = '${DataJsonEMQ.Year_2023}'
                                              -- AND C.reason_cd <> 'LK'
                                              AND C.loc_no='${DataJsonEMQ.Store_00335}') AS RESULT
                                            ORDER BY div_no, loc_no;`

export let sfpEvent_storeNum = `SELECT t.loc_no AS 'Store Location'
                                FROM SFPApps.dbo.sfp_event_schedule_store_tbl t
                                WHERE loc_no IN ('@store')
                                  and event_id = '@eventID';`


export let sfpEvent_DbKeyNum = `SELECT distinct t.dbkey AS 'DBKey'
                                FROM SFPApps.dbo.sfp_event_planogram_tbl t
                                WHERE dbkey IN ('@dbKey')
                                  and event_id = ('@eventID')
                                ORDER BY t.dbkey;`

export let upcSummaryAutoActivation = `SELECT COUNT(DISTINCT pog.DBKey)   AS pog_count,
                                              COUNT(DISTINCT store.DBKey) AS store_count,
                                              COUNT(DISTINCT store.desc2) AS div_count,
                                              COUNT(DISTINCT loc_no)      AS loc_err_count,
                                              MAX(retry_cnt)              AS retry_cnt
                                       FROM CKB.dbo.ix_spc_position pos
                                                JOIN CKB.dbo.ix_spc_planogram pog ON pog.DBKey = pos.DBParentplanogramkey
                                                JOIN CKB.dbo.ix_spc_product prod ON pos.DBParentproductkey = prod.DBKey
                                                JOIN CKB.dbo.ix_flr_section sect ON pog.DBKey = sect.DBParentPlanogramKey
                                                JOIN CKB.dbo.ix_str_store_floorplan stfl
                                                     ON sect.DBParentFloorplanKey = stfl.DBParentFloorplanKey
                                                JOIN CKB.dbo.ix_str_store store ON stfl.DBParentStoreKey = store.DBKey
                                                JOIN sfpapps.dbo.auto_activation_error_management_tbl err
                                                     ON prod.upc = err.con_upc_no
                                       WHERE prod.upc = '@upc'
                                         AND err.deleted IS NULL;`

export let SfpEvent_Auto_Activation = `SELECT t.event_aut_act_fl, t.event_2_wk_prior_fl
                                       FROM SFPApps.dbo.sfp_event_header_tbl t
                                       WHERE event_id = '@eventID'`


export let SfpEventRequestShelfStripTypeQuery = `SELECT t.event_shf_strp_typ_cd, t.event_strip_typ
                                                 FROM SFPApps.dbo.sfp_event_header_tbl t
                                                 WHERE event_id = '@eventID'`

export let sfpEventRequestTagsType = `SELECT t.event_sys_head_fl
                                      FROM SFPApps.dbo.sfp_event_header_tbl t
                                      WHERE event_id = '@eventID'`
export let SfpEventTagstypeSets = `SELECT CAST(t.event_sets AS varchar)
                                   FROM SFPApps.dbo.sfp_event_header_tbl t
                                   WHERE event_id = '@eventID'`

export let resetCompletionStoreReviewSingleValueDelete = `SELECT div_no
                                                               , loc_no
                                                               , vendor_name
                                                               , reset_type
                                                               , year
                                                               , period
                                                               , week
                                                               , reset_date
                                                               , pog_id
                                                               , name
                                                               , shelf_size
                                                               , status
                                                               , deleted
                                                          FROM (SELECT C.flr_key
                                                                  , C.event_id AS event_id
                                                                  , C.dbkey AS dbkey
                                                                  , C.rec_id AS rec_id
                                                                  , C.div_no AS div_no
                                                                  , C.loc_no AS loc_no
                                                                  , V.vendor_name AS vendor_name
                                                                  , C.reset_type_cd AS reset_type
                                                                  , FC.fiscal_year AS year
                                                                  , FC.fiscal_period AS period
                                                                  , FC.fiscal_week AS week
                                                                  , CONVERT (varchar, C.event_date, 1) AS reset_date
                                                                  , C.pog_id AS pog_id
                                                                  , P.Name AS name
                                                                  , CAST (P.Width AS INT) AS shelf_size
                                                                  , CASE C.status WHEN 'Y' THEN 'Yes' WHEN 'N' THEN 'No' ELSE 'No Response' END AS status
                                                                  , CASE C.deleted WHEN 'Y' THEN 'Deleted' WHEN NULL THEN NULL END AS deleted
                                                              FROM sfp_event_vendor_tbl V
                                                              INNER JOIN sfp_vendor_ref_tbl VR
                                                              ON V.vendor_cd = VR.vendor_cd
                                                              RIGHT OUTER JOIN sfp_compliancy_detail_tbl C
                                                              ON VR.cpt_dpt_cd = C.cpt_dpt_cd
                                                              AND VR.loc_no = C.loc_no
                                                              LEFT OUTER JOIN sfp_event_vendor_tbl a
                                                              ON a.vendor_cd = c.event_vendor_cd
                                                              LEFT OUTER JOIN sfp_event_reset_types r
                                                              ON r.reset_type_cd = c.reset_type_cd
                                                              LEFT OUTER JOIN [CKB].[DBO].[IX_SPC_PLANOGRAM] p
                                                              ON C.dbkey = p.dbkey
                                                              INNER JOIN fiscal_calendar_tbl FC
                                                              ON C.event_date = FC.[date]
                                                              WHERE 1 = 1
                                                              AND C.Status IS NULL
                                                              AND FC.fiscal_year = '${DataJsonEMQ.Year_2022}'
                                                              AND C.div_no IN (SELECT value FROM STRING_SPLIT('@div', 'T'))
                                                              AND C.loc_no = '@store'
                                                              AND C.pog_id = '@ssn'
                                                              )
                                                              AS RESULT
                                                          order by div_no, loc_no;`

export let resetCompletionStoreReviewMultipleValuesDelete = `SELECT div_no
                                                                  , loc_no
                                                                  , vendor_name
                                                                  , reset_type
                                                                  , year
                                                                  , period
                                                                  , week
                                                                  , reset_date
                                                                  , pog_id
                                                                  , name
                                                                  , shelf_size
                                                                  , status
                                                                  , deleted
                                                             FROM (SELECT C.flr_key
                                                                     , C.event_id AS event_id
                                                                     , C.dbkey AS dbkey
                                                                     , C.rec_id AS rec_id
                                                                     , C.div_no AS div_no
                                                                     , C.loc_no AS loc_no
                                                                     , V.vendor_name AS vendor_name
                                                                     , C.reset_type_cd AS reset_type
                                                                     , FC.fiscal_year AS year
                                                                     , FC.fiscal_period AS period
                                                                     , FC.fiscal_week AS week
                                                                     , CONVERT (varchar, C.event_date, 1) AS reset_date
                                                                     , C.pog_id AS pog_id
                                                                     , P.Name AS name
                                                                     , CAST (P.Width AS INT) AS shelf_size
                                                                     , CASE C.status WHEN 'Y' THEN 'Yes' WHEN 'N' THEN 'No' ELSE 'No Response' END AS status
                                                                     , CASE C.deleted WHEN 'Y' THEN 'Deleted' WHEN NULL THEN NULL END AS deleted
                                                                 FROM sfp_event_vendor_tbl V
                                                                 INNER JOIN sfp_vendor_ref_tbl VR
                                                                 ON V.vendor_cd = VR.vendor_cd
                                                                 RIGHT OUTER JOIN sfp_compliancy_detail_tbl C
                                                                 ON VR.cpt_dpt_cd = C.cpt_dpt_cd
                                                                 AND VR.loc_no = C.loc_no
                                                                 LEFT OUTER JOIN sfp_event_vendor_tbl a
                                                                 ON a.vendor_cd = c.event_vendor_cd
                                                                 LEFT OUTER JOIN sfp_event_reset_types r
                                                                 ON r.reset_type_cd = c.reset_type_cd
                                                                 LEFT OUTER JOIN [CKB].[DBO].[IX_SPC_PLANOGRAM] p
                                                                 ON C.dbkey = p.dbkey
                                                                 INNER JOIN fiscal_calendar_tbl FC
                                                                 ON C.event_date = FC.[date]
                                                                 WHERE 1 = 1
                                                                 AND C.Status IS NULL
                                                                 AND FC.fiscal_year = '${DataJsonEMQ.Year_2022}'
                                                                 AND C.div_no IN ('@div')
                                                                 AND C.loc_no IN ('@loc')
                                                                 AND C.pog_id IN ('@ssn')
                                                                 ) AS RESULT
                                                             order by div_no, loc_no;`

export let resetCompletionStoreReview = `SELECT div_no
                                              , loc_no
                                              , vendor_name
                                              , reset_type
                                              , year
                                              , period
                                              , week
                                              , reset_date
                                              , pog_id
                                              , name
                                              , shelf_size
                                              , status
                                              , deleted
                                              --, reason
                                         FROM (SELECT C.flr_key
                                                 , C.event_id AS event_id
                                                 , C.dbkey AS dbkey
                                                 , C.rec_id AS rec_id
                                                 , C.div_no AS div_no
                                                 , C.loc_no AS loc_no
                                                 , CASE WHEN V.vendor_name IS NULL THEN '' ELSE V.vendor_name END AS vendor_name
                                                 , C.reset_type_cd AS reset_type
                                                 , CAST (FC.fiscal_year AS VARCHAR) AS year
                                                 , CAST (FC.fiscal_period AS VARCHAR) AS period
                                                 , CAST (FC.fiscal_week AS VARCHAR) AS week
                                                 , CONVERT (varchar, C.event_date, 1) AS reset_date
                                                 , C.pog_id AS pog_id
                                                 , P.Name AS name
                                                 , CAST (P.Width AS VARCHAR) AS shelf_size
                                                 , CASE C.status WHEN 'Y' THEN 'Yes' WHEN 'N' THEN 'No' ELSE 'No Response' END AS status
                                                 , CASE C.deleted WHEN 'Y' THEN 'Deleted' ELSE '' END AS deleted
                                             -- , C.reason_cd      AS reason
                                             FROM sfp_event_vendor_tbl V
                                             INNER JOIN sfp_vendor_ref_tbl VR
                                             ON V.vendor_cd = VR.vendor_cd
                                             RIGHT OUTER JOIN sfp_compliancy_detail_tbl C
                                             ON VR.cpt_dpt_cd = C.cpt_dpt_cd
                                             AND VR.loc_no = C.loc_no
                                             LEFT OUTER JOIN sfp_event_vendor_tbl a
                                             ON a.vendor_cd = c.event_vendor_cd
                                             LEFT OUTER JOIN sfp_event_reset_types r
                                             ON r.reset_type_cd = c.reset_type_cd
                                             LEFT OUTER JOIN [CKB].[DBO].[IX_SPC_PLANOGRAM] p
                                             ON C.dbkey = p.dbkey
                                             INNER JOIN fiscal_calendar_tbl FC
                                             ON C.event_date = FC.[date]
                                             WHERE 1 = 1
                                             AND C.Status = 'Y'
                                             AND C.div_no IN ('@div')
                                             AND C.loc_no IN ('@loc')
                                             AND FC.fiscal_year = '${DataJsonEMQ.Year_2021}') AS RESULT
                                         order by div_no, loc_no;`

export let resetComplete_Delete_StoreReview = `SELECT deleted
                                               FROM (SELECT C.flr_key
                                                          , C.event_id                                                     AS event_id
                                                          , C.dbkey                                                        AS dbkey
                                                          , C.rec_id                                                       AS rec_id
                                                          , C.div_no                                                       AS div_no
                                                          , C.loc_no                                                       AS loc_no
                                                          , CASE WHEN V.vendor_name IS NULL THEN '' ELSE V.vendor_name END AS vendor_name
                                                          , C.reset_type_cd                                                AS reset_type
                                                          , CAST(FC.fiscal_year AS VARCHAR) AS year
                                                    , CAST (FC.fiscal_period AS VARCHAR) AS period
                                                    , CAST (FC.fiscal_week AS VARCHAR)  AS week
                                                    , CONVERT(varchar, C.event_date, 1)    AS reset_date
                                                    , C.pog_id         AS pog_id
                                                    , P.Name           AS name
                                                    , CAST(P.Width AS VARCHAR)          AS shelf_size
                                                    , CASE C.status WHEN 'Y' THEN 'Yes' WHEN 'N' THEN 'No' ELSE 'No Response' END AS status
                                                    , CASE C.deleted WHEN 'Y' THEN 'Deleted' ELSE '' END AS deleted
                                                     FROM sfp_event_vendor_tbl V
                                                         INNER JOIN sfp_vendor_ref_tbl VR
                                                     ON V.vendor_cd = VR.vendor_cd
                                                         RIGHT OUTER JOIN sfp_compliancy_detail_tbl C
                                                         ON VR.cpt_dpt_cd = C.cpt_dpt_cd
                                                         AND VR.loc_no = C.loc_no
                                                         LEFT OUTER JOIN sfp_event_vendor_tbl a
                                                         ON a.vendor_cd = c.event_vendor_cd
                                                         LEFT OUTER JOIN sfp_event_reset_types r
                                                         ON r.reset_type_cd = c.reset_type_cd
                                                         LEFT OUTER JOIN [CKB].[DBO].[IX_SPC_PLANOGRAM] p
                                                         ON C.dbkey = p.dbkey
                                                         INNER JOIN fiscal_calendar_tbl FC
                                                         ON C.event_date = FC.[date]
                                                     WHERE 1 = 1
                                                       AND C.deleted = '${DataJsonEMQ.Status_Y}'
                                                       AND C.div_no IN ('@div')
                                                       AND FC.fiscal_year = '@year') AS RESULT
                                               order by div_no, loc_no;`

export let displayPogType = `select Desc6
                             from [CKB].[dbo].IX_SPC_PLANOGRAM
                             where DBKey in (@POGKEYS)`

export let sfpRefreshEventData = `SELECT RTRIM(DESC37)                                                  AS SpaceStationName,
                                         RTRIM(REPLACE(DESC42, '  ', ' '))                              AS SpaceStationVersionDescription,
                                         status1                                                        AS POGStatus,
                                         isnull(FORMAT(date3, 'MM/d/yyyy'), FORMAT(date2, 'MM/d/yyyy')) AS ActualPendingDate,
                                         FORMAT(LiveDate, 'MM/d/yyyy')                                  AS live_date,
                                         P.dbkey                                                        AS POGDBKey,
                                         RTRIM(Desc43)                                                  AS enterprise_project_identifier,
                                      Left (Department, 2) AS Department,
                                      Desc10 AS SubDepartment,
                                      RTRIM(desc7) AS Commodity,
                                      RTRIM(Desc3) AS CommodityGroup
                                  FROM [CKB].[DBO].[IX_SPC_PLANOGRAM] P
                                      inner join
                                      sfpapps.[dbo].[sfp_event_planogram_tbl] EP
                                  on P.DBKEY = EP.dbkey
                                  WHERE 1 = 1
                                    AND P.DBSTATUS IN (1
                                      , 2)
                                    and EP.event_id = '@eventID'
                                  order by P.dbkey;`

export let SfpEvent_corpStatus = `SELECT t.corp_status AS 'Corporate Status'
                                  FROM SFPApps.dbo.sfp_event_header_tbl t
                                  WHERE event_id = ${'eventID'}`


export let remodelStores = `Select top(@num) loc_no
                            FROM (
                                     Select Distinct s.dbkey as id,
                                                     s.Desc2 AS div_no,
                                                     s.Desc1 AS Divisionname, Right (s.Desc3, 3) AS loc_no, 'system' AS rev_by
                                     FROM [CKB].[dbo].[ix_str_store] as s
                                     WHERE 1=1
                                       AND Desc2 = '@div'
                                       AND s.DBStatus <>4
                                       AND Desc2 IS NOT NULL
                                       AND Desc2 != 'N/A'
                                       AND Desc3 IS NOT NULL
                                       AND Desc3 != 'N/A'
                                 ) s
                            where s.loc_no IN (SELECT loc_no
                                               from dbo.sfp_event_remodal_stores_info
                                               where div_no = '@div')
                            order by div_no, loc_no;`

export let UPlEventcreated = `SELECT event_id
                              FROM dbo.sfp_event_requests_history_tbl
                              WHERE SpecInstruc1 = '@AttNO'
                                AND CAST(CONVERT(VARCHAR (10), GETDATE(), 103) as VARCHAR) =
                                    CAST(CONVERT(VARCHAR (10), UPLIssueDate, 103) AS VARCHAR)`

export let noResponseStore = `select TOP(1) div_no, loc_no
                              from sfp_compliancy_detail_tbl
                              where status is null
                              GROUP by loc_no, div_no
                              having COUNT(loc_no) = 1`

export let recordID = `select *
                       from sfp_compliancy_detail_tbl
                       where div_no = '@div'
                         and loc_no = '@loc'`

export let scheduledStores = `Select top(@num) loc_no
                              FROM (
                                       Select Distinct s.dbkey as id,
                                                       s.Desc2 AS div_no,
                                                       s.Desc1 AS Divisionname, Right (s.Desc3, 3) AS loc_no, 'system' AS rev_by
                                       FROM [CKB].[dbo].[ix_str_store] as s
                                       WHERE 1=1
                                         AND Desc2 = '@div'
                                         AND s.DBStatus <>4
                                         AND Desc2 IS NOT NULL
                                         AND Desc2 != 'N/A'
                                         AND Desc3 IS NOT NULL
                                         AND Desc3 != 'N/A'
                                   ) s
                              where s.loc_no IN (SELECT loc_no
                                                 from dbo.sfp_event_schedule_div_tbl
                                                 where div_no = '@div')
                              order by div_no, loc_no;`

export let resetCompletionStatusResetId = `SELECT CASE C.status
                                                      WHEN 'Y' THEN 'Completed'
                                                      WHEN 'N' THEN 'Not Completed'
                                                      ELSE 'No Response' END AS status
                                           FROM sfp_event_vendor_tbl V
                                                    INNER JOIN
                                                sfp_vendor_ref_tbl VR ON V.vendor_cd = VR.vendor_cd
                                                    RIGHT OUTER JOIN
                                                sfp_compliancy_detail_tbl C ON VR.cpt_dpt_cd = C.cpt_dpt_cd AND
                                                                               VR.loc_no = C.loc_no
                                                    LEFT OUTER JOIN
                                                sfp_event_vendor_tbl a ON a.vendor_cd = c.event_vendor_cd
                                                    LEFT OUTER JOIN
                                                sfp_event_reset_types r ON r.reset_type_cd = c.reset_type_cd
                                                    LEFT OUTER JOIN
                                               [CKB].[DBO].[IX_SPC_PLANOGRAM] p
                                           ON C.dbkey = p.dbkey INNER JOIN
                                               fiscal_calendar_tbl FC ON C.event_date = FC.[date]
                                           WHERE
                                               1 = 1
                                             AND
                                               C.rec_id = '@rec_id'
                                           ORDER BY
                                               C.div_no, C.loc_no;`

export let resetCompleteDeleteStatusResetId = `SELECT CASE C.deleted WHEN 'Y' THEN 'Deleted' ELSE '' END AS deleted
                                               FROM sfp_event_vendor_tbl V
                                                        INNER JOIN
                                                    sfp_vendor_ref_tbl VR ON V.vendor_cd = VR.vendor_cd
                                                        RIGHT OUTER JOIN
                                                    sfp_compliancy_detail_tbl C ON VR.cpt_dpt_cd = C.cpt_dpt_cd AND
                                                                                   VR.loc_no = C.loc_no
                                                        LEFT OUTER JOIN
                                                    sfp_event_vendor_tbl a ON a.vendor_cd = c.event_vendor_cd
                                                        LEFT OUTER JOIN
                                                    sfp_event_reset_types r ON r.reset_type_cd = c.reset_type_cd
                                                        LEFT OUTER JOIN
                                                   [CKB].[DBO].[IX_SPC_PLANOGRAM] p
                                               ON C.dbkey = p.dbkey INNER JOIN
                                                   fiscal_calendar_tbl FC ON C.event_date = FC.[date]
                                               WHERE
                                                   1 = 1
                                                 AND
                                                   C.rec_id = '@rec_id'
                                               ORDER BY
                                                   C.div_no, C.loc_no;`


export let uniquePogCount = `select count(distinct pog_id)
                             from sfp_event_planogram_tbl
                             where event_id = '@evID'`

export let caOEventID = `select cao_event_id
                         from sfp_event_header_tbl
                         where event_id = '@evID'`

export let caOEventDscp = `select event_tx
                           from sfp_event_header_tbl
                           where cao_event_id is not null`

export let corporateSplitEvent = `select event_id,
                                         Format(created_datetime, 'MM-dd-yyyy') created_datetime,
                                         Format(rev_datetime, 'MM-dd-yyyy')     rev_datetime
                                  from sfp_event_header_tbl
                                  where event_tx = '@discrp'`

export let splitCreatedDate = `select Format(created_datetime, 'MM-dd-yyyy') created_datetime
                               from sfp_event_header_tbl
                               where event_tx = '@discrp'`

export let revisedDate = `select Format(rev_datetime, 'MM-dd-yyyy') rev_datetime
                          from sfp_event_header_tbl
                          where event_id = '@evID'`

export let splitRevisedDate = `select Format(rev_datetime, 'MM-dd-yyyy') rev_datetime
                               from sfp_event_header_tbl
                               where event_tx = '@discrp'`

export let updateRevisedDate = `update sfp_event_header_tbl
                                set rev_datetime = DATEADD(day, -2, convert(date, GETDATE()))
                                where event_id = '@evID'`

export let eventScheduleStoreTbl = `Select schedule_id
                                    from SFPApps.dbo.sfp_event_schedule_store_tbl
                                    where event_id = '@evID'`

export let eventScheduleDivTbl = `Select schedule_id
                                  from SFPApps.dbo.sfp_event_schedule_div_tbl
                                  where event_id = '@evID'`

export let eventScheduleTbl = `Select schedule_id
                               from SFPApps.dbo.sfp_event_schedule_tbl
                               where event_id = '@evID'`

export let eventScheduleStoreTblId = `Select schedule_id, event_schedule_id
                                      from SFPApps.dbo.sfp_event_schedule_store_tbl
                                      where event_id = '@evID'`

export let planogramEventScheduleTblId = `Select store_schedule_id
                                          from SFPApps.dbo.sfp_event_planogram_tbl
                                          where event_id = '@evID'`

export let resetCompleteSnnvalue = `select top 1 dbkey
                                    from sfp_compliancy_detail_tbl
                                    where div_no = '@div'
                                      and loc_no = '@loc'
                                      and pog_id = '@ssn'`

export let pogCount = `select count(*)
                       from sfp_event_planogram_tbl
                       where event_id = '@evID'`

export let floorPlanDBKey = `select fp_dbkey
                             from sfp_event_planogram_tbl
                             where event_id = '@evID'`

export let floorPlanDBKeySTRPOGMAP = `select FP_DBKEY
                                      from ckb.jdacustom.RPT_STR_POG_MAP_FUTURELIVE_VW FUT
                                               join ckb.dbo.ix_str_store STR on STR.DBKey = FUT.STR_DBKey
                                      where str.desc2 = '@div'
                                        and str.desc3 = '@STR'
                                        and FUT.POG_DBKEY in (@dbkey)`
export let stores_ilp_area_rebuild = `select notes
                                      from queue_table
                                      where notes like '%@eventID%'
                                        and event_type = 'AREA_REBUILD'`

export let stores_ilp_area_delete = `select notes
                                     from queue_table
                                     where notes like '%@eventID%'
                                       and event_type = 'AREA_DELETE'`

export let customGroupColumnList = `SELECT distinct cus_grp_name from SFPApps.dbo.fp_cus_grp_tbl;`

export let singleCustomGroupName = `SELECT distinct cus_grp_name from SFPApps.dbo.sfp_cus_grp_tbl
                                    where cus_grp_name = '@customGroupName';`

export let customGroupColumnTopList = `SELECT cus_grp_name from SFPApps.dbo.sfp_cus_grp_tbl
                                    order by div_no, cus_grp_name OFFSET 1 ROWS;`
