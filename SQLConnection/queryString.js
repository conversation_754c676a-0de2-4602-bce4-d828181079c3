const DataJson = require('../TestData/SFPNavigatorData/navigatorQueryData.json');


//SFP-Stage

export let pogItemByStoreQuery = `SELECT STR.desc2            AS Div,
                                         STR.desc3            AS Store,
                                         PDT.UPC,
                                         PDT.ID AS     'Case', PDT.PartID AS SKU,
                                         PDT.DESC50           AS Magic_Lead_Article_Number,
                                         PDT.NAME             AS Product_Name,
                                         M_VIEW.AISLE_NO      AS Aisle_#,
                                         AISLE_N.aisle_name   AS Aisle_Desc,
                                         M_VIEW.AISLE_SIDE_CD AS Orn,
                                         M_VIEW.POG_SEQUENCE  AS Seq,
                                         M_VIEW.MAX_POG_BAY   AS Bay_In_Aisle,
                                         CAST(POS.Value8 AS varchar)  AS POG_Segment,
                                         CAST(POS.Value6 AS varchar)  AS Fixture,
                                         CAST(POS.Value5 AS varchar)   AS    Position,
    CAST(SPERF.Flag4 AS varchar) AS     new_flag,
    CAST(SPERF.Flag3 AS varchar) AS     delete_flag,
    CAST(pos.Flag4 AS varchar)   AS     move_flag,
    CASE SPERF.Desc4 WHEN 0 THEN '' ELSE SPERF.Desc4 END AS Pusher,
CASE SPERF.Desc6 WHEN 0 THEN '' ELSE SPERF.Desc6 END AS Tray,
CASE WHEN PDT.value14 IS NOT NULL THEN CAST(FLOOR(PDT.value14) AS VARCHAR) + '.' + CAST(CAST(PDT.value14 AS DECIMAL(10, 1)) - FLOOR(PDT.value14) AS VARCHAR(1))
           ELSE '' END AS recstat_code,
    PDT.Desc11                   AS     Prod_Group,
  CAST(POG.dbkey AS VARCHAR) as     POG_DBKEY,
    POG.desc37                          Space_Station_Name,
    POG.desc42                          Space_Station_Version_Description,
    POG.name                     as     Name,
    POG.status1                  as     POG_Status,
    FORMAT(POG.livedate, 'MM-dd-yy')    Pog_Live_Date,
    FORMAT(POG.pendingdate, 'MM-dd-yy') Pog_Pending_Date,
    FP.status1                   AS     FP_Status,
    FORMAT(FP.LiveDate, 'MM-dd-yy')     FP_Live_Date,
    FORMAT(FP.PendingDate, 'MM-dd-yy')  FP_Pending_Date,
    FP.desc40                           Event_Type,
CASE FP.desc45 WHEN 0 THEN '' ELSE CAST(FP.desc45 AS varchar(255)) END AS Event_Name
FROM [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG ON M_VIEW.POG_DBKEY = POG.DBKEY
INNER JOIN [CKB].[dbo].IX_FLR_FLOORPLAN FP ON M_VIEW.FP_DBKEY = FP.DBKEY
INNER JOIN [CKB].[dbo].IX_STR_STORE AS STR ON (M_VIEW.STR_DBKEY = STR.DBKEY)
and (fp.desc21 = str.desc2 and fp.desc22 = str.desc3)
INNER JOIN [CKB].[dbo].IX_SPC_POSITION AS POS ON (POG.DBKEY = POS.DBPARENTPLANOGRAMKEY)
LEFT OUTER JOIN [CKB].[dbo].IX_SPC_PERFORMANCE AS SPERF
ON (SPERF.DBPARENTPLANOGRAMKEY = POS.DBPARENTPLANOGRAMKEY AND
SPERF.DBPARENTPRODUCTKEY = POS.DBPARENTPRODUCTKEY)
INNER JOIN [CKB].[dbo].IX_SPC_PRODUCT AS PDT ON (SPERF.DBPARENTPRODUCTKEY = PDT.DBKEY)
LEFT OUTER JOIN [CKB].[jdacustom].csg_Aisle AISLE_N
ON M_VIEW.AISLE_NO = RIGHT('000' + ISNULL(CAST(AISLE_N.AISLE_NO AS VARCHAR), ''''), 3)
WHERE STR.dbstatus = 1
and FP.dbstatus in (1, 2)
and POG.dbstatus in (1, 2)
AND STR.desc2 IN ('@Div')
AND STR.desc3 IN ('@Store')
'@Condition'
order by Div desc, store desc, space_station_name desc, pog_segment desc, fixture desc, Position desc;`

export let storeTabQuery = `select str.desc2                                      division,
                                 str.desc3 'store number', str.Desc49 as 'GM Store Type' , str.Name 'store name', str.Address1 address,
                                 str.AddressCity                                city,
                                 str.AddressState                               state,
                                 CONCAT(SUBSTRING(str.AddressPostalCode, 1, 5), '-',
                                        SUBSTRING(str.AddressPostalCode, 6, 4)) zip,
                                 CONCAT(SUBSTRING(str.phone, 1, 3), '-', SUBSTRING(str.phone, 4, 3), '-',
                                        SUBSTRING(str.phone, 7, 4)) 'phone number', CASE WHEN str.Flag3 = 1 THEN 'Y' ELSE 'N' END AS 'Floor Planning', CASE WHEN str.Flag8 = 1 THEN 'Y' ELSE 'N' END AS 'ILP Ready Flag', CASE WHEN str.Flag9 = 1 THEN 'Y' ELSE 'N' END AS 'ILP', CASE WHEN str.Flag7 = 1 THEN 'Y' ELSE 'N' END AS 'Area Attributes', str.value4 'Total Square Footage'
                              --str.value5 'Sales Floor Footage',
                              --count(*) over() as totalrecords
                          from [CKB].[dbo].ix_str_store str
                          where desc2 = '@Div'
                            and str.dbstatus = 1
                          order by division, [store number] asc;`

export let mappingReportQuery = `Select division,
                                        store_number,
                                        store_type,
                                        pog_status,
                                        pog_type,
                                        CAST(pog_dbkey as varchar ) AS pog_dbkey,
                                        space_station_name,
                                        space_station_version_description,
                                        pog_pendingdate,
                                        pog_livedate,
                                        dept,
                                        aisle,
                                        ad,
                                        orientation,
                                        pog_sequence,
                                        CAST(num_of_items as varchar ) AS num_of_items,
                                        CAST(num_of_stores as varchar ) AS num_of_stores,
                                        pog_name,
                                        min_bay,
                                        max_bay,
                                        strip_color,
                                        CAST(fp_dbkey as varchar ) AS fp_dbkey,
                                        fp_status,
                                        fp_pendingdate,
                                        fp_livedate,
                                        store_segment,
                                        alt_store_number,
                                        planogram_department,
                                        planogram_sub_department,
                                        commodity_number,
                                        commodity_group
                                 FROM (Select distinct *,
                                                       POGTypeOrder = case
                                                                          when pog_type in ('Display Space', 'White Space')
                                                                              THEN 1
                                                                          ELSE 0 END
                                       FROM (
                                                SELECT distinct concat(STR.Desc2, STR.DESC3, M_VIEW.FP_DBKEY,
                                                                       M_VIEW.POG_DBKEY, POG.DESC6, POG.NAME,
                                                                       POG.DESC37, POG.DESC42, POG.LIVEDATE,
                                                                       POG.PENDINGDATE, POG.STATUS1, FP.LIVEDATE,
                                                                       FP.PENDINGDATE, FP.STATUS1,
                                                                       M_VIEW.DISTINCT_PRODUCT_COUNT,
                                                                       M_VIEW.STRIP_COLOR,
                                                                       M_VIEW.AISLE_NO, M_VIEW.AISLE_SIDE_CD,
                                                                       AISLE_N.AISLE_NAME, M_VIEW.POG_SEQUENCE,
                                                                       M_VIEW.MIN_POG_BAY, M_VIEW.MAX_POG_BAY,
                                                                       DEP.DEPT_NAME,
                                                                       STR.DESC49, STR.DESC7, STR.EMAIL, POG.DEPARTMENT,
                                                                       POG.DESC10, POG.DESC22, POG.DESC3,
                                                                       POG.NUMBEROFSTORES)    AS    unikey,
                                                                STR.DESC2                     AS    division,
                                                                STR.DESC3                     AS    store_number,
                                                                STR.DESC49                    AS    store_type,
                                                                POG.STATUS1                   AS    pog_status,
                                                                POG.DESC6                     AS    pog_type,
                                                                FORMAT(POG.PENDINGDATE, 'MM-dd-yy') pog_pendingdate,
                                                                FORMAT(POG.LIVEDATE, 'MM-dd-yy')    pog_livedate,
                                                                DEP.DEPT_NAME                 AS    dept,
                                                                M_VIEW.AISLE_NO               AS    aisle,
                                                                AISLE_N.AISLE_NAME            AS    ad,
                                                                M_VIEW.AISLE_SIDE_CD          AS    orientation,
                                                                M_VIEW.POG_SEQUENCE           AS    pog_sequence,
                                                                M_VIEW.DISTINCT_PRODUCT_COUNT AS    num_of_items,
                                                                POG.NUMBEROFSTORES            AS    num_of_stores,
                                                                POG.DBKEY                     AS    pog_dbkey,
                                                                POG.DESC37                    AS    space_station_name,
                                                                POG.DESC42                    AS    space_station_version_description,
                                                                POG.NAME                      AS    pog_name,
                                                                M_VIEW.MIN_POG_BAY            AS    min_bay,
                                                                M_VIEW.MAX_POG_BAY            AS    max_bay,
                                                                M_VIEW.STRIP_COLOR            AS    strip_color,
                                                                FP.DBKEY                      AS    fp_dbkey,
                                                                FP.STATUS1                    AS    fp_status,
                                                                FORMAT(FP.PENDINGDATE, 'MM-dd-yy')  fp_pendingdate,
                                                                FORMAT(FP.LIVEDATE, 'MM-dd-yy')     fp_livedate,
                                                                STR.Desc7                     AS    store_segment,
                                                                STR.Email                     AS    alt_store_number,
                                                                POG.Department                AS    planogram_department,
                                                                POG.Desc10                    AS    planogram_sub_department,
                                                                POG.Desc22                    AS    commodity_number,
                                                                POG.Desc3                     AS    commodity_group
                                                FROM [CKB].[jdacustom].RPT_STR_POG_MAP_LIVE_VW M_VIEW
                                                    INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG
                                                ON M_VIEW.POG_DBKEY = POG.DBKEY
                                                    INNER JOIN [CKB].[dbo].IX_FLR_FLOORPLAN FP ON M_VIEW.FP_DBKEY = FP.DBKEY
                                                    INNER JOIN [CKB].[dbo].IX_FLR_SECTION SEC
                                                    ON SEC.DBPARENTFLOORPLANKEY = FP.DBKEY AND SEC.DBPARENTPLANOGRAMKEY = POG.DBVERSIONKEY
                                                    INNER JOIN [CKB].[JDACUSTOM].CSG_DEPT DEP ON DEP.DEPT_CD = M_VIEW.FFIX_DEPT
                                                    INNER JOIN [CKB].[dbo].IX_STR_STORE STR ON M_VIEW.STR_DBKEY = STR.DBKEY
                                                    LEFT OUTER JOIN [CKB].[jdacustom].csg_Aisle AISLE_N
                                                    ON M_VIEW.AISLE_NO = RIGHT ('000' + ISNULL(CAST (AISLE_N.AISLE_NO AS VARCHAR), ''), 3)
                                                WHERE STR.DESC2 IN ('@Div')
                                                  AND STR.DESC3 IN ('@Store')
                                                  --AND POG.DBKEY = '@pogKey'
                                                  AND STR.DBSTATUS = 1
                                                  AND POG.DBSTATUS = 1
                                                  and FP.DBSTATUS = 1
                                            ) AS result) AS X
                                 order by division asc, store_number asc, case when aisle is null or aisle = '' then 1 else 0 end, aisle asc,
                                          orientation asc, min_bay asc, max_bay asc, pog_sequence asc ;`

export let FloorplansQueryDbKey = `SELECT
         CAST(MORE_INFO.DivNo AS VARCHAR(3))          AS Div,
         CAST(MORE_INFO.StoreNo AS VARCHAR(5))        AS Store,
         CAST(FP.dbkey AS VARCHAR(10))                AS DBKey,
         FP.status1                                   AS Status,
         CAST(FP.desc40 AS VARCHAR(3))                AS event_type,
         TRIM(REPLACE(FP.Name, '  ', ' '))            AS FP_Name,
         FORMAT(FP.livedate, 'MM-dd-yy')              AS Live_Date,
         CAST(MORE_INFO.num_of_pogs  AS VARCHAR(10))  AS #_Of_Planograms,
         CAST(MORE_INFO.num_of_items  AS VARCHAR(10)) AS #_Of_Items,
         CAST(FP.desc2 AS VARCHAR(30))                AS Floorplan_Department,
         CAST(FP.desc39 AS VARCHAR(7))                AS Project_type,
         CAST(FP.desc44 AS VARCHAR(50) )              AS Modifier
     FROM (
              SELECT
                  STR_DBKEY          AS str_dbkey,
                  FP_DBKEY,
                  STR.desc2          AS DivNo,
                  STR.desc3          AS StoreNo,
                  sum(pog_count)     AS num_of_pogs,
                  sum(product_count) AS num_of_items
              FROM (
                       SELECT STR_DBKEY,
                              FP_DBKEY,
                              POG_DBKEY,
                              max(DISTINCT_PRODUCT_COUNT) AS product_count,
                              1                           AS pog_count
                       FROM
                           [CKB].[jdacustom].RPT_STR_POG_MAP_LIVE_VW M_VIEW
                       WHERE
                           M_VIEW.str_dbstatus <> 4
                       GROUP BY
                           STR_DBKEY, FP_DBKEY, DISTINCT_PRODUCT_COUNT, POG_DBKEY
                   )                           AS STR_FP_POG_INFO_SEQ                                INNER JOIN
                  [CKB].[dbo].IX_STR_STORE     AS STR ON STR_FP_POG_INFO_SEQ.STR_DBKEY = STR.DBKEY INNER JOIN
                  [ckb].[dbo].ix_spc_planogram AS pog on pog.dbkey = STR_FP_POG_INFO_SEQ.pog_dbkey
              WHERE
                  1 = 1
              GROUP BY
                  STR_DBKEY, FP_DBKEY, STR.desc2, STR.desc3
          )                           AS MORE_INFO INNER JOIN
         [CKB].[dbo].IX_FLR_FLOORPLAN AS FP ON MORE_INFO.FP_DBKEY = FP.DBKEY
     WHERE
         1 = 1                              AND
         FP.dbkey        LIKE '@dbkey'      AND
         FP.desc40       LIKE '@Event_Type' AND
         MORE_INFO.DivNo LIKE '@Div'
     GROUP BY FP.desc44,
         FP.desc39,
         FP.desc45,
         FP.desc40,
         FP.dbkey,
         FORMAT(FP.livedate, 'MM-dd-yy'),
         FP.Name,
         FP.desc2,
         FP.dbstatus,
         FP.status1,
         MORE_INFO.DivNo,
         MORE_INFO.StoreNo,
         MORE_INFO.num_of_pogs,
         MORE_INFO.num_of_items,
         MORE_INFO.str_dbkey,
         FP.desc1
     ORDER BY
         Div, Store, Floorplan_Department, Status, Live_Date DESC;`

export let  FloorplansQuery = `SELECT MORE_INFO.DivNo                 AS Div,
                                     MORE_INFO.StoreNo               AS Store,
                                     FP.dbkey                        AS DBKey,
                                     FP.status1                      AS Status,
                                     FP.desc40                          event_type,
                                     REPLACE(REPLACE(REPLACE( FP.Name, ' ', '{}'), '}{', ''), '{}', ' ') as FP_Name,
                                     FORMAT(FP.livedate, 'MM-dd-yy') AS Live_Date,
                                     MORE_INFO.num_of_pogs           AS #_Of_Planograms,
                                     MORE_INFO.num_of_items          AS #_Of_Items,
                                     FP.desc2                        AS Floorplan_Department,
                                     FP.desc39                          Project_type,
                                     FP.desc44                          Modifier
                              from (
                                       SELECT STR_DBKEY          as str_dbkey,
                                              FP_DBKEY,
                                              STR.desc2          as DivNo,
                                              STR.desc3          as StoreNo,
                                              sum(pog_count)     as num_of_pogs,
                                              sum(product_count) as num_of_items

                                       from (
                                                SELECT STR_DBKEY,
                                                       FP_DBKEY,
                                                       POG_DBKEY,
                                                       max(DISTINCT_PRODUCT_COUNT) as product_count,
                                                       1                           as pog_count
                                                FROM [CKB].[jdacustom].RPT_STR_POG_MAP_LIVE_VW M_VIEW
                                                WHERE M_VIEW.str_dbstatus <> 4
                                                group by STR_DBKEY, FP_DBKEY, DISTINCT_PRODUCT_COUNT, POG_DBKEY
                                            ) AS STR_FP_POG_INFO_SEQ
                                                INNER JOIN [CKB].[dbo].IX_STR_STORE STR
                                       ON STR_FP_POG_INFO_SEQ.STR_DBKEY = STR.DBKEY
                                           inner join [ckb].[dbo].ix_spc_planogram pog on pog.dbkey = STR_FP_POG_INFO_SEQ.pog_dbkey
                                       where 1 = 1
                                       group by STR_DBKEY, FP_DBKEY, STR.desc2, STR.desc3) AS MORE_INFO
                                       INNER JOIN [CKB].[dbo].IX_FLR_FLOORPLAN FP
                              ON MORE_INFO.FP_DBKEY = FP.DBKEY
                              WHERE 1 = 1
                                AND MORE_INFO.DivNo IN ('@div')
                                AND MORE_INFO.StoreNo IN ('@store')
                                AND FP.dbstatus IN ('1')
                              group by FP.desc44,
                                  FP.desc39,
                                  FP.desc45,
                                  FP.desc40,
                                  FP.dbkey,
                                  FORMAT(FP.livedate, 'MM-dd-yy'),
                                  FP.Name,
                                  FP.desc2,
                                  FP.dbstatus,
                                  FP.status1,
                                  MORE_INFO.DivNo,
                                  MORE_INFO.StoreNo,
                                  MORE_INFO.num_of_pogs,
                                  MORE_INFO.num_of_items,
                                  MORE_INFO.str_dbkey,
                                  FP.desc1
                              order by Div, Store, Floorplan_Department, Status, Live_Date desc;`

export let pogTabQueryWithDivStore = `Select distinct *
                        FROM (
                                 Select pog.status1 AS Status,
                                        pog.Desc6 AS 'POG Type', 
                                        FORMAT(pog.PendingDate, 'MM-dd-yy') AS 'Pending Date', 
                                        FORMAT(pog.dbdateeffectivefrom, 'MM-dd-yy') AS 'Date Live', 
                                        pog.desc38 'Period Week Year', 
                                        CAST(pog.NumberOfStores AS varchar) AS 'Store Count',
                                        CAST(pog.NumberOfProductsAllocated AS varchar ) AS 'Number(#) of Items',
                                        CAST(pog.dbkey AS varchar ) AS DBkey,
                                        CASE
                                            WHEN pog.partid = '' OR pog.partid IS NULL THEN 'D' + CASE
                                                                                                      WHEN IsNumeric(pog.Desc39) = 1
                                                                                                          THEN pog.Desc39
                                                                                                      ELSE '___' END +
                                                                                            '-L' +
                                                                                            CASE
                                                                                                WHEN IsNumeric(pog.Desc1) = 1
                                                                                                    THEN pog.Desc1
                                                                                                ELSE '00000' END +
                                                                                            '-D' + CASE
                                                                                                       WHEN IsNumeric(Left(pog.Department,2))
                                                                                                           = 1
                                                                                                           THEN Left(pog.Department, 2) ELSE '__' END + '-C' + CASE WHEN IsNumeric(pog.Desc22) = 1 THEN pog.Desc22 ELSE '___' END +'-V___'
                                     + '-' + Left (COALESCE (pog.uom, '_'), 1) + Right ('000'+ COALESCE (CONVERT (varchar, pog.Value4), '___'), 3)
                                     + '-' + CASE WHEN COALESCE (pog.Desc21, '') = '' THEN '__' ELSE pog.Desc21 END ELSE pog.partid END 'Space Station Name',
                                     pog.desc42 'Space Station Version Description',
                                     pog.name 'POG Name'
                                 FROM [CKB].[jdacustom].RPT_STR_POG_MAP_LIVE_VW M_VIEW
                                     INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG
                                 ON M_VIEW.POG_DBKEY = POG.DBKEY
                                     INNER JOIN [CKB].[dbo].IX_STR_STORE str ON M_VIEW.STR_DBKEY = str.DBKEY
                                 WHERE str.dbstatus = 1
                                   AND pog.status1='Live'
                                   AND str.desc2 IN ('${DataJson.Division_018}')
                                   AND str.Desc3 IN ('${DataJson.Store_00038}')
                                   AND POG.dbstatus IN (1)) AS result
                        order by 'Space Station Name' asc, 'Date Live' asc`

export let POGTypeQuery = `SELECT distinct POG.DESC6 AS pogType
                           FROM
                               [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
                               INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM AS PREPOG
                           ON M_VIEW.POG_DBKEY = PREPOG.DBKEY
                               INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM AS POG ON POG.DBVERSIONKEY = PREPOG.DBVERSIONKEY
                               INNER JOIN [CKB].[dbo].IX_FLR_FLOORPLAN AS PREFP ON M_VIEW.FP_DBKEY = PREFP.DBKEY
                               INNER JOIN [CKB].[dbo].IX_FLR_FLOORPLAN AS FP ON FP.DBVERSIONKEY = PREFP.DBVERSIONKEY
                               INNER JOIN [CKB].[dbo].IX_STR_STORE STR ON M_VIEW.STR_DBKEY = STR.DBKEY
                               LEFT OUTER JOIN [CKB].[jdacustom].csg_Aisle AISLE_N ON M_VIEW.AISLE_NO = RIGHT ('000'+ISNULL(CAST (AISLE_N.AISLE_NO AS VARCHAR),''''),3)
                           WHERE STR.DESC2='@Div'
                             AND STR.DESC3='@Store'
                               --AND POG.DBKEY = '@pogKey'
                             AND STR.DBSTATUS = 1
                             AND POG.STATUS1='Live'
                             and FP.STATUS1='Live'`


export let FpDepartmentMR = `SELECT distinct flx.desc8
                             from
                                 [CKB].[dbo].ix_flr_Floorplan flp,
                                 [CKB].[dbo].ix_flr_fixture flx,
                                 [CKB].[dbo].ix_flr_section sec,
                                 [CKB].[dbo].IX_SPC_PLANOGRAM POG
                             WHERE sec.dbparentfloorplankey = flp.dbkey
                               and sec.dbparentfixturekey = flx.dbkey
                               and sec.dbparentfloorplankey = flx.dbparentfloorplankey
                               and flx.desc8 is not null
                               and flp.desc21 = '@Div'
                               and flp.desc22 = '@Store'
                                 --AND POG.DBKEY = '@pogKey'
                               and flp.dbstatus = 1
                             order by flx.desc8;`

export let ItemByPOGQuery = `Select upc,
                                    product_case,
                                    sku,
                                    magic_lead,
                                    product_name,
                                    pog_segment,
                                    fixture,
                                    position_number,
                                    facing_width,
                                    facing_height,
                                    cap_number,
                                    merch_style,
                                    new_flag,
                                    delete_flag,
                                    move_flag,
                                    pusher_flag,
                                    tray_flag,
                                    recstat_code,
                                    product_group,
                                    pog_dbkey,
                                    space_station_name,
                                    space_station_version_description,
                                    pog_name,
                                    status,
                                    live_date,
                                    pending_date
                             FROM (
                                      Select distinct concat(pog.dbkey, prd.upc, pos.value6, pos.value5, pos.value8,
                                                             pos.HFacings,
                                                             pos.VFacings)                                          random_key,
                                                      prd.upc,
                                                      prd.ID                                                     AS product_case,
                                                      prd.PartID                                                 AS sku,
                                                      prd.DESC50                                                 AS magic_lead,
                                                      prd.NAME                                                   AS product_name,
                                                      CAST(pos.value8 AS int)                                    AS pog_segment,
                                                      CAST(pos.value6 AS int)                                       fixture,
                                                      CAST(pos.value5 AS int)                                       position_number,
                                                      CASE pos.HFacings WHEN 0 THEN NULL ELSE pos.HFacings END   AS facing_width,
                                                      CASE pos.VFacings WHEN 0 THEN NULL ELSE pos.VFacings END   AS facing_height,
                                                      CASE pos.YCapNum WHEN 0 THEN NULL ELSE pos.YCapNum END     AS cap_number,
                                                      CASE pos.MerchStyle WHEN 0 THEN NULL WHEN 1 THEN 'SRP' END AS merch_style,
                                                      CAST(spcperf.Flag4 AS varchar)                             AS new_flag,
                                                      CAST(spcperf.Flag3 AS varchar)                             AS delete_flag,
                                                      CAST(pos.Flag4 AS varchar)                                 AS move_flag,
                                                      spcperf.Desc4                                              AS pusher_flag,
                                                      spcperf.Desc6                                              AS tray_flag,
                                                      CASE prd.value14
                                                          WHEN 0 THEN null
                                                          ELSE CAST(prd.value14 as decimal(10, 1)) END           AS recstat_code,
                                                      prd.Desc11                                                 AS product_group,
                                                      pog.dbkey                                                     pog_dbkey,
                                                      pog.desc37                                                    space_station_name,
                                                      pog.desc42                                                    space_station_version_description,
                                                      pog.name                                                      pog_name,
                                                      pog.status1                                                   status,
                                                      FORMAT(pog.livedate, 'MM-dd-yy')                              live_date,
                                                      FORMAT(pog.pendingdate, 'MM-dd-yy')                           pending_date
                                      FROM [CKB].[dbo].IX_SPC_PLANOGRAM PREPOG
                                          INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM AS POG
                                      ON POG.DBVERSIONKEY = PREPOG.DBVERSIONKEY
                                          INNER JOIN [CKB].[dbo].IX_SPC_PERFORMANCE AS spcperf ON (pog.DBKEY = spcperf.DBPARENTPLANOGRAMKEY)
                                          LEFT OUTER JOIN [CKB].[dbo].IX_SPC_POSITION AS pos
                                          ON (spcperf.DBPARENTPLANOGRAMKEY = pos.DBPARENTPLANOGRAMKEY
                                          AND spcperf.DBPARENTPRODUCTKEY = pos.DBPARENTPRODUCTKEY)
                                          INNER JOIN [CKB].[dbo].IX_SPC_PRODUCT AS prd ON (spcperf.DBPARENTPRODUCTKEY = prd.DBKEY)
                                      WHERE pog.dbstatus IN (1)
                                        AND prd.upc IN ('?')
                                      UNION
                                      Select distinct concat(pog.dbkey, prd.upc, pos.value6, pos.value5, pos.value8, pos.HFacings,
                                          pos.VFacings) random_key,
                                          prd.upc,
                                          prd.ID AS product_case,
                                          prd.PartID AS sku,
                                          prd.DESC50 AS magic_lead,
                                          prd.NAME AS product_name,
                                          CAST (pos.value8 AS int) AS pog_segment,
                                          CAST (pos.value6 AS int) fixture,
                                          CAST (pos.value5 AS int) position_number,
                                          CASE pos.HFacings WHEN 0 THEN NULL ELSE pos.HFacings END AS facing_width,
                                          CASE pos.VFacings WHEN 0 THEN NULL ELSE pos.VFacings END AS facing_height,
                                          CASE pos.YCapNum WHEN 0 THEN NULL ELSE pos.YCapNum END AS cap_number,
                                          CASE pos.MerchStyle WHEN 0 THEN NULL WHEN 1 THEN 'SRP' END AS merch_style,
                                          CAST (spcperf.Flag4 AS varchar) AS new_flag,
                                          CAST (spcperf.Flag3 AS varchar) AS delete_flag,
                                          CAST (pos.Flag4 AS varchar) AS move_flag,
                                          spcperf.Desc4 AS pusher_flag,
                                          spcperf.Desc6 AS tray_flag,
                                          CASE prd.value14
                                          WHEN 0 THEN null
                                          ELSE CAST (prd.value14 as decimal (10, 1)) END AS recstat_code,
                                          prd.Desc11 AS product_group,
                                          pog.dbkey pog_dbkey,
                                          pog.desc37 space_station_name,
                                          pog.desc42 space_station_version_description,
                                          pog.name pog_name,
                                          pog.status1 status,
                                          FORMAT(pog.livedate, 'MM-dd-yy') live_date,
                                          FORMAT(pog.pendingdate, 'MM-dd-yy') pending_date
                                      FROM [CKB].[dbo].IX_SPC_PLANOGRAM PREPOG
                                          INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM AS POG
                                      ON POG.DBVERSIONKEY = PREPOG.DBVERSIONKEY
                                          INNER JOIN [CKB].[dbo].IX_SPC_PERFORMANCE AS spcperf ON (pog.DBKEY = spcperf.DBPARENTPLANOGRAMKEY)
                                          LEFT OUTER JOIN [CKB].[dbo].IX_SPC_POSITION AS pos
                                          ON (spcperf.DBPARENTPLANOGRAMKEY = pos.DBPARENTPLANOGRAMKEY
                                          AND spcperf.DBPARENTPRODUCTKEY = pos.DBPARENTPRODUCTKEY)
                                          INNER JOIN [CKB].[dbo].IX_SPC_PRODUCT AS prd ON (spcperf.DBPARENTPRODUCTKEY = prd.DBKEY)
                                      WHERE pog.dbstatus IN (1)
                                        AND prd.upc IN ('?')) AS result
                             order by pog_segment asc, fixture asc, position_number asc, pog_dbkey asc;`

export let ItemByPOGQuery_POGDBKey = `Select distinct * FROM (
                                                                 Select distinct
                                                                     prd.upc,
                                                                     prd.ID                                                            AS product_case,
                                                                     prd.PartID                                                        AS sku,
                                                                     prd.DESC50                                                        AS magic_lead,
                                                                     prd.NAME                                                          AS product_name,
                                                                     CAST(pos.value8 AS varchar)                                           AS pog_segment,
                                                                     CAST(pos.value6 AS varchar)                                              fixture,
                                                                     CAST(pos.value5 AS varchar)                                              position_number,
                                                                     CASE pos.HFacings WHEN 0 THEN NULL ELSE pos.HFacings END          AS facing_width,
                                                                     CASE pos.VFacings WHEN 0 THEN NULL ELSE pos.VFacings END          AS facing_height,
                                                                     CASE pos.YCapNum WHEN null THEN 0 ELSE pos.YCapNum END AS cap_number,
                                                                     CASE pos.MerchStyle WHEN 0 THEN '' WHEN 1 THEN 'SRP' WHEN null THEN '' END AS merch_style,
                                                                     CAST(spcperf.Flag4 AS varchar)                                    AS new_flag,
                                                                     CAST(spcperf.Flag3 AS varchar)                                    AS delete_flag,
                                                                     CAST(pos.Flag4 AS varchar)                                        AS move_flag,
                                                                     CASE WHEN spcperf.Desc4 is null or spcperf.Desc4 = '<null>' THEN 'null' ELSE spcperf.Desc4 END AS pusher_flag,
                                                                     CASE WHEN spcperf.Desc6 is null THEN 'null' ELSE spcperf.Desc6 END AS tray_flag,
                                                                     CASE prd.value14 WHEN 0 THEN 'null' ELSE CAST(CAST(prd.value14 as decimal(10, 1)) AS varchar) END AS recstat_code,
                                                                     CASE WHEN prd.Desc11 is null THEN 'null' ELSE prd.Desc11 END AS product_group,
                                                                     CAST(pog.dbkey AS varchar)                                        AS pog_dbkey,
                                                                     pog.desc37                                                           space_station_name,
                                                                     pog.desc42                                                           space_station_version_description,
                                                                     pog.name                                                             pog_name,
                                                                     pog.status1                                                          status,
                                                                     FORMAT(pog.livedate, 'MM-dd-yy')                                     live_date,
                                                                     FORMAT(pog.pendingdate, 'MM-dd-yy')                                  pending_date
                                                                 FROM [CKB].[dbo].IX_SPC_PLANOGRAM PREPOG
                                                                     INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM AS POG ON POG.DBVERSIONKEY = PREPOG.DBVERSIONKEY
                                                                     INNER JOIN [CKB].[dbo].IX_SPC_PERFORMANCE AS spcperf ON (pog.DBKEY = spcperf.DBPARENTPLANOGRAMKEY)
                                                                     LEFT OUTER JOIN [CKB].[dbo].IX_SPC_POSITION AS pos ON (spcperf.DBPARENTPLANOGRAMKEY = pos.DBPARENTPLANOGRAMKEY
                                                                     AND spcperf.DBPARENTPRODUCTKEY = pos.DBPARENTPRODUCTKEY)
                                                                     INNER JOIN [CKB].[dbo].IX_SPC_PRODUCT AS prd ON (spcperf.DBPARENTPRODUCTKEY = prd.DBKEY)
                                                                 WHERE pog.dbstatus IN ('1')
                                                                   AND pog.dbkey IN ('?')
                                                                   AND spcperf.Flag3 IS NOT NULL AND  spcperf.Flag3 = '0'
                                                                 UNION
                                                                 Select distinct
                                                                     prd.upc,
                                                                     prd.ID AS product_case,
                                                                     prd.PartID AS sku,
                                                                     prd.DESC50 AS magic_lead,
                                                                     prd.NAME AS product_name,
                                                                     CAST (pos.value8 AS varchar) AS pog_segment,
                                                                     CAST (pos.value6 AS varchar) fixture,
                                                                     CAST (pos.value5 AS varchar) position_number,
                                                                     CASE pos.HFacings WHEN 0 THEN NULL ELSE pos.HFacings END AS facing_width,
                                                                     CASE pos.VFacings WHEN 0 THEN NULL ELSE pos.VFacings END AS facing_height,
                                                                     CASE pos.YCapNum WHEN null THEN 0 ELSE pos.YCapNum END AS cap_number,
                                                                     CASE pos.MerchStyle WHEN 0 THEN '' WHEN 1 THEN 'SRP' WHEN null THEN '' END AS merch_style,
                                                                     CAST (spcperf.Flag4 AS varchar) AS new_flag,
                                                                     CAST (spcperf.Flag3 AS varchar) AS delete_flag,
                                                                     CAST (pos.Flag4 AS varchar) AS move_flag,
                                                                     CASE WHEN spcperf.Desc4 is null or spcperf.Desc4 = '<null>' THEN 'null' ELSE spcperf.Desc4 END AS pusher_flag,
                                                                     CASE WHEN spcperf.Desc6 is null THEN 'null' ELSE spcperf.Desc6 END AS tray_flag,
                                                                     CASE prd.value14 WHEN 0 THEN 'null' ELSE CAST (CAST (prd.value14 as decimal (10, 1)) AS varchar) END AS recstat_code,
                                                                     CASE WHEN prd.Desc11 is null THEN 'null' ELSE prd.Desc11 END AS product_group,
                                                                     CAST(pog.dbkey AS varchar)                                        AS pog_dbkey,
                                                                     pog.desc37 space_station_name,
                                                                     pog.desc42 space_station_version_description,
                                                                     pog.name pog_name,
                                                                     pog.status1 status,
                                                                     FORMAT(pog.livedate, 'MM-dd-yy') live_date,
                                                                     FORMAT(pog.pendingdate, 'MM-dd-yy') pending_date
                                                                 FROM
                                                                     [CKB].[dbo].IX_SPC_PLANOGRAM PREPOG
                                                                     INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM AS POG ON POG.DBVERSIONKEY = PREPOG.DBVERSIONKEY
                                                                     INNER JOIN [CKB].[dbo].IX_SPC_PERFORMANCE AS spcperf ON (pog.DBKEY = spcperf.DBPARENTPLANOGRAMKEY)
                                                                     LEFT OUTER JOIN [CKB].[dbo].IX_SPC_POSITION AS pos ON (spcperf.DBPARENTPLANOGRAMKEY = pos.DBPARENTPLANOGRAMKEY
                                                                     AND spcperf.DBPARENTPRODUCTKEY = pos.DBPARENTPRODUCTKEY)
                                                                     INNER JOIN [CKB].[dbo].IX_SPC_PRODUCT AS prd ON (spcperf.DBPARENTPRODUCTKEY = prd.DBKEY)
                                                                 WHERE pog.dbstatus IN ('1')
                                                                   AND pog.dbkey IN ('?')
                                                                   AND spcperf.Flag3 IS NOT NULL AND  spcperf.Flag3 = '0'
                                                             ) AS result order by pog_segment asc, fixture asc, position_number asc, pog_dbkey asc OFFSET (0) ROWS;
`


export let compositeTabQuery = `with seq_info as (
    SELECT STR_DBKEY,
           max(DISTINCT_PRODUCT_COUNT) as product_count,
           1                           as pog_count
    FROM [CKB].[jdacustom].RPT_STR_POG_MAP_LIVE_VW M_VIEW
                                group by STR_DBKEY, DISTINCT_PRODUCT_COUNT
                                    ),
                                    extra_info as (
                                SELECT STR_DBKEY,
                                    STR.desc2 as division,
                                    STR.desc3 as storenumber,
                                    STR.DBDateEffectiveFrom as fpld,
                                    sum (pog_count) as num_of_pogs,
                                    sum (product_count) as num_of_items
                                from seq_info
                                    INNER JOIN [CKB].[dbo].IX_STR_STORE STR
                                ON seq_info.STR_DBKEY = STR.DBKEY
                                group by STR_DBKEY, STR.desc2, STR.desc3, STR.DBDateEffectiveFrom
                                    ),
                                    final_info as (
                                SELECT distinct concat(extra_info.storenumber,
                                    CASE
                                    WHEN NOT EXISTS (SELECT DBPARENTFLOORPLANKEY
                                    FROM [CKB].[jdacustom].csg_composite_view_detail AS CVD
                                    WHERE extra_info.STR_DBKEY = CVD.DBPARENTSTOREKEY
                                    AND CVD.CV_Type = 1
                                    EXCEPT
                                    SELECT DBParentFloorplankey
                                    FROM [CKB].[jdacustom].csg_composite_view_detail AS CVD
                                    WHERE extra_info.STR_DBKEY = CVD.DBPARENTSTOREKEY
                                    AND CVD.CV_Type = 2) THEN 1
                                    ELSE cv.cv_type END) unikey,
                                    CASE
                                    WHEN NOT EXISTS (
                                    SELECT DBPARENTFLOORPLANKEY
                                    FROM [CKB].[jdacustom].csg_composite_view_detail AS CVD
                                    WHERE extra_info.STR_DBKEY = CVD.DBPARENTSTOREKEY
                                    AND CVD.CV_Type = 1
                                    EXCEPT
                                    SELECT DBParentFloorplankey
                                    FROM [CKB].[jdacustom].csg_composite_view_detail AS CVD
                                    WHERE extra_info.STR_DBKEY = CVD.DBPARENTSTOREKEY
                                    AND CVD.CV_Type = 2
                                    ) THEN 1
                                    ELSE cv.cv_type END AS cv_type,
                                    CASE WHEN cv.cv_document is null THEN 'no' ELSE 'yes' END AS cv_document,
                                    extra_info.division as pwy,
                                    extra_info.division,
                                    extra_info.storenumber,
                                    cv.FloorLevel as FloorLevel,
                                    extra_info.num_of_pogs,
                                    extra_info.num_of_items,
                                    max (try_cast(extra_info.fpld as date)) fpld,
                                    (select CONCAT(CONCAT('P', format(fsc.period, '0#')), ' ', CONCAT('W', format(fsc.week, '0#')),
                                    ' ', CONCAT('Y', format(fsc.Year, '0#')))
                                    from [CKB].[jdacustom].fiscal_calendar fsc
                                    where date = max (try_cast(extra_info.fpld AS date))) as pwystr,
                                    count (*) OVER () as totalrecords
                                from extra_info
                                    LEFT OUTER JOIN [CKB].[JDACUSTOM].CSG_COMPOSITE_VIEW_DOCUMENT AS CV
                                ON extra_info.STR_DBKEY = CV.DBPARENTSTOREKEY
                                    INNER JOIN [CKB].[JDACUSTOM].CSG_COMPOSITE_VIEW_DETAIL AS CV_DTL
                                    ON extra_info.STR_DBKEY = CV_DTL.DBPARENTSTOREKEY
                                WHERE extra_info.division IN ('@Div')
                                  AND extra_info.storenumber IN ('@Store')
                                group by cv.CV_Type, cv_document, cv.FloorLevel, extra_info.division, extra_info.storenumber, extra_info.num_of_pogs,
                                    extra_info.num_of_items, extra_info.STR_DBKEY, extra_info.fpld
                                    )
SELECT division    as Div,
       storenumber as Store,
       case when cv_type in (1) THEN 'Live' ELSE 'Future Live' END as 'Composite View Type', FORMAT(fpld, 'yyyy-MM-dd') as Date,
       pwystr       as PWY,
       CAST(FloorLevel AS VARCHAR) as FloorLevel,
       num_of_pogs  as '# of Planograms',
       num_of_items 'as # of Items'
from final_info;`

export let mappingReportSsnQuery = `Select division,
                                           store_number,
                                           store_type,
                                           pog_status,
                                           pog_type,
                                           pog_dbkey,
                                           space_station_name,
                                           space_station_version_description,
                                           pog_pendingdate,
                                           pog_livedate,
                                           dept,
                                           aisle,
                                           ad,
                                           orientation,
                                           pog_sequence,
                                           num_of_items,
                                           num_of_stores,
                                           pog_name,
                                           min_bay,
                                           max_bay,
                                           strip_color,
                                           fp_dbkey,
                                           fp_status,
                                           fp_pendingdate,
                                           fp_livedate,
                                           store_segment,
                                           alt_store_number,
                                           planogram_department,
                                           planogram_sub_department,
                                           commodity_number,
                                           commodity_group
                                    FROM (Select distinct *,
                                                          POGTypeOrder = case
                                                                             when pog_type in ('Display Space', 'White Space')
                                                                                 THEN 1
                                                                             ELSE 0 END
                                          FROM (
                                                   SELECT distinct concat(STR.Desc2, STR.DESC3, M_VIEW.FP_DBKEY,
                                                                          M_VIEW.POG_DBKEY, POG.DESC6, POG.NAME,
                                                                          POG.DESC37, POG.DESC42, POG.LIVEDATE,
                                                                          POG.PENDINGDATE, POG.STATUS1, FP.LIVEDATE,
                                                                          FP.PENDINGDATE, FP.STATUS1,
                                                                          M_VIEW.DISTINCT_PRODUCT_COUNT,
                                                                          M_VIEW.STRIP_COLOR,
                                                                          M_VIEW.AISLE_NO, M_VIEW.AISLE_SIDE_CD,
                                                                          AISLE_N.AISLE_NAME, M_VIEW.POG_SEQUENCE,
                                                                          M_VIEW.MIN_POG_BAY, M_VIEW.MAX_POG_BAY,
                                                                          DEP.DEPT_NAME,
                                                                          STR.DESC49, STR.DESC7, STR.EMAIL, POG.DEPARTMENT,
                                                                          POG.DESC10, POG.DESC22, POG.DESC3,
                                                                          POG.NUMBEROFSTORES)    AS    unikey,
                                                                   STR.DESC2                     AS    division,
                                                                   STR.DESC3                     AS    store_number,
                                                                   STR.DESC49                    AS    store_type,
                                                                   POG.STATUS1                   AS    pog_status,
                                                                   POG.DESC6                     AS    pog_type,
                                                                   FORMAT(POG.PENDINGDATE, 'MM-dd-yy') pog_pendingdate,
                                                                   FORMAT(POG.LIVEDATE, 'MM-dd-yy')    pog_livedate,
                                                                   DEP.DEPT_NAME                 AS    dept,
                                                                   M_VIEW.AISLE_NO               AS    aisle,
                                                                   AISLE_N.AISLE_NAME            AS    ad,
                                                                   M_VIEW.AISLE_SIDE_CD          AS    orientation,
                                                                   CAST(M_VIEW.POG_SEQUENCE AS varchar)   AS    pog_sequence,
                                                                   CAST(M_VIEW.DISTINCT_PRODUCT_COUNT  AS varchar) AS  num_of_items,
                                                                   CAST(POG.NUMBEROFSTORES AS varchar)    AS    num_of_stores,
                                                                   CAST(POG.DBKEY AS varchar)             AS    pog_dbkey,
                                                                   POG.DESC37                    AS    space_station_name,
                                                                   POG.DESC42                    AS    space_station_version_description,
                                                                   POG.NAME                      AS    pog_name,
                                                                   M_VIEW.MIN_POG_BAY            AS    min_bay,
                                                                   M_VIEW.MAX_POG_BAY            AS    max_bay,
                                                                   M_VIEW.STRIP_COLOR            AS    strip_color,
                                                                   CAST(FP.DBKEY AS varchar )    AS    fp_dbkey,
                                                                   FP.STATUS1                    AS    fp_status,
                                                                   FORMAT(FP.PENDINGDATE, 'MM-dd-yy')  fp_pendingdate,
                                                                   FORMAT(FP.LIVEDATE, 'MM-dd-yy')     fp_livedate,
                                                                   STR.Desc7                     AS    store_segment,
                                                                   STR.Email                     AS    alt_store_number,
                                                                   POG.Department                AS    planogram_department,
                                                                   POG.Desc10                    AS    planogram_sub_department,
                                                                   POG.Desc22                    AS    commodity_number,
                                                                   POG.Desc3                     AS    commodity_group
                                                   FROM [CKB].[jdacustom].RPT_STR_POG_MAP_LIVE_VW M_VIEW
                                                       INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG
                                                   ON M_VIEW.POG_DBKEY = POG.DBKEY
                                                       INNER JOIN [CKB].[dbo].IX_FLR_FLOORPLAN FP ON M_VIEW.FP_DBKEY = FP.DBKEY
                                                       INNER JOIN [CKB].[dbo].IX_FLR_SECTION SEC
                                                       ON SEC.DBPARENTFLOORPLANKEY = FP.DBKEY AND SEC.DBPARENTPLANOGRAMKEY = POG.DBVERSIONKEY
                                                       INNER JOIN [CKB].[JDACUSTOM].CSG_DEPT DEP ON DEP.DEPT_CD = M_VIEW.FFIX_DEPT
                                                       INNER JOIN [CKB].[dbo].IX_STR_STORE STR ON M_VIEW.STR_DBKEY = STR.DBKEY
                                                       LEFT OUTER JOIN [CKB].[jdacustom].csg_Aisle AISLE_N
                                                       ON M_VIEW.AISLE_NO = RIGHT ('000' + ISNULL(CAST (AISLE_N.AISLE_NO AS VARCHAR), ''), 3)
                                                   WHERE
                                                       STR.DESC2 = '@Div'
                                                     AND STR.DESC3 IN ('@StoreList')
                                                     AND pog.desc37 = '@SsnValue'
                                                     AND STR.DBSTATUS = 1
                                                     AND POG.DBSTATUS = 1
                                                     AND FP.DBSTATUS = 1
                                               ) AS result) AS X
                                    order by division asc, store_number asc, case when aisle is null or aisle = '' then 1 else 0 end, aisle asc,
                                             orientation asc, min_bay asc, max_bay asc, pog_sequence asc;`

export let mappingReportAisleQuery = `Select division,
                                           store_number,
                                           store_type,
                                           pog_status,
                                           pog_type,
                                           pog_pendingdate,
                                           pog_livedate,
                                           dept,
                                           aisle,
                                           ad,
                                           orientation,
                                           pog_sequence,
                                           num_of_items,
                                           num_of_stores,
                                           pog_dbkey,
                                           space_station_name,
                                           space_station_version_description,
                                           pog_name,
                                           min_bay,
                                           max_bay,
                                           strip_color,
                                           fp_dbkey,
                                           fp_status,
                                           fp_pendingdate,
                                           fp_livedate,
                                           store_segment,
                                           alt_store_number,
                                           planogram_department,
                                           planogram_sub_department,
                                           commodity_number,
                                           commodity_group
                                    FROM (Select distinct *,
                                                          POGTypeOrder = case
                                                                             when pog_type in ('Display Space', 'White Space')
                                                                                 THEN 1
                                                                             ELSE 0 END
                                          FROM (
                                                   SELECT distinct concat(STR.Desc2, STR.DESC3, M_VIEW.FP_DBKEY,
                                                                          M_VIEW.POG_DBKEY, POG.DESC6, POG.NAME,
                                                                          POG.DESC37, POG.DESC42, POG.LIVEDATE,
                                                                          POG.PENDINGDATE, POG.STATUS1, FP.LIVEDATE,
                                                                          FP.PENDINGDATE, FP.STATUS1,
                                                                          M_VIEW.DISTINCT_PRODUCT_COUNT,
                                                                          M_VIEW.STRIP_COLOR,
                                                                          M_VIEW.AISLE_NO, M_VIEW.AISLE_SIDE_CD,
                                                                          AISLE_N.AISLE_NAME, M_VIEW.POG_SEQUENCE,
                                                                          M_VIEW.MIN_POG_BAY, M_VIEW.MAX_POG_BAY,
                                                                          DEP.DEPT_NAME,
                                                                          STR.DESC49, STR.DESC7, STR.EMAIL, POG.DEPARTMENT,
                                                                          POG.DESC10, POG.DESC22, POG.DESC3,
                                                                          POG.NUMBEROFSTORES)    AS    unikey,
                                                                   STR.DESC2                     AS    division,
                                                                   STR.DESC3                     AS    store_number,
                                                                   STR.DESC49                    AS    store_type,
                                                                   POG.STATUS1                   AS    pog_status,
                                                                   POG.DESC6                     AS    pog_type,
                                                                   FORMAT(POG.PENDINGDATE, 'MM-dd-yy') pog_pendingdate,
                                                                   FORMAT(POG.LIVEDATE, 'MM-dd-yy')    pog_livedate,
                                                                   DEP.DEPT_NAME                 AS    dept,
                                                                   M_VIEW.AISLE_NO               AS    aisle,
                                                                   AISLE_N.AISLE_NAME            AS    ad,
                                                                   M_VIEW.AISLE_SIDE_CD          AS    orientation,
                                                                   CAST(M_VIEW.POG_SEQUENCE AS varchar)   AS    pog_sequence,
                                                                   CAST(M_VIEW.DISTINCT_PRODUCT_COUNT  AS varchar) AS  num_of_items,
                                                                   CAST(POG.NUMBEROFSTORES AS varchar)    AS    num_of_stores,
                                                                   CAST(POG.DBKEY AS varchar)             AS    pog_dbkey,
                                                                   POG.DESC37                    AS    space_station_name,
                                                                   POG.DESC42                    AS    space_station_version_description,
                                                                   POG.NAME                      AS    pog_name,
                                                                   M_VIEW.MIN_POG_BAY            AS    min_bay,
                                                                   M_VIEW.MAX_POG_BAY            AS    max_bay,
                                                                   M_VIEW.STRIP_COLOR            AS    strip_color,
                                                                   CAST(FP.DBKEY AS varchar )    AS    fp_dbkey,
                                                                   FP.STATUS1                    AS    fp_status,
                                                                   FORMAT(FP.PENDINGDATE, 'MM-dd-yy')  fp_pendingdate,
                                                                   FORMAT(FP.LIVEDATE, 'MM-dd-yy')     fp_livedate,
                                                                   STR.Desc7                     AS    store_segment,
                                                                  -- STR.Email                     AS    alt_store_number,
                                                                   CASE WHEN STR.Email is null THEN 'null' ELSE CAST(STR.Email as varchar) END AS alt_store_number,
                                                                   
                                                                   POG.Department                AS    planogram_department,
                                                                   POG.Desc10                    AS    planogram_sub_department,
                                                                   POG.Desc22                    AS    commodity_number,
                                                                   POG.Desc3                     AS    commodity_group
                                                   FROM [CKB].[jdacustom].RPT_STR_POG_MAP_LIVE_VW M_VIEW
                                                       INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG
                                                   ON M_VIEW.POG_DBKEY = POG.DBKEY
                                                       INNER JOIN [CKB].[dbo].IX_FLR_FLOORPLAN FP ON M_VIEW.FP_DBKEY = FP.DBKEY
                                                       INNER JOIN [CKB].[dbo].IX_FLR_SECTION SEC
                                                       ON SEC.DBPARENTFLOORPLANKEY = FP.DBKEY AND SEC.DBPARENTPLANOGRAMKEY = POG.DBVERSIONKEY
                                                       INNER JOIN [CKB].[JDACUSTOM].CSG_DEPT DEP ON DEP.DEPT_CD = M_VIEW.FFIX_DEPT
                                                       INNER JOIN [CKB].[dbo].IX_STR_STORE STR ON M_VIEW.STR_DBKEY = STR.DBKEY
                                                       LEFT OUTER JOIN [CKB].[jdacustom].csg_Aisle AISLE_N
                                                       ON M_VIEW.AISLE_NO = RIGHT ('000' + ISNULL(CAST (AISLE_N.AISLE_NO AS VARCHAR), ''), 3)
                                                   WHERE
                                                       STR.DESC2 = '@Div'
                                                     AND STR.DESC3 IN ('@Stores')
                                                     AND M_VIEW.AISLE_NO IN ( '@Aisle')
                                                     AND STR.DBSTATUS = 1
                                                     AND POG.DBSTATUS = 1
                                                     AND FP.DBSTATUS = 1
                                               ) AS result) AS X
                                    order by division asc, store_number asc, case when aisle is null or aisle = '' then 1 else 0 end, aisle asc,
                                             orientation asc, min_bay asc, max_bay asc, pog_sequence asc;`

export let pogSsnQuery = `Select DISTINCT pog.status1 AS Status,
                                          POG.DESC6 AS 'POG Type',
                              FORMAT (pog.pendingdate, 'MM-dd-yy') AS 'Pending Date',
                              FORMAT (pog.livedate, 'MM-dd-yy') AS 'Date Live',
                              pog.desc38 AS 'Period Week Year',
                              CAST(pog.NumberOfStores AS varchar) AS 'Store Count',
                              CAST(pog.NumberOfProductsAllocated AS varchar) AS 'Number(#) of Items',
                              CAST(pog.dbkey AS varchar) AS DBkey,
                                          pog.desc37 AS 'Space Station Name',
                              pog.desc42 AS 'Space Station Version Description',
                              pog.name AS 'POG Name'
                --prd.NAME AS product_name,
                          FROM [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
                              INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG ON M_VIEW.POG_DBKEY = POG.DBKEY
                              LEFT JOIN [CKB].[dbo].IX_STR_STORE str ON M_VIEW.STR_DBKEY = str.DBKEY
                          where str.Desc2= '@Div'
                            and pog.desc37 = '@Upc';`


export let mappingReportDivStoreQuery = `Select division,
                                                store_number,
                                                pog_status,
                                                pog_type,
                                                pog_pendingdate,
                                                pog_livedate,
                                                dept,
                                                aisle,
                                                ad,
                                                orientation,
                                                pog_sequence,
                                                num_of_items,
                                                pog_dbkey,
                                                space_station_name,
                                                space_station_version_description,
                                                pog_name,
                                                min_bay,
                                                max_bay,
                                                strip_color,
                                                fp_dbkey,
                                                fp_status,
                                                fp_pendingdate,
                                                fp_livedate
                                         FROM (Select distinct *,
                                                               POGTypeOrder = case
                                                                                  when pog_type in ('Display Space', 'White Space')
                                                                                      THEN 1
                                                                                  ELSE 0 END
                                               FROM (
                                                        SELECT distinct concat(STR.Desc2, STR.DESC3, M_VIEW.FP_DBKEY,
                                                                               M_VIEW.POG_DBKEY, POG.DESC6, POG.NAME,
                                                                               POG.DESC37, POG.DESC42, POG.LIVEDATE,
                                                                               POG.PENDINGDATE, POG.STATUS1,
                                                                               FP.LIVEDATE,
                                                                               FP.PENDINGDATE, FP.STATUS1,
                                                                               M_VIEW.DISTINCT_PRODUCT_COUNT,
                                                                               M_VIEW.STRIP_COLOR,
                                                                               M_VIEW.AISLE_NO, M_VIEW.AISLE_SIDE_CD,
                                                                               AISLE_N.AISLE_NAME, M_VIEW.POG_SEQUENCE,
                                                                               M_VIEW.MIN_POG_BAY, M_VIEW.MAX_POG_BAY,
                                                                               FIX.desc8)             AS    unikey,
                                                                        STR.DESC2                     AS    division,
                                                                        STR.DESC3                     AS    store_number,
                                                                        POG.STATUS1                   AS    pog_status,
                                                                        POG.DESC6                     AS    pog_type,
                                                                        FORMAT(POG.PENDINGDATE, 'MM-dd-yy') pog_pendingdate,
                                                                        FORMAT(POG.LIVEDATE, 'MM-dd-yy')    pog_livedate,
                                                                        FIX.desc8                     AS    dept,
                                                                        M_VIEW.AISLE_NO               AS    aisle,
                                                                        AISLE_N.AISLE_NAME            AS    ad,
                                                                        M_VIEW.AISLE_SIDE_CD          AS    orientation,
                                                                        M_VIEW.POG_SEQUENCE           AS    pog_sequence,
                                                                        M_VIEW.DISTINCT_PRODUCT_COUNT AS    num_of_items,
                                                                        POG.DBKEY                     AS    pog_dbkey,
                                                                        POG.DESC37                    AS    space_station_name,
                                                                        POG.DESC42                    AS    space_station_version_description,
                                                                        POG.NAME                      AS    pog_name,
                                                                        M_VIEW.MIN_POG_BAY            AS    min_bay,
                                                                        M_VIEW.MAX_POG_BAY            AS    max_bay,
                                                                        M_VIEW.STRIP_COLOR            AS    strip_color,
                                                                        FP.DBKEY                      AS    fp_dbkey,
                                                                        FP.STATUS1                    AS    fp_status,
                                                                        FORMAT(FP.PENDINGDATE, 'MM-dd-yy')  fp_pendingdate,
                                                                        FORMAT(FP.LIVEDATE, 'MM-dd-yy')     fp_livedate
                                                        FROM [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
                                                            INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG
                                                        ON M_VIEW.POG_DBKEY = POG.DBKEY
                                                            INNER JOIN [CKB].[dbo].IX_FLR_FLOORPLAN FP ON M_VIEW.FP_DBKEY = FP.DBKEY
                                                            INNER JOIN [CKB].[dbo].IX_FLR_SECTION SEC
                                                            ON SEC.DBPARENTFLOORPLANKEY = FP.DBKEY AND SEC.DBPARENTPLANOGRAMKEY = POG.DBVERSIONKEY
                                                            LEFT JOIN [CKB].[dbo].IX_FLR_FIXTURE FIX ON FIX.DBKEY = SEC.DBPARENTFIXTUREKEY AND
                                                            SEC.DBPARENTFLOORPLANKEY = FIX.DBPARENTFLOORPLANKEY
                                                            INNER JOIN [CKB].[dbo].IX_STR_STORE STR ON M_VIEW.STR_DBKEY = STR.DBKEY
                                                            LEFT OUTER JOIN [CKB].[jdacustom].csg_Aisle AISLE_N
                                                            ON M_VIEW.AISLE_NO = RIGHT ('000' + ISNULL(CAST (AISLE_N.AISLE_NO AS VARCHAR), ''), 3)
                                                        WHERE STR.DESC2 IN ('?')
                                                          AND STR.DESC3 IN ('$')
                                                          AND STR.DBSTATUS = 1
                                                          AND POG.DBSTATUS = 1
                                                          and FP.DBSTATUS = 1
                                                    ) AS result) AS X
                                         order by division asc, store_number asc,
                                                  case when pog_type in ('Display Space', 'White Space') THEN 1 ELSE 0 END,
                                                  aisle asc, orientation asc, min_bay asc, max_bay asc,
                                                  pog_sequence asc;`

export let FpDepartFloorPlansQuery = `SELECT distinct FP.desc2 FP_Department
                                      from (
                                               SELECT STR_DBKEY          as str_dbkey,
                                                      FP_DBKEY,
                                                      STR.desc2          as DivNo,
                                                      STR.desc3          as StoreNo,
                                                      sum(pog_count)     as num_of_pogs,
                                                      sum(product_count) as num_of_items
                                               from (
                                                        SELECT STR_DBKEY,
                                                               FP_DBKEY,
                                                               POG_DBKEY,
                                                               max(DISTINCT_PRODUCT_COUNT) as product_count,
                                                               1                           as pog_count
                                                        FROM [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
                                                        group by STR_DBKEY, FP_DBKEY, DISTINCT_PRODUCT_COUNT, POG_DBKEY
                                                    ) AS STR_FP_POG_INFO_SEQ
                                                        INNER JOIN [CKB].[dbo].IX_STR_STORE STR
                                               ON STR_FP_POG_INFO_SEQ.STR_DBKEY = STR.DBKEY
                                                   inner join [ckb].[dbo].ix_spc_planogram pog on pog.dbkey = STR_FP_POG_INFO_SEQ.pog_dbkey
                                               where 1 = 1
                                               group by STR_DBKEY, FP_DBKEY, STR.desc2, STR.desc3) AS MORE_INFO
                                               INNER JOIN [CKB].[dbo].IX_FLR_FLOORPLAN FP
                                      ON MORE_INFO.FP_DBKEY = FP.DBKEY
                                      WHERE 1=1
                                        AND MORE_INFO.DivNo IN (SELECT value FROM STRING_SPLIT('${DataJson.Division_016}'
                                          , 'T'))
                                        AND MORE_INFO.StoreNo IN (SELECT value FROM STRING_SPLIT('${DataJson.Store_00268}'
                                          , 'T'))
                                      order by FP_Department asc;`

export let ItemByPOGQueryPOGDBKeyFilter = `Select POGS_RANKED_BY_FAMILY.*
                                             FROM (
                                                      Select distinct prd.upc,
                                                                      prd.ID                                                     AS product_case,
                                                                      prd.PartID                                                 AS sku,
                                                                      prd.DESC50                                                 AS magic_lead,
                                                                      productNameCondition                                       AS product_name,
                                                                      CAST(pos.value8 AS int)                                    AS pog_segment,
                                                                      CAST(pos.value6 AS int)                                       fixture,
                                                                      CAST(pos.value5 AS int)                                       position_number,
                                                                      CASE pos.HFacings WHEN 0 THEN NULL ELSE pos.HFacings END   AS facing_width,
                                                                      CASE pos.VFacings WHEN 0 THEN NULL ELSE pos.VFacings END   AS facing_height,
                                                                      CASE WHEN pos.YCapNum = 0 OR pos.YCapNum IS NULL THEN 0 ELSE pos.YCapNum END AS cap_number,
                                                                      CASE pos.MerchStyle WHEN 0 THEN '' WHEN 1 THEN 'SRP' END AS merch_style,
                                                                      CAST(spcperf.Flag4 AS varchar)                             AS new_flag,
                                                                      CAST(spcperf.Flag3 AS varchar)                             AS delete_flag,
                                                                      CAST(pos.Flag4 AS varchar)                                 AS move_flag,
                                                                      spcperf.Desc4                                              AS pusher_flag,
                                                                      spcperf.Desc6                                              AS tray_flag,
                                                                      CASE prd.value14 WHEN 0 THEN null ELSE CAST (CAST (prd.value14 as decimal (10, 1)) AS varchar) END AS recstat_code,
                                                                      prd.Desc11                                                 AS product_group,
                                                                      pog.dbkey                                                     pog_dbkey,
                                                                      pog.desc37                                                    space_station_name,
                                                                      pog.desc42                                                    space_station_version_description,
                                                                      pog.name                                                      pog_name,
                                                                      pog.status1                                                   status,
                                                                      FORMAT(pog.livedate, 'MM-dd-yy')                              live_date,
                                                                      FORMAT(pog.pendingdate, 'MM-dd-yy')                           pending_date
                                                      FROM [CKB].[dbo].IX_SPC_PLANOGRAM POG
                                                          INNER JOIN [CKB].[dbo].IX_SPC_PERFORMANCE AS spcperf
                                                      ON (pog.DBKEY = spcperf.DBPARENTPLANOGRAMKEY)
                                                          LEFT OUTER JOIN [CKB].[dbo].IX_SPC_POSITION AS pos
                                                          ON (spcperf.DBPARENTPLANOGRAMKEY = pos.DBPARENTPLANOGRAMKEY AND
                                                          spcperf.DBPARENTPRODUCTKEY = pos.DBPARENTPRODUCTKEY)
                                                          INNER JOIN [CKB].[dbo].IX_SPC_PRODUCT AS prd ON (spcperf.DBPARENTPRODUCTKEY = prd.DBKEY)
                                                          INNER JOIN [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW ON M_VIEW.POG_DBKEY = POG.DBKEY
                                                          INNER JOIN [CKB].[dbo].IX_STR_STORE AS str ON M_VIEW.STR_DBKEY = str.DBKEY
                                                      WHERE pog.dbstatus IN (1
                                                          , 2)
                                                        AND POG.DBVERSIONKEY IN (SELECT POG.DBVERSIONKEY
                                                          FROM [CKB].[dbo].IX_SPC_PLANOGRAM AS POG
                                                          WHERE 1 = 1
                                                        AND pog.dbkey IN ('?')
                                                        AND FILTER_CONDITION)
                                                      UNION
                                                      Select distinct
                                                          prd.upc,
                                                          prd.ID AS product_case,
                                                          prd.PartID AS sku,
                                                          prd.DESC50 AS magic_lead,
                                                          productNameCondition  AS product_name,
                                                          CAST (pos.value8 AS int) AS pog_segment,
                                                          CAST (pos.value6 AS int) fixture,
                                                          CAST (pos.value5 AS int) position_number,
                                                          CASE pos.HFacings WHEN 0 THEN NULL ELSE pos.HFacings END AS facing_width,
                                                          CASE pos.VFacings WHEN 0 THEN NULL ELSE pos.VFacings END AS facing_height,
                                                          CASE WHEN pos.YCapNum = 0 OR pos.YCapNum IS NULL THEN 0 ELSE pos.YCapNum END AS cap_number,
                                                          CASE pos.MerchStyle WHEN 0 THEN '' WHEN 1 THEN 'SRP' END AS merch_style,
                                                          CAST (spcperf.Flag4 AS varchar) AS new_flag,
                                                          CAST (spcperf.Flag3 AS varchar) AS delete_flag,
                                                          CAST (pos.Flag4 AS varchar) AS move_flag,
                                                          spcperf.Desc4 AS pusher_flag,
                                                          spcperf.Desc6 AS tray_flag,
                                                          CASE prd.value14 WHEN 0 THEN null ELSE CAST (CAST (prd.value14 as decimal (10, 1)) AS varchar) END AS recstat_code,
                                                          prd.Desc11 AS product_group,
                                                          pog.dbkey pog_dbkey,
                                                          pog.desc37 space_station_name,
                                                          pog.desc42 space_station_version_description,
                                                          pog.name pog_name,
                                                          pog.status1 status,
                                                          FORMAT(pog.livedate, 'MM-dd-yy') live_date,
                                                          FORMAT(pog.pendingdate, 'MM-dd-yy') pending_date
                                                      FROM [CKB].[dbo].IX_SPC_PLANOGRAM POG
                                                          INNER JOIN [CKB].[dbo].IX_SPC_PERFORMANCE AS spcperf
                                                      ON (pog.DBKEY = spcperf.DBPARENTPLANOGRAMKEY)
                                                          LEFT OUTER JOIN [CKB].[dbo].IX_SPC_POSITION AS pos
                                                          ON (spcperf.DBPARENTPLANOGRAMKEY = pos.DBPARENTPLANOGRAMKEY AND
                                                          spcperf.DBPARENTPRODUCTKEY = pos.DBPARENTPRODUCTKEY)
                                                          INNER JOIN [CKB].[dbo].IX_SPC_PRODUCT AS prd ON (spcperf.DBPARENTPRODUCTKEY = prd.DBKEY)
                                                          INNER JOIN [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW ON M_VIEW.POG_DBKEY = POG.DBKEY
                                                          INNER JOIN [CKB].[dbo].IX_STR_STORE AS str ON M_VIEW.STR_DBKEY = str.DBKEY
                                                      WHERE pog.dbstatus IN (1
                                                          , 2)
                                                        AND POG.DBVERSIONKEY IN (SELECT POG.DBVERSIONKEY
                                                          FROM [CKB].[dbo].IX_SPC_PLANOGRAM AS POG
                                                          WHERE 1 = 1
                                                        AND pog.dbkey IN ('?')
                                                        AND FILTER_CONDITION
                                                          )) POGS_RANKED_BY_FAMILY
                                             order by pog_segment asc, fixture asc,
                                                      position_number asc;`

export let storeListQuery = `select DISTINCT STR.DESC3
                             from [CKB].[dbo].IX_STR_STORE STR
                                 INNER JOIN [CKB].[jdacustom].RPT_STR_POG_MAP_LIVE_VW M_VIEW
                             ON M_VIEW.STR_DBKEY = STR.DBKEY
                             WHERE STR.DESC2 = '${DataJson.Division_014}'
                               and M_VIEW.STR_DBStatus <> 4;`

export let pogItemByStoreQueryPOG = `SELECT STR.desc2            AS Div,
                                             STR.desc3            AS Store,
                                             PDT.UPC,
                                             PDT.ID AS     'Case', PDT.PartID AS SKU,
                                             PDT.DESC50           AS Magic_Lead_Article_Number,
                                             PDT.NAME             AS Product_Name,
                                             M_VIEW.AISLE_NO      AS Aisle_#,
                                             AISLE_N.aisle_name   AS Aisle_Desc,
                                             M_VIEW.AISLE_SIDE_CD AS Orn,
                                             M_VIEW.POG_SEQUENCE  AS Seq,
                                             M_VIEW.MAX_POG_BAY   AS Bay_In_Aisle,
                                             POS.Value8           AS POG_Segment,
                                             POS.Value6           AS Fixture,
                                             POS.Value5 AS     Position,
       CAST(SPERF.Flag4 AS varchar) AS     new_flag,
       CAST(SPERF.Flag3 AS varchar) AS     delete_flag,
       CAST(pos.Flag4 AS varchar)   AS     move_flag,
       SPERF.Desc4                  AS     Pusher,
       SPERF.Desc6                  AS     Tray,
       CASE PDT.value14
           WHEN 0 THEN null
           ELSE PDT.value14
END
AS     recstat_code,
       PDT.Desc11                   AS     Prod_Group,
       POG.dbkey                    as     POG_DBKEY,
       POG.desc37                          Space_Station_Name,
       POG.desc42                          Space_Station_Version_Description,
       POG.name                     as     Name,
       POG.status1                  as     POG_Status,
       FORMAT(POG.livedate, 'MM-dd-yy')    Pog_Live_Date,
       FORMAT(POG.pendingdate, 'MM-dd-yy') Pog_Pending_Date,
       FP.status1                   AS     FP_Status,
       FORMAT(FP.LiveDate, 'MM-dd-yy')     FP_Live_Date,
       FORMAT(FP.PendingDate, 'MM-dd-yy')  FP_Pending_Date,
       FP.desc40                           Event_Type,
       FP.desc45                           Event_Name
FROM [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
         INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG ON M_VIEW.POG_DBKEY = POG.DBKEY
         INNER JOIN [CKB].[dbo].IX_FLR_FLOORPLAN FP ON M_VIEW.FP_DBKEY = FP.DBKEY
         INNER JOIN [CKB].[dbo].IX_STR_STORE AS STR ON (M_VIEW.STR_DBKEY = STR.DBKEY)
    and (fp.desc21 = str.desc2 and fp.desc22 = str.desc3)
         INNER JOIN [CKB].[dbo].IX_SPC_POSITION AS POS ON (POG.DBKEY = POS.DBPARENTPLANOGRAMKEY)
         LEFT OUTER JOIN [CKB].[dbo].IX_SPC_PERFORMANCE AS SPERF
                         ON (SPERF.DBPARENTPLANOGRAMKEY = POS.DBPARENTPLANOGRAMKEY AND
                             SPERF.DBPARENTPRODUCTKEY = POS.DBPARENTPRODUCTKEY)
         INNER JOIN [CKB].[dbo].IX_SPC_PRODUCT AS PDT ON (SPERF.DBPARENTPRODUCTKEY = PDT.DBKEY)
         LEFT OUTER JOIN [CKB].[jdacustom].csg_Aisle AISLE_N
                         ON M_VIEW.AISLE_NO = RIGHT('000' + ISNULL(CAST(AISLE_N.AISLE_NO AS VARCHAR), ''''), 3)
WHERE STR.dbstatus = 1
  and FP.dbstatus in (1, 2)
  and POG.dbstatus in (1, 2)
  AND POG.dbkey IN ('${DataJson.pogKey_4788078}')
  AND STR.desc3 IN ('${DataJson.Store_00353}')
  AND STR.desc2 IN ('${DataJson.Division_014}')
 order by Div asc, store asc, space_station_name asc, pog_segment asc, fixture asc, Position asc ;
`

export let getMappingReportPOGKey = `select TOP(1) POG.DBKEY
                                     from [CKB].[dbo].IX_SPC_PLANOGRAM POG
                                         INNER JOIN [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
                                     ON M_VIEW.POG_DBKEY = POG.DBKEY
                                         INNER JOIN [CKB].[dbo].IX_STR_STORE STR ON M_VIEW.STR_DBKEY = STR.DBKEY
                                     WHERE STR.DESC2 = '@div'
                                       AND STR.DESC3 = '@store'
                                       AND POG.NUMBEROFSTORES >0
                                     ORDER BY POG.DBKEY DESC;`

export let mappingReportNoOfStores = `Select distinct  division,
                                                       store_number,
                                                       store_type,
                                                       pog_status,
                                                       pog_type,
                                                       CAST(pog_dbkey as varchar ) AS pog_dbkey,
                                                       space_station_name,
                                                       space_station_version_description,
                                                       pog_pendingdate,
                                                       pog_livedate,
                                                       dept,
                                                       aisle,
                                                       ad,
                                                       orientation,
                                                       pog_sequence,
                                                       CAST(num_of_items as varchar ) AS num_of_items,
                                                       CAST(num_of_stores as varchar ) AS num_of_stores,
                                                       pog_name,
                                                       min_bay,
                                                       max_bay,
                                                       strip_color,
                                                       CAST(fp_dbkey as varchar ) AS fp_dbkey,
                                                       fp_status,
                                                       fp_pendingdate,
                                                       fp_livedate,
                                                       store_segment,
                                                       alt_store_number,
                                                       planogram_department,
                                                       planogram_sub_department,
                                                       commodity_number,
                                                       commodity_group
FROM (Select distinct *,
                      POGTypeOrder = case
                                         when pog_type in ('Display Space', 'White Space')
                                             THEN 1
                                         ELSE 0 END
      FROM (
               SELECT distinct concat(STR.Desc2, STR.DESC3, M_VIEW.FP_DBKEY,
                                      M_VIEW.POG_DBKEY, POG.DESC6, POG.NAME,
                                      POG.DESC37, POG.DESC42, POG.LIVEDATE,
                                      POG.PENDINGDATE, POG.STATUS1, FP.LIVEDATE,
                                      FP.PENDINGDATE, FP.STATUS1,
                                      M_VIEW.DISTINCT_PRODUCT_COUNT,
                                      M_VIEW.STRIP_COLOR,
                                      M_VIEW.AISLE_NO, M_VIEW.AISLE_SIDE_CD,
                                      AISLE_N.AISLE_NAME, M_VIEW.POG_SEQUENCE,
                                      M_VIEW.MIN_POG_BAY, M_VIEW.MAX_POG_BAY,
                                      DEP.DEPT_NAME,
                                      STR.DESC49, STR.DESC7, STR.EMAIL,
                                      POG.DEPARTMENT, POG.DESC10, POG.DESC22,
                                      POG.DESC3, POG.NUMBEROFSTORES) AS unikey,
                               STR.DESC2                             AS division,
                               STR.DESC3                             AS store_number,
                               STR.DESC49                            AS store_type,
                               POG.STATUS1                           AS pog_status,
                               POG.DESC6                             AS pog_type,
                               FORMAT(POG.PENDINGDATE, 'MM-dd-yy')      pog_pendingdate,
                               FORMAT(POG.LIVEDATE, 'MM-dd-yy')         pog_livedate,
                               DEP.DEPT_NAME                         AS dept,
                               M_VIEW.AISLE_NO                       AS aisle,
                               AISLE_N.AISLE_NAME                    AS ad,
                               M_VIEW.AISLE_SIDE_CD                  AS orientation,
                               M_VIEW.POG_SEQUENCE                   AS pog_sequence,
                               M_VIEW.DISTINCT_PRODUCT_COUNT         AS num_of_items,
                               POG.NUMBEROFSTORES                    AS num_of_stores,
                               POG.DBKEY                             AS pog_dbkey,
                               POG.DESC37                            AS space_station_name,
                               POG.DESC42                            AS space_station_version_description,
                               POG.NAME                              AS pog_name,
                               M_VIEW.MIN_POG_BAY                    AS min_bay,
                               M_VIEW.MAX_POG_BAY                    AS max_bay,
                               M_VIEW.STRIP_COLOR                    AS strip_color,
                               FP.DBKEY                              AS fp_dbkey,
                               FP.STATUS1                            AS fp_status,
                               FORMAT(FP.PENDINGDATE, 'MM-dd-yy')       fp_pendingdate,
                               FORMAT(FP.LIVEDATE, 'MM-dd-yy')          fp_livedate,
                               STR.Desc7                             AS store_segment,
                               CASE when STR.Email is null THEN null ELSE STR.Email END as alt_store_number,
                               POG.Department                        AS planogram_department,
                               POG.Desc10                            AS planogram_sub_department,
                               POG.Desc22                            AS commodity_number,
                               POG.Desc3                             AS commodity_group
      from [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
               inner join [CKB].[dbo].IX_SPC_PLANOGRAM POG
                          ON M_VIEW.POG_DBKEY = POG.DBKEY
               INNER JOIN [CKB].[dbo].IX_FLR_FLOORPLAN FP ON M_VIEW.FP_DBKEY = FP.DBKEY
               INNER JOIN [CKB].[dbo].IX_FLR_SECTION SEC
                          ON SEC.DBPARENTFLOORPLANKEY = FP.DBKEY AND SEC.DBPARENTPLANOGRAMKEY = POG.DBVERSIONKEY
               INNER JOIN [CKB].[JDACUSTOM].CSG_DEPT DEP ON DEP.DEPT_CD = M_VIEW.FFIX_DEPT
               INNER JOIN [CKB].[dbo].IX_STR_STORE STR ON M_VIEW.STR_DBKEY = STR.DBKEY
               LEFT OUTER JOIN [CKB].[jdacustom].csg_Aisle AISLE_N
                               ON M_VIEW.AISLE_NO = RIGHT('000' + ISNULL(CAST(AISLE_N.AISLE_NO AS VARCHAR), ''), 3)
      WHERE STR.DBSTATUS = 1
        AND POG.DBKEY IN ('@pogKey')
     ) AS result ) AS X
order by
         division asc, store_number asc, aisle asc,
         orientation asc, min_bay asc, max_bay asc, pog_sequence asc;`


export let FPDeptMRStores = `Select DEP.DEPT_NAME
                             from [CKB].[dbo].IX_SPC_PLANOGRAM POG
                                 INNER JOIN [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
                             ON M_VIEW.POG_DBKEY = POG.DBKEY
                                 INNER JOIN [CKB].[dbo].IX_STR_STORE STR ON M_VIEW.STR_DBKEY = STR.DBKEY
                                 INNER JOIN [CKB].[JDACUSTOM].CSG_DEPT DEP ON DEP.DEPT_CD = M_VIEW.FFIX_DEPT
                                 AND STR.DESC2 = '@div'
                                 --AND STR.DESC3 = '@store'
                                 AND POG.DBKEY = '@pogKey';`


export let POGTypeMRStores = `Select POG.DESC6
                              from [CKB].[dbo].IX_SPC_PLANOGRAM POG
                                  INNER JOIN [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
                              ON M_VIEW.POG_DBKEY = POG.DBKEY
                                  INNER JOIN [CKB].[dbo].IX_STR_STORE STR ON M_VIEW.STR_DBKEY = STR.DBKEY
                                  AND STR.DESC2 = '@div'
                                  AND STR.DESC3 = '@store'
                                  AND POG.DBKEY = '@pogKey';`

export let mappingReportQueryFloorKey = `Select division,
                                                store_number,
                                                store_type,
                                                pog_status,
                                                pog_type,
                                                pog_pendingdate,
                                                pog_livedate,
                                                dept,
                                                aisle,
                                                ad,
                                                orientation,
                                                pog_sequence,
                                                num_of_items,
                                                CAST(num_of_stores AS varchar ) AS num_of_stores,
                                                CAST(pog_dbkey AS varchar ) AS pog_dbkey,
                                                space_station_name,
                                                space_station_version_description,
                                                pog_name,
                                                min_bay,
                                                max_bay,
                                                strip_color,
                                                CAST(fp_dbkey AS varchar ) AS fp_dbkey,
                                                fp_status,
                                                fp_pendingdate,
                                                fp_livedate,
                                                store_segment,
                                                alt_store_number,
                                                planogram_department,
                                                planogram_sub_department,
                                                commodity_number,
                                                commodity_group
                                         FROM (Select distinct *,
                                                               POGTypeOrder = case
                                                                                  when pog_type in ('Display Space', 'White Space')
                                                                                      THEN 1
                                                                                  ELSE 0 END
                                               FROM (
                                                        SELECT distinct concat(STR.Desc2, STR.DESC3, M_VIEW.FP_DBKEY,
                                                                               M_VIEW.POG_DBKEY, POG.DESC6, POG.NAME,
                                                                               POG.DESC37, POG.DESC42, POG.LIVEDATE,
                                                                               POG.PENDINGDATE, POG.STATUS1, FP.LIVEDATE,
                                                                               FP.PENDINGDATE, FP.STATUS1,
                                                                               M_VIEW.DISTINCT_PRODUCT_COUNT,
                                                                               M_VIEW.STRIP_COLOR,
                                                                               M_VIEW.AISLE_NO, M_VIEW.AISLE_SIDE_CD,
                                                                               AISLE_N.AISLE_NAME, M_VIEW.POG_SEQUENCE,
                                                                               M_VIEW.MIN_POG_BAY, M_VIEW.MAX_POG_BAY,
                                                                               DEP.DEPT_NAME,
                                                                               STR.DESC49, STR.DESC7, STR.EMAIL, POG.DEPARTMENT,
                                                                               POG.DESC10, POG.DESC22, POG.DESC3,
                                                                               POG.NUMBEROFSTORES)    AS    unikey,
                                                                        STR.DESC2                     AS    division,
                                                                        STR.DESC3                     AS    store_number,
                                                                        STR.DESC49                    AS    store_type,
                                                                        POG.STATUS1                   AS    pog_status,
                                                                        POG.DESC6                     AS    pog_type,
                                                                        FORMAT(POG.PENDINGDATE, 'MM-dd-yy') pog_pendingdate,
                                                                        FORMAT(POG.LIVEDATE, 'MM-dd-yy')    pog_livedate,
                                                                        DEP.DEPT_NAME                 AS    dept,
                                                                        M_VIEW.AISLE_NO               AS    aisle,
                                                                        AISLE_N.AISLE_NAME            AS    ad,
                                                                        M_VIEW.AISLE_SIDE_CD          AS    orientation,
                                                                        M_VIEW.POG_SEQUENCE           AS    pog_sequence,
                                                                        CAST(M_VIEW.DISTINCT_PRODUCT_COUNT AS VARCHAR ) AS    num_of_items,
                                                                        POG.NUMBEROFSTORES            AS    num_of_stores,
                                                                        POG.DBKEY                     AS    pog_dbkey,
                                                                        POG.DESC37                    AS    space_station_name,
                                                                        POG.DESC42                    AS    space_station_version_description,
                                                                        POG.NAME                      AS    pog_name,
                                                                        M_VIEW.MIN_POG_BAY            AS    min_bay,
                                                                        M_VIEW.MAX_POG_BAY            AS    max_bay,
                                                                        M_VIEW.STRIP_COLOR            AS    strip_color,
                                                                        FP.DBKEY                      AS    fp_dbkey,
                                                                        FP.STATUS1                    AS    fp_status,
                                                                        FORMAT(FP.PENDINGDATE, 'MM-dd-yy')  fp_pendingdate,
                                                                        FORMAT(FP.LIVEDATE, 'MM-dd-yy')     fp_livedate,
                                                                        STR.Desc7                     AS    store_segment,
                                                                        STR.Email                     AS    alt_store_number,
                                                                        POG.Department                AS    planogram_department,
                                                                        POG.Desc10                    AS    planogram_sub_department,
                                                                        POG.Desc22                    AS    commodity_number,
                                                                        POG.Desc3                     AS    commodity_group
                                                        FROM [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
                                                            INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG
                                                        ON M_VIEW.POG_DBKEY = POG.DBKEY
                                                            INNER JOIN [CKB].[dbo].IX_FLR_FLOORPLAN FP ON M_VIEW.FP_DBKEY = FP.DBKEY
                                                            INNER JOIN [CKB].[dbo].IX_FLR_SECTION SEC
                                                            ON SEC.DBPARENTFLOORPLANKEY = FP.DBKEY AND SEC.DBPARENTPLANOGRAMKEY = POG.DBVERSIONKEY
                                                            INNER JOIN [CKB].[JDACUSTOM].CSG_DEPT DEP ON DEP.DEPT_CD = M_VIEW.FFIX_DEPT
                                                            INNER JOIN [CKB].[dbo].IX_STR_STORE STR ON M_VIEW.STR_DBKEY = STR.DBKEY
                                                            LEFT OUTER JOIN [CKB].[jdacustom].csg_Aisle AISLE_N
                                                            ON M_VIEW.AISLE_NO = RIGHT ('000' + ISNULL(CAST (AISLE_N.AISLE_NO AS VARCHAR), ''), 3)
                                                        WHERE FP.DBKEY= '@FPKey'
                                                          and FP.DBSTATUS = '@status'
                                                    ) AS result) AS X
                                         order by division asc, store_number asc,
                                                  case when pog_type in ('Display Space', 'White Space') THEN 1 ELSE 0 END,
                                                  aisle asc, orientation asc, min_bay asc, max_bay asc, pog_sequence asc;`

export let ItemByPOGQuery_SSN = `Select upc,
                                        product_case,
                                        sku,
                                        magic_lead,
                                        product_name,
                                        pog_segment,
                                        fixture,
                                        position_number,
                                        facing_width,
                                        facing_height,
                                        cap_number,
                                        merch_style,
                                        new_flag,
                                        delete_flag,
                                        move_flag,
                                        pusher_flag,
                                        tray_flag,
                                        recstat_code,
                                        product_group,
                                        pog_dbkey,
                                        space_station_name,
                                        space_station_version_description,
                                        pog_name,
                                        status,
                                        live_date,
                                        pending_date
                                 FROM (
                                          Select distinct concat(pog.dbkey, prd.upc, pos.value6, pos.value5, pos.value8,
                                                                 pos.HFacings,
                                                                 pos.VFacings)                                          random_key,
                                                          prd.upc                                                    AS upc,
                                                          prd.ID                                                     AS product_case,
                                                          prd.PartID                                                 AS sku,
                                                          prd.DESC50                                                 AS magic_lead,
                                                          prd.NAME                                                   AS product_name,
                                                          CAST(pos.value8 AS int)                                    AS pog_segment,
                                                          CAST(pos.value6 AS int)                                       fixture,
                                                          CAST(pos.value5 AS int)                                       position_number,
                                                          CASE pos.HFacings WHEN 0 THEN NULL ELSE pos.HFacings END   AS facing_width,
                                                          CASE pos.VFacings WHEN 0 THEN NULL ELSE pos.VFacings END   AS facing_height,
                                                          CASE pos.YCapNum WHEN 0 THEN NULL ELSE pos.YCapNum END     AS cap_number,
                                                          CASE pos.MerchStyle WHEN 0 THEN NULL WHEN 1 THEN 'SRP' END AS merch_style,
                                                          CAST(spcperf.Flag4 AS varchar)                             AS new_flag,
                                                          CAST(spcperf.Flag3 AS varchar)                             AS delete_flag,
                                                          CAST(pos.Flag4 AS varchar)                                 AS move_flag,
                                                          spcperf.Desc4                                              AS pusher_flag,
                                                          spcperf.Desc6                                              AS tray_flag,
                                                          CASE prd.value14
                                                              WHEN 0 THEN null
                                                              ELSE CAST(prd.value14 as decimal(10, 1)) END           AS recstat_code,
                                                          prd.Desc11                                                 AS product_group,
                                                          pog.dbkey                                                     pog_dbkey,
                                                          pog.desc37                                                    space_station_name,
                                                          pog.desc42                                                    space_station_version_description,
                                                          pog.name                                                      pog_name,
                                                          pog.status1                                                   status,
                                                          FORMAT(pog.livedate, 'MM-dd-yy')                              live_date,
                                                          FORMAT(pog.pendingdate, 'MM-dd-yy')                           pending_date
                                          FROM [CKB].[dbo].IX_SPC_PLANOGRAM PREPOG
                                              INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM AS POG
                                          ON POG.DBVERSIONKEY = PREPOG.DBVERSIONKEY
                                              INNER JOIN [CKB].[dbo].IX_SPC_PERFORMANCE AS spcperf ON (pog.DBKEY = spcperf.DBPARENTPLANOGRAMKEY)
                                              LEFT OUTER JOIN [CKB].[dbo].IX_SPC_POSITION AS pos
                                              ON (spcperf.DBPARENTPLANOGRAMKEY = pos.DBPARENTPLANOGRAMKEY
                                              AND spcperf.DBPARENTPRODUCTKEY = pos.DBPARENTPRODUCTKEY)
                                              INNER JOIN [CKB].[dbo].IX_SPC_PRODUCT AS prd ON (spcperf.DBPARENTPRODUCTKEY = prd.DBKEY)
                                          WHERE pog.dbstatus IN (1)
                                            --AND pog.status1 = 'Live'
                                            AND pog.desc37 IN ('?')
                                          UNION
                                          Select distinct concat(pog.dbkey, prd.upc, pos.value6, pos.value5, pos.value8, pos.HFacings,
                                              pos.VFacings) random_key,
                                              prd.upc,
                                              prd.ID AS product_case,
                                              prd.PartID AS sku,
                                              prd.DESC50 AS magic_lead,
                                              prd.NAME AS product_name,
                                              CAST (pos.value8 AS int) AS pog_segment,
                                              CAST (pos.value6 AS int) fixture,
                                              CAST (pos.value5 AS int) position_number,
                                              CASE pos.HFacings WHEN 0 THEN NULL ELSE pos.HFacings END AS facing_width,
                                              CASE pos.VFacings WHEN 0 THEN NULL ELSE pos.VFacings END AS facing_height,
                                              CASE pos.YCapNum WHEN 0 THEN NULL ELSE pos.YCapNum END AS cap_number,
                                              CASE pos.MerchStyle WHEN 0 THEN NULL WHEN 1 THEN 'SRP' END AS merch_style,
                                              CAST (spcperf.Flag4 AS varchar) AS new_flag,
                                              CAST (spcperf.Flag3 AS varchar) AS delete_flag,
                                              CAST (pos.Flag4 AS varchar) AS move_flag,
                                              spcperf.Desc4 AS pusher_flag,
                                              spcperf.Desc6 AS tray_flag,
                                              CASE prd.value14
                                              WHEN 0 THEN null
                                              ELSE CAST (prd.value14 as decimal (10, 1)) END AS recstat_code,
                                              prd.Desc11 AS product_group,
                                              pog.dbkey pog_dbkey,
                                              pog.desc37 space_station_name,
                                              pog.desc42 space_station_version_description,
                                              pog.name pog_name,
                                              pog.status1 status,
                                              FORMAT(pog.livedate, 'MM-dd-yy') live_date,
                                              FORMAT(pog.pendingdate, 'MM-dd-yy') pending_date
                                          FROM [CKB].[dbo].IX_SPC_PLANOGRAM PREPOG
                                              INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM AS POG
                                          ON POG.DBVERSIONKEY = PREPOG.DBVERSIONKEY
                                              INNER JOIN [CKB].[dbo].IX_SPC_PERFORMANCE AS spcperf ON (pog.DBKEY = spcperf.DBPARENTPLANOGRAMKEY)
                                              LEFT OUTER JOIN [CKB].[dbo].IX_SPC_POSITION AS pos
                                              ON (spcperf.DBPARENTPLANOGRAMKEY = pos.DBPARENTPLANOGRAMKEY
                                              AND spcperf.DBPARENTPRODUCTKEY = pos.DBPARENTPRODUCTKEY)
                                              INNER JOIN [CKB].[dbo].IX_SPC_PRODUCT AS prd ON (spcperf.DBPARENTPRODUCTKEY = prd.DBKEY)
                                          WHERE pog.dbstatus IN (1)
                                            AND pog.status1 = 'Live'
                                            AND pog.desc37 IN ('?')) AS result
                                 order by pog_segment asc, fixture asc, position_number asc, pog_dbkey asc;`

export let selectActivePDFPOGKeys=`select DISTINCT TOP 10 a.dbparentplanogramkey
from [CKB].[dbo].ix_net_document (nolock) a
         join
     [CKB].[dbo].ix_net_document_view (nolock) b on b.dbkey = a.dbparentdocumentviewkey
         join [CKB].[dbo].ix_net_document_type (nolock) c on c.dbkey = b.DBParentDocumentTypeKey
         JOIN [CKB].[dbo].IX_SPC_PLANOGRAM (nolock) POG on pog.dbkey = a.dbparentplanogramkey
where a.document is not null
  and
        b.description = 'Kroger Planogram PDF' and c.type = 'PDF' ;`

export let selectInactivePDFPOGKeys=`Select DISTINCT TOP 10 pog.dbkey
FROM [CKB].[jdacustom].RPT_STR_POG_MAP_LIVE_VW M_VIEW
         INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG
                    ON M_VIEW.POG_DBKEY = POG.DBKEY
         INNER JOIN [CKB].[dbo].IX_STR_STORE str ON M_VIEW.STR_DBKEY = str.DBKEY
WHERE str.dbstatus = 1
  AND pog.status1 = 'Live'
  AND POG.dbstatus IN (1)
  AND pog.Desc6 = 'Display Space';`

export let pogItemByStoreQuery_multipleInputs = `SELECT DISTINCT STR.desc2            AS Div,
                                                                 STR.desc3            AS Store,
                                                                 PDT.UPC,
                                                                 PDT.ID AS     'Case', PDT.PartID AS SKU,
                                                                 PDT.DESC50                     AS Magic_Lead_Article_Number,
                                                                 PDT.NAME                       AS Product_Name,
                                                                 M_VIEW.AISLE_NO                AS Aisle_#,
                                                                 AISLE_N.aisle_name             AS Aisle_Desc,
                                                                 M_VIEW.AISLE_SIDE_CD                                    AS Orn,
                                                                 M_VIEW.POG_SEQUENCE                                     AS Seq,
                                                                 M_VIEW.MAX_POG_BAY                                AS Bay_In_Aisle,
                                                                 CAST(POS.Value8 AS varchar)                       AS POG_Segment,
                                                                 CAST(POS.Value6 AS varchar)                       AS Fixture,
                                                                 CAST(POS.Value5 AS varchar)                       AS Position,
       CAST(SPERF.Flag4 AS varchar)                      AS new_flag,
       CAST(SPERF.Flag3 AS varchar)                      AS delete_flag,
       CAST(pos.Flag4 AS varchar)                        AS move_flag,
            CASE SPERF.Desc4 WHEN NULL THEN '' ELSE SPERF.Desc4 END AS Pusher,
            CASE SPERF.Desc6 WHEN NULL THEN '' ELSE SPERF.Desc6 END AS Tray,
CASE WHEN PDT.value14 IS NOT NULL THEN CAST(FLOOR(PDT.value14) AS VARCHAR) + '.' + CAST(CAST(PDT.value14 AS DECIMAL(10, 1)) - FLOOR(PDT.value14) AS VARCHAR(1))
                  ELSE '' END AS recstat_code,
       PDT.Desc11                                        AS Prod_Group,
       Cast(POG.dbkey AS varchar)                        AS POG_DBKEY,
       POG.desc37                                           Space_Station_Name,
       REPLACE(REPLACE(REPLACE(POG.desc42, ' ', '{}'), '}{', ''), '{}', ' ')  Space_Station_Version_Description,
       POG.name                                          AS Name,
       POG.status1                                       AS POG_Status,
       FORMAT(POG.livedate, 'MM-dd-yy')                     Pog_Live_Date,
       FORMAT(POG.pendingdate, 'MM-dd-yy')                  Pog_Pending_Date,
       FP.status1                                        AS FP_Status,
       FORMAT(FP.LiveDate, 'MM-dd-yy')     FP_Live_Date,
       FORMAT(FP.PendingDate, 'MM-dd-yy')  FP_Pending_Date,
       FP.desc40                           Event_Type,
       CASE FP.desc45 WHEN NULL THEN '' ELSE FP.desc45 END AS Event_Name

FROM [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
         INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG ON M_VIEW.POG_DBKEY = POG.DBKEY
         INNER JOIN [CKB].[dbo].IX_FLR_FLOORPLAN FP ON M_VIEW.FP_DBKEY = FP.DBKEY
         INNER JOIN [CKB].[dbo].IX_STR_STORE AS STR ON (M_VIEW.STR_DBKEY = STR.DBKEY)
    and (fp.desc21 = str.desc2 and fp.desc22 = str.desc3)
         INNER JOIN [CKB].[dbo].IX_SPC_POSITION AS POS ON (POG.DBKEY = POS.DBPARENTPLANOGRAMKEY)
         LEFT OUTER JOIN [CKB].[dbo].IX_SPC_PERFORMANCE AS SPERF
                         ON (SPERF.DBPARENTPLANOGRAMKEY = POS.DBPARENTPLANOGRAMKEY AND
                             SPERF.DBPARENTPRODUCTKEY = POS.DBPARENTPRODUCTKEY)
         INNER JOIN [CKB].[dbo].IX_SPC_PRODUCT AS PDT ON (SPERF.DBPARENTPRODUCTKEY = PDT.DBKEY)
         LEFT OUTER JOIN [CKB].[jdacustom].csg_Aisle AISLE_N
                         ON M_VIEW.AISLE_NO = RIGHT('000' + ISNULL(CAST(AISLE_N.AISLE_NO AS VARCHAR), ''''), 3)
WHERE STR.dbstatus = 1
  and FP.dbstatus in (1, 2)
  and POG.dbstatus in (1, 2)
  AND STR.desc2 IN ('@divList')
  AND STR.desc3 IN ('@storeList')
  AND PDT.UPC IN ('@upc')
order by Div desc, store desc, space_station_name desc, pog_segment desc, fixture desc, Position desc;`

export let mappingReport_StoreTypeDropdown = `SELECT distinct STR.DESC49 AS storeType
                           FROM
                               [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
                               INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM AS PREPOG
                           ON M_VIEW.POG_DBKEY = PREPOG.DBKEY
                               INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM AS POG ON POG.DBVERSIONKEY = PREPOG.DBVERSIONKEY
                               INNER JOIN [CKB].[dbo].IX_FLR_FLOORPLAN AS PREFP ON M_VIEW.FP_DBKEY = PREFP.DBKEY
                               INNER JOIN [CKB].[dbo].IX_FLR_FLOORPLAN AS FP ON FP.DBVERSIONKEY = PREFP.DBVERSIONKEY
                               INNER JOIN [CKB].[dbo].IX_STR_STORE STR ON M_VIEW.STR_DBKEY = STR.DBKEY
                               LEFT OUTER JOIN [CKB].[jdacustom].csg_Aisle AISLE_N ON M_VIEW.AISLE_NO = RIGHT ('000'+ISNULL(CAST (AISLE_N.AISLE_NO AS VARCHAR),''''),3)
                           WHERE STR.DESC2='${DataJson.Division_014}'
                             AND STR.DESC3 IN ('@storeMR')
                             AND STR.DBSTATUS = 1
                             AND POG.STATUS1='Live'
                             and FP.STATUS1='Live';`

export let comparisonViewTable = `select
                                    STR.desc2 as DIV,
                                    STR.desc3 as Store,
                                    CASE WHEN STR.flag10=0 THEN 'N' WHEN STR.flag10 = 1 THEN 'Y' ELSE CONVERT(varchar(10), STR.flag10)  END [REM Flag],
                                    PDT.upc as UPC,
                                    PDT.desc1 as Description
                                    FROM [CKB].[jdacustom].RPT_STR_POG_MAP_LIVE_VW M_VIEW
                                    INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG ON M_VIEW.POG_DBKEY = POG.DBKEY
                                    INNER JOIN [CKB].[dbo].IX_STR_STORE STR ON M_VIEW.STR_DBKEY = STR.DBKEY
                                    INNER JOIN [CKB].[dbo].IX_SPC_PERFORMANCE SPERF ON (POG.DBKEY = SPERF.DBPARENTPLANOGRAMKEY)
                                    LEFT OUTER JOIN [CKB].[dbo].IX_SPC_POSITION POS ON (SPERF.DBPARENTPLANOGRAMKEY = POS.DBPARENTPLANOGRAMKEY AND SPERF.DBPARENTPRODUCTKEY = POS.DBPARENTPRODUCTKEY)
                                    INNER JOIN [CKB].[dbo].IX_SPC_PRODUCT PDT ON (SPERF.DBPARENTPRODUCTKEY = PDT.DBKEY)
                                    where POG.DBStatus = 1 and STR.desc2 IN (SELECT value FROM STRING_SPLIT('@div','T')) and PDT.upc IN (SELECT value FROM STRING_SPLIT('@upcList',','))
                                    and STR.Desc3 IN (SELECT value FROM STRING_SPLIT('@str','T'))  order by DIV asc, Store asc, UPC asc`



export let sfpItemLocationProcessorTable = `select distinct
                                      'ILP' as System,
                                        --FS.store_number as Store,
                                        --ILT.upc as upc,
                                        CONVERT(varchar(100), ILT.cao_aisle_number) as [CAO Aisle Number],
                                        ILT.cao_aisle_desc as [CAO Aisle Desc],
                                        ILT.cao_aisle_side as Orientation,
                                        ILT.cao_bay_number_in_aisle as [Bay in Aisle],
                                        '' as [Bay in POG (Segment)],
                                        CONVERT(varchar(100),ILT.cao_shelf_number) as [Fixture Number],
                                        CONVERT(varchar(100),ILT.cao_position ) as [Position number],
                                        CONVERT(varchar(100), ILT.pog_dbkey) as DBkey,
                                        PE.pog_status as Status,
                                        '' as [Live Date],
                                        ILT.pog_name as SSN,
                                        '' as [Version Desc],
                                        '' as [KOM Completion? Y/N],
                                        '' as [Reason],
                                        '' as [Completion Date],
                                        '' as [No Response]
                                        FROM [item_location_processor].[dbo].ITEM_LOCATION ILT
                                            INNER JOIN [item_location_processor].[dbo].FACILITY_STORE FS ON ILT.FACILITY_ID = FS.FACILITY_ID
                                            INNER JOIN [item_location_processor].[dbo].PROCESSING_EVENT PE ON ILT.POG_DBKEY = PE.POG_DBKEY
                                        where PE.POG_STATUS = 'LIVE' AND ILT.IS_ACTIVE = 'true' AND FS.DIVISION IN (SELECT value FROM STRING_SPLIT('@div','T')) and ILT.UPC IN (SELECT value FROM STRING_SPLIT('@upcList',','))
                                        and FS.store_number IN (SELECT value FROM STRING_SPLIT('@str','T')) order by ILT.cao_bay_number_in_aisle asc`

export let getNavigatorDeptDropdown = `SELECT distinct RTRIM(POG.DEPARTMENT) AS Department
FROM [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
         INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG ON M_VIEW.POG_DBKEY = POG.DBKEY
         INNER JOIN [CKB].[dbo].IX_STR_STORE STR ON M_VIEW.STR_DBKEY = STR.DBKEY
WHERE STR.DBSTATUS = 1  AND STR.desc2 IN ('@div') order by Department;`

export let getNavigatorSubDeptDropdown = `SELECT distinct RTRIM(sd.sub_department)
                                          FROM [CKB].[dbo].[ix_spc_planogram_key] d
                                              INNER JOIN
                                              (SELECT DBParentKey, DBKey, Description AS sub_department
                                              FROM [CKB].[dbo].[ix_spc_planogram_key]
                                              WHERE KeyLevel = 3) sd
                                          ON sd.DBParentKey = d.DBKey

                                              INNER JOIN (SELECT DBParentKey, DBKey, Description AS commodity
                                              FROM [CKB].[dbo].[ix_spc_planogram_key]
                                              WHERE KeyLevel = 4) c
                                              ON c.DBParentKey = sd.DBKey

                                              INNER JOIN (SELECT DBParentKey, DBKey, Description AS commodity_group
                                              FROM [CKB].[dbo].[ix_spc_planogram_key]
                                              WHERE KeyLevel = 5) cg
                                              ON cg.DBParentKey = c.DBKey
                                          Where 1 = 1
                                            AND d.Description != 'N/A'
                                            AND d.Description != ''
                                            AND d.Description = '@dept';`

export let getNavigatorCommodityDropdown = `SELECT distinct RTRIM(c.commodity)
                                            FROM [CKB].[dbo].[ix_spc_planogram_key] d
                                                INNER JOIN
                                                (SELECT DBParentKey, DBKey, Description AS sub_department
                                                FROM [CKB].[dbo].[ix_spc_planogram_key]
                                                WHERE KeyLevel = 3) sd
                                            ON sd.DBParentKey = d.DBKey

                                                INNER JOIN (SELECT DBParentKey, DBKey, Description AS commodity
                                                FROM [CKB].[dbo].[ix_spc_planogram_key]
                                                WHERE KeyLevel = 4) c
                                                ON c.DBParentKey = sd.DBKey

                                                INNER JOIN (SELECT DBParentKey, DBKey, Description AS commodity_group
                                                FROM [CKB].[dbo].[ix_spc_planogram_key]
                                                WHERE KeyLevel = 5) cg
                                                ON cg.DBParentKey = c.DBKey
                                            Where 1 = 1
                                              AND sd.sub_department='@subDept';`

export let getNavigatorCommodityGrpDropdown = `SELECT distinct RTRIM(cg.commodity_group)
                                               FROM [CKB].[dbo].[ix_spc_planogram_key] d
                                                   INNER JOIN
                                                   (SELECT DBParentKey, DBKey, Description AS sub_department
                                                   FROM [CKB].[dbo].[ix_spc_planogram_key]
                                                   WHERE KeyLevel = 3) sd
                                               ON sd.DBParentKey = d.DBKey

                                                   INNER JOIN (SELECT DBParentKey, DBKey, Description AS commodity
                                                   FROM [CKB].[dbo].[ix_spc_planogram_key]
                                                   WHERE KeyLevel = 4) c
                                                   ON c.DBParentKey = sd.DBKey

                                                   INNER JOIN (SELECT DBParentKey, DBKey, Description AS commodity_group
                                                   FROM [CKB].[dbo].[ix_spc_planogram_key]
                                                   WHERE KeyLevel = 5) cg
                                                   ON cg.DBParentKey = c.DBKey
                                               Where 1 = 1
                                                 AND sd.sub_department= '@subDept'
                                                 AND c.commodity='@commodity'`

export let comparisonReportSFPData=`select STR.desc2          as            DIV,
       STR.desc3          as            Store,
                                           CASE WHEN STR.flag10=0 THEN 'N' WHEN STR.flag10 = 1 THEN 'Y' ELSE CONVERT(varchar(10), STR.flag10)  END [REM Flag],
       PD.upc             as            UPC,
       PD.desc1           as            Description,
       'SFP'              as            System,
       M_VIEW.AISLE_NO    AS            caoAisleNo,
       AISLE_N.AISLE_NAME AS            caoAisleDesc,
--        ''                 as            orn,
--        ''                 as            bayInAisle,
--        ''                 as            bayInPOG,
       CAST(POS.value6 AS int)          fixtureNo,
       CAST(POS.value5 AS int)          positionNo,
       CONVERT(varchar(10), pog.dbkey)                       dbKey,
       POG.status1                      status,
       FORMAT(POG.livedate, 'MM-dd-yy') liveDate,
       POG.desc37                       ssn,
       POG.desc42                       versionDesc,
       (select cdt.status
        from Cincinnati.[caopog].[dbo].compliancy_detail_tbl cdt
        where cdt.dbkey = POG.DBKEY
          and cdt.loc_no = RIGHT('000' + ISNULL(CAST(str.desc3 AS VARCHAR), ''), 3))
                                        komComp,
       (select cdt.completed_date
        from Cincinnati.[caopog].[dbo].compliancy_detail_tbl cdt
        where cdt.dbkey = POG.DBKEY
          and cdt.loc_no = RIGHT('000' + ISNULL(CAST(str.desc3 AS VARCHAR), ''), 3))
                                        completionDate,
       (select cdt.reason_cd
        from CINCINNATI.[caopog].[dbo].compliancy_detail_tbl cdt
        where cdt.dbkey = POG.DBKEY
          and cdt.loc_no = RIGHT('000' + ISNULL(CAST(str.desc3 AS VARCHAR), ''), 3))
                                  noRespComment
FROM [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
         INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG ON M_VIEW.POG_DBKEY = POG.DBKEY
         INNER JOIN [CKB].[dbo].IX_STR_STORE AS STR ON (M_VIEW.STR_DBKEY = STR.DBKEY)
         INNER JOIN [CKB].[dbo].IX_SPC_POSITION AS POS ON (POG.DBKEY = POS.DBPARENTPLANOGRAMKEY)
         LEFT OUTER JOIN [CKB].[jdacustom].csg_Aisle AISLE_N
                         ON M_VIEW.AISLE_NO = RIGHT('000' + ISNULL(CAST(AISLE_N.AISLE_NO AS VARCHAR), ''), 3)
         LEFT JOIN [CKB].[dbo].IX_SPC_PRODUCT pd ON pd.dbkey = POS.dbparentproductkey
WHERE STR.dbstatus = 1
  and upc IN ('@upcList')
  and STR.desc3 IN ('@str')
  and STR.desc2 IN ('@div')
order by pd.upc asc, POG.livedate asc;`

export let comparisonReportSFPUIData = `Select 
                                               CONVERT(varchar (10), pog.dbkey) dbKey,
                                               POG.status1                      status,
                                               FORMAT(POG.livedate, 'MM-dd-yy') liveDate,
                                               POG.desc37                       ssn,
                                               POG.desc42                       versionDesc,
                                               case
                                                   when (select cdt.status
                                                         from Cincinnati.[caopog].[dbo].compliancy_detail_tbl cdt where cdt.dbkey = POG.DBKEY
                    and cdt.flr_key = M_VIEW.FP_DBKEY and event_date IN (SELECT max(event_date) FROM CINCINNATI.caopog.dbo.compliancy_detail_tbl)
                                                                            and cdt.loc_no = RIGHT('000' + ISNULL(CAST(str.desc3 AS VARCHAR), ''), 3)) != 'Y'  then 'No Response'
           when (select cdt.status
                                        from Cincinnati.[caopog].[dbo].compliancy_detail_tbl cdt where cdt.dbkey = POG.DBKEY
                                                                                                   and cdt.flr_key = M_VIEW.FP_DBKEY and event_date IN (SELECT max(event_date) FROM CINCINNATI.caopog.dbo.compliancy_detail_tbl)
                                                                                                   and cdt.loc_no = RIGHT('000' + ISNULL(CAST(str.desc3 AS VARCHAR), ''), 3)) != 'N'  then 'No Response'
                                            else (
                                        select cdt.status
                                        from Cincinnati.[caopog].[dbo].compliancy_detail_tbl cdt
                                        where cdt.dbkey = POG.DBKEY
                                          and cdt.flr_key = M_VIEW.FP_DBKEY and event_date IN (SELECT max(event_date) FROM CINCINNATI.caopog.dbo.compliancy_detail_tbl)
                                          and cdt.loc_no = RIGHT ('000' + ISNULL(CAST (str.desc3 AS VARCHAR)
                                            , '')
                                            , 3)) end as   komComp,
       case when (select cdt.reason_cd
                  from CINCINNATI.[caopog].[dbo].compliancy_detail_tbl cdt
                  where cdt.dbkey = POG.DBKEY
                    and cdt.flr_key = M_VIEW.FP_DBKEY and event_date IN (SELECT max(event_date) FROM CINCINNATI.caopog.dbo.compliancy_detail_tbl)
                    and cdt.loc_no = RIGHT('000' + ISNULL(CAST(str.desc3 AS VARCHAR), ''), 3)) = 'SM' then 'Store Manager Postponed'
            when (select cdt.reason_cd
                  from CINCINNATI.[caopog].[dbo].compliancy_detail_tbl cdt
                  where cdt.dbkey = POG.DBKEY
                    and cdt.flr_key = M_VIEW.FP_DBKEY and event_date IN (SELECT max(event_date) FROM CINCINNATI.caopog.dbo.compliancy_detail_tbl)
                    and cdt.loc_no = RIGHT('000' + ISNULL(CAST(str.desc3 AS VARCHAR), ''), 3)) ='KP' then 'Kompass Personnel did not show'
            else (select cdt.reason_cd
                  from CINCINNATI.[caopog].[dbo].compliancy_detail_tbl cdt
                  where cdt.dbkey = POG.DBKEY
                    and cdt.flr_key = M_VIEW.FP_DBKEY and event_date IN (SELECT max(event_date) FROM CINCINNATI.caopog.dbo.compliancy_detail_tbl)
                    and cdt.loc_no = RIGHT('000' + ISNULL(CAST(str.desc3 AS VARCHAR), ''), 3)) end noRespComment,
       CAST (FORMAT( (select cdt.completed_date
                      from Cincinnati.[caopog].[dbo].compliancy_detail_tbl cdt
                      where cdt.dbkey = POG.DBKEY
                        and cdt.flr_key = M_VIEW.FP_DBKEY and event_date IN (SELECT max(event_date) FROM CINCINNATI.caopog.dbo.compliancy_detail_tbl)
                        and cdt.loc_no = RIGHT('000' + ISNULL(CAST(str.desc3 AS VARCHAR), ''), 3)),'MM-dd-yyyy')AS VARCHAR) completionDate,
       CAST (FORMAT( (select cdt.rev_datetime
                      from Cincinnati.[caopog].[dbo].compliancy_detail_tbl cdt
                      where cdt.dbkey = POG.DBKEY
                        and cdt.flr_key = M_VIEW.FP_DBKEY and event_date IN (SELECT max(event_date) FROM CINCINNATI.caopog.dbo.compliancy_detail_tbl)
                        and cdt.loc_no = RIGHT('000' + ISNULL(CAST(str.desc3 AS VARCHAR), ''), 3)),'MM-dd-yyyy')AS VARCHAR) revisedDate,
       M_VIEW.AISLE_NO    AS            caoAisleNo,
       AISLE_N.AISLE_NAME AS            caoAisleDesc,
       CAST(POS.value6  AS VARCHAR)        fixtureNo,
       CAST(POS.value5  AS VARCHAR)      positionNo
FROM [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
         INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG ON M_VIEW.POG_DBKEY = POG.DBKEY
         INNER JOIN [CKB].[dbo].IX_STR_STORE AS STR ON (M_VIEW.STR_DBKEY = STR.DBKEY)
         INNER JOIN [CKB].[dbo].IX_SPC_POSITION AS POS ON (POG.DBKEY = POS.DBPARENTPLANOGRAMKEY)
         LEFT OUTER JOIN [CKB].[jdacustom].csg_Aisle AISLE_N
                         ON M_VIEW.AISLE_NO = RIGHT('000' + ISNULL(CAST(AISLE_N.AISLE_NO AS VARCHAR), ''), 3)
         LEFT JOIN [CKB].[dbo].IX_SPC_PRODUCT pd ON pd.dbkey = POS.dbparentproductkey
WHERE STR.dbstatus = 1
  and upc IN ('@upc')
  and STR.desc3 IN ('@str')
  and STR.desc2 IN ('@div')
order by pd.upc asc, POG.livedate asc;`

export let comparisonViewUPCSummary = `Select
                                    STR.desc2 as DIV,
                                    STR.desc3 as Store,
                                    CASE WHEN STR.flag10=0 THEN 'N' WHEN STR.flag10 = 1 THEN 'Y' ELSE CONVERT(varchar(10), STR.flag10)  END [REM Flag],
                                    PDT.upc as UPC,
                                    PDT.desc1 as Description,
                                    concat(PDT.size,' ',PDT.uom) as size
                                    FROM [CKB].[jdacustom].RPT_STR_POG_MAP_LIVE_VW M_VIEW
                                    INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG ON M_VIEW.POG_DBKEY = POG.DBKEY
                                    INNER JOIN [CKB].[dbo].IX_STR_STORE STR ON M_VIEW.STR_DBKEY = STR.DBKEY
                                    INNER JOIN [CKB].[dbo].IX_SPC_PERFORMANCE SPERF ON (POG.DBKEY = SPERF.DBPARENTPLANOGRAMKEY)
                                    LEFT OUTER JOIN [CKB].[dbo].IX_SPC_POSITION POS ON (SPERF.DBPARENTPLANOGRAMKEY = POS.DBPARENTPLANOGRAMKEY AND SPERF.DBPARENTPRODUCTKEY = POS.DBPARENTPRODUCTKEY)
                                    INNER JOIN [CKB].[dbo].IX_SPC_PRODUCT PDT ON (SPERF.DBPARENTPRODUCTKEY = PDT.DBKEY)
                                    where POG.DBStatus = 1 and STR.desc2 IN ('@div') and PDT.upc IN ('@upc')
                                    and STR.Desc3 IN ('@str')  order by DIV asc, Store asc, UPC asc`

export let pogTabQueryWithDivDbKey = `Select distinct *
                        FROM (
                                 Select pog.status1 AS Status,
                                        pog.Desc6 AS 'POG Type', 
                                        FORMAT(pog.PendingDate, 'MM-dd-yy') AS 'Pending Date', 
                                        FORMAT(pog.dbdateeffectivefrom, 'MM-dd-yy') AS 'Date Live', 
                                        pog.desc38 'Period Week Year', 
                                        CAST(pog.NumberOfStores AS varchar) AS 'Store Count',
                                        CAST(pog.NumberOfProductsAllocated AS varchar ) AS 'Number(#) of Items',
                                        CAST(pog.dbkey AS varchar ) AS DBkey,
                                        CASE WHEN pog.partid = '' OR pog.partid IS NULL THEN 'D' + CASE WHEN IsNumeric(pog.Desc39) = 1 THEN pog.Desc39
                                        ELSE '___' END + '-L' + CASE WHEN IsNumeric(pog.Desc1) = 1 THEN pog.Desc1 ELSE '00000' END + '-D' + CASE
                                        WHEN IsNumeric(Left(pog.Department,2)) = 1 THEN Left(pog.Department, 2) ELSE '__' END + '-C' + CASE WHEN IsNumeric(pog.Desc22) = 1 
                                        THEN pog.Desc22 ELSE '___' END +'-V___' + '-' + Left (COALESCE (pog.uom, '_'), 1) + Right ('000'+ COALESCE (CONVERT (varchar, pog.Value4), '___'), 3)
                                     + '-' + CASE WHEN COALESCE (pog.Desc21, '') = '' THEN '__' ELSE pog.Desc21 END ELSE pog.partid END 'Space Station Name',
                                     pog.desc42 'Space Station Version Description',
                                     pog.name 'POG Name'
                                 FROM [CKB].[jdacustom].RPT_STR_POG_MAP_LIVE_VW M_VIEW
                                     INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG
                                 ON M_VIEW.POG_DBKEY = POG.DBKEY
                                     INNER JOIN [CKB].[dbo].IX_STR_STORE str ON M_VIEW.STR_DBKEY = str.DBKEY
                                 WHERE str.dbstatus = 1
                                   AND pog.status1='Live'
                                   AND str.desc2 IN ('@Div')
                                   AND pog.dbkey IN ('@DbKey')
                                   AND POG.dbstatus IN (1)) AS result
                        order by 'Space Station Name' asc, 'Date Live' asc`;

export let pogInfoTableQuery = `Select distinct *
                             FROM (Select
                             CAST(pog.dbkey AS varchar) AS DBkey,
                             CASE WHEN pog.partid = '' OR pog.partid IS NULL THEN 'D' + CASE WHEN IsNumeric(pog.Desc39) = 1
                             THEN pog.Desc39 ELSE '___' END + '-L' + CASE WHEN IsNumeric(pog.Desc1) = 1 THEN pog.Desc1
                             ELSE '00000' END + '-D' + CASE WHEN IsNumeric(Left(pog.Department, 2)) = 1 THEN Left(pog.Department, 2)
                             ELSE '__' END + '-C' + CASE WHEN IsNumeric(pog.Desc22) = 1 THEN pog.Desc22 ELSE '___' END + '-V___'
                             + '-' + Left(COALESCE(pog.uom, '_'), 1) + Right('000' + COALESCE(CONVERT(varchar, pog.Value4), '___'), 3)
                             + '-' + CASE WHEN COALESCE(pog.Desc21, '') = '' THEN '__' ELSE pog.Desc21 END ELSE pog.partid END AS SpaceStationName,
                             pog.desc42 AS SpaceStationVersionDescription
                         FROM [CKB].[jdacustom].RPT_STR_POG_MAP_LIVE_VW M_VIEW
                                  INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG
                                  ON M_VIEW.POG_DBKEY = POG.DBKEY
                                  INNER JOIN [CKB].[dbo].IX_STR_STORE str ON M_VIEW.STR_DBKEY = str.DBKEY
                         WHERE str.dbstatus = 1
                           AND pog.dbkey IN ('@DbKey')
                    ) AS result
                        order by DBkey asc;`

export let storeTabWithDivStoreQuery = `SELECT
         str.desc2 division,
         str.desc3 'store number',
             str.Desc49 as 'GM Store Type' ,
             str.Name 'store name',
             str.Address1 address,
         str.AddressCity city,
         str.AddressState state,
         CONCAT(SUBSTRING(str.AddressPostalCode, 1, 5), '-',
                SUBSTRING(str.AddressPostalCode, 6, 4)) zip,
         CONCAT(SUBSTRING(str.phone, 1, 3), '-',
                SUBSTRING(str.phone, 4, 3), '-',
                SUBSTRING(str.phone, 7, 4)) 'phone number',
         CASE WHEN str.Flag3 = 1 THEN 'Y' ELSE 'N' END AS 'Floor Planning',
         CASE WHEN str.Flag8 = 1 THEN 'Y' ELSE 'N' END AS 'ILP Ready Flag',
         CASE WHEN str.Flag9 = 1 THEN 'Y' ELSE 'N' END AS 'ILP',
         CASE WHEN str.Flag7 = 1 THEN 'Y' ELSE 'N' END AS 'Area Attributes',
         str.value4 'Total Square Footage',
         str.value5 'Sales Floor Footage'
     FROM
         [CKB].[dbo].ix_str_store str
     WHERE
         str.DBKey IN (@DBKeyList) AND 
         str.dbstatus = 1
     ORDER BY
         division,
         [store number] asc;`

export let  FloorplansQueryEventType = `SELECT  
    TOP 90
    CAST(MORE_INFO.DivNo AS VARCHAR(3))   Div,
    CAST(MORE_INFO.StoreNo AS VARCHAR(5)) Store,
    FP.dbkey                              DBKey,
    FP.status1                            Status,
    CAST(FP.desc40 AS VARCHAR(3))         Event_Type,
    TRIM(REPLACE(FP.Name, '  ', ' '))     FP_Name,
    FORMAT (FP.livedate, 'MM-dd-yy')      Live_Date,
    MORE_INFO.num_of_pogs,
    MORE_INFO.num_of_items,
    CAST(FP.desc2 AS VARCHAR(30))         Floorplan_Department,
    CAST(FP.desc39 AS VARCHAR(7))         Project_type,
    CAST(FP.desc44 AS VARCHAR(50) )       Modifier 
 FROM 
      (SELECT
          STR_DBKEY          as str_dbkey,
          FP_DBKEY,
          STR.desc2          as DivNo,
          STR.desc3          as StoreNo,
          sum(pog_count)     as num_of_pogs,
          sum(product_count) as num_of_items
      FROM
           (SELECT 
                STR_DBKEY, 
                FP_DBKEY, 
                POG_DBKEY,
                max(DISTINCT_PRODUCT_COUNT) as product_count,
                1                           as pog_count   
            FROM 
                [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
            WHERE 
                M_VIEW.str_dbstatus <> 4 
            GROUP BY
                STR_DBKEY, FP_DBKEY, DISTINCT_PRODUCT_COUNT, POG_DBKEY
           ) AS STR_FP_POG_INFO_SEQ INNER JOIN 
                 [CKB].[dbo].IX_STR_STORE STR ON STR_FP_POG_INFO_SEQ.STR_DBKEY = STR.DBKEY INNER JOIN
                 [ckb].[dbo].ix_spc_planogram pog on pog.dbkey = STR_FP_POG_INFO_SEQ.pog_dbkey
      WHERE 
          1 = 1  and pog.dbstatus in (1)  
      GROUP BY 
          STR_DBKEY, FP_DBKEY, STR.desc2, STR.desc3
       ) AS MORE_INFO INNER JOIN 
    [CKB].[dbo].IX_FLR_FLOORPLAN FP ON MORE_INFO.FP_DBKEY = FP.DBKEY 
WHERE 
    1=1 AND 
    FP.desc40 IN (SELECT value FROM STRING_SPLIT('@Event_Type',','))  
GROUP BY
    FP.desc44,
    FP.desc39,
    FP.desc45,
    FP.desc40,
    FP.dbkey,
    FORMAT (FP.livedate, 'MM-dd-yy'),
    FP.Name,
    FP.desc2,
    FP.dbstatus,
    FP.status1,
    MORE_INFO.DivNo,
    MORE_INFO.StoreNo,
    MORE_INFO.num_of_pogs,
    MORE_INFO.num_of_items,
    MORE_INFO.str_dbkey,
    FP.desc1 
ORDER BY Div, Store, Floorplan_Department, Status, Live_Date desc;`

export let pogTabQueryWithDivStoreDBKey = `Select distinct *
                        FROM (
                                 Select pog.status1 AS Status,
                                        pog.Desc6 AS 'POG Type', 
                                        FORMAT(pog.PendingDate, 'MM-dd-yy') AS 'Pending Date', 
                                        FORMAT(pog.dbdateeffectivefrom, 'MM-dd-yy') AS 'Date Live', 
                                        pog.desc38 'Period Week Year', 
                                        CAST(pog.NumberOfStores AS varchar) AS 'Store Count',
                                        CAST(pog.NumberOfProductsAllocated AS varchar ) AS 'Number(#) of Items',
                                        CAST(pog.dbkey AS varchar ) AS DBkey,
                                        CASE
                                            WHEN pog.partid = '' OR pog.partid IS NULL THEN 'D' + CASE
                                                                                                      WHEN IsNumeric(pog.Desc39) = 1
                                                                                                          THEN pog.Desc39
                                                                                                      ELSE '___' END +
                                                                                            '-L' +
                                                                                            CASE
                                                                                                WHEN IsNumeric(pog.Desc1) = 1
                                                                                                    THEN pog.Desc1
                                                                                                ELSE '00000' END +
                                                                                            '-D' + CASE
                                                                                                       WHEN IsNumeric(Left(pog.Department,2))
                                                                                                           = 1
                                                                                                           THEN Left(pog.Department, 2) ELSE '__' END + '-C' + CASE WHEN IsNumeric(pog.Desc22) = 1 THEN pog.Desc22 ELSE '___' END +'-V___'
                                     + '-' + Left (COALESCE (pog.uom, '_'), 1) + Right ('000'+ COALESCE (CONVERT (varchar, pog.Value4), '___'), 3)
                                     + '-' + CASE WHEN COALESCE (pog.Desc21, '') = '' THEN '__' ELSE pog.Desc21 END ELSE pog.partid END 'Space Station Name',
                                     pog.desc42 'Space Station Version Description',
                                     pog.name 'POG Name'
                                 FROM [CKB].[jdacustom].RPT_STR_POG_MAP_LIVE_VW M_VIEW
                                     INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG
                                 ON M_VIEW.POG_DBKEY = POG.DBKEY
                                     INNER JOIN [CKB].[dbo].IX_STR_STORE str ON M_VIEW.STR_DBKEY = str.DBKEY
                                 WHERE str.dbstatus = 1
                                   AND pog.status1='Live'
                                   AND str.desc2 IN ('@Div')
                                   AND str.Desc3 IN ('@Store')
                                   AND pog.dbkey IN ('@DBKey')
                                   AND POG.dbstatus IN (1)) AS result
                        order by 'Space Station Name' asc, 'Date Live' asc`

export let compositeTabDBKeyQuery = `SELECT       
    CAST(MORE_INFO.division AS VARCHAR) as DIV,
    CAST(MORE_INFO.storenumber AS VARCHAR) AS Store,
    CAST(case when cv.cv_type = 1 THEN 'Live' ELSE 'Future Live' END as varchar) as CompositeViewType,
    CAST(max(try_cast(net.dbtime as date)) AS VARCHAR) AS Date,
    (select CONCAT(CONCAT('P',format(fsc.period,'0#')),' ',CONCAT('W',format(fsc.week,'0#')),' ', CONCAT('Y',format(fsc.Year,'0#'))) from [CKB].[jdacustom].fiscal_calendar fsc where date = max(try_cast(net.dbtime as date))) as PWY,
    CAST(cv.FloorLevel AS VARCHAR) as FloorLevel,
    CAST(MORE_INFO.num_of_pogs AS VARCHAR) AS #OfPlanograms,
    CAST(MORE_INFO.num_of_items AS VARCHAR) AS #OfItems
 from 
    (
    SELECT
       STR_DBKEY,
       STR.desc2               as division,
       STR.desc3               as storenumber,
       sum(pog_count)          as num_of_pogs,
       sum(product_count)      as num_of_items
    from 
       (
       SELECT
          STR_DBKEY, 
          max(DISTINCT_PRODUCT_COUNT)     as product_count,
          1                               as pog_count
     FROM 
        [CKB].[jdacustom].RPT_STR_POG_MAP_LIVE_VW M_VIEW 
     group by STR_DBKEY, DISTINCT_PRODUCT_COUNT, POG_DBKEY
       ) 
       AS STR_FP_POG_INFO_SEQ INNER JOIN 
       [CKB].[dbo].IX_STR_STORE STR ON STR_FP_POG_INFO_SEQ.STR_DBKEY = STR.DBKEY 
       group by STR_DBKEY, STR.desc2, STR.desc3
    ) AS MORE_INFO LEFT OUTER JOIN 
    [CKB].[JDACUSTOM].CSG_COMPOSITE_VIEW_DOCUMENT AS CV ON MORE_INFO.STR_DBKEY = CV.DBPARENTSTOREKEY        LEFT OUTER JOIN 
    [CKB].[JDACUSTOM].CSG_COMPOSITE_VIEW_DETAIL AS CV_DTL ON  CV.DBPARENTSTOREKEY = CV_DTL.DBPARENTSTOREKEY LEFT OUTER JOIN 
    [CKB].[dbo].ix_net_document net with (nolock) ON CV_DTL.DBPARENTFLOORPLANKEY = net.DBPARENTFLOORPLANKEY 
WHERE 
   1=1   AND 
   MORE_INFO.STR_DBKEY IN (@DBKey)
group by 
   cv.CV_Type, cv_document, cv.FloorLevel, 
   MORE_INFO.division,
   MORE_INFO.storenumber, 
   MORE_INFO.num_of_pogs,
   MORE_INFO.num_of_items,
   MORE_INFO.STR_DBKEY
order by division, CompositeViewType desc, store, FloorLevel;`

export const getAllDeptDropdownQuery = `SELECT distinct RTRIM(POG.DEPARTMENT) AS Department
FROM [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
         INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG ON M_VIEW.POG_DBKEY = POG.DBKEY
         INNER JOIN [CKB].[dbo].IX_STR_STORE STR ON M_VIEW.STR_DBKEY = STR.DBKEY
WHERE STR.DBSTATUS = 1 order by Department;`


export const getAllDivisionDropdownQuery = `SELECT DISTINCT A.DESC2 AS division,
                                                          A.DESC1 AS divisionName
                                          FROM CKB.DBO.IX_STR_STORE A
                                                   JOIN CKB.DBO.IX_STR_STORE_FLOORPLAN B
                                                        ON B.DBPARENTSTOREKEY = A.DBKEY
                                                   JOIN CKB.DBO.IX_FLR_FLOORPLAN C
                                                        ON C.DBKEY = B.DBPARENTFLOORPLANKEY
                                          WHERE C.DBSTATUS IN (1,2,7,6,3)
                                            AND A.DBSTATUS = 1
                                            AND A.DESC1 != '88888';`


export const getAllStoreDropdownQuery = `SELECT distinct RTRIM(STR.desc3) AS Store
                                          FROM [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
                                              INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG ON M_VIEW.POG_DBKEY = POG.DBKEY
                                              INNER JOIN [CKB].[dbo].IX_STR_STORE STR ON M_VIEW.STR_DBKEY = STR.DBKEY
                                          WHERE STR.DBSTATUS = 1
                                          order by Store;`

export const getAllStoreDropdownByDivQuery = `SELECT distinct STR.DESC2 AS division,
                                                RTRIM(STR.desc3) AS Store
                                            FROM [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
                                                INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG ON M_VIEW.POG_DBKEY = POG.DBKEY
                                                INNER JOIN [CKB].[dbo].IX_STR_STORE STR ON M_VIEW.STR_DBKEY = STR.DBKEY
                                            WHERE STR.DBSTATUS = 1
                                              AND STR.DESC2 IN ('@Div')
                                            ORDER BY Store;`


export const getPogDBKeyQuery = `Select top (@topNum)
       pog_type,
       pog_dbkey
FROM (Select distinct *,
POGTypeOrder = case when pog_type in ('Display Space', 'White Space') THEN 1 ELSE 0 END
      FROM (SELECT distinct POG.DESC6  AS    pog_type,
                               CAST(POG.DBKEY AS varchar) AS pog_dbkey
               FROM [CKB].[jdacustom].RPT_STR_POG_MAP_LIVE_VW M_VIEW
                        INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG
                                   ON M_VIEW.POG_DBKEY = POG.DBKEY
                        INNER JOIN [CKB].[dbo].IX_FLR_FLOORPLAN FP ON M_VIEW.FP_DBKEY = FP.DBKEY
                        INNER JOIN [CKB].[dbo].IX_FLR_SECTION SEC
                                   ON SEC.DBPARENTFLOORPLANKEY = FP.DBKEY AND SEC.DBPARENTPLANOGRAMKEY = POG.DBVERSIONKEY
                        INNER JOIN [CKB].[JDACUSTOM].CSG_DEPT DEP ON DEP.DEPT_CD = M_VIEW.FFIX_DEPT
                        INNER JOIN [CKB].[dbo].IX_STR_STORE STR ON M_VIEW.STR_DBKEY = STR.DBKEY
                        LEFT OUTER JOIN [CKB].[jdacustom].csg_Aisle AISLE_N
                                        ON M_VIEW.AISLE_NO = RIGHT ('000' + ISNULL(CAST (AISLE_N.AISLE_NO AS VARCHAR), ''), 3)
               WHERE
                      STR.DESC2 = '@div'
                 AND STR.DBSTATUS = 1
                 AND POG.DBSTATUS = 1
                 AND FP.DBSTATUS = 1
           ) AS result) AS X
order by pog_dbkey desc ;`


export let mappingReportMultipleStoreDivQuery = `Select distinct *,count(*) over() as totalrecords,aisleOrder = case when aisle is null or aisle = '' then 1 else 0 end FROM 
(
    SELECT distinct concat(STR.Desc2,STR.DESC3,M_VIEW.FP_DBKEY,M_VIEW.POG_DBKEY,POG.DESC6,POG.NAME,POG.DESC37,POG.DESC42,POG.LIVEDATE,
                           POG.PENDINGDATE,POG.STATUS1,FP.LIVEDATE,FP.PENDINGDATE,FP.STATUS1,M_VIEW.DISTINCT_PRODUCT_COUNT,M_VIEW.STRIP_COLOR,
                           M_VIEW.AISLE_NO,M_VIEW.AISLE_SIDE_CD,AISLE_N.AISLE_NAME,M_VIEW.POG_SEQUENCE,M_VIEW.MIN_POG_BAY,M_VIEW.MAX_POG_BAY,DEP.DEPT_NAME,
                           STR.DESC49,STR.DESC7,STR.EMAIL,POG.DEPARTMENT,POG.DESC10,POG.DESC22,POG.DESC3,POG.NUMBEROFSTORES) AS unikey,
         STR.DESC2 AS division,
         STR.DESC3 AS store_number,
         STR.DESC49 AS store_type,
         FP.DBKEY AS fp_dbkey,
         DEP.DEPT_NAME AS dept,
         POG.DBKEY AS pog_dbkey,
         POG.DESC6 AS pog_type,
         POG.NAME AS pog_name,
         POG.DESC37 AS space_station_name,
         POG.DESC42 AS space_station_version_description,
         FORMAT (POG.LIVEDATE, 'MM-dd-yy') pog_livedate,
         FORMAT (POG.PENDINGDATE, 'MM-dd-yy') pog_pendingdate,
         POG.STATUS1 AS pog_status,
         FORMAT (FP.LIVEDATE, 'MM-dd-yy') fp_livedate,
         FORMAT (FP.PENDINGDATE, 'MM-dd-yy') fp_pendingdate,
         FP.STATUS1 AS fp_status,
         M_VIEW.DISTINCT_PRODUCT_COUNT AS num_of_items,
         M_VIEW.STRIP_COLOR AS strip_color,
         M_VIEW.AISLE_NO AS aisle,
         M_VIEW.AISLE_SIDE_CD AS orientation,
         AISLE_N.AISLE_NAME AS ad,
         M_VIEW.POG_SEQUENCE AS pog_sequence,
         M_VIEW.MIN_POG_BAY AS min_bay,
         M_VIEW.MAX_POG_BAY AS max_bay,
         STR.DESC7 AS store_segment,
         STR.EMAIL AS alt_store_number,
         POG.DEPARTMENT AS pog_dept,
         POG.DESC10 AS sub_dept,
         POG.DESC22 AS comm_number,
         POG.DESC3 AS comm_group,
         POG.NUMBEROFSTORES AS num_of_stores 
    FROM 
        [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW  M_VIEW                                    INNER JOIN 
        [CKB].[dbo].IX_SPC_PLANOGRAM                     POG     ON M_VIEW.POG_DBKEY = POG.DBKEY   INNER JOIN 
        [CKB].[dbo].IX_FLR_FLOORPLAN                     FP      ON M_VIEW.FP_DBKEY = FP.DBKEY     INNER JOIN 
        [CKB].[JDACUSTOM].CSG_DEPT                       DEP     ON DEP.DEPT_CD = M_VIEW.FFIX_DEPT INNER JOIN 
        [CKB].[dbo].IX_STR_STORE                         STR     ON M_VIEW.STR_DBKEY = STR.DBKEY   LEFT OUTER JOIN 
        [CKB].[jdacustom].csg_Aisle                      AISLE_N ON M_VIEW.AISLE_NO = RIGHT('000'+ISNULL(CAST(AISLE_N.AISLE_NO AS VARCHAR),''),3) 
    WHERE 
        STR.DBSTATUS = 1 AND 
        STR.desc2 IN (SELECT value FROM STRING_SPLIT('@Div','T')) AND 
        STR.dbkey IN (SELECT value FROM STRING_SPLIT('@Store','T')) 
) 
AS result order by division asc, store_number asc, case when aisle is null or aisle = '' then 1 else 0 end, aisle asc, orientation asc, min_bay asc, max_bay asc, pog_sequence asc; `


export const ilpComparisonNew = `select distinct 
FS.division as division,
FS.store_number as store,
'N' as REM_FLAG,
ILT.upc as upc,
'ILP' as system,
ILT.cao_aisle_number as caoAisleNumber,
ILT.cao_aisle_desc as caoAisleDescription,
ILT.cao_aisle_side as orientation,
ILT.cao_bay_number_in_aisle as bayInAisle,
'' as BayinPOG,
ILT.cao_shelf_number as fixtureNumber,
ILT.cao_position as positionNumber,
ILT.pog_dbkey as dbkey,
PE.pog_status as status,
'' as liveDate,
ILT.pog_name as ssn,
'' as [Version Desc],
'' as [KOM Completion? Y/N],
'' as [Reason],
'' as [Completion Date],
'' as [No Response]
FROM [dbo].ITEM_LOCATION ILT
INNER JOIN [dbo].FACILITY_STORE FS ON ILT.FACILITY_ID = FS.FACILITY_ID
INNER JOIN [dbo].PROCESSING_EVENT PE ON ILT.POG_DBKEY = PE.POG_DBKEY and PE.store_dbkey = FS.STORE_DBKEY
where ILT.IS_ACTIVE = 'true' AND FS.DIVISION IN (SELECT value FROM STRING_SPLIT('@div','T'))
and FS.STORE_DBKEY IN (SELECT value FROM STRING_SPLIT('@str','T')) and ILT.UPC IN (SELECT value FROM STRING_SPLIT('@upcList',',')) order by ILT.upc asc`