const queryParam = require('./queryString');
const globalQueryParam = require('./globalQueries')
const DB_ENV = require('./dbconfig.json');
const sql = require("mssql");
const { DBNames } = require('../envConfig.js');

function getResult(queryParam, dbName = DBNames.SFP_STAGE) {                                                //function needs to be exported
    return new Promise((resolve, reject) => {                                   //wrapping the connection with Promise
        let connect = sql.connect(DB_ENV[dbName], function (err) {     // 1. connect to db
            if (err) {
                reject(err + "Error: DB connection failed")
            }                                                                  //Execution stops if DB connect fails
            let request = new sql.Request();                                // 2. create Request object
            request.query(queryParam, function (err, recordset) {       // 3. query to the database and get the records
                if (err) {                                              //if (err) reject(err)
                    console.log("Error: Something is wrong with the query!!! " + err)
                    reject(err);                                       //rejection of promise is needed here to stop test execution
                    return;
                } else if (recordset.length === 0) {
                    console.log("DB connection result is empty ")
                } else {
                    console.log("DB connection is successful ")            //recordset can be printed on this step if required
                }
                let recordList = recordset.recordset.map(record => Object.values(record)) //reading all objects with JS Object
                resolve(recordList)
                resolve(connect.close())                            // close the DB connection
            })
        })
    })
}

function executeQuery(globalQueryParam) {
    return new Promise((resolve, reject) => {                                   //wrapping the connection with Promise
        let connect = sql.connect(DB_ENV['SFP-Stage'], function (err) {     // 1. connect to db
            if (err) {
                reject(err + "Error: DB connection failed")
            }                                                                  //Execution stops if DB connect fails
            let request = new sql.Request();                                // 2. create Request object
            request.query(globalQueryParam, function (err) {       // 3. query to the database and get the records
                if (err) {                                              //if (err) reject(err)
                    console.log("Error: Something is wrong with the query!!! " + err)
                    reject(err);                                       //rejection of promise is needed here to stop test execution
                    return;
                } else {
                    console.log("DB connection is successful ")            //recordset can be printed on this step if required
                }
                // resolve(recordset)
                console.log('closing connection')
                resolve(connect.close())                            // close the DB connection
            })
        })
    })
}

function getResultCKB(queryParam) {                                                //function needs to be exported
    return new Promise((resolve, reject) => {                                   //wrapping the connection with Promise
        let connect = sql.connect(DB_ENV['CKB-Stage'], function (err) {     // 1. connect to db
            if (err) {
                reject(err + "Error: DB connection failed")
            }                                                                  //Execution stops if DB connect fails
            let request = new sql.Request();                                // 2. create Request object
            request.query(queryParam, function (err, recordset) {       // 3. query to the database and get the records
                if (err) {                                              //if (err) reject(err)
                    console.log("Error: Something is wrong with the query!!! " + err)
                    reject(err);                                       //rejection of promise is needed here to stop test execution
                    return;
                } else if (recordset.length === 0) {
                    console.log("DB connection result is empty ")
                } else {
                    console.log("DB connection is successful ")            //recordset can be printed on this step if required
                }
                let recordList = recordset.recordset.map(record => Object.values(record)) //reading all objects with JS Object
                resolve(recordList)
                resolve(connect.close())                            // close the DB connection
            })
        })
    })
}

module.exports = {
    getResult: getResult,
    executeQuery: executeQuery,
    getResultCKB: getResultCKB
};
