export let spaceStationModifiedDateQuery = `Select FORMAT(DELETE_REQUESTED_DATE, 'MM-dd-yyyy HH:mm:ss') AS VERSION_DSC
                                            From [SFPApps].[dbo].[sfp_oss_spaceStation_version_tbl]
                                            where VERSION_DSC = '@Description';`

export let spaceStationVersionIdQuery = `Select VERSION_NO
                                         From [dbo].[sfp_oss_spaceStation_version_tbl]
                                         where VERSION_DSC= '@Description';`

export let spaceStationVersionFiltersQuery = `Select FAMILY_TREE_PRIMARY_DEPARTMENT_CD,
                                                     FAMILY_TREE_COMMODITY_CD,
                                                     VERSION_NO,
                                                     VERSION_DSC AS VERSION_DSC
                                              From [SFPApps].[dbo].[sfp_oss_spaceStation_version_tbl]
                                              where VERSION_DSC = '@Description'; `

export let addVersionCommodityDropdownQuery = `Select DISTINCT concat(cdt.cpt_com_cd, '-', RTRIM(cdt.com_nam_tx)) as data
                                               FROM familyTree_primary_dept_tbl pdt,
                                                    familyTree_commodity_dept_tbl cdt,
                                                    familyTree_corp_dept_tbl corp
                                               WHERE pdt.pri_dpt_cd = corp.pri_dpt_cd
                                                 and corp.cpt_dpt_cd = cdt.cpt_dpt_cd
                                                 and pdt.pri_dpt_cd = '@DeptNum' `

export let sathFilterDivisionDropdownQuery = `SELECT DISTINCT (A.DESC2 + '-' + A.DESC1) AS DIVISION
                                              FROM CKB.DBO.IX_STR_STORE A
                                                       JOIN CKB.DBO.IX_STR_STORE_FLOORPLAN B
                                                            ON B.DBPARENTSTOREKEY = A.DBKEY
                                                       JOIN CKB.DBO.IX_FLR_FLOORPLAN C
                                                            ON C.DBKEY = B.DBPARENTFLOORPLANKEY
                                              WHERE C.DBSTATUS IN (1, 2, 7, 6, 3)
                                                AND A.DBSTATUS = 1
                                                AND A.DESC1 != '88888'`

export let sathFilterStatusQuery = `SELECT distinct status as STATUS
                                    from [SFPAPPS].[dbo].[sfp_threshold_status_tbl];`

export let sathFilterDropdownQuery2 = `SELECT distinct RTRIM(STR.DESC3) AS Store
                                      FROM [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
                                          INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG
                                      ON M_VIEW.POG_DBKEY = POG.DBKEY
                                          INNER JOIN [CKB].[dbo].IX_STR_STORE STR ON M_VIEW.STR_DBKEY = STR.DBKEY
                                      WHERE STR.DBSTATUS = 1 AND STR.desc2 IN ('@Div')
                                      order by Store; `

export let sathFilterDropdownQuery = `Select Distinct  A.Desc3 AS Location
                                      FROM CKB.DBO.IX_STR_STORE A
                                               JOIN CKB.DBO.IX_STR_STORE_FLOORPLAN B ON B.DBPARENTSTOREKEY = A.DBKEY
                                               JOIN CKB.DBO.IX_FLR_FLOORPLAN C ON C.DBKEY = B.DBPARENTFLOORPLANKEY
                                      WHERE C.DBSTATUS IN (1,2,7,6,3)
                                        AND A.DBSTATUS = 1
                                        AND A.DESC1 != '88888'
                                        AND ISNULL(C.DESC2,'') != 'SCHED'
                                        AND A.Desc2 IS NOT NULL
                                        AND A.Desc2 != 'N/A'
                                        AND A.Desc3 IS NOT NULL
                                        AND A.Desc3 != 'N/A'
                                        AND A.Desc2 in ('@Div'); `

export let sathTableQuery = `SELECT TOP(@num)
                                    div_no AS Div, sto_no AS Store,
                                    CAST(rule_id as varchar)                   AS RuleID,
                                    rule_desc                                  AS Description,
                                    CAST(threshold_value as varchar)           AS ThresholdValue,
                                    case
                                        WHEN status = 'P' THEN 'Pass'
                                        WHEN status = 'F' THEN 'Fail'
                                        ELSE convert(varchar (10), status) END AS ProcessStatus,
                                    FORMAT(insert_date, 'yyyy-MM-dd HH:mm:ss') AS ProcessedDate,
                                    CAST(previous_good_value as varchar)       AS PreviousValue,
                                    CAST(current_value as varchar)             AS CurrentValue
                             from [SFPAPPS].[dbo].[sfp_threshold_status_tbl]
                             where div_no = '@Div'
                               and sto_no = '@Store'
                               and status = '@Status'
                             order by div_no asc, sto_no asc, ProcessStatus desc, insert_date asc; `

export let sathStatusQuery = `SELECT case
                                         WHEN status = 'P' THEN 'Pass'
                                         WHEN status = 'F' THEN 'Fail'
                                         ELSE convert(varchar (10), status) END AS ProcessStatus
                              FROM [SFPAPPS].[dbo].[sfp_threshold_status_tbl]
                              WHERE 
                                insert_date IN ('@PDate');`

export let sathExcelDataQuery = `SELECT STORE.desc2 AS DIVISION,
                                        STORE.desc3 AS STORE_NO,
                                        CAST(POG.dbkey as varchar) AS POG_DBKEY,
                                        POG.desc37 AS POG_NAME,
                                        CASE
                                            WHEN POG.dbstatus = 1 THEN 'Live'
                                            ELSE CAST(POG.dbstatus as varchar) END AS POG_STATUS,
                                        CAST(FLIVE_VW.FP_DBKEY as varchar) AS FLOOR_PLAN_DBKEY,
                                        CASE
                                            WHEN flr.dbStatus = 1 THEN 'Live'
                                            ELSE CAST(flr.dbStatus as varchar) END AS FLOOR_PLAN_STATUS,
                                        CASE
                                            WHEN (FLIVE_VW.area_type = 'CL' OR FLIVE_VW.area_type = 'US')
                                                then '300'
                                            ELSE REPLACE(LTRIM(REPLACE(FLIVE_VW.aisle_no, '0', ' ')),' ', '0')END AS aisle_no,
                                        FLIVE_VW.POG_sequence AS aisle_seq_no,
                                        CASE
                                            WHEN (FLIVE_VW.area_type = 'CL' OR FLIVE_VW.area_type = 'US')
                                                THEN 'L'
                                            ELSE FLIVE_VW.aisle_side_cd END AS aisle_orn_cd
                                 FROM ckb.jdacustom.rpt_str_pog_map_futurelive_vw FLIVE_VW
                                          INNER JOIN ckb.dbo.ix_str_store AS STORE with (nolock)
                                 ON FLIVE_VW.str_dbkey = STORE.dbkey
                                     INNER JOIN ckb.dbo.ix_spc_planogram as POG
                                 with (nolock)
                                 ON FLIVE_VW.POG_dbkey = POG.dbkey
                                     INNER JOIN ckb.dbo.ix_flr_floorplan flr ON FLIVE_VW.FP_DBKEY = flr.dbkey
                                 WHERE 1=1
                                   AND POG.dbstatus in (1)
                                   AND POG.partid not like '%CPHC%'
                                   AND POG.partid not like '%_V000_F000_%'
                                   AND STORE.flag3 = 1
                                   AND STORE.desc2 = '@Div'
                                   AND STORE.desc3 = '@Store'
                                 ORDER BY POG.dbkey, STORE.desc3;`
                                 
export let ossTableQuery = `Select distinct TOP (@num)
                                ver.FAMILY_TREE_PRIMARY_DEPARTMENT_CD as department,
                                    ver.FAMILY_TREE_COMMODITY_CD as commodity,
                                            ver.VERSION_NO as versionID,
                                            CASE ver.POG_STATUS WHEN 1 THEN 'Live' WHEN 0 THEN '' ELSE CAST(ver.POG_STATUS as varchar) END as pogStatus,
                                            REPLACE(ver.VERSION_DSC, NCHAR(0x00A0), '') as versionDesc,
                                            LTRIM(RTRIM(ver.NUM_OF_POGS)) as noOfPOGs,
                                            LTRIM(RTRIM(ver.MAPPED_POGS)) as mappedPOGs,
                                            FORMAT(ver.CREATED_DATE, 'MM-dd-yyyy HH:mm:ss') as createdDate,
                                            ver.CREATED_BY as createdBy,
                                            FORMAT(ver.MODIFIED_DATE, 'MM-dd-yyyy HH:mm:ss') as modifiedDate,
                                            ver.MODIFIED_BY as modifiedBy,
                                            CASE WHEN ver.DELETE_REQUESTED_DATE is null THEN '' ELSE FORMAT(ver.DELETE_REQUESTED_DATE, 'MM-dd-yyyy HH:mm:ss') END  as deleteReqDate,
                                            CASE WHEN ver.DELETE_REQUESTED_BY is null THEN '' ELSE CAST(ver.DELETE_REQUESTED_BY AS varchar) END as deleteReqBy
                               FROM [SFPAPPS].[dbo].[sfp_oss_spaceStation_version_tbl] ver
                              where POG_STATUS != 'Historic' and DELETED_DATE is null
                                AND FAMILY_TREE_PRIMARY_DEPARTMENT_CD IN ('@Depart' )
                                AND FAMILY_TREE_COMMODITY_CD IN ('@Comm')
                              order by department asc, commodity asc, versionID asc;`

export let ssFindRecordsForPogMapping = `select FAMILY_TREE_PRIMARY_DEPARTMENT_CD, FAMILY_TREE_COMMODITY_CD, VERSION_NO
                                         FROM [CKB].[jdacustom].[csg_ss_pog_vw] csg
                                         where
                                             VERSION_NO not in (
                                             select VERSION_NO
                                             from [SFPAPPS].[dbo].[sfp_oss_spaceStation_version_tbl] );`

export let updateMappedPogsQuery = `update V
                                    set V.MAPPED_POGS =
                                            (
                                                SELECT COUNT(csg.POG_ID)
                                                FROM [CKB].[jdacustom].[csg_ss_pog_vw] csg
                                    WITH (NOLOCK)
                                    WHERE (csg.POG_STATUS_CD = 'WIP'
                                       OR csg.POG_STATUS_CD = 'PROD')
                                      AND csg.[POG_NAME] IN (SELECT POG.DESC37 FROM [CKB].[dbo].IX_SPC_PLANOGRAM POG WITH (NOLOCK))
                                      and csg.FAMILY_TREE_PRIMARY_DEPARTMENT_CD = V.FAMILY_TREE_PRIMARY_DEPARTMENT_CD
                                      and csg.FAMILY_TREE_COMMODITY_CD = V.FAMILY_TREE_COMMODITY_CD
                                      and csg.version_no = V.VERSION_NO
                                        )
                                    from [SFPAPPS].[dbo].[sfp_oss_spaceStation_version_tbl] V`;

export let updateNumOfPogsQuery = `update V
                                   set V.NUM_OF_POGS =
                                           (
                                               SELECT COUNT(csg.POG_ID)
                                               FROM [CKB].[jdacustom].[csg_ss_pog_vw] csg
                                   WITH (NOLOCK)
                                   WHERE (csg.POG_STATUS_CD = 'WIP'
                                      OR csg.POG_STATUS_CD = 'PROD')
                                     and csg.FAMILY_TREE_PRIMARY_DEPARTMENT_CD = V.FAMILY_TREE_PRIMARY_DEPARTMENT_CD
                                     and csg.FAMILY_TREE_COMMODITY_CD = V.FAMILY_TREE_COMMODITY_CD
                                     and csg.version_no = V.VERSION_NO
                                       )
                                   from [SFPAPPS].[dbo].[sfp_oss_spaceStation_version_tbl] V;`

export let calculatePogStatusQuery = `select distinct pog.dbstatus                          as status
                                                    , csg.FAMILY_TREE_PRIMARY_DEPARTMENT_CD as dept
                                                    , csg.FAMILY_TREE_COMMODITY_CD          as comm
                                                    , csg.version_no                        as version
                                      into #trdx
                                      from [CKB].[jdacustom].[csg_ss_pog_vw] csg
                                          join [CKB].[dbo].IX_SPC_PLANOGRAM POG
                                      on csg.pog_name = pog.desc37
                                      WHERE
                                          (csg.POG_STATUS_CD = 'WIP'
                                         OR csg.POG_STATUS_CD = 'PROD')
                                        and POG.dbstatus != 200
                                        AND POG.dbstatus != 4
                                        AND POG.dbstatus != 8;
             update V
                 set V.POG_STATUS =
        case
            when V.NUM_OF_POGS = 0 then 0
            else (
                select case
                           when min(status) is null then 0
                           else min(status)
                           end
                FROM #trdx x
                where x.dept = V.FAMILY_TREE_PRIMARY_DEPARTMENT_CD
                  and x.comm = V.FAMILY_TREE_COMMODITY_CD
                  and x.version = V.VERSION_NO
            )
            end from [SFPAPPS].[dbo].[sfp_oss_spaceStation_version_tbl] V
             drop table #trdx;`

export let ssPogInfoQuery = `SELECT distinct POG.STATUS1 AS                      pogStatus,
                                             POG.DESC6   AS                      pogType,
                                             POG.DBKEY   AS                      dbkey,
                                             POG.DESC37  AS                      ssn,
                                             POG.NAME    AS                      pogName,
                                             FORMAT(POG.PENDINGDATE, 'MM-dd-yy') pendingDate,
                                             FORMAT(POG.LIVEDATE, 'MM-dd-yy')    liveDate
                             FROM [CKB].[dbo].IX_SPC_PLANOGRAM POG
                             WHERE POG.DBSTATUS not in (4
                                 , 8)
                               AND POG.DESC37 IN ('@SSN' )
                             order by dbkey asc;`

export let pogMappedInfoQuery = `SELECT distinct STR.DESC2  AS                      division,
                                                 STR.DESC3  AS                      store,
                                                 POG.DBKEY  AS                      pogDbkey,
                                                 POG.DESC37 AS                      ssn,
                                                 FP.DBKEY   AS                      fpDbkey,
                                                 FP.STATUS1 AS                      fpStatus,
                                                 FORMAT(FP.LIVEDATE, 'MM-dd-yy')    fpLiveDate,
                                                 FORMAT(FP.PENDINGDATE, 'MM-dd-yy') fpPendingDate
                                 FROM [CKB].[jdacustom].RPT_STR_POG_MAP_FUTURELIVE_VW M_VIEW
                                     INNER JOIN [CKB].[dbo].IX_SPC_PLANOGRAM POG
                                 ON M_VIEW.POG_DBKEY = POG.DBKEY
                                     INNER JOIN [CKB].[dbo].IX_FLR_FLOORPLAN FP ON M_VIEW.FP_DBKEY = FP.DBKEY
                                     INNER JOIN [CKB].[dbo].IX_STR_STORE STR ON M_VIEW.STR_DBKEY = STR.DBKEY
                                 WHERE STR.DBSTATUS = 1
                                   AND POG.DESC37 IN ('@SSN')
                                 order by division asc, store asc`

export let updateNewVersionWithZeroPogQuery = `update sfp_oss_spaceStation_version_tbl
                                               set NUM_OF_POGS = 0,
                                                   MAPPED_POGS = 0
                                               where FAMILY_TREE_PRIMARY_DEPARTMENT_CD = '@Depart'
                                                 and FAMILY_TREE_COMMODITY_CD = '@Comm'
                                                 and VERSION_NO = '@VersionId';`

export let readNewVersionWhenPogIsZeroQuery = `select *
                                               from sfp_oss_spaceStation_version_tbl
                                               where FAMILY_TREE_PRIMARY_DEPARTMENT_CD = '@Depart'
                                                 and FAMILY_TREE_COMMODITY_CD = '@Comm'
                                                 and VERSION_NO = '@VersionId';`



