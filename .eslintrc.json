{"env": {"browser": true, "es6": true, "jest": true, "webextensions": true, "jquery": true, "node": true, "mocha": true}, "extends": ["plugin:wdio/recommended", "eslint:recommended"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"semi": ["off", "always"], "camelcase": ["error", {"properties": "always"}], "no-undef": ["off"], "no-unreachable": ["error"], "no-unused-vars": ["warn"], "require-await": ["warn"], "await-expect": ["off"], "no-unexpected-multiline": ["off"], "wdio/await-expect": ["warn"]}}